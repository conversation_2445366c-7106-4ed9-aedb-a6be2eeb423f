#!/bin/bash

# CodeReview 测试结果合并脚本
# 与 finetune_CodeReview.sh 配合使用，自动合并生成的测试结果

# 设置默认路径（与 finetune_CodeReview.sh 中的路径保持一致）
CURRENT_DIR=${CURRENT_DIR:-"/data/xjd/SodaCoder-main"}
OUTPUT_DIR="${CURRENT_DIR}/outputs/models/fine-tuning/CodeReview"
MODEL_DIR="${CURRENT_DIR}/models/fine-tuning/CodeReview"

# 测试集文件路径
TEST_FILE="${CURRENT_DIR}/Dataset/fine-tuning/CodeReview/test.jsonl"

# 输出文件名
OUTPUT_FILE="merged_codereview_results.jsonl"

# 使用说明
usage() {
    echo "Usage: $0 [OPTIONS]"
    echo "Options:"
    echo "  -d, --dir DIR          指定搜索结果文件的目录 (默认: $OUTPUT_DIR)"
    echo "  -t, --test-file FILE   指定测试集文件路径 (默认: $TEST_FILE)"
    echo "  -o, --output FILE      指定输出文件名 (默认: $OUTPUT_FILE)"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                                    # 使用默认设置"
    echo "  $0 -d /path/to/results               # 指定结果文件目录"
    echo "  $0 -t /path/to/test.jsonl            # 指定测试集文件"
    echo "  $0 -o my_results.jsonl               # 指定输出文件名"
    exit 1
}

# 解析命令行参数
SEARCH_DIR="$OUTPUT_DIR"

while [[ $# -gt 0 ]]; do
    case $1 in
        -d|--dir)
            SEARCH_DIR="$2"
            shift 2
            ;;
        -t|--test-file)
            TEST_FILE="$2"
            shift 2
            ;;
        -o|--output)
            OUTPUT_FILE="$2"
            shift 2
            ;;
        -h|--help)
            usage
            ;;
        *)
            echo "未知选项: $1"
            usage
            ;;
    esac
done

echo "=== CodeReview 测试结果合并 ==="
echo "测试集文件: $TEST_FILE"
echo "搜索目录: $SEARCH_DIR"
echo "输出文件: $OUTPUT_FILE"
echo ""

# 检查测试集文件是否存在
if [ ! -f "$TEST_FILE" ]; then
    echo "错误: 测试集文件不存在: $TEST_FILE"
    echo "请确保已正确设置测试集文件路径"
    exit 1
fi

# 检查搜索目录是否存在
if [ ! -d "$SEARCH_DIR" ]; then
    echo "错误: 搜索目录不存在: $SEARCH_DIR"
    echo "请确保已运行 finetune_CodeReview.sh 脚本生成结果文件"
    exit 1
fi

# 检查 Python 脚本是否存在
if [ ! -f "merge_test_results.py" ]; then
    echo "错误: 找不到 merge_test_results.py 脚本"
    echo "请确保该脚本在当前目录中"
    exit 1
fi

# 运行合并脚本
echo "正在合并测试结果..."
python merge_test_results.py \
    --test-file "$TEST_FILE" \
    --search-dir "$SEARCH_DIR" \
    --output-file "$OUTPUT_FILE"

# 检查合并是否成功
if [ $? -eq 0 ]; then
    echo ""
    echo "✅ 合并成功完成！"
    echo "输出文件: $OUTPUT_FILE"
    
    # 显示文件信息
    if [ -f "$OUTPUT_FILE" ]; then
        line_count=$(wc -l < "$OUTPUT_FILE")
        echo "合并数据条数: $line_count"
        
        echo ""
        echo "合并结果预览 (前2条):"
        head -n 2 "$OUTPUT_FILE" | while read line; do
            echo "$line" | python -m json.tool 2>/dev/null || echo "$line"
            echo "---"
        done
    fi
    
    echo ""
    echo "合并后的数据包含以下字段:"
    echo "  - index: 数据索引"
    echo "  - oldf: 原始代码 (code change 的原始部分)"
    echo "  - patch: 代码变更 (code change 的差异部分)"
    echo "  - predicted_msg: 模型预测的提交消息"
    echo "  - actual_msg: 实际的提交消息"
    
else
    echo ""
    echo "❌ 合并失败！"
    echo "请检查错误信息并确保:"
    echo "1. 已运行 finetune_CodeReview.sh 脚本"
    echo "2. 生成了 test-preds.txt 和 test-golds.txt 文件"
    echo "3. 测试集文件路径正确"
    exit 1
fi
