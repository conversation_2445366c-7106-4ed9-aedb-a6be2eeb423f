#!/usr/bin/env python3
"""
JSONL文件合并脚本
用于合并多个由negative_generation_inference.py生成的JSONL文件
"""

import json
import logging
import os
import argparse
from pathlib import Path
from typing import List, Dict, Any, Set
import glob

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def validate_jsonl_record(record: Dict[str, Any], line_num: int, file_path: str) -> bool:
    """验证JSONL记录的格式是否正确"""
    required_fields = [
        'idx', 'diff_original', 'diff_processed',
        'positive_comment', 'negative_comment', 'success'
    ]

    missing_fields = [field for field in required_fields if field not in record]
    if missing_fields:
        logger.warning(f"File {file_path}, Line {line_num}: Missing required fields: {missing_fields}")
        return False

    return True

def transform_record(record: Dict[str, Any]) -> Dict[str, Any]:
    """转换记录字段名称"""
    transformed = record.copy()

    # 重命名字段
    if 'diff_original' in transformed:
        transformed['patch'] = transformed.pop('diff_original')
    if 'positive_comment' in transformed:
        transformed['msg'] = transformed.pop('positive_comment')

    return transformed

def load_jsonl_file(file_path: str) -> List[Dict[str, Any]]:
    """加载单个JSONL文件"""
    records = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    line = line.strip()
                    if not line:
                        continue
                    
                    record = json.loads(line)
                    
                    # 验证记录格式
                    if validate_jsonl_record(record, line_num, file_path):
                        records.append(record)
                    else:
                        logger.warning(f"Skipping invalid record at line {line_num} in {file_path}")
                        
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error in {file_path} at line {line_num}: {e}")
                except Exception as e:
                    logger.error(f"Error processing line {line_num} in {file_path}: {e}")
                    
    except FileNotFoundError:
        logger.error(f"File not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        raise
    
    logger.info(f"Loaded {len(records)} valid records from {file_path}")
    return records

def merge_jsonl_files(input_files: List[str], output_file: str, 
                     remove_duplicates: bool = True, 
                     sort_by_idx: bool = True) -> None:
    """合并多个JSONL文件"""
    
    all_records = []
    seen_indices: Set[str] = set()
    duplicate_count = 0
    
    logger.info(f"Starting to merge {len(input_files)} files")
    
    # 逐个加载文件
    for file_path in input_files:
        logger.info(f"Processing file: {file_path}")
        records = load_jsonl_file(file_path)
        
        for record in records:
            idx = str(record['idx'])
            
            if remove_duplicates:
                if idx in seen_indices:
                    duplicate_count += 1
                    logger.debug(f"Duplicate idx found: {idx}")
                    continue
                seen_indices.add(idx)

            # 转换字段名称
            transformed_record = transform_record(record)
            all_records.append(transformed_record)
    
    # 排序（如果需要）
    if sort_by_idx:
        logger.info("Sorting records by idx")
        try:
            # 尝试按数字排序
            all_records.sort(key=lambda x: int(x['idx']))
        except ValueError:
            # 如果idx不是数字，按字符串排序
            all_records.sort(key=lambda x: str(x['idx']))
    
    # 保存合并结果
    logger.info(f"Saving merged results to {output_file}")
    # os.makedirs(os.path.dirname(output_file), exist_ok=True)
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for record in all_records:
            f.write(json.dumps(record, ensure_ascii=False) + '\n')
    
    # 统计信息
    successful_records = sum(1 for r in all_records if r.get('success', False))
    failed_records = len(all_records) - successful_records

    logger.info("=== Merge Complete ===")
    logger.info(f"Input files: {len(input_files)}")
    logger.info(f"Total records merged: {len(all_records)}")
    logger.info(f"Successful records: {successful_records}")
    logger.info(f"Failed records: {failed_records}")
    if remove_duplicates:
        logger.info(f"Duplicates removed: {duplicate_count}")
    logger.info(f"Output file: {output_file}")
    logger.info("Field transformations applied: diff_original → patch, positive_comment → msg")

def find_jsonl_files(directory: str, pattern: str = "*.jsonl") -> List[str]:
    """在指定目录中查找JSONL文件"""
    search_pattern = os.path.join(directory, pattern)
    files = glob.glob(search_pattern)
    files.sort()  # 按文件名排序
    return files

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="合并多个由negative_generation_inference.py生成的JSONL文件，并将字段重命名（diff_original→patch, positive_comment→msg）",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 合并指定的文件
  python merge_jsonl_files.py -i file1.jsonl file2.jsonl file3.jsonl -o merged.jsonl

  # 合并目录中的所有JSONL文件
  python merge_jsonl_files.py -d ./results/ -o merged.jsonl

  # 合并时保留重复项且不排序
  python merge_jsonl_files.py -d ./results/ -o merged.jsonl --keep-duplicates --no-sort

注意: 合并后的文件中，diff_original字段会重命名为patch，positive_comment字段会重命名为msg
        """
    )
    
    # 输入选项（互斥）
    input_group = parser.add_mutually_exclusive_group(required=True)
    input_group.add_argument(
        '-i', '--input-files', 
        nargs='+', 
        help='要合并的JSONL文件列表'
    )
    input_group.add_argument(
        '-d', '--directory', 
        help='包含JSONL文件的目录路径'
    )
    
    # 其他选项
    parser.add_argument(
        '-o', '--output', 
        required=True,
        help='输出文件路径'
    )
    parser.add_argument(
        '-p', '--pattern', 
        default='*.jsonl',
        help='文件匹配模式（仅在使用-d时有效，默认: *.jsonl）'
    )
    parser.add_argument(
        '--keep-duplicates', 
        action='store_true',
        help='保留重复的记录（默认会去重）'
    )
    parser.add_argument(
        '--no-sort', 
        action='store_true',
        help='不按idx排序（默认会排序）'
    )
    parser.add_argument(
        '-v', '--verbose', 
        action='store_true',
        help='显示详细日志'
    )
    
    args = parser.parse_args()
    
    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 获取输入文件列表
    if args.input_files:
        input_files = args.input_files
    else:
        input_files = find_jsonl_files(args.directory, args.pattern)
        if not input_files:
            logger.error(f"No JSONL files found in directory: {args.directory}")
            return
    
    # 验证输入文件存在
    missing_files = [f for f in input_files if not os.path.exists(f)]
    if missing_files:
        logger.error(f"The following files do not exist: {missing_files}")
        return
    
    logger.info(f"Found {len(input_files)} files to merge:")
    for f in input_files:
        logger.info(f"  - {f}")
    
    # 执行合并
    try:
        merge_jsonl_files(
            input_files=input_files,
            output_file=args.output,
            remove_duplicates=not args.keep_duplicates,
            sort_by_idx=not args.no_sort
        )
    except Exception as e:
        logger.error(f"Merge failed: {e}")
        raise

if __name__ == "__main__":
    main()
