#!/usr/bin/env python3
"""
合并测试结果脚本
将 test-preds.txt 和 test-golds.txt 与测试集文件合并，
只保留测试集中的 code change 部分（oldf 和 patch 字段）
"""

import json
import argparse
import os
from typing import List, Dict, Any


def load_jsonl(file_path: str) -> List[Dict[str, Any]]:
    """加载 JSONL 文件"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line:  # 跳过空行
                    try:
                        data.append(json.loads(line))
                    except json.JSONDecodeError as e:
                        print(f"警告: 第 {line_num} 行 JSON 解析错误: {e}")
                        continue
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"错误: 读取文件 {file_path} 时出错: {e}")
        return []
    
    return data


def load_text_lines(file_path: str) -> List[str]:
    """加载文本文件的每一行"""
    lines = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = [line.strip() for line in f.readlines()]
    except FileNotFoundError:
        print(f"错误: 找不到文件 {file_path}")
        return []
    except Exception as e:
        print(f"错误: 读取文件 {file_path} 时出错: {e}")
        return []
    
    return lines


def merge_results(test_file: str, preds_file: str, golds_file: str, output_file: str):
    """合并测试结果"""
    
    print(f"正在加载测试集文件: {test_file}")
    test_data = load_jsonl(test_file)
    if not test_data:
        print("错误: 无法加载测试集数据")
        return False
    
    print(f"正在加载预测结果文件: {preds_file}")
    preds = load_text_lines(preds_file)
    if not preds:
        print("错误: 无法加载预测结果")
        return False
    
    print(f"正在加载真实标签文件: {golds_file}")
    golds = load_text_lines(golds_file)
    if not golds:
        print("错误: 无法加载真实标签")
        return False
    
    # 检查数据长度一致性
    if len(test_data) != len(preds) or len(test_data) != len(golds):
        print(f"警告: 数据长度不一致")
        print(f"  测试集: {len(test_data)} 条")
        print(f"  预测结果: {len(preds)} 条")
        print(f"  真实标签: {len(golds)} 条")
        
        # 取最小长度
        min_len = min(len(test_data), len(preds), len(golds))
        print(f"  将使用前 {min_len} 条数据进行合并")
        test_data = test_data[:min_len]
        preds = preds[:min_len]
        golds = golds[:min_len]
    
    # 合并数据
    merged_data = []
    for i, (test_item, pred, gold) in enumerate(zip(test_data, preds, golds)):
        merged_item = {
            "index": i,
            # "oldf": test_item.get("oldf", ""),  # 原始代码
            "patch": test_item.get("patch", ""),  # 代码变更
            "predicted_msg": pred,  # 预测的提交消息
            "actual_msg": gold,  # 实际的提交消息
        }
        
        # 可选：添加其他可能有用的字段
        if "cmtid" in test_item:
            merged_item["cmtid"] = test_item["cmtid"]
        if "y" in test_item:
            merged_item["y"] = test_item["y"]
            
        merged_data.append(merged_item)
    
    # 保存合并结果
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in merged_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        print(f"成功合并 {len(merged_data)} 条数据到文件: {output_file}")
        return True
        
    except Exception as e:
        print(f"错误: 保存合并结果时出错: {e}")
        return False


def find_result_files(base_dir: str) -> tuple:
    """在指定目录中查找结果文件"""
    preds_file = None
    golds_file = None
    
    # 常见的文件名模式
    possible_preds = ["test-preds.txt", "test_cache-preds.txt", "preds.txt"]
    possible_golds = ["test-golds.txt", "test_cache-golds.txt", "golds.txt"]
    
    for filename in possible_preds:
        filepath = os.path.join(base_dir, filename)
        if os.path.exists(filepath):
            preds_file = filepath
            break
    
    for filename in possible_golds:
        filepath = os.path.join(base_dir, filename)
        if os.path.exists(filepath):
            golds_file = filepath
            break
    
    return preds_file, golds_file


def main():
    parser = argparse.ArgumentParser(description="合并测试结果文件")
    parser.add_argument("--test-file", "-t", required=True, -
                       help="测试集文件路径 (JSONL格式)")
    parser.add_argument("--preds-file", "-p", default="/data/xjd/SodaCoder-main/models/fine-tuning/CodeReview/test-preds.txt",
                       help="预测结果文件路径 (每行一个预测结果)")
    parser.add_argument("--golds-file", "-g", default="/data/xjd/SodaCoder-main/models/fine-tuning/CodeReview/test-golds.txt",
                       help="真实标签文件路径 (每行一个真实标签)")
    parser.add_argument("--output-file", "-o", default="merged_test_results.jsonl",
                       help="输出文件路径 (默认: merged_test_results.jsonl)")
    parser.add_argument("--search-dir", "-d", 
                       help="搜索结果文件的目录 (如果未指定 preds-file 和 golds-file)")
    
    args = parser.parse_args()
    
    # 如果没有指定结果文件，尝试在搜索目录中查找
    preds_file = args.preds_file
    golds_file = args.golds_file
    
    if not preds_file or not golds_file:
        if args.search_dir:
            print(f"在目录 {args.search_dir} 中搜索结果文件...")
            found_preds, found_golds = find_result_files(args.search_dir)
            
            if not preds_file and found_preds:
                preds_file = found_preds
                print(f"找到预测结果文件: {preds_file}")
            
            if not golds_file and found_golds:
                golds_file = found_golds
                print(f"找到真实标签文件: {golds_file}")
        
        if not preds_file or not golds_file:
            print("错误: 必须指定预测结果文件和真实标签文件，或提供搜索目录")
            return 1
    
    # 检查文件是否存在
    for file_path, file_desc in [(args.test_file, "测试集文件"), 
                                 (preds_file, "预测结果文件"), 
                                 (golds_file, "真实标签文件")]:
        if not os.path.exists(file_path):
            print(f"错误: {file_desc} 不存在: {file_path}")
            return 1
    
    # 执行合并
    success = merge_results(args.test_file, preds_file, golds_file, args.output_file)
    
    return 0 if success else 1


if __name__ == "__main__":
    exit(main())
