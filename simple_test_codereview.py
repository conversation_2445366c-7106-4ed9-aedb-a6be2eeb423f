#!/usr/bin/env python3
"""
Simple CodeReview Model Testing Script
This script tests the fine-tuned CodeReview model with minimal dependencies.
All parameters are hardcoded - no command line arguments needed.
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
import torch
from datasets import load_dataset
import numpy as np
from tqdm import tqdm

# Add the source directory to path
sys.path.append('/data/xjd/SodaCoder-main/LLM4CodeChange-main/SLM')
sys.path.append('/data/xjd/SodaCoder-main/LLM4CodeChange-main/SLM/src')

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

def create_simple_args():
    """Create a simple arguments object with all required parameters"""
    class SimpleArgs:
        def __init__(self):
            # Hardcoded parameters
            CURRENT_DIR = "/data/xjd/SodaCoder-main"
            
            # File paths
            self.test_filename = f"{CURRENT_DIR}/LLM4CodeChange-main/SLM/test_data.jsonl"
            self.load_model_path = f"{CURRENT_DIR}/models/fine-tuning/CodeReview/pytorch_model.bin"
            self.output_dir = f"{CURRENT_DIR}/outputs/models/fine-tuning/CodeReview"
            self.tokenizer_name = f"{CURRENT_DIR}/models"
            self.model_name_or_path = f"{CURRENT_DIR}/models"
            
            # Model parameters
            self.model_type = "codet5_CC"
            self.max_source_length = 512
            self.max_target_length = 128
            self.beam_size = 5
            self.eval_batch_size = 4
            
            # Other parameters
            self.seed = 42
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.local_rank = -1
            self.no_cuda = False
            
            # Required for model loading
            self.do_train = False
            self.do_test = True
            self.do_eval = False
            
    return SimpleArgs()

def load_test_data(test_file):
    """Load test data from JSONL file"""
    logger.info(f"Loading test data from {test_file}")
    
    if not os.path.exists(test_file):
        logger.error(f"Test file not found: {test_file}")
        return None
    
    try:
        data = load_dataset('json', data_files=test_file, split='train')
        logger.info(f"Loaded {len(data)} test examples")
        return data
    except Exception as e:
        logger.error(f"Error loading test data: {e}")
        return None

def prepare_input_text(oldf, patch):
    """Prepare input text for the model"""
    # Simple format: combine old file and patch
    return f"<msg>{oldf}<diff>{patch}"

def run_inference(model, tokenizer, test_data, args):
    """Run inference on test data"""
    logger.info("Starting inference...")
    
    model.eval()
    results = []
    
    # Get pad token id
    pad_token_id = getattr(tokenizer, 'pad_token_id', 0)
    
    for i, example in enumerate(tqdm(test_data, desc="Processing examples")):
        try:
            # Prepare input
            oldf = example.get("oldf", "")
            patch = example.get("patch", example.get("diff", ""))
            ground_truth = example.get("msg", example.get("nl", ""))
            
            input_text = prepare_input_text(oldf, patch)
            
            # Tokenize input
            inputs = tokenizer.encode(input_text, 
                                    max_length=args.max_source_length,
                                    truncation=True,
                                    return_tensors="pt")
            
            inputs = inputs.to(args.device)
            attention_mask = inputs.ne(pad_token_id)
            
            # Generate prediction
            with torch.no_grad():
                outputs = model.generate(
                    inputs,
                    attention_mask=attention_mask,
                    max_length=args.max_target_length,
                    num_beams=args.beam_size,
                    early_stopping=True,
                    do_sample=False
                )
            
            # Decode prediction
            prediction = tokenizer.decode(outputs[0], skip_special_tokens=True, 
                                        clean_up_tokenization_spaces=False)
            
            # Remove any prefix tokens if present
            if prediction.startswith("<msg>"):
                prediction = prediction[5:]
            
            # Store result
            result = {
                "index": i,
                "input": {
                    "oldf": oldf,
                    "patch": patch,
                    "input_text": input_text
                },
                "prediction": prediction.strip(),
                "ground_truth": ground_truth.strip(),
                "exact_match": prediction.strip().lower() == ground_truth.strip().lower()
            }
            results.append(result)
            
        except Exception as e:
            logger.error(f"Error processing example {i}: {e}")
            continue
    
    return results

def calculate_metrics(results):
    """Calculate evaluation metrics"""
    if not results:
        return {"bleu": 0.0, "exact_match": 0.0, "total_samples": 0}
    
    # Calculate exact match
    exact_matches = sum(1 for r in results if r["exact_match"])
    exact_match_rate = (exact_matches / len(results)) * 100
    
    # Simple BLEU approximation (word overlap)
    def simple_bleu(pred, ref):
        pred_words = set(pred.lower().split())
        ref_words = set(ref.lower().split())
        if not ref_words:
            return 0.0
        overlap = len(pred_words & ref_words)
        return overlap / len(ref_words)
    
    bleu_scores = []
    for r in results:
        bleu = simple_bleu(r["prediction"], r["ground_truth"])
        bleu_scores.append(bleu)
    
    avg_bleu = np.mean(bleu_scores) * 100 if bleu_scores else 0.0
    
    return {
        "bleu": avg_bleu,
        "exact_match": exact_match_rate,
        "total_samples": len(results)
    }

def save_results(results, metrics, output_file):
    """Save results to JSON file"""
    output_data = {
        "timestamp": datetime.now().isoformat(),
        "metrics": metrics,
        "detailed_results": results
    }
    
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Results saved to {output_file}")

def main():
    """Main function"""
    # Create arguments
    args = create_simple_args()
    
    # Check if test file exists
    if not os.path.exists(args.test_filename):
        logger.error(f"Test file not found: {args.test_filename}")
        return
    
    # Load test data
    test_data = load_test_data(args.test_filename)
    if test_data is None:
        return
    
    try:
        # Import model building function
        from src.models import build_or_load_gen_model
        from src.configs import set_dist
        
        # Set up distributed training (even for single GPU)
        set_dist(args)
        
        # Load model and tokenizer
        logger.info("Loading model and tokenizer...")
        config, model, tokenizer = build_or_load_gen_model(args)
        
        # Load trained model if available
        if os.path.exists(args.load_model_path):
            model.load_state_dict(torch.load(args.load_model_path, map_location=args.device))
            logger.info(f"Loaded model from {args.load_model_path}")
        elif os.path.exists(f"{args.output_dir}/checkpoints-last/pytorch_model.bin"):
            model.load_state_dict(torch.load(f"{args.output_dir}/checkpoints-last/pytorch_model.bin", 
                                           map_location=args.device))
            logger.info(f"Loaded model from {args.output_dir}/checkpoints-last/pytorch_model.bin")
        else:
            logger.warning("No trained model found, using base model")
        
        # Run inference
        results = run_inference(model, tokenizer, test_data, args)
        
        # Calculate metrics
        metrics = calculate_metrics(results)
        
        # Print results
        logger.info(f"Test Results:")
        logger.info(f"  Total samples: {metrics['total_samples']}")
        logger.info(f"  Exact Match: {metrics['exact_match']:.2f}%")
        logger.info(f"  BLEU (approx): {metrics['bleu']:.2f}")
        
        # Save results
        output_file = f"/data/xjd/SodaCoder-main/simple_test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        save_results(results, metrics, output_file)
        
        logger.info("Testing completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
