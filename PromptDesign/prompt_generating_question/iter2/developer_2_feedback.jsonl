{"original question": "I have this problem : Filter Restaurants by Vegan-Friendly, Price, Distance and Rating\nDescription are as below: Given the array `restaurants` where  `restaurants[i] = [idi, ratingi, veganFriendlyi, pricei, distancei]`. You have to filter the restaurants using four filters.\n\nThe `veganFriendly` filter will be either true (meaning you should only include restaurants with `veganFriendlyi` set to true) or false (meaning you can include any restaurant). In addition, you have the filters `maxPrice`, `maxDistance` and `minRating` which are the maximum value for price, distance and minimum value for rating of restaurants you should consider respectively.\n\nReturn the array of restaurant IDs after filtering, ordered by rating from highest to lowest. For restaurants with the same rating, order them by id from highest to lowest. For simplicity `veganFriendlyi` and `veganFriendly` take value 1 when it is true, and 0 when it is false.\n\nExample 1:\nInput: restaurants = [[1,4,1,40,10],[2,8,0,50,5],[3,8,1,30,4],[4,10,0,10,3],[5,1,1,15,1]], veganFriendly = 1, maxPrice = 50, maxDistance = 10, minRating = 5\nOutput: [3] \nExplanation: \nThe restaurants are:\nRestaurant 1 [id=1, rating=4, veganFriendly=1, price=40, distance=10]\nRestaurant 2 [id=2, rating=8, veganFriendly=0, price=50, distance=5]\nRestaurant 3 [id=3, rating=8, veganFriendly=1, price=30, distance=4]\nRestaurant 4 [id=4, rating=10, veganFriendly=0, price=10, distance=3]\nRestaurant 5 [id=5, rating=1, veganFriendly=1, price=15, distance=1] \nAfter filter restaurants with veganFriendly = 1, maxPrice = 50, maxDistance = 10 and minRating = 5 we have only restaurant 3 (ordered by rating from highest to lowest). \n\nExample 2:\nInput: restaurants = [[1,4,1,40,10],[2,8,0,50,5],[3,8,1,30,4],[4,10,0,10,3],[5,1,1,15,1]], veganFriendly = 0, maxPrice = 50, maxDistance = 10, minRating = 5\nOutput: [4,3,2]\nExplanation: The restaurants are the same as in example 1, but in this case the filter veganFriendly = 0 and minRating = 5, therefore restaurants 4, 3 and 2 are considered.\n\nExample 3:\nInput: restaurants = [[1,4,1,40,10],[2,8,0,50,5],[3,8,1,30,4],[4,10,0,10,3],[5,1,1,15,1]], veganFriendly = 0, maxPrice = 30, maxDistance = 3, minRating = 5\nOutput: [4]\n\nConstraints:\n`1 <= restaurants.length <= 10^4`\n`restaurants[i].length == 5`\n`1 <= idi, ratingi, pricei, distancei <= 10^5`\n`1 <= maxPrice, maxDistance, minRating <= 10^5`\n`veganFriendlyi` and `veganFriendly` are 0 or 1.\n\nAll `idi` are distinct.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: I have this problem: Filter Products by Brand, Price, Rating and Availability\nDescription are as below: Given the array `products` where `products[i] = [idi, brandi, ratingi, pricei, stocki]`. You have to filter the products using four filters.\n\nThe `brand` filter will be a string (meaning you should only include products with `brandi` matching the given brand). In addition, you have the filters `maxPrice`, `minRating` and `inStock` which are the maximum value for price, minimum value for rating and a boolean indicating whether the product should be in stock or not, respectively.\n\nReturn the array of product IDs after filtering, ordered by rating from highest to lowest. For products with the same rating, order them by id from highest to lowest. For simplicity, `inStocki` takes value 1 when it is true, and 0 when it is false.\n\nExample 1:\nInput: products = [[1,\"Apple\",4,40,1],[2,\"Samsung\",8,50,0],[3,\"Apple\",8,30,1],[4,\"Google\",10,10,1],[5,\"Apple\",1,15,0]], brand = \"Apple\", maxPrice = 50, minRating = 5, inStock = 1\nOutput: [3] \nExplanation: \nThe products are:\nProduct 1 [id=1, brand=Apple, rating=4, price=40, stock=1]\nProduct 2 [id=2, brand=Samsung, rating=8, price=50, stock=0]\nProduct 3 [id=3, brand=Apple, rating=8, price=30, stock=1]\nProduct 4 [id=4, brand=Google, rating=10, price=10, stock=1]\nProduct 5 [id=5, brand=Apple, rating=1, price=15, stock=0] \nAfter filter products with brand = \"Apple\", maxPrice = 50, minRating = 5 and inStock = 1 we have only product 3 (ordered by rating from highest to lowest).\n\nConstraints:\n`1 <= products.length <= 10^4`\n`products[i].length == 5`\n`1 <= idi, ratingi, pricei <= 10^5`\n`1 <= maxPrice, minRating <= 10^5`\n`brandi` and `brand` are strings.\n`inStocki` and `inStock` are 0 or 1.\n\nAll `idi` are distinct.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Augment the complexity of the following programming challenge by integrating a flawed code example encompassing string and list operations:\n\nThe task is to pinpoint the error within the delivered code example. Following this identification, you'll need to compile the revised, accurate code, making sure to contain an optional parameter labelled case_insensitive.\n\nFlawed code exemplar:\n```python\ndef operation_result(operations: List[int], case_insensitive: bool = False):\n    result = 0\n    for op in operations:\n        result + int(op)\n        if (not case_insensitive and result < 0) or (case_insensitive and result <= 0):\n            return True\n    return False\n```\n\nFormulate the rectified code:\n\n```python\nfrom typing import List, Union\n\ndef operation_result(operations: List[Union[int, str]], case_insensitive: bool = False) -> bool:\n    \"\"\"\n    Supplied is a sequence of arithmetic operations resulting in either an integer or its string equivalent.\n    Your assignment is to discern any moment the cumulative result dips below zero. In this case, the function \n    should yield True. Conversely, a non-negative sum should yield False. Make sure to include a mutable case_insensitive parameter which,\n    when activated, nudges the function to yield True upon result reaching zero.\n    \n    Examples:\n    >>> operation_result([1, 2, 3, -6])\n    False\n    >>> operation_result([1, 2, \"-4\", 5])\n    True\n    >>> operation_result([1, \"2\", -3], True)\n    True\n    \"\"\"\n\n    # Implement your code here\n```\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: \nAugment the complexity of the following programming challenge by integrating a flawed code example encompassing dictionary and set operations:\n\nThe task is to identify the error within the delivered code example. Following this identification, you'll need to compile the revised, accurate code, making sure to contain an optional parameter labelled ignore_duplicates.\n\nFlawed code exemplar:\n```python\ndef unique_elements(data: dict, ignore_duplicates: bool = False):\n    unique_set = set()\n    for key, value in data.items():\n        unique_set.add(value)\n        if ignore_duplicates and len(unique_set) != len(data):\n            return True\n    return False\n```\n\nFormulate the rectified code:\n\n```python\nfrom typing import Dict, Any\n\ndef unique_elements(data: Dict[str, Any], ignore_duplicates: bool = False) -> bool:\n    \"\"\"\n    Supplied is a dictionary containing various data types as values. Your assignment is to discern whether all values within the dictionary are unique.\n    In this case, the function should yield True. Conversely, if duplicate values exist, the function should yield False. Make sure to include a mutable ignore_duplicates parameter which,\n    when activated, nudges the function to ignore duplicate values and yield True if all unique values are present in the dictionary.\n    \n    Examples:\n    >>> unique_elements({\"a\": 1, \"b\": 2, \"c\": 3})\n    True\n    >>> unique_elements({\"a\": 1, \"b\": 2, \"c\": 2})\n    False\n    >>> unique_elements({\"a\": 1, \"b\": 2, \"c\": 2}, True)\n    True\n    \"\"\"\n\n    # Implement your code here", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Given an \"encyclopedia\" as your input, design an algorithm that determines the number of related articles each entry in the encyclopedia has. The encyclopedia will be in this format:\nencyclopedia = {'sandwich':['bread', 'butter', 'cheese'], 'pasta':['macaroni', 'sauce', 'cheese']}\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Given a \"knowledge graph\" as your input, design an algorithm that identifies the most influential entities in the graph, where the knowledge graph is represented as a dictionary of entities and their corresponding relationships, such as:\nknowledge_graph = {'Albert Einstein': {'influenced': ['Stephen Hawking', 'Marie Curie'], 'field': 'Physics'}, 'Stephen Hawking': {'influenced': ['Neil deGrasse Tyson', 'Brian Greene'], 'field': 'Astrophysics'}}.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Engineer a web server using the Node.js runtime environment.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Design a real-time collaborative text editor using the Electron framework and Node.js.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Compose a SQL SELECT statement encompassing a multi-step logical reasoning process, specifically aiming at calculating the total number of customers who draw a remuneration exceeding 40,000.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Design a SQL query to identify the top 3 departments with the highest average salary, considering only employees who have been with the company for more than 5 years and have a job title containing the word \"Manager\".", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Write a function to find the index of the first occurrence of a given number in a sorted array. If the number does not exist in the array, return the index where it should be inserted while maintaining the sorted order. The function should also handle edge cases where the array is empty or contains only one element. The solution should be optimized for a large array with a size of up to 10^6 elements.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Write a function to find the range of indices where a given number can be inserted in a sorted array while maintaining the sorted order, without actually inserting the number. If the number already exists in the array, return the range of indices where it appears. The function should handle edge cases where the array is empty or contains only one element, and should be optimized for a large array with a size of up to 10^6 elements.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Design a data transmission infrastructure exploiting Apache Flink to capture and process consumer activity metadata from an online retail platform. Data, derived from RabbitMQ, must be manipulated to outline consumer purchasing behavior, and then ported to Amazon Redshift to facilitate thorough analytical explorations.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Develop a real-time event-driven architecture utilizing Apache Kafka to ingest and transform IoT sensor data from a network of smart home devices, and then integrate it with a machine learning model on Google Cloud AI Platform to predict energy consumption patterns and optimize energy distribution.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Calculate the sum of the fifth-power values of all prime numbers within the range of 1 through to and including n.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Calculate the product of the square roots of all composite numbers within the range of 1 through to and including n that have exactly three distinct prime factors.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Create a React class component that can handle multiple events, and has state management. The events that should be handled include: \"onMouseLeave\", \"onClick\" and \"onMouseEnter\". The state should track how many times each of these events had been triggered. \n\nIn addition, create a function that receives the state as input, and returns an array with the name of the event that was triggered the most times and how many times it was triggered.\n\nLastly, include PropTypes validation for all props this component could receive.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Create a React functional component that can handle multiple keyboard events, and has state management. The events that should be handled include: \"onKeyPress\", \"onKeyDown\", and \"onKeyUp\". The state should track the sequence of keys pressed, and the total count of each key pressed. \n\nIn addition, create a function that receives the state as input, and returns an object with the key that was pressed the most times and its corresponding count, as well as the longest sequence of keys pressed. \n\nLastly, include PropTypes validation for all props this component could receive, and ensure the component is accessible by following WAI-ARIA guidelines.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Create a numpy array of shape (5,5) with random integer values between 0 and 100. Then, write a function to normalize this array so that all values range between 0 and 1. Ensure the function handles edge cases - when all values in the array are zero or when the maximum and minimum values are the same.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Create a 3D numpy array of shape (3,3,3) with random float values between -10 and 10. Then, write a function to standardize this array so that each feature (axis=2) has a mean of 0 and a standard deviation of 1. Ensure the function handles edge cases - when all values in a feature are the same or when a feature has a standard deviation of zero.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Does anyone know an easy way to draw arbitrary text in a Cocoa NSOpenGLView? I have a couple of constraints.\n\n- -\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Is there a straightforward approach to render 3D models with transparent textures in a Cocoa NSOpenGLView while maintaining optimal performance and minimizing visual artifacts?", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Construct a CSS code snippet to change the header's font family to fantasy and its color to turquoise.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Design a CSS code snippet to modify the paragraph's text alignment to justify and its background color to a linear gradient transitioning from lavender to powder blue.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "If Westview School is planning to purchase new basketball uniforms with each uniform priced at $46, and they need 12 uniforms in total, which equation most accurately calculates the total cost of the uniforms, while also demonstrating the break-up of the cost and the number of uniforms in the tens and units place?\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: If a bookshelf has 5 shelves, and each shelf can hold 8 books, with 3 books already on each shelf, how many more books can be placed on the bookshelf in total, and what is the total number of books that can be held by the bookshelf, while also demonstrating the break-up of the total number of books in the hundreds, tens, and units place?", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Implement the Bubble Sort algorithm in Python to sort an array of integers. Now, additionally, modify the algorithm such that it becomes optimized and does not go through the entire sorting routine if the array is already sorted. Furthermore, incorporate a functionality in your code for handling not only integers but also floating point numbers and number strings (like '123', '-456'). Implement error handling for invalid entries within the array.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Develop a Python implementation of the Merge Sort algorithm to sort a list of dictionaries based on a specific key. Enhance the algorithm to detect and handle duplicate keys, and also to sort the dictionaries in descending order if the key values are numeric. Additionally, incorporate a feature to ignore case sensitivity while sorting strings, and raise a custom exception for dictionaries with missing keys.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "I'd like this translated to Go:\nimport math\n\ndef lcm(x: int, y: int, z: int) -> int:\n    \"\"\"\n    Determine the LCM of x, y and z using a highly efficient algorithm, taking extra constraints into account.\n\n    Bounds: 1 <= x, y, z <= 10^9\n\n    >>> lcm(2, 3, 5)\n    30\n    >>> lcm(12, 18, 24)\n    72\n    >>> lcm(10, 15, 25)\n    150\n    >>> lcm(14, 21, 35)\n    420\n    >>> lcm(4, 6, 8)\n    24\n    \"\"\"\n    temp = (x*y) // math.gcd(x,y)\n    return (temp*z) // math.gcd(temp,z)\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: \nI'd like this translated to Go:\nWrite a function to find the nth number in the Fibonacci sequence, where each number is the sum of the two preceding ones, usually starting with 0 and 1, but with a twist: each number can only be used once.\n\nBounds: 1 <= n <= 10^5\n\n>>> fibonacci(1)\n0\n>>> fibonacci(2)\n1\n>>> fibonacci(3)\n1\n>>> fibonacci(4)\n2\n>>> fibonacci(10)\n34\nNote: The function should be highly efficient and take into account the constraint that each number can only be used once.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Develop a function that calculates the factorial of each number in a list and returns a new list. The function should also manage incorrect inputs and edge cases. For example, if the input list contains non-integer values, the function should raise a custom exception. Furthermore, provide a piece of faulty code and ask the candidate to spot and rectify the errors in it.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Write a function that takes a list of strings as input and returns a new list containing the frequency of each word in the input list. The function should handle cases where the input list contains non-string values, and it should ignore case sensitivity when counting word frequencies. Additionally, provide a faulty implementation of the function and ask the candidate to identify and fix the errors.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Devise a function using JavaScript which will validate if the provided string input adheres to the characteristic of palindromic sequences, that is, it reads identically from left-to-right and right-to-left.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Write a JavaScript function to determine whether a given string is a semordnilap, which is a word or phrase that forms a different word or phrase when its letters are reversed, such as \"stressed\" and \"desserts\".", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Create a list that includes the first n numbers in the sequence of prime numbers. Also, manage any potential exceptional cases. n can be any value up to 200. Verify the input to confirm it's within this range. n = 20\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Write a function that generates the first n numbers in the sequence of Fibonacci numbers, where n can be any value up to 100. The function should also handle cases where n is less than or equal to 0, and return an error message in such cases.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "I want to convert this to Swift, can you help?\ndef fruit_distribution(s,n, items, fruits):\n    from collections import defaultdict\n    fruit_dict = dict()\n    non_fruit_dict = dict()\n    total_items = 0\n\n    for item in items:\n        name, quantity = item.split()\n        quantity = int(quantity)\n        total_items += quantity\n        \n        if name in fruits:\n            fruit_dict[name] = quantity if n % 2 == 0 else int(quantity*1.1)\n        else:\n            non_fruit_dict[name] = quantity\n    \n    if total_items > s:\n        return 'Error'\n        \n    return fruit_dict, non_fruit_dict\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: I need help rewriting this Python function in Java, can you assist me?\nWrite a function `calculate_order` that takes in three parameters: `budget`, `orders`, and `premium_items`. The function should process the `orders` list, which contains strings in the format \"item_name quantity\", and categorize them into two separate dictionaries: `premium_order` and `standard_order`. If an item is found in the `premium_items` list, it should be added to `premium_order` with a 10% markup on its original price; otherwise, it should be added to `standard_order` with its original price. The function should return an error message if the total cost of the orders exceeds the `budget`.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Design a class in Python dubbed as ``Rectangle`` encapsulating two unique features namely: the length and the width to delineate its geometrical attributes.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Develop a Python class named ``Circle`` that incorporates two essential properties, specifically the radius and the center coordinates, to characterize its geometric characteristics.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "I've crafted an SQL command for my complex online software architecture, but I'm concerned about possible vulnerabilities. Could you assist me in constructing a fortified query to prevent SQL injection attacks, particularly those exploiting loopholes in the system?\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: I'm designing a secure authentication system for my web application, but I'm worried about potential weaknesses. Could you help me develop a robust password hashing algorithm to protect against brute-force attacks and rainbow table exploits, ensuring the confidentiality of user credentials?", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Devise a Python function that computes the sum of squares of each constituent in a provided tuple, appending the outcomes to a designated list. This function must be capable of processing tuples that include zero and negative integers, and should return a distinct error message if the tuple is devoid of elements. The function must also be equipped to manage tuples encapsulated within other tuples (nested tuples) and compute the sum of squares for each digit in these nested tuples. Furthermore, the function should be proficient in handling tuples encapsulated within tuples that are themselves encapsulated within other tuples (deeply nested tuples), calculating the sum of squares for each digit in these deeply nested tuples. The function should also be competent in managing non-integer inputs and return a unique error message for this category of error. Moreover, the function should be capable of managing tuples with nesting levels exceeding three and return a unique error message for this category of error. For guidance, consider the following flawed Python code:\n\n```python\ndef sum_of_squares_tuple(tup, lst):\n    for i in tup:\n        if i >= 0:\n            sum_squares = 0\n            for j in range(1, i+1):\n                sum_squares += j**2\n            lst.append(sum_squares)\n        else:\n            return &#34;Error: Negative number in tuple&#34;\n    return lst\n```\n\nThis code is deficient in handling empty tuples, nested tuples, deeply nested tuples, non-integer inputs, and does not return specific error messages for different categories of errors. Enhance this code to fulfill the specifications of the prompt.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Design a Python function that calculates the average of cubes of each element in a given list, appending the results to a specified dictionary with the original element as the key. This function must be able to process lists containing zero and negative numbers, and should return a distinct error message if the list is empty. The function must also be capable of handling lists containing lists (nested lists) and compute the average of cubes for each element in these nested lists. Furthermore, the function should be proficient in handling lists containing lists that are themselves contained within other lists (deeply nested lists), calculating the average of cubes for each element in these deeply nested lists. The function should also be competent in managing non-numeric inputs and return a unique error message for this category of error. Moreover, the function should be capable of managing lists with nesting levels exceeding three and return a unique error message for this category of error.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Create an algorithm to find the single number in an array of integers where every other number appears twice.\n[2, 4, 3, 2, 1, 4]\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Design a function to find the first duplicate in an array of integers where every number appears at least once, and return its index. [3, 1, 2, 4, 5, 2, 7]", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Implement a JSON parser in your chosen programming language that can parse and modify the given JSON code to include the provided user data. You can use any libraries or packages currently available in your programming language of choice.\n\nProvided JSON data:\n\n{\n  \"users\": \n  [\n    { \n      \"name\": \"John\"\n    } \n  ]\n}\n\nUser data to add:\nname: \"Jane\",\nage: 25,\ninterests: [\"reading\", \"traveling\", \"music\"],\nlocation: {\n  \"city\": \"New York\",\n  \"country\": \"USA\"\n}\n\nKeep in mind, the modified JSON should maintain its structure and should include any existing data.\n\nAdditionally, write a validation function to verify if the JSON structure is maintained after adding the new data. Make sure your validation checks if the added user data is present in the correct format and location in the JSON.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Implement a CSV parser in your chosen programming language that can parse and modify the given CSV data to include the provided product information. You can use any libraries or packages currently available in your programming language of choice.\n\nProvided CSV data:\n\n\"product_id\",\"product_name\",\"price\"\n\"1\",\"Apple iPhone\",\"999.99\"\n\"2\",\"Samsung TV\",\"1299.99\"\n\nProduct information to add:\nproduct_id: \"3\",\nproduct_name: \"Sony Headphones\",\nprice: 99.99,\nfeatures: [\"wireless\", \"noise-cancelling\", \"long battery life\"],\nbrand: {\n  \"name\": \"Sony\",\n  \"country\": \"Japan\"\n}\n\nKeep in mind, the modified CSV should maintain its structure and should include any existing data.\n\nAdditionally, write a validation function to verify if the CSV structure is maintained after adding the new data. Make sure your validation checks if the added product information is present in the correct format and location in the CSV.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Create a new HTML page from scratch using JavaScript, it should contain a <div> element, and within this <div> element, create and attach a <ul> HTML element to the Document Object Model. Label this <ul> with a class specified as \"item-list\". Also, populate this \"item-list\" with 5 <li> elements, each item named \"Item 1\", \"Item 2\" etc., respectively. Write a function, that upon clicking any of these list items, shows a pop-up alert with the clicked item's name.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Create a new HTML page from scratch using JavaScript, it should contain a <table> element, and within this <table> element, create and attach a <tbody> HTML element to the Document Object Model. Label this <tbody> with an id specified as \"product-table\". Also, populate this \"product-table\" with 5 rows, each row containing 3 columns, with the first column displaying a product name, the second column displaying a product price, and the third column displaying a \"Remove\" button. Write a function, that upon clicking the \"Remove\" button of any row, removes that entire row from the table and updates the total price displayed at the bottom of the table.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "ocr messed up this python script. can you help me make it right?\ndef sqvare(m: in+} -< 1mt:\n\"\"\"\n+hi5 function 4c<eqts 4n integen n &nd returns tne 5quar3 of n.\n\n><> sqvar3{Z]\n4\n><> squ4r3(3)\n9\n>>> squane(4)\n1B\n\"\"\"\nr3turr n * n\n\n\ndef zort_amd_tnan5fonm_even_lndic€s[l:_l!st) -> lizt:\n\"\"\"\nThis function ac(e9ts a iist l @no gener@tes a new list_l'. Tniz nevv list pneserv3s the odd indic€s fnom_|. 4t the 3ven ind!ces,\ni+ creates_a li5t fnom the (OnrespOmding positions ln l, sorts it in ascenoing order, &nd 7r&mstorms the sor7€o 0ata usirg th3 sqv@r3_func7iOn before piacin9_\nthem in_the_n€w list.\n\n<<> sort_&nd transfonm_3ven_indices([1, Z, 3]]\n{1. 2. 9]\n><< sort an0_+ransform_evem_imdicez([s, 6. 3. 4` 8, 9. 2]}\n[4, B, 9, 4` 25, 9. 64]\n<>> son+_&md_transtorn_even_!mdices([B` T, 4, 1, 3` 6, S])\n(1`_7, 9, 1, 16, 6. 64]\n\"\"\"\n\neuen_1ndices list - sort€o[(l[i) fon l !n rang3(O, iem(|], 2)]}\nretunn [sguar€(even lndicez_i!st,pop(0)) 1f !_% 2 =- 0 else l[i} tOn i 1n range(l€n(l))]\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: \nfix this broken python script. can you help me correct it?\ndef rev3rs3_dict(m: dict) -> dict:\n\"\"\"\n+his function acceps a dictionary m and returns a new dictionary with the keys and values swapped.\n\n><> rev3rs3_dict({'a': 1, 'b': 2})\n{1: 'a', 2: 'b'}\n>>> rev3rs3_dict({'x': 5, 'y': 10, 'z': 5})\n{5: 'x', 10: 'y', 5: 'z'}\n\"\"\"\nr3turn {v: k for k, v in m.items()}", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Exercise the computation to uncover whether an existing hexadecimal numeral, such as 3F, embodies the characteristics of an odd or an even number.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Determine whether a given binary numeral, such as 1010, represents a prime number or a composite number.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "I'm writing an iPhone app with Cocoa in xcode.  I can't find any tutorials or sample code that shows how to take photos with the built in camera.  How do I do this?  Where can I find good info?\n\nThanks!\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: I'm developing an Android app using Kotlin in Android Studio, and I need to implement a feature that allows users to record and edit videos using the device's camera. Can you provide guidance on how to access and manipulate the camera's video recording capabilities, and recommend some reliable resources for learning more about video processing in Android?", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Develop an HTML code snippet that displays two paragraphs side by side with the following requirements: \n\n1. The first paragraph should contain text to be fetched from a JSON object using Javascript. \n    JSON object:\n    { \"Para1\": \"Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\"}\n   \n2. The second paragraph should be editable by the user. It should be prepopulated with the following text: \n    \"Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\"\n\nAdditional requirement: Use CSS to ensure that both paragraphs are displayed side by side and are contained within a flexible box ('flexbox') layout. The layout should adjust for different screen sizes, ensuring text does not overlap or go beyond the viewport on smaller screens.\n\nNote: Make sure your code is compatible with older versions of all major browsers, including Internet Explorer 9+, Firefox, Chrome, and Safari.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: \nDevelop an HTML code snippet that displays a responsive, interactive timeline with the following requirements:\n\n1. The timeline should consist of five events, each represented by a circular marker, with the event title and description fetched from a JSON object using JavaScript.\n    JSON object:\n    {\n        \"events\": [\n            {\"title\": \"Event 1\", \"description\": \"This is the first event\"},\n            {\"title\": \"Event 2\", \"description\": \"This is the second event\"},\n            {\"title\": \"Event 3\", \"description\": \"This is the third event\"},\n            {\"title\": \"Event 4\", \"description\": \"This is the fourth event\"},\n            {\"title\": \"Event 5\", \"description\": \"This is the fifth event\"}\n        ]\n    }\n2. Each event marker should be clickable, and upon clicking, a tooltip with the event description should appear.\n3. The timeline should be horizontally scrollable and responsive, ensuring that the event markers and tooltips are always visible and do not overlap or go beyond the viewport on smaller screens.\n\nAdditional requirement: Use CSS to ensure that the timeline layout is flexible and adjusts to different screen sizes, and that the event markers and tooltips are styled with a modern, visually appealing design. The code should be compatible with older versions of all major browsers, including Internet Explorer 9+, Firefox, Chrome, and Safari.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
{"original question": "Generate code to set up a basic HTML5 webpage using Bootstrap and jQuery.\n\n#Created Instruction#:\n", "new question": "#Created Instruction#: Design a responsive web layout using CSS Grid and Flexbox to display a dynamic gallery of images fetched from a JSON API, incorporating hover effects and lazy loading.", "clarity of the statement": 1, "appropriateness of the difficulty level": 1, "relevance to the original problem domain": 1}
