
prompt = """Below is an instruction that describes a programming task. Your goal is to generate a code solution to the #Given Instruction#.

Your solution MUST contain at least one of the following types of errors:
1. Syntax Error: This type of error occurs when the code violates the grammar rules of the programming language. For example, forgetting to close a parenthesis or a quotation mark, or misspelling a keyword. 
2. Logical Error: These errors sneak into your code when there’s a misunderstanding of the problem you’re solving, leading to incorrect results despite the code running without crashing. For example, calculating the average of a list of numbers by summing them up but forgetting to divide by the count of the numbers.
3. Type Error: This error occurs when an operation is applied to an object of an inappropriate type. For example, attempting to concatenate a string with an integer without converting the integer to a string first.
4. Name Error: This happens when the code attempts to reference a variable or a function name that hasn’t been defined. For example, trying to print a variable that hasn’t been declared.
5. Timeout Error: This error occurs when your code gets stuck in a loop that never ends, either due to a logic flaw or acondition that never becomes false. In programming, such an error can cause your application to hang indefinitely, consuming resources and potentially leading to a crash if not handled properly. For example, writing a loop that waits for a certain condition to change, but the condition is never updated within the loop.

Do not explain the errors in the solution. Just write your thoughts and code as normal. 
Do not mention this is an incorrect solution in any form (e.g., in text/code/comments). Just pretend you are writing the correct code and still not recognizing the errors. 

#Given Instruction#: {prompt}

#Response#:
"""