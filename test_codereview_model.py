#!/usr/bin/env python3
"""
CodeReview Model Testing Script
This script tests the fine-tuned CodeReview model and saves input, predictions, and ground truth.
All parameters are hardcoded - no command line arguments needed.
"""

import argparse
import json
import logging
import multiprocessing
import os
import pickle
import random
import sys
import time
from datetime import datetime
from datasets import load_dataset
import numpy as np
import torch
from tqdm import tqdm

# Add the source directory to path
sys.path.append('LLM4CodeChange-main/SLM')
sys.path.append('LLM4CodeChange-main/SLM/src')

from src.utils import SimpleGenDataset
from src.evaluator.smooth_bleu import bleu_fromstr
from src.configs import add_args, set_dist
from src.models import build_or_load_gen_model

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

# Global Args class to avoid pickle issues in multiprocessing
class Args:
    pass

# Simple dataset class to avoid multiprocessing issues
class SimpleTestDataset:
    def __init__(self, tokenizer, args, file_path):
        self.tokenizer = tokenizer
        self.args = args

        # Load data
        data = load_dataset('json', data_files=file_path, split='train')
        self.feats = []

        for i, dic in enumerate(data):
            # Process each example
            if "patch" in dic:
                diff = dic["patch"]
            elif "diff" in dic:
                diff = dic["diff"]
            else:
                diff = ""

            if "oldf" in dic:
                oldf = dic["oldf"]
            else:
                oldf = ""

            if "msg" in dic:
                msg = dic["msg"]
            elif "nl" in dic:
                msg = dic["nl"]
            else:
                msg = ""

            # Create source text (similar to original implementation)
            source_text = f"<msg>{oldf}<diff>{diff}"

            # Tokenize
            source_ids = tokenizer.encode(source_text, max_length=args.max_source_length,
                                        truncation=True, padding='max_length')
            target_ids = tokenizer.encode(msg, max_length=args.max_target_length,
                                        truncation=True, padding='max_length')

            # Create feature object
            class Feature:
                def __init__(self, example_id, source_ids, target_ids):
                    self.example_id = example_id
                    self.source_ids = source_ids
                    self.target_ids = target_ids

            self.feats.append(Feature(i, source_ids, target_ids))

    def __len__(self):
        return len(self.feats)

    def __getitem__(self, idx):
        return self.feats[idx]

def set_seeds(seed=42):
    """Set random seeds for reproducibility"""
    torch.manual_seed(seed)
    torch.random.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.cuda.manual_seed(seed)

def get_loader(data_file, args, tokenizer, pool, batch_size=8):
    """Create data loader for testing"""
    def fn(features):
        return features

    logger.info(f"Loading test data from {data_file}")
    # Use our simplified dataset to avoid multiprocessing issues
    dataset = SimpleTestDataset(tokenizer, args, data_file)
    from torch.utils.data import DataLoader, SequentialSampler
    sampler = SequentialSampler(dataset)
    dataloader = DataLoader(dataset, sampler=sampler, batch_size=batch_size,
                          num_workers=0, collate_fn=fn)  # num_workers=0 to avoid multiprocessing
    return dataset, sampler, dataloader

def test_and_save_results(args, model, tokenizer, test_file, output_file):
    """Test model and save detailed results"""
    set_seeds(args.seed)
    # Use None for pool to avoid multiprocessing pickle issues
    pool = None
    
    # Load model if checkpoint exists
    if os.path.exists(f"{args.output_dir}/checkpoints-last/pytorch_model.bin"):
        model.load_state_dict(
            torch.load(f"{args.output_dir}/checkpoints-last/pytorch_model.bin")
        )
        logger.info(f"Loaded model from {args.output_dir}/checkpoints-last/pytorch_model.bin")
    elif args.load_model_path and os.path.exists(args.load_model_path):
        model.load_state_dict(torch.load(args.load_model_path))
        logger.info(f"Loaded model from {args.load_model_path}")
    
    # Get test data loader
    _, _, test_dataloader = get_loader(test_file, args, tokenizer, pool, 
                                     batch_size=args.eval_batch_size)
    
    model.eval()
    logger.info(f"Starting evaluation on {test_file}")
    
    # Generate predictions
    pred_ids, ex_ids, indexes = [], [], []
    all_source_texts = []
    
    for step, examples in enumerate(tqdm(test_dataloader, desc="Generating predictions")):
        source_ids = torch.tensor(
            [ex.source_ids for ex in examples], dtype=torch.long
        ).to(args.device)
        ids = [ex.example_id for ex in examples]
        
        # Store source texts for later
        for ex in examples:
            source_text = tokenizer.decode(ex.source_ids, skip_special_tokens=True, 
                                         clean_up_tokenization_spaces=False)
            all_source_texts.append(source_text)
        
        # Get pad token id (different tokenizers have different attributes)
        pad_token_id = getattr(tokenizer, 'pad_token_id', getattr(tokenizer, 'pad_id', 0))
        source_mask = source_ids.ne(pad_token_id)
        
        with torch.no_grad():
            preds = model.generate(
                source_ids,
                attention_mask=source_mask,
                use_cache=True,
                num_beams=args.beam_size,
                early_stopping=True,
                max_length=args.max_target_length
            )
        
        top_preds = list(preds.cpu().numpy())
        pred_ids.extend(top_preds)
        indexes.extend(ids)
    
    # Decode predictions (remove beginning token)
    pred_nls = [tokenizer.decode(id[1:], skip_special_tokens=True, 
                               clean_up_tokenization_spaces=False) for id in pred_ids]
    
    # Load ground truth
    logger.info("Loading ground truth data")
    data = load_dataset('json', data_files=test_file, split='train')
    all_golds = []
    all_inputs = []
    
    for js in data:
        if "msg" in js:
            msg = js["msg"]
        elif "nl" in js:
            msg = js["nl"]
        else:
            msg = ""
        all_golds.append(msg)
        
        # Store input information
        input_info = {
            "oldf": js.get("oldf", ""),
            "patch": js.get("patch", ""),
            "diff": js.get("diff", "")
        }
        all_inputs.append(input_info)
    
    # Align predictions with ground truth using indexes
    golds = [all_golds[x] for x in indexes]
    inputs = [all_inputs[x] for x in indexes]
    
    # Calculate metrics
    eval_accs = []
    processed_preds = []
    processed_golds = []
    
    for i in range(len(pred_nls)):
        # Process text for evaluation (same as original code)
        chars = "(_)`."
        pred_processed = pred_nls[i]
        gold_processed = golds[i]
        
        for c in chars:
            pred_processed = pred_processed.replace(c, " " + c + " ")
            pred_processed = " ".join(pred_processed.split())
            gold_processed = gold_processed.replace(c, " " + c + " ")
            gold_processed = " ".join(gold_processed.split())
        
        processed_preds.append(pred_processed)
        processed_golds.append(gold_processed)
        eval_accs.append(pred_processed.strip() == gold_processed.strip())
    
    # Calculate BLEU score
    bleu = bleu_fromstr(processed_preds, processed_golds, rmstop=False)
    em_score = np.mean(eval_accs) * 100
    
    # Prepare results for saving
    results = {
        "test_file": test_file,
        "timestamp": datetime.now().isoformat(),
        "model_path": args.load_model_path,
        "metrics": {
            "bleu": bleu,
            "exact_match": em_score,
            "total_samples": len(pred_nls)
        },
        "detailed_results": []
    }
    
    # Save detailed results for each example
    for i in range(len(pred_nls)):
        result_item = {
            "index": indexes[i],
            "input": {
                "source_text": all_source_texts[i],
                "oldf": inputs[i]["oldf"],
                "patch": inputs[i]["patch"] if inputs[i]["patch"] else inputs[i]["diff"]
            },
            "prediction": pred_nls[i],
            "ground_truth": golds[i],
            "exact_match": eval_accs[i]
        }
        results["detailed_results"].append(result_item)
    
    # Save results to file
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    logger.info(f"Results saved to {output_file}")
    logger.info(f"Test BLEU: {bleu:.4f}")
    logger.info(f"Test Exact Match: {em_score:.2f}%")
    logger.info(f"Total samples: {len(pred_nls)}")
    
    return results

def main():
    """Main function with hardcoded parameters"""
    # Hardcoded parameters - modify these as needed
    CURRENT_DIR = "/data/xjd/SodaCoder-main"
    TEST_FILE = f"{CURRENT_DIR}/Dataset/fine-tuning/CodeReview/test.jsonl"  # Test data file
    MODEL_PATH = f"{CURRENT_DIR}/models/fine-tuning/CodeReview/pytorch_model.bin"
    OUTPUT_DIR = f"{CURRENT_DIR}/outputs/models/fine-tuning/CodeReview"
    RESULTS_FILE = f"{CURRENT_DIR}/test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

    # Create arguments object manually to avoid command line parsing
    args = Args()
    
    # Set all required arguments manually
    args.do_train = False
    args.do_test = True
    args.do_eval = False
    args.test_filename = TEST_FILE
    args.model_type = "codet5_CC"
    args.tokenizer_name = f"{CURRENT_DIR}/models"
    args.model_name_or_path = f"{CURRENT_DIR}/models"
    args.load_model_path = MODEL_PATH
    args.output_dir = OUTPUT_DIR
    args.eval_batch_size = 8
    args.max_source_length = 512
    args.max_target_length = 128
    args.beam_size = 5
    args.seed = 42
    args.cpu_count = multiprocessing.cpu_count()
    args.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    args.gpu_id = "0"

    # Additional required parameters
    args.train_filename = None
    args.dev_filename = None
    args.config_name = None
    args.train_batch_size = 32
    args.gradient_accumulation_steps = 1
    args.learning_rate = 3e-4
    args.weight_decay = 0.0
    args.adam_epsilon = 1e-8
    args.max_grad_norm = 1.0
    args.warmup_steps = 0
    args.save_steps = 1000
    args.log_steps = 100
    args.train_steps = 0
    args.max_steps = -1
    args.eval_steps = 1000
    args.evaluate_sample_size = -1
    args.local_rank = -1
    args.no_cuda = False
    args.do_lower_case = False
    args.mask_rate = 0.15
    args.raw_input = True
    args.add_lang_ids = False
    args.add_task_prefix = False
    # args.lang = "java"
    args.eval_task = None
    args.data_dir = None
    args.res_fn = None
    args.save_last_checkpoints = False
    args.always_save_model = False
    args.do_eval_bleu = False
    args.zero_shot = False
    args.load_optimizer_path = None
    args.treesitter_path = None
    args.test_features_filename = None
    args.start_epoch = 0
    args.num_train_epochs = 1
    args.patience = 5
    args.from_scratch = False
    args.gpu_per_node = 1
    args.debug = False
    
    # Set up distributed training (even for single GPU)
    set_dist(args)
    set_seeds(args.seed)
    args.global_rank = 0
    
    # Check if test file exists
    if not os.path.exists(TEST_FILE):
        logger.error(f"Test file not found: {TEST_FILE}")
        logger.info("Please create a test file in JSONL format with the following structure:")
        logger.info('{"oldf": "original code", "patch": "diff content", "msg": "expected message"}')
        return
    
    # Check if model exists
    if not os.path.exists(MODEL_PATH) and not os.path.exists(f"{OUTPUT_DIR}/checkpoints-last/pytorch_model.bin"):
        logger.error(f"Model not found at {MODEL_PATH} or {OUTPUT_DIR}/checkpoints-last/")
        logger.info("Please ensure the model is trained and saved properly")
        return
    
    # Build model
    logger.info("Loading model and tokenizer...")
    config, model, tokenizer = build_or_load_gen_model(args)
    
    # Run testing and save results
    logger.info("Starting model testing...")
    results = test_and_save_results(args, model, tokenizer, TEST_FILE, RESULTS_FILE)
    
    logger.info("Testing completed successfully!")
    logger.info(f"Results saved to: {RESULTS_FILE}")

if __name__ == "__main__":
    main()
