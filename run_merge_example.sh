#!/bin/bash

# 合并测试结果示例脚本
# 此脚本展示如何使用 merge_test_results.py 来合并测试结果

echo "=== 合并测试结果示例 ==="

# 设置路径变量
CURRENT_DIR="/data/xjd/SodaCoder-main"
TEST_FILE="${CURRENT_DIR}/Dataset/fine-tuning/CodeReview/test.jsonl"
OUTPUT_DIR="${CURRENT_DIR}/outputs/models/fine-tuning/CodeReview"
MODEL_DIR="${CURRENT_DIR}/models/fine-tuning/CodeReview"

# 方法1: 指定具体的文件路径
echo "方法1: 指定具体的预测和标签文件"
python merge_test_results.py \
    --test-file "$TEST_FILE" \
    --preds-file "$OUTPUT_DIR/test-preds.txt" \
    --golds-file "$OUTPUT_DIR/test-golds.txt" \
    --output-file "merged_results_method1.jsonl"

echo ""

# 方法2: 在输出目录中自动搜索结果文件
echo "方法2: 在输出目录中自动搜索结果文件"
python merge_test_results.py \
    --test-file "$TEST_FILE" \
    --search-dir "$OUTPUT_DIR" \
    --output-file "merged_results_method2.jsonl"

echo ""

# 方法3: 在模型目录中搜索结果文件（如果结果文件保存在模型目录）
echo "方法3: 在模型目录中搜索结果文件"
python merge_test_results.py \
    --test-file "$TEST_FILE" \
    --search-dir "$MODEL_DIR" \
    --output-file "merged_results_method3.jsonl"

echo ""

# 方法4: 使用本地测试数据进行演示
echo "方法4: 使用本地测试数据进行演示"
if [ -f "LLM4CodeChange-main/SLM/test_data.jsonl" ]; then
    # 创建示例预测和标签文件
    echo "Creating example prediction and gold files..."
    
    # 从测试数据中提取真实标签作为示例
    python -c "
import json
with open('LLM4CodeChange-main/SLM/test_data.jsonl', 'r') as f:
    data = [json.loads(line.strip()) for line in f if line.strip()]

# 创建示例预测文件（这里用真实标签稍作修改作为预测）
with open('example_preds.txt', 'w') as f:
    for item in data:
        pred = item.get('msg', '') + ' (predicted)'
        f.write(pred + '\n')

# 创建真实标签文件
with open('example_golds.txt', 'w') as f:
    for item in data:
        gold = item.get('msg', '')
        f.write(gold + '\n')

print('Created example files: example_preds.txt and example_golds.txt')
"
    
    # 运行合并
    python merge_test_results.py \
        --test-file "LLM4CodeChange-main/SLM/test_data.jsonl" \
        --preds-file "example_preds.txt" \
        --golds-file "example_golds.txt" \
        --output-file "merged_results_example.jsonl"
    
    echo "示例合并完成！查看 merged_results_example.jsonl 文件"
    
    # 显示合并结果的前几行
    echo ""
    echo "合并结果预览:"
    head -n 2 merged_results_example.jsonl | python -m json.tool
    
    # 清理示例文件
    rm -f example_preds.txt example_golds.txt
else
    echo "未找到测试数据文件，跳过示例演示"
fi

echo ""
echo "=== 使用说明 ==="
echo "1. 确保已运行 finetune_CodeReview.sh 脚本生成预测结果"
echo "2. 根据实际路径修改脚本中的路径变量"
echo "3. 选择合适的方法运行合并脚本"
echo "4. 合并后的文件包含:"
echo "   - index: 数据索引"
echo "   - oldf: 原始代码"
echo "   - patch: 代码变更"
echo "   - predicted_msg: 预测的提交消息"
echo "   - actual_msg: 实际的提交消息"
