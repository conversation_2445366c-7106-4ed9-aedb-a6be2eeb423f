#!/usr/bin/env python3
"""
测试meaningless值提取功能的独立脚本
"""

def extract_meaningless_value(generated_comment: str) -> int:
    """
    从生成的评论中提取meaningless值
    
    Args:
        generated_comment: 生成的评论内容
        
    Returns:
        int: meaningless值 (0或1)
        
    规则:
    - 如果generated_comment中只有0或1，则meaningless为该值
    - 否则从字符串后面往前，第一个出现的0或1作为meaningless的值
    - 如果不存在0或1则meaningless设置为1
    """
    if not generated_comment:
        return 1
    
    # 去除首尾空白字符
    comment = generated_comment.strip()
    
    # 如果整个字符串只有0或1
    if comment == "0":
        return 0
    elif comment == "1":
        return 1
    
    # 从后往前查找第一个0或1
    for i in range(len(comment) - 1, -1, -1):
        if comment[i] == '0':
            return 0
        elif comment[i] == '1':
            return 1
    
    # 如果没有找到0或1，返回1
    return 1

def test_meaningless_extraction():
    """测试meaningless值提取功能"""
    print("=== Testing Meaningless Value Extraction ===")
    
    test_cases = [
        # 测试用例: (输入, 期望输出, 描述)
        ("0", 0, "只有0"),
        ("1", 1, "只有1"),
        ("", 1, "空字符串"),
        ("   0   ", 0, "只有0带空格"),
        ("   1   ", 1, "只有1带空格"),
        ("This is a good review. 0", 0, "以0结尾"),
        ("This is a good review. 1", 1, "以1结尾"),
        ("The code looks good. Rating: 0", 0, "包含0"),
        ("The code looks good. Rating: 1", 1, "包含1"),
        ("Good job! 1 out of 10. Final: 0", 0, "多个数字，最后一个是0"),
        ("Good job! 0 out of 10. Final: 1", 1, "多个数字，最后一个是1"),
        ("This is a great improvement!", 1, "不包含0或1"),
        ("Nice work! Keep it up.", 1, "不包含0或1"),
        ("Rating: 2", 1, "包含其他数字但不包含0或1"),
        ("0 is good, but 1 is better", 1, "多个数字，从后往前第一个是1"),
        ("1 is good, but 0 is better", 0, "多个数字，从后往前第一个是0"),
        ("   ", 1, "只有空格"),
        ("Good work! 10", 1, "包含10但不包含单独的0或1"),
        ("Score: 10/10", 1, "包含10但不包含单独的0或1"),
        ("Result: 01", 1, "包含01，从后往前第一个是1"),
        ("Result: 10", 0, "包含10，从后往前第一个是0"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (input_text, expected, description) in enumerate(test_cases):
        result = extract_meaningless_value(input_text)
        if result == expected:
            print(f"  ✓ Test {i+1}: {description} - Input: {repr(input_text)} -> {result}")
            passed += 1
        else:
            print(f"  ✗ Test {i+1}: {description} - Input: {repr(input_text)} -> Expected: {expected}, Got: {result}")
            failed += 1
    
    print(f"\nTest Results: {passed} passed, {failed} failed")
    print("Meaningless value extraction testing complete")

if __name__ == "__main__":
    test_meaningless_extraction()
