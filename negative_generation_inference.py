#!/usr/bin/env python3
"""
Negative Generation Parallel Inference Script
专门用于negative_generation任务的并行推理脚本
从quality_check保存的文件中读取数据，跳过meaningless=1的数据，生成负样本
"""

import json
import logging
import threading
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from openai import OpenAI
import random

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 硬编码配置参数
CONFIG = {
    # API配置
    "api_keys": [
        "sk-eb93cdc660424a3da557bc4c0f0cf608",  # 替换为实际的API key
        "sk-4fb414ba14b6476b9bdb584a4b4270b6",  # 替换为实际的API key
        "sk-e5e4b80d36ac4c9bb7b65ebb8a22b175",  # 替换为实际的API key
        "sk-f1e89e927afc4d0187da85228e394743",  # 替换为实际的API key
        "sk-f8b1b5c9bfa84bdc87bfb932aaa39658",  # 替换为实际的API key
        "sk-5954a951841e47188ed9a3aee266783e",  # 替换为实际的API key
        "sk-75198ad5e8a0497995f680b7eff36178",  # 替换为实际的API key
        "sk-1b7b6f862cc648f6bbbbeb9306d1a24c",  # 替换为实际的API key
    ],
    "base_url": "https://api.deepseek.com",
    "model_name": "deepseek-chat",
    
    # 并发配置
    "max_workers": 8,
    "max_retries": 3,
    "retry_delay": 1.0,
    "timeout": 30,
    
    # 生成配置
    "max_tokens": 400,
    "temperature": 0.7,
    
    # 文件路径
    "input_file": "/data/xjd/SodaCoder-main/CodeReviewer/code/generated_comments_10000-19999.jsonl",  # quality_check保存的文件
    "output_file": "negative_generation_results.jsonl",  # 输出文件
}

@dataclass
class InferenceRequest:
    """推理请求数据结构"""
    request_id: str
    messages: List[Dict[str, str]]
    model: str = CONFIG["model_name"]
    max_tokens: int = CONFIG["max_tokens"]
    temperature: float = CONFIG["temperature"]
    stream: bool = False

@dataclass
class InferenceResponse:
    """推理响应数据结构"""
    request_id: str
    content: str
    success: bool
    error: Optional[str] = None
    response_time: Optional[float] = None
    api_key_index: Optional[int] = None

@dataclass
class NegativeGenerationData:
    """负样本生成数据结构"""
    idx: str
    diff_original: str
    diff_processed: str
    msg: str  # 原始正样本评论
    meaningless: int

class ParallelInferenceEngine:
    """并行推理引擎"""
    
    def __init__(self):
        self.api_keys = CONFIG["api_keys"]
        self.base_url = CONFIG["base_url"]
        self.clients = [OpenAI(api_key=key, base_url=self.base_url) for key in self.api_keys]
        self.api_key_lock = threading.Lock()
        self.current_key_index = 0
        
    def get_next_client(self) -> tuple[OpenAI, int]:
        """获取下一个可用的API客户端"""
        with self.api_key_lock:
            client = self.clients[self.current_key_index]
            key_index = self.current_key_index
            self.current_key_index = (self.current_key_index + 1) % len(self.clients)
            return client, key_index
    
    def make_single_request(self, request: InferenceRequest) -> InferenceResponse:
        """执行单个推理请求"""
        client, key_index = self.get_next_client()
        
        for attempt in range(CONFIG["max_retries"]):
            try:
                start_time = time.time()
                
                response = client.chat.completions.create(
                    model=request.model,
                    messages=request.messages,
                    max_tokens=request.max_tokens,
                    temperature=request.temperature,
                    stream=request.stream,
                    timeout=CONFIG["timeout"]
                )
                
                response_time = time.time() - start_time
                content = response.choices[0].message.content.strip()
                
                return InferenceResponse(
                    request_id=request.request_id,
                    content=content,
                    success=True,
                    response_time=response_time,
                    api_key_index=key_index
                )
                
            except Exception as e:
                error_msg = f"Attempt {attempt + 1} failed: {str(e)}"
                logger.warning(f"Request {request.request_id} - {error_msg}")
                
                if attempt < CONFIG["max_retries"] - 1:
                    time.sleep(CONFIG["retry_delay"] * (2 ** attempt))
                else:
                    return InferenceResponse(
                        request_id=request.request_id,
                        content="",
                        success=False,
                        error=error_msg,
                        api_key_index=key_index
                    )
    
    def batch_inference(self, requests: List[InferenceRequest]) -> List[InferenceResponse]:
        """批量并行推理"""
        responses = [None] * len(requests)
        
        with ThreadPoolExecutor(max_workers=CONFIG["max_workers"]) as executor:
            # 提交所有任务
            future_to_index = {
                executor.submit(self.make_single_request, req): i 
                for i, req in enumerate(requests)
            }
            
            # 收集结果
            completed = 0
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    response = future.result()
                    responses[index] = response
                    completed += 1
                    
                    if completed % 10 == 0:
                        logger.info(f"Completed {completed}/{len(requests)} requests")
                        
                except Exception as e:
                    logger.error(f"Future execution failed for index {index}: {e}")
                    responses[index] = InferenceResponse(
                        request_id=requests[index].request_id,
                        content="",
                        success=False,
                        error=f"Future execution failed: {str(e)}"
                    )
        
        return responses

def load_quality_check_data(file_path: str) -> List[NegativeGenerationData]:
    """从quality_check结果文件中加载数据"""
    data_list = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                try:
                    line = line.strip()
                    if not line:
                        continue
                        
                    data = json.loads(line)
                    
                    # 检查必需字段
                    required_fields = ['idx', 'diff_original', 'diff_processed', 'msg', 'meaningless']
                    missing_fields = [field for field in required_fields if field not in data]
                    if missing_fields:
                        logger.warning(f"Line {line_num}: Missing fields {missing_fields}, skipping")
                        continue
                    
                    # 跳过meaningless=1的数据
                    if data.get('meaningless', 1) == 1:
                        logger.debug(f"Skipping idx {data['idx']} due to meaningless=1")
                        continue
                    
                    neg_data = NegativeGenerationData(
                        idx=str(data['idx']),
                        diff_original=data['diff_original'],
                        diff_processed=data['diff_processed'],
                        msg=data['msg'],
                        meaningless=data['meaningless']
                    )
                    data_list.append(neg_data)
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Line {line_num}: JSON decode error - {e}")
                except Exception as e:
                    logger.error(f"Line {line_num}: Error processing data - {e}")
                    
    except FileNotFoundError:
        logger.error(f"Input file not found: {file_path}")
        raise
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        raise
    
    logger.info(f"Loaded {len(data_list)} valid samples from {file_path}")
    return data_list

def create_negative_generation_requests(data_list: List[NegativeGenerationData]) -> List[InferenceRequest]:
    """创建负样本生成请求"""
    # Negative generation prompt模板
    prompt_template = """You are an AI assistant that specializes in generating low-quality, unhelpful code review comments. Your purpose is to create negative samples for a contrastive learning model.Based on the provided code change (`code_diff`) and its corresponding high-quality "ground truth" review comment (`review_comment`), you must generate one negative review comment.

Your generated negative review comment must exhibit at least one of the following problems:
1.  Vague and Ambiguous: If the ground truth comment is clear and specific, your comment should be vague and non-actionable. For example, if the ground truth is "Rename the variable `h` to `height`," your negative comment could be "`h` needs to be changed."
2.  Contradictory or Opposite Advice: If the ground truth provides a correct suggestion, your comment should suggest the opposite or a clearly worse alternative. For example, if the ground truth is "Changing from `a` to `b` will cause multiple errors," your negative comment could be "I think `b` is a better implementation."
3.  Misdirected Feedback: Your comment is relevant to the `code_diff`, but it completely misses the core issue raised in the `ground_truth_comment`. Instead, it focuses on a different part of the code change. For example, if the ground truth points out a critical logic bug, your negative comment might be about code formatting in another file or praise a variable rename.
4.  Meaningless or Low-Effort: If the ground truth provides meaningful feedback, your comment should be meaningless or conversational filler. For example, "Looks good!".

- Do NOT explain why your comment is negative or what problems it has.
- Do NOT mention words like "negative sample," "incorrect," or anything that implies the comment is low-quality.
- You must act as if you are a real, but unskilled or distracted, reviewer.
- Your response MUST contain ONLY the generated review comment itself, with no extra text or formatting.

# Code Changes:
{code_diff}

# Ground truth review comment:
{review_comment}

# Response:

"""
    
    requests = []


    for data in data_list:
        # 使用处理后的diff
        content = prompt_template.format(
            code_diff=data.diff_processed,
            review_comment=data.msg
        )
        
        messages = [{"role": "user", "content": content}]
        
        request = InferenceRequest(
            request_id=f"neg_gen_{data.idx}",
            messages=messages
        )
        requests.append(request)

    
    return requests

def save_results(data_list: List[NegativeGenerationData], 
                responses: List[InferenceResponse], 
                output_file: str):
    """保存负样本生成结果"""
    results_saved = 0
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for data, response in zip(data_list, responses):
            result = {
                'idx': data.idx,
                'diff_original': data.diff_original,
                'diff_processed': data.diff_processed,
                'positive_comment': data.msg,  # 原始正样本评论
                'negative_comment': response.content if response.success else "",  # 生成的负样本评论
                'success': response.success,
                'error': response.error,
                'response_time': response.response_time,
                'api_key_index': response.api_key_index
            }
            f.write(json.dumps(result, ensure_ascii=False) + '\n')
            
            if response.success:
                results_saved += 1
    
    logger.info(f"Saved {results_saved}/{len(data_list)} successful results to {output_file}")

def main():
    """主函数"""
    logger.info("Starting Negative Generation Parallel Inference")
    logger.info(f"Configuration: {CONFIG}")
    
    # 加载数据
    logger.info(f"Loading data from {CONFIG['input_file']}")
    data_list = load_quality_check_data(CONFIG['input_file'])
    
    if not data_list:
        logger.error("No valid data found, exiting")
        return
    
    # 创建推理请求
    logger.info("Creating inference requests")
    requests = create_negative_generation_requests(data_list)
    
    # 执行并行推理
    logger.info(f"Starting parallel inference with {CONFIG['max_workers']} workers")
    engine = ParallelInferenceEngine()
    responses = engine.batch_inference(requests)
    
    # 保存结果
    logger.info(f"Saving results to {CONFIG['output_file']}")
    save_results(data_list, responses, CONFIG['output_file'])
    
    # 统计结果
    successful = sum(1 for r in responses if r.success)
    failed = len(responses) - successful
    
    logger.info("=== Negative Generation Complete ===")
    logger.info(f"Total samples processed: {len(responses)}")
    logger.info(f"Successful: {successful}")
    logger.info(f"Failed: {failed}")
    logger.info(f"Success rate: {successful/len(responses)*100:.1f}%")

if __name__ == "__main__":
    main()
