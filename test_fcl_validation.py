#!/usr/bin/env python3
"""
Test script to verify that the FCL training script has proper validation functionality.
"""

import sys
import os
import ast
import inspect

def check_fcl_validation_implementation():
    """Check if the FCL training script has proper validation implementation."""
    
    # Read the FCL training script
    fcl_script_path = "CodeReviewer/code/run_finetune_fcl.py"
    
    if not os.path.exists(fcl_script_path):
        print(f"❌ FCL script not found: {fcl_script_path}")
        return False
    
    with open(fcl_script_path, 'r') as f:
        content = f.read()
    
    # Check for required imports
    required_imports = [
        "CommentGenDataset",
        "SimpleGenDataset", 
        "bleu_fromstr"
    ]
    
    print("🔍 Checking imports...")
    for imp in required_imports:
        if imp in content:
            print(f"  ✅ {imp} imported")
        else:
            print(f"  ❌ {imp} missing")
            return False
    
    # Check for required functions
    required_functions = [
        "get_loaders",
        "eval_bleu_epoch"
    ]
    
    print("\n🔍 Checking functions...")
    for func in required_functions:
        if f"def {func}(" in content:
            print(f"  ✅ {func} function defined")
        else:
            print(f"  ❌ {func} function missing")
            return False
    
    # Check for validation in save checkpoints
    print("\n🔍 Checking validation integration...")
    
    # Check if validation is called before saving checkpoints
    if "eval_bleu_epoch(args, valid_dataloader, model, tokenizer)" in content:
        print("  ✅ Validation called before saving checkpoints")
    else:
        print("  ❌ Validation not integrated into checkpoint saving")
        return False
    
    # Check if BLEU score is included in checkpoint directory names
    if 'str(bleu)' in content and 'checkpoints-' in content:
        print("  ✅ BLEU score included in checkpoint directory names")
    else:
        print("  ❌ BLEU score not included in checkpoint names")
        return False
    
    # Check for validation file requirement
    if "dev_filename is None" in content:
        print("  ✅ Validation file requirement check added")
    else:
        print("  ❌ Validation file requirement check missing")
        return False
    
    print("\n✅ All validation checks passed!")
    return True

def check_consistency_with_msg_script():
    """Check consistency with the reference msg training script."""
    
    msg_script_path = "CodeReviewer/code/run_finetune_msg.py"
    fcl_script_path = "CodeReviewer/code/run_finetune_fcl.py"
    
    if not os.path.exists(msg_script_path):
        print(f"❌ Reference script not found: {msg_script_path}")
        return False
    
    if not os.path.exists(fcl_script_path):
        print(f"❌ FCL script not found: {fcl_script_path}")
        return False
    
    with open(msg_script_path, 'r') as f:
        msg_content = f.read()
    
    with open(fcl_script_path, 'r') as f:
        fcl_content = f.read()
    
    print("🔍 Checking consistency with reference script...")
    
    # Check if eval_bleu_epoch function signature is similar
    if "def eval_bleu_epoch(args, eval_dataloader, model, tokenizer):" in msg_content:
        if "def eval_bleu_epoch(args, eval_dataloader, model, tokenizer):" in fcl_content:
            print("  ✅ eval_bleu_epoch function signature matches")
        else:
            print("  ❌ eval_bleu_epoch function signature doesn't match")
            return False
    
    # Check if validation is called in similar places
    validation_patterns = [
        "eval_bleu_epoch(args, valid_dataloader, model, tokenizer)",
        "checkpoints-last\" + \"-\" + str(bleu)",
        "checkpoints-\" + str(global_step) + \"-\" + str(bleu)"
    ]
    
    for pattern in validation_patterns:
        if pattern in fcl_content:
            print(f"  ✅ Pattern found: {pattern[:50]}...")
        else:
            print(f"  ❌ Pattern missing: {pattern[:50]}...")
            return False
    
    print("  ✅ Consistency checks passed!")
    return True

def main():
    """Main test function."""
    print("🧪 Testing FCL validation implementation...\n")
    
    # Test 1: Check validation implementation
    test1_passed = check_fcl_validation_implementation()
    
    print("\n" + "="*60 + "\n")
    
    # Test 2: Check consistency with reference script
    test2_passed = check_consistency_with_msg_script()
    
    print("\n" + "="*60 + "\n")
    
    if test1_passed and test2_passed:
        print("🎉 All tests passed! FCL validation implementation is correct.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
