{"instruction": "Write a Python function to add two numbers", "response": "def add_numbers(a, b):\n    return a + b"}
{"instruction": "Create a function to check if a number is even", "response": "def is_even(n):\n    return n % 2 == 0"}
{"instruction": "Write a function to find the maximum of two numbers", "response": "def max_of_two(a, b):\n    return a if a > b else b"}
{"instruction": "Create a function to calculate factorial", "response": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n - 1)"}
{"instruction": "Write a function to reverse a string", "response": "def reverse_string(s):\n    return s[::-1]"}
{"instruction": "Create a function to check if a string is palindrome", "response": "def is_palindrome(s):\n    return s == s[::-1]"}
{"instruction": "Write a function to count vowels in a string", "response": "def count_vowels(s):\n    vowels = 'aeiouAEIOU'\n    return sum(1 for char in s if char in vowels)"}
{"instruction": "Create a function to find the length of a list", "response": "def list_length(lst):\n    return len(lst)"}
{"instruction": "Write a function to sum all elements in a list", "response": "def sum_list(lst):\n    return sum(lst)"}
{"instruction": "Create a function to find the minimum element in a list", "response": "def find_min(lst):\n    return min(lst) if lst else None"}
