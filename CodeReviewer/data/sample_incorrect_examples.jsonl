{"instruction": "Write a Python function to add two numbers", "response": "def add_numbers(a, b)\n    return a + b", "incorrect": 1, "error type": "Syntax Error"}
{"instruction": "Create a function to check if a number is even", "response": "def is_even(n):\n    return n % 2 = 0", "incorrect": 1, "error type": "Syntax Error"}
{"instruction": "Write a function to find the maximum of two numbers", "response": "def max_of_two(a, b):\n    return a if a < b else b", "incorrect": 1, "error type": "Logical Error"}
{"instruction": "Create a function to calculate factorial", "response": "def factorial(n):\n    if n <= 1:\n        return 1\n    return n * factorial(n + 1)", "incorrect": 1, "error type": "Logical Error"}
{"instruction": "Write a function to reverse a string", "response": "def reverse_string(s):\n    return s[::1]", "incorrect": 1, "error type": "Logical Error"}
{"instruction": "Create a function to check if a string is palindrome", "response": "def is_palindrome(s):\n    return s == reverse(s)", "incorrect": 1, "error type": "Name Error"}
{"instruction": "Write a function to count vowels in a string", "response": "def count_vowels(s):\n    vowels = 'aeiou'\n    return sum(1 for char in s if char in vowels)", "incorrect": 1, "error type": "Logical Error"}
{"instruction": "Create a function to find the length of a list", "response": "def list_length(lst):\n    return length(lst)", "incorrect": 1, "error type": "Name Error"}
{"instruction": "Write a function to sum all elements in a list", "response": "def sum_list(lst):\n    total = 0\n    for i in range(len(lst) + 1):\n        total += lst[i]\n    return total", "incorrect": 1, "error type": "Index Error"}
{"instruction": "Create a function to find the minimum element in a list", "response": "def find_min(lst):\n    return min(lst)", "incorrect": 1, "error type": "Runtime Error"}
