{"patch": "@@ -1,3 +1,4 @@\n def add_numbers(a, b):\n+    # Add two numbers together\n     return a + b", "msg": "Add documentation comment to add_numbers function", "negative_comment": "Fix bug in add function"}
{"patch": "@@ -1,3 +1,3 @@\n def calculate_area(radius):\n-    return 3.14 * radius * radius\n+    return math.pi * radius * radius", "msg": "Use math.pi constant instead of hardcoded value", "negative_comment": "Remove calculation function"}
{"patch": "@@ -1,4 +1,5 @@\n def process_data(data):\n+    if not data:\n+        return []\n     result = []\n     for item in data:", "msg": "Add null check for input data", "negative_comment": "Optimize data processing speed"}
{"patch": "@@ -1,3 +1,3 @@\n def get_user_name(user_id):\n-    return users[user_id]\n+    return users.get(user_id, 'Unknown')", "msg": "<PERSON>le missing user_id gracefully", "negative_comment": "Update user database schema"}
{"patch": "@@ -1,5 +1,6 @@\n def validate_email(email):\n+    import re\n     pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$'\n-    return bool(re.match(pattern, email))\n+    return bool(re.match(pattern, email)) if email else False", "msg": "Add import and handle None email input", "negative_comment": "Remove email validation entirely"}
{"patch": "@@ -1,4 +1,4 @@\n def fibonacci(n):\n     if n <= 1:\n         return n\n-    return fibonacci(n-1) + fibonacci(n-2)\n+    return fibonacci(n-1) + fibonacci(n-2)  # Recursive calculation", "msg": "Add comment explaining recursive calculation", "negative_comment": "Convert to iterative implementation"}
{"patch": "@@ -1,3 +1,4 @@\n def divide_numbers(a, b):\n+    if b == 0:\n+        raise ValueError('Cannot divide by zero')\n     return a / b", "msg": "Add zero division error handling", "negative_comment": "Change division to multiplication"}
{"patch": "@@ -1,4 +1,5 @@\n class Calculator:\n     def __init__(self):\n+        self.history = []\n         self.result = 0", "msg": "Add history tracking to calculator", "negative_comment": "Remove calculator class entirely"}
{"patch": "@@ -1,6 +1,7 @@\n def read_file(filename):\n+    if not os.path.exists(filename):\n+        return None\n     try:\n         with open(filename, 'r') as f:\n             return f.read()", "msg": "Check file existence before reading", "negative_comment": "Use binary mode for file reading"}
{"patch": "@@ -1,3 +1,4 @@\n def format_currency(amount):\n-    return f'${amount:.2f}'\n+    return f'${amount:.2f}' if amount >= 0 else f'-${abs(amount):.2f}'", "msg": "Handle negative currency amounts properly", "negative_comment": "Remove currency formatting function"}
