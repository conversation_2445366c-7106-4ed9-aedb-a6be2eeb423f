# Focal Contrastive Learning (FCL) for CodeReviewer

This implementation adds Focal Contrastive Learning (FCL) loss to the CodeReviewer model to improve code quality assessment by contrasting correct and incorrect code examples.

## FCL Loss Formula

The FCL loss is computed as:

```
L_FCL = -E_{(x,y_c,y_f)~D_FCL} [log σ(β log(S_fcl(x,y_c)/S_cls(x,y_c)) - β log(S_fcl(x,y_f)/S_cls(x,y_f)))]
```

Where:
- `x`: Input code changes/instructions
- `y_c`: Correct code examples
- `y_f`: Incorrect code examples  
- `β`: Temperature parameter controlling contrastive strength
- `S_fcl(x,y)`: FCL score based on generation likelihood
- `S_cls(x,y)`: Classification score based on encoder representation

## Key Components

### 1. FCL Loss Implementation (`models.py`)

- `compute_fcl_scores()`: Computes S_fcl and S_cls scores
- `fcl_loss()`: Implements the complete FCL loss formula
- Updated `forward()` method to support FCL loss computation

### 2. FCL Dataset (`utils.py`)

- `FCLDataset`: Pairs correct and incorrect code examples by instruction
- `FCLFeatures`: Data structure for FCL training samples
- Handles tokenization and padding for contrastive pairs

### 3. FCL Training Script (`run_finetune_fcl.py`)

- Dedicated training script for FCL loss
- Supports distributed training
- Integrates FCL loss with existing model architecture

### 4. Configuration (`configs.py`)

New FCL-related parameters:
- `--use_fcl`: Enable FCL loss
- `--fcl_beta`: Temperature parameter (default: 1.0)
- `--fcl_weight`: Weight for FCL loss (default: 1.0)
- `--correct_file`: Path to correct examples
- `--incorrect_file`: Path to incorrect examples
- `--fcl_sample_ratio`: Sampling ratio for efficiency

## Usage

### 1. Prepare Data

Create JSONL files with msg task data that includes both correct and incorrect comments:

**FCL data format** (`fcl_msg_data.jsonl`):
```json
{
  "patch": "@@ -1,3 +1,4 @@\n def add_numbers(a, b):\n+    # Add two numbers together\n     return a + b",
  "msg": "Add documentation comment to add_numbers function",
  "negative_comment": "Fix bug in add function"
}
```

Each line should contain:
- `patch`: The code diff/change
- `msg`: The correct commit message/comment
- `negative_comment`: An incorrect or misleading comment for the same patch

### 2. Run FCL Training

```bash
cd CodeReviewer/code/sh
bash finetune-fcl.sh
```

Or run directly:
```bash
python run_finetune_fcl.py \
  --model_name_or_path microsoft/codereviewer \
  --output_dir ./save/fcl \
  --fcl_data_file ./data/fcl_msg_data.jsonl \
  --fcl_beta 1.0 \
  --fcl_weight 1.0 \
  --train_batch_size 8 \
  --learning_rate 5e-5 \
  --train_steps 10000
```

### 3. Test FCL Implementation

```bash
python test_fcl.py
```

This will:
- Create sample test data
- Load the model and tokenizer
- Test FCL dataset creation
- Validate FCL loss computation
- Display score analysis

## Parameters

### FCL-Specific Parameters

- **fcl_beta** (float, default=1.0): Temperature parameter that controls the strength of contrastive learning. Higher values make the model more sensitive to differences between correct and incorrect examples.

- **fcl_weight** (float, default=1.0): Weight for FCL loss in the total loss computation. Can be used to balance FCL loss with other losses.

- **fcl_data_file** (str): Path to JSONL file containing msg task data with both correct (`msg`) and incorrect (`negative_comment`) comments for the same patches.

### Training Parameters

Standard CodeReviewer training parameters apply:
- `max_source_length`: Maximum length for input sequences (default: 512)
- `max_target_length`: Maximum length for target sequences (default: 256)
- `train_batch_size`: Batch size for training (default: 8)
- `learning_rate`: Learning rate (default: 5e-5)

## File Structure

```
CodeReviewer/
├── code/
│   ├── models.py              # FCL loss implementation
│   ├── utils.py               # FCL dataset implementation
│   ├── configs.py             # FCL configuration parameters
│   ├── run_finetune_fcl.py    # FCL training script
│   ├── test_fcl.py            # FCL testing script
│   └── sh/
│       └── finetune-fcl.sh    # FCL training bash script
├── data/
│   └── sample_fcl_msg_data.jsonl        # Sample FCL data with patch, msg, negative_comment
└── FCL_README.md              # This file
```

## Expected Benefits

1. **Improved Code Quality Assessment**: By contrasting correct and incorrect examples, the model learns to better distinguish code quality.

2. **Enhanced Error Detection**: The model becomes more sensitive to common programming errors through contrastive learning.

3. **Better Representation Learning**: FCL encourages the model to learn more discriminative representations for code quality.

## Notes

- Ensure each data entry contains both correct (`msg`) and incorrect (`negative_comment`) comments for the same patch for effective contrastive learning.
- The FCL loss is computed in addition to standard language modeling losses.
- Adjust `fcl_beta` and `fcl_weight` based on your specific use case and validation performance.
- The implementation supports distributed training for large-scale experiments.
