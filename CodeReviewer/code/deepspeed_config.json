{"train_batch_size": 12, "train_micro_batch_size_per_gpu": 3, "gradient_accumulation_steps": 2, "optimizer": {"type": "AdamW", "params": {"lr": 0.0003, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.0}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 0.0003, "warmup_num_steps": 3000}}, "fp16": {"enabled": true, "auto_cast": false, "loss_scale": 0, "initial_scale_power": 12, "loss_scale_window": 500, "hysteresis": 1, "min_loss_scale": 1}, "zero_optimization": {"stage": 2, "allgather_partitions": true, "allgather_bucket_size": 200000000.0, "overlap_comm": true, "reduce_scatter": true, "reduce_bucket_size": 200000000.0, "contiguous_gradients": true}, "gradient_clipping": 1.0, "wall_clock_breakdown": false, "steps_per_print": 100}