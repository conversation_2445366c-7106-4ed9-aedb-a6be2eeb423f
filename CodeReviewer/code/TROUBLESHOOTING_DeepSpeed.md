# DeepSpeed故障排除指南

## 常见错误及解决方案

### 1. 参数解析错误

**错误信息:**
```
error: unrecognized arguments: --deepspeed_config
```

**解决方案:**
使用标准的DeepSpeed参数格式：
```bash
# 错误的方式
--deepspeed_config config.json

# 正确的方式  
--deepspeed=config.json
```

**推荐使用简化脚本:**
```bash
bash sh/finetune_deepspeed_simple.sh
```

### 2. DeepSpeed未安装

**错误信息:**
```
ImportError: No module named 'deepspeed'
```

**解决方案:**
```bash
# 安装DeepSpeed
bash install_deepspeed.sh

# 或手动安装
pip install deepspeed
```

### 3. CUDA版本不兼容

**错误信息:**
```
CUDA extension not found
```

**解决方案:**
```bash
# 检查CUDA版本
nvcc --version
python -c "import torch; print(torch.version.cuda)"

# 重新安装匹配的DeepSpeed版本
pip uninstall deepspeed
pip install deepspeed --no-cache-dir
```

### 4. 内存不足

**错误信息:**
```
CUDA out of memory
```

**解决方案:**
1. 减小批次大小
2. 使用更高的ZeRO stage
3. 启用CPU offload

```json
{
  "zero_optimization": {
    "stage": 3,
    "offload_optimizer": {"device": "cpu"},
    "offload_param": {"device": "cpu"}
  }
}
```

### 5. 分布式初始化失败

**错误信息:**
```
RuntimeError: Default process group has not been initialized
```

**解决方案:**
确保使用正确的启动方式：
```bash
# 使用deepspeed命令启动
deepspeed --num_gpus=2 script.py

# 不要使用torch.distributed.run
```

## 测试步骤

### 1. 基础测试
```bash
# 测试DeepSpeed安装
python -c "import deepspeed; print('DeepSpeed版本:', deepspeed.__version__)"

# 测试CUDA
python -c "import torch; print('CUDA可用:', torch.cuda.is_available())"
```

### 2. 快速功能测试
```bash
python quick_test_deepspeed.py
```

### 3. 参数测试
```bash
bash test_deepspeed_args.sh
```

### 4. 完整训练测试
```bash
# 使用简化脚本（推荐）
bash sh/finetune_deepspeed_simple.sh

# 或使用主脚本
USE_DEEPSPEED=1 bash sh/finetune_with_negative.sh
```

## 性能优化建议

### 1. 批次大小调优
```bash
# 16GB GPU推荐配置
# Stage 2: micro_batch_size=3, gradient_accumulation=4
# Stage 3: micro_batch_size=6, gradient_accumulation=2
```

### 2. ZeRO Stage选择
- **Stage 1**: 分片优化器状态（节省内存较少）
- **Stage 2**: 分片优化器状态+梯度（推荐开始使用）
- **Stage 3**: 分片所有参数（最大内存节省）

### 3. CPU Offload
```json
{
  "zero_optimization": {
    "stage": 3,
    "offload_optimizer": {
      "device": "cpu",
      "pin_memory": true
    }
  }
}
```

## 监控和调试

### 1. GPU监控
```bash
# 实时监控GPU使用
nvidia-smi -l 1

# 查看GPU内存详情
nvidia-smi --query-gpu=memory.used,memory.total --format=csv
```

### 2. DeepSpeed日志
```bash
# 启用详细日志
export DEEPSPEED_LOG_LEVEL=INFO
export NCCL_DEBUG=INFO
```

### 3. 性能分析
```bash
# 使用DeepSpeed profiler
python -m deepspeed.profiling.flops_profiler script.py
```

## 配置文件模板

### 基础配置 (deepspeed_config.json)
```json
{
  "train_batch_size": 12,
  "train_micro_batch_size_per_gpu": 3,
  "gradient_accumulation_steps": 2,
  "optimizer": {
    "type": "AdamW",
    "params": {"lr": 3e-4}
  },
  "fp16": {"enabled": true},
  "zero_optimization": {"stage": 2}
}
```

### 高级配置 (deepspeed_config_stage3.json)
```json
{
  "train_batch_size": 12,
  "train_micro_batch_size_per_gpu": 6,
  "gradient_accumulation_steps": 1,
  "optimizer": {
    "type": "AdamW",
    "params": {"lr": 3e-4}
  },
  "fp16": {"enabled": true},
  "zero_optimization": {
    "stage": 3,
    "offload_optimizer": {"device": "cpu"},
    "offload_param": {"device": "cpu"}
  }
}
```

## 联系支持

如果遇到其他问题：
1. 查看DeepSpeed官方文档: https://deepspeed.readthedocs.io/
2. 检查GitHub Issues: https://github.com/microsoft/DeepSpeed/issues
3. 运行诊断脚本: `ds_report`
