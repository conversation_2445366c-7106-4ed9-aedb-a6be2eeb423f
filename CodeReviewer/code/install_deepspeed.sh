#!/bin/bash

# DeepSpeed安装脚本
echo "=== 安装DeepSpeed ==="

# 检查CUDA版本
echo "检查CUDA版本..."
nvcc --version

# 检查PyTorch版本
echo "检查PyTorch版本..."
python -c "import torch; print(f'PyTorch版本: {torch.__version__}'); print(f'CUDA可用: {torch.cuda.is_available()}')"

# 安装DeepSpeed
echo "安装DeepSpeed..."
pip install deepspeed

# 验证安装
echo "验证DeepSpeed安装..."
python -c "import deepspeed; print(f'DeepSpeed版本: {deepspeed.__version__}')"

# 检查DeepSpeed环境
echo "检查DeepSpeed环境..."
ds_report

echo "=== DeepSpeed安装完成 ==="

# 创建简单测试脚本
cat > test_deepspeed.py << 'EOF'
import torch
import deepspeed
import torch.nn as nn

class SimpleModel(nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(10, 1)
    
    def forward(self, x):
        return self.linear(x)

def test_deepspeed():
    model = SimpleModel()
    
    # 简单的DeepSpeed配置
    ds_config = {
        "train_batch_size": 4,
        "train_micro_batch_size_per_gpu": 2,
        "optimizer": {
            "type": "Adam",
            "params": {"lr": 0.001}
        },
        "fp16": {"enabled": False}
    }
    
    # 初始化DeepSpeed
    model_engine, optimizer, _, _ = deepspeed.initialize(
        model=model,
        config_params=ds_config
    )
    
    # 测试前向传播
    x = torch.randn(4, 10)
    if torch.cuda.is_available():
        x = x.cuda()
    
    output = model_engine(x)
    loss = output.sum()
    
    # 测试反向传播
    model_engine.backward(loss)
    model_engine.step()
    
    print("✅ DeepSpeed测试成功!")

if __name__ == "__main__":
    test_deepspeed()
EOF

echo "运行DeepSpeed测试..."
python test_deepspeed.py

echo "如果看到'DeepSpeed测试成功!'，说明安装正确！"
