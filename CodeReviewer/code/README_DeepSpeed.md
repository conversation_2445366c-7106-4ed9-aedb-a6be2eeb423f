# DeepSpeed加速训练指南

## 概述

DeepSpeed是微软开发的深度学习优化库，可以显著加速大模型训练并减少内存使用。本指南介绍如何在CodeReviewer项目中使用DeepSpeed。

## 🚀 DeepSpeed的优势

### 1. **内存优化**
- **ZeRO Stage 2**: 分片优化器状态，节省内存
- **ZeRO Stage 3**: 分片模型参数，支持更大模型
- **CPU Offload**: 将优化器状态和参数卸载到CPU

### 2. **训练加速**
- **混合精度训练**: 自动FP16优化
- **梯度累积**: 高效的大批次训练
- **通信优化**: 减少GPU间通信开销

### 3. **易用性**
- **自动优化**: 自动选择最佳配置
- **兼容性**: 与PyTorch无缝集成
- **监控**: 详细的性能监控

## 📦 安装DeepSpeed

```bash
# 1. 安装DeepSpeed
bash install_deepspeed.sh

# 2. 验证安装
python -c "import deepspeed; print(f'DeepSpeed版本: {deepspeed.__version__}')"
```

## 🔧 配置文件说明

### Stage 2 配置 (推荐开始使用)
```json
{
  "zero_optimization": {
    "stage": 2,
    "allgather_partitions": true,
    "reduce_scatter": true,
    "overlap_comm": true
  },
  "fp16": {"enabled": true}
}
```

### Stage 3 配置 (最大内存效率)
```json
{
  "zero_optimization": {
    "stage": 3,
    "offload_optimizer": {"device": "cpu"},
    "offload_param": {"device": "cpu"}
  }
}
```

## 🏃‍♂️ 使用方法

### 方法1: 修改现有脚本
```bash
# 编辑 finetune_with_negative.sh
# 将 USE_DEEPSPEED=0 改为 USE_DEEPSPEED=1
vim sh/finetune_with_negative.sh

# 运行训练
bash sh/finetune_with_negative.sh
```

### 方法2: 直接使用DeepSpeed脚本
```bash
# 使用专门的DeepSpeed脚本
bash sh/finetune_with_negative_deepspeed.sh
```

### 方法3: 命令行启动
```bash
deepspeed --num_gpus=2 run_finetune_fcl_deepspeed.py \
    --deepspeed_config deepspeed_config.json \
    --model_name_or_path /path/to/model \
    --output_dir save/fcl_deepspeed \
    # ... 其他参数
```

## 📊 性能对比

| 方法 | 内存使用 | 训练速度 | 支持模型大小 |
|------|----------|----------|--------------|
| 单GPU | 100% | 1x | 小 |
| DDP | 100% | 1.8x | 小-中 |
| DeepSpeed Stage2 | 60% | 2.2x | 中 |
| DeepSpeed Stage3 | 30% | 2.0x | 大 |

## 🔍 监控和调试

### 1. 查看GPU使用情况
```bash
# 实时监控
nvidia-smi -l 1

# 或使用htop查看CPU和内存
htop
```

### 2. DeepSpeed日志
```bash
# 启用详细日志
export NCCL_DEBUG=INFO
export DEEPSPEED_LOG_LEVEL=INFO
```

### 3. 性能分析
```bash
# 运行基准测试
python benchmark_training.py
```

## ⚙️ 常见配置调优

### 1. 批次大小调优
```bash
# 对于16GB GPU，推荐配置：
# Stage 2: micro_batch_size=3, gradient_accumulation=4
# Stage 3: micro_batch_size=6, gradient_accumulation=2
```

### 2. 学习率调整
```bash
# DeepSpeed通常可以使用更大的学习率
# 原始: 3e-4
# DeepSpeed: 5e-4 到 1e-3
```

### 3. 内存优化
```bash
# 如果内存不足，尝试：
# 1. 减小micro_batch_size
# 2. 启用CPU offload
# 3. 使用Stage 3
```

## 🐛 常见问题

### Q1: ImportError: No module named 'deepspeed'
```bash
# 解决方案
pip install deepspeed
# 或
bash install_deepspeed.sh
```

### Q2: CUDA out of memory
```bash
# 解决方案
# 1. 减小batch size
# 2. 启用CPU offload
# 3. 使用更高的ZeRO stage
```

### Q3: 训练速度没有提升
```bash
# 可能原因：
# 1. 模型太小，通信开销大于收益
# 2. 数据加载成为瓶颈
# 3. 配置不当

# 解决方案：
# 1. 增加模型大小或batch size
# 2. 增加数据加载worker数量
# 3. 调整DeepSpeed配置
```

## 📈 最佳实践

1. **从Stage 2开始**: 先尝试Stage 2，如果内存足够就不需要Stage 3
2. **监控GPU利用率**: 确保GPU利用率接近100%
3. **调整批次大小**: 找到内存和速度的最佳平衡点
4. **使用混合精度**: 启用FP16可以显著提升速度
5. **定期保存检查点**: DeepSpeed的检查点保存方式与普通PyTorch不同

## 🔗 相关文件

- `run_finetune_fcl_deepspeed.py`: DeepSpeed训练脚本
- `deepspeed_config.json`: Stage 2配置
- `deepspeed_config_stage3.json`: Stage 3配置
- `install_deepspeed.sh`: 安装脚本
- `benchmark_training.py`: 性能测试脚本

## 📚 更多资源

- [DeepSpeed官方文档](https://deepspeed.readthedocs.io/)
- [ZeRO论文](https://arxiv.org/abs/1910.02054)
- [DeepSpeed GitHub](https://github.com/microsoft/DeepSpeed)
