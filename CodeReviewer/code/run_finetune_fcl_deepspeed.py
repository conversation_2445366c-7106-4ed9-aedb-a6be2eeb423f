import os
import torch
import logging
import argparse
import random
import json
import signal
import sys
from tqdm import tqdm
import multiprocessing
import time
from itertools import cycle
from torch.utils.data import Data<PERSON><PERSON><PERSON>, RandomSampler, SequentialSampler
from torch.utils.data import ConcatDataset
from torch.utils.data.distributed import DistributedSampler
from transformers import AdamW, get_linear_schedule_with_warmup
from models import build_or_load_gen_model
from configs import set_seed
from utils import FCLDataset
from evaluator.smooth_bleu import bleu_fromstr
import deepspeed


logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s -   %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


def signal_handler(signum, frame):
    """Handle interruption signals gracefully"""
    logger.info(f"Received signal {signum}. Cleaning up and exiting...")
    sys.exit(0)


# Set up signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


def get_fcl_loader(data_file, args, tokenizer, pool, eval=False):
    """Create FCL dataloader for contrastive learning."""
    def fn(features):
        return features

    global_rank = args.global_rank
    dataset = FCLDataset(tokenizer, pool, args, data_file)
    data_len = len(dataset)

    if global_rank == 0:
        logger.info(f"FCL Data length: {data_len}.")

    if eval:
        sampler = SequentialSampler(dataset)
    else:
        sampler = DistributedSampler(dataset)

    dataloader = DataLoader(
        dataset,
        sampler=sampler,
        batch_size=args.train_batch_size if not eval else args.eval_batch_size,
        num_workers=0,  # Disable multiprocessing workers to avoid deadlocks in distributed training
        collate_fn=fn
    )

    return dataset, sampler, dataloader


def get_fcl_loaders(data_files, args, tokenizer, pool, eval=False):
    """Create FCL dataloaders for multiple files."""
    def fn(features):
        return features
    global_rank = args.global_rank
    for data_file in data_files:
        dataset = FCLDataset(tokenizer, pool, args, data_file)
        data_len = len(dataset)
        if global_rank == 0:
            logger.info(f"FCL Data length: {data_len}.")
        if eval:
            sampler = SequentialSampler(dataset)
        else:
            sampler = DistributedSampler(dataset)
        dataloader = DataLoader(dataset, sampler=sampler, batch_size=args.train_batch_size if not eval else args.eval_batch_size, \
                                num_workers=0, collate_fn=fn)  # Disable multiprocessing workers
        yield dataset, sampler, dataloader


def save_model(model_engine, output_dir, config, global_rank=0):
    """Save model using DeepSpeed with proper synchronization"""
    try:
        # Only rank 0 creates the directory and logs
        if global_rank == 0:
            logger.info(f"Creating output directory: {output_dir}")
            if not os.path.exists(output_dir):
                os.makedirs(output_dir, exist_ok=True)

        # Synchronize all processes before saving
        if torch.distributed.is_initialized():
            if global_rank == 0:
                logger.info("Synchronizing processes before checkpoint save...")
            torch.distributed.barrier()

        # DeepSpeed checkpoint saving - this should be called by all processes
        if global_rank == 0:
            logger.info("Starting DeepSpeed checkpoint save...")

        # Use a timeout for the checkpoint save to prevent hanging
        import signal

        def timeout_handler(signum, frame):
            raise TimeoutError("Checkpoint save timed out")

        # Set a 5-minute timeout for checkpoint saving
        signal.signal(signal.SIGALRM, timeout_handler)
        signal.alarm(300)  # 5 minutes

        try:
            model_engine.save_checkpoint(output_dir)
            signal.alarm(0)  # Cancel the alarm
            if global_rank == 0:
                logger.info("DeepSpeed checkpoint save completed")
        except TimeoutError:
            signal.alarm(0)
            raise TimeoutError("DeepSpeed checkpoint save timed out after 5 minutes")

        # Only rank 0 saves the config to avoid conflicts
        if global_rank == 0:
            logger.info("Saving config...")
            config.save_pretrained(output_dir)

        # Synchronize again after saving
        if torch.distributed.is_initialized():
            if global_rank == 0:
                logger.info("Synchronizing processes after checkpoint save...")
            torch.distributed.barrier()

        if global_rank == 0:
            logger.info(f"Model save completed successfully: {output_dir}")

    except Exception as e:
        logger.error(f"Error saving model: {e}")
        # Don't let saving errors crash the training
        if global_rank == 0:
            logger.warning("Continuing training despite save error...")


def main(args):
    # Ensure required FCL arguments are provided
    if args.fcl_data_file is None:
        raise ValueError("--fcl_data_file is required for FCL training")

    # DeepSpeed handles distributed initialization
    deepspeed.init_distributed()
    
    args.local_rank = int(os.environ.get("LOCAL_RANK", 0))
    args.global_rank = int(os.environ.get("RANK", 0))
    args.world_size = int(os.environ.get("WORLD_SIZE", 1))
    
    logger.warning("Process rank: %s, global rank: %s, world size: %s",
                   args.local_rank, args.global_rank, args.world_size)
    
    torch.cuda.set_device(args.local_rank)
    set_seed(args)

    config, model, tokenizer = build_or_load_gen_model(args)

    # Don't use multiprocessing pool in distributed training to avoid deadlocks
    pool = None

    # Initialize DeepSpeed with hardcoded config path
    deepspeed_config_path = "/data/xjd/SodaCoder-main/CodeReviewer/code/deepspeed_config.json"

    # Load DeepSpeed config
    with open(deepspeed_config_path, 'r') as f:
        ds_config = json.load(f)

    model_engine, optimizer, _, lr_scheduler = deepspeed.initialize(
        args=args,
        model=model,
        config_params=ds_config
    )

    global_step = 0
    save_steps = args.save_steps
    train_file = args.fcl_data_file
    valid_file = args.dev_filename

    # Handle train files
    if os.path.isdir(train_file):
        train_files = [file for file in os.listdir(train_file) if file.startswith("train") and file.endswith(".jsonl")]
        random.seed(args.seed)
        random.shuffle(train_files)
        train_files = [os.path.join(train_file, file) for file in train_files]
    else:
        train_files = [train_file]
    valid_files = [valid_file]
    
    # Start training with epoch loop
    for epoch in range(1, args.train_epochs + 1):
        # set seed for reproducible data split
        save_seed = args.seed
        args.seed += epoch
        set_seed(args)
        args.seed = save_seed
        model_engine.train()
        nb_tr_examples, nb_tr_steps, tr_loss = 0, 0, 0

        for _, _, train_dataloader in get_fcl_loaders(train_files, args, tokenizer, pool):
            for step, examples in enumerate(train_dataloader, 1):
                if step == 1:
                    ex = examples[0]
                    logger.info(f"batch size: {len(examples)}")
                    logger.info(f"example source: {tokenizer.convert_ids_to_tokens(ex.source_ids)}")

                # Prepare batch data
                source_ids = torch.tensor(
                    [ex.source_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                correct_ids = torch.tensor(
                    [ex.correct_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                incorrect_ids = torch.tensor(
                    [ex.incorrect_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)

                source_mask = source_ids.ne(tokenizer.pad_id)
                correct_mask = correct_ids.ne(tokenizer.pad_id)
                incorrect_mask = incorrect_ids.ne(tokenizer.pad_id)

                # Compute FCL loss
                fcl_loss = model_engine(
                    fcl=True,
                    input_ids=source_ids,
                    attention_mask=source_mask,
                    correct_ids=correct_ids,
                    correct_mask=correct_mask,
                    incorrect_ids=incorrect_ids,
                    incorrect_mask=incorrect_mask,
                    beta=args.fcl_beta,
                    alpha=args.fcl_alpha
                )

                # Apply FCL weight
                loss = args.fcl_weight * fcl_loss

                # DeepSpeed backward
                model_engine.backward(loss)
                model_engine.step()

                tr_loss += loss.item()
                nb_tr_examples += source_ids.size(0)
                nb_tr_steps += 1
                global_step += 1

                if args.global_rank == 0 and global_step % args.log_steps == 0:
                    train_loss = round(tr_loss / nb_tr_steps, 4)
                    logger.info(
                        "step {}/{}: Train loss {}".format(
                            global_step,
                            args.train_steps,
                            round(train_loss, 3),
                        )
                    )

                if global_step == args.train_steps:
                    # end training - all processes participate in saving
                    output_dir = os.path.join(args.output_dir, "checkpoints-last")
                    save_model(model_engine, output_dir, config, args.global_rank)
                    if args.global_rank == 0:
                        logger.info(f"Reach max steps {args.train_steps}.")
                    # Ensure all processes exit together
                    return

                if global_step % save_steps == 0:
                    # Add debugging and safety check
                    if args.global_rank == 0:
                        logger.info(f"Starting checkpoint save at step {global_step}...")

                    try:
                        # All processes participate in checkpoint saving
                        output_dir = os.path.join(args.output_dir, "checkpoints-" + str(global_step))
                        save_model(model_engine, output_dir, config, args.global_rank)
                        if args.global_rank == 0:
                            logger.info(
                                "Successfully saved the {}-step model into {}".format(
                                    global_step, output_dir
                                )
                            )
                    except Exception as e:
                        if args.global_rank == 0:
                            logger.error(f"Failed to save checkpoint at step {global_step}: {e}")
                            logger.warning("Continuing training without saving checkpoint...")


def add_custom_args(parser):
    """添加自定义参数到parser"""
    # 基本参数
    parser.add_argument("--model_name_or_path", type=str, required=True)
    parser.add_argument("--output_dir", type=str, required=True)
    parser.add_argument("--fcl_data_file", type=str, required=True)
    parser.add_argument("--dev_filename", type=str, required=True)
    parser.add_argument("--max_source_length", type=int, default=512)
    parser.add_argument("--max_target_length", type=int, default=128)
    parser.add_argument("--learning_rate", type=float, default=3e-4)
    parser.add_argument("--train_epochs", type=int, default=15)
    parser.add_argument("--train_steps", type=int, default=30000)
    parser.add_argument("--save_steps", type=int, default=1800)
    parser.add_argument("--log_steps", type=int, default=100)
    parser.add_argument("--seed", type=int, default=2233)
    parser.add_argument("--mask_rate", type=float, default=0.15)
    parser.add_argument("--raw_input", action="store_true")
    parser.add_argument("--train_batch_size", type=int, default=3)
    parser.add_argument("--eval_batch_size", type=int, default=8)
    parser.add_argument("--gradient_accumulation_steps", type=int, default=2)

    # FCL参数
    parser.add_argument("--fcl_beta", type=float, default=1.0)
    parser.add_argument("--fcl_alpha", type=float, default=1.0)
    parser.add_argument("--fcl_weight", type=float, default=1.0)

    # models.py需要的参数
    parser.add_argument("--load_model_path", type=str, default=None)

    # 分布式训练参数
    parser.add_argument("--local_rank", type=int, default=-1)
    parser.add_argument("--gpu_per_node", type=int, default=2)
    parser.add_argument("--node_index", type=int, default=0)

    return parser


if __name__ == "__main__":
    # 创建parser
    parser = argparse.ArgumentParser()

    # 先添加自定义参数
    parser = add_custom_args(parser)

    # 解析参数（不让DeepSpeed添加参数，避免冲突）
    args = parser.parse_args()

    args.cpu_count = 8
    logging.getLogger("transformers.tokenization_utils_base").setLevel(logging.ERROR)
    logger.info(args)
    main(args)
    logger.info("Training finished.")
