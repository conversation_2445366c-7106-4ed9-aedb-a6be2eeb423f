#!/usr/bin/env python3
"""
训练性能基准测试脚本
比较不同训练方法的性能：单GPU、DDP、DeepSpeed
"""

import time
import torch
import psutil
import subprocess
import json
from datetime import datetime

def get_gpu_memory():
    """获取GPU内存使用情况"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=memory.used,memory.total', 
                               '--format=csv,nounits,noheader'], 
                              capture_output=True, text=True)
        lines = result.stdout.strip().split('\n')
        gpu_info = []
        for line in lines:
            used, total = map(int, line.split(', '))
            gpu_info.append({'used': used, 'total': total, 'usage_percent': used/total*100})
        return gpu_info
    except:
        return []

def get_system_info():
    """获取系统信息"""
    return {
        'cpu_count': psutil.cpu_count(),
        'memory_total_gb': psutil.virtual_memory().total / (1024**3),
        'memory_available_gb': psutil.virtual_memory().available / (1024**3),
        'gpu_info': get_gpu_memory()
    }

def run_benchmark(script_path, config_name, duration_minutes=5):
    """运行基准测试"""
    print(f"\n=== 开始测试: {config_name} ===")
    
    start_time = time.time()
    start_info = get_system_info()
    
    print(f"开始时间: {datetime.now()}")
    print(f"GPU内存使用: {start_info['gpu_info']}")
    
    # 这里应该启动实际的训练脚本
    # 为了演示，我们只是等待指定时间
    print(f"模拟训练 {duration_minutes} 分钟...")
    time.sleep(duration_minutes * 60)  # 实际使用时替换为真实训练
    
    end_time = time.time()
    end_info = get_system_info()
    
    duration = end_time - start_time
    
    result = {
        'config_name': config_name,
        'duration_seconds': duration,
        'start_time': datetime.fromtimestamp(start_time).isoformat(),
        'end_time': datetime.fromtimestamp(end_time).isoformat(),
        'start_gpu_memory': start_info['gpu_info'],
        'end_gpu_memory': end_info['gpu_info'],
        'system_info': start_info
    }
    
    print(f"完成时间: {datetime.now()}")
    print(f"总耗时: {duration:.2f} 秒")
    print(f"GPU内存使用: {end_info['gpu_info']}")
    
    return result

def main():
    """主函数 - 运行所有基准测试"""
    results = []
    
    # 测试配置
    test_configs = [
        {
            'name': '单GPU训练',
            'script': 'run_finetune_fcl.py',
            'description': '传统单GPU训练方法'
        },
        {
            'name': 'DDP双GPU训练', 
            'script': 'sh/finetune_with_negative.sh',
            'description': '分布式数据并行训练'
        },
        {
            'name': 'DeepSpeed Stage2',
            'script': 'sh/finetune_with_negative_deepspeed.sh',
            'description': 'DeepSpeed ZeRO Stage 2优化'
        },
        {
            'name': 'DeepSpeed Stage3',
            'script': 'sh/finetune_with_negative_deepspeed.sh',
            'description': 'DeepSpeed ZeRO Stage 3优化（最大内存效率）'
        }
    ]
    
    print("=== 训练性能基准测试 ===")
    print(f"系统信息: {get_system_info()}")
    
    for config in test_configs:
        try:
            result = run_benchmark(
                config['script'], 
                config['name'],
                duration_minutes=1  # 短时间测试，实际使用时可以增加
            )
            results.append(result)
        except Exception as e:
            print(f"测试 {config['name']} 失败: {e}")
    
    # 保存结果
    with open('benchmark_results.json', 'w') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    
    # 打印总结
    print("\n=== 性能对比总结 ===")
    for result in results:
        print(f"{result['config_name']}: {result['duration_seconds']:.2f}秒")
    
    print("\n详细结果已保存到 benchmark_results.json")

if __name__ == "__main__":
    main()
