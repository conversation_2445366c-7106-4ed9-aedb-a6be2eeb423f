#!/bin/bash

# 快速修复DeepSpeed梯度溢出问题
echo "=== 快速修复DeepSpeed梯度溢出 ==="

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

echo "检测到梯度溢出问题，正在应用修复..."

# 备份原配置
if [ -f "${mnt_dir}/deepspeed_config.json" ]; then
    cp ${mnt_dir}/deepspeed_config.json ${mnt_dir}/deepspeed_config.json.backup
    echo "✅ 已备份原配置到 deepspeed_config.json.backup"
fi

# 应用稳定配置
echo "应用稳定配置..."
cat > ${mnt_dir}/deepspeed_config.json << 'EOF'
{
  "train_batch_size": 12,
  "train_micro_batch_size_per_gpu": 3,
  "gradient_accumulation_steps": 2,
  "optimizer": {
    "type": "AdamW",
    "params": {
      "lr": 3e-4,
      "betas": [0.9, 0.999],
      "eps": 1e-8,
      "weight_decay": 0.0
    }
  },
  "scheduler": {
    "type": "WarmupLR",
    "params": {
      "warmup_min_lr": 0,
      "warmup_max_lr": 3e-4,
      "warmup_num_steps": 3000
    }
  },
  "fp16": {
    "enabled": true,
    "auto_cast": false,
    "loss_scale": 0,
    "initial_scale_power": 10,
    "loss_scale_window": 200,
    "hysteresis": 1,
    "min_loss_scale": 1,
    "consecutive_hysteresis": false
  },
  "zero_optimization": {
    "stage": 2,
    "allgather_partitions": true,
    "allgather_bucket_size": 2e8,
    "overlap_comm": true,
    "reduce_scatter": true,
    "reduce_bucket_size": 2e8,
    "contiguous_gradients": true
  },
  "gradient_clipping": 0.5,
  "wall_clock_breakdown": false,
  "steps_per_print": 100
}
EOF

echo "✅ 已应用稳定配置"

echo ""
echo "修复内容："
echo "- 降低initial_scale_power: 16 → 10"
echo "- 减少hysteresis: 2 → 1" 
echo "- 缩短loss_scale_window: 1000 → 200"
echo "- 启用梯度裁剪: 0.5"
echo "- 禁用consecutive_hysteresis"

echo ""
echo "如果仍有溢出问题，可以："
echo "1. 使用FP32配置: bash switch_deepspeed_config.sh (选择2)"
echo "2. 进一步降低批次大小"
echo "3. 运行诊断: python diagnose_overflow.py"

echo ""
echo "现在可以重新运行训练："
echo "bash sh/finetune_with_negative.sh"
