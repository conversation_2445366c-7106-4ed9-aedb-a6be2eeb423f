#!/bin/bash

# 测试独立DeepSpeed脚本的参数解析
echo "=== 测试独立DeepSpeed脚本参数解析 ==="

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

echo "测试脚本: ${mnt_dir}/run_fcl_deepspeed_standalone.py"

# 测试帮助信息
echo "1. 测试帮助信息..."
python ${mnt_dir}/run_fcl_deepspeed_standalone.py --help

echo ""
echo "2. 测试参数解析（模拟）..."

# 创建临时测试文件
echo '{"test": "data"}' > /tmp/test_fcl.jsonl
echo '{"test": "dev"}' > /tmp/test_dev.jsonl

# 测试参数解析（不实际运行训练）
python -c "
import sys
sys.path.append('${mnt_dir}')

try:
    import argparse
    import deepspeed
    
    # 模拟参数解析
    parser = argparse.ArgumentParser()
    parser.add_argument('--model_name_or_path', type=str, required=True)
    parser.add_argument('--output_dir', type=str, required=True)
    parser.add_argument('--fcl_data_file', type=str, required=True)
    parser.add_argument('--dev_filename', type=str, required=True)
    parser.add_argument('--max_source_length', type=int, default=512)
    parser.add_argument('--max_target_length', type=int, default=128)
    parser.add_argument('--train_batch_size', type=int, default=3)
    parser.add_argument('--learning_rate', type=float, default=3e-4)
    parser.add_argument('--train_epochs', type=int, default=15)
    parser.add_argument('--train_steps', type=int, default=30000)
    parser.add_argument('--save_steps', type=int, default=1800)
    parser.add_argument('--log_steps', type=int, default=100)
    parser.add_argument('--seed', type=int, default=2233)
    parser.add_argument('--mask_rate', type=float, default=0.15)
    parser.add_argument('--fcl_beta', type=float, default=1.0)
    parser.add_argument('--fcl_alpha', type=float, default=1.0)
    parser.add_argument('--fcl_weight', type=float, default=1.0)
    parser.add_argument('--raw_input', action='store_true')
    parser.add_argument('--local_rank', type=int, default=-1)
    
    # 添加DeepSpeed参数
    parser = deepspeed.add_config_arguments(parser)
    
    # 测试参数
    test_args = [
        '--deepspeed=${mnt_dir}/deepspeed_config.json',
        '--model_name_or_path', '/tmp/test_model',
        '--output_dir', '/tmp/test_output',
        '--fcl_data_file', '/tmp/test_fcl.jsonl',
        '--dev_filename', '/tmp/test_dev.jsonl',
        '--raw_input'
    ]
    
    args = parser.parse_args(test_args)
    print('✅ 参数解析成功!')
    print(f'DeepSpeed配置: {getattr(args, \"deepspeed\", \"None\")}')
    print(f'模型路径: {args.model_name_or_path}')
    print(f'输出目录: {args.output_dir}')
    
except Exception as e:
    print(f'❌ 参数解析失败: {e}')
    import traceback
    traceback.print_exc()
"

# 清理临时文件
rm -f /tmp/test_fcl.jsonl /tmp/test_dev.jsonl

echo ""
echo "3. 检查DeepSpeed配置文件..."
if [ -f "${mnt_dir}/deepspeed_config.json" ]; then
    echo "✅ DeepSpeed配置文件存在"
    echo "配置内容:"
    cat ${mnt_dir}/deepspeed_config.json | head -10
else
    echo "❌ DeepSpeed配置文件不存在"
fi

echo ""
echo "=== 测试完成 ==="
echo "如果看到'参数解析成功!'，说明独立脚本可以正常工作"
