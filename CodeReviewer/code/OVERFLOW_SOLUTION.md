# DeepSpeed梯度溢出问题解决方案

## 🔍 问题分析

你遇到的错误信息：
```
OVERFLOW! Rank 0 Skipping step. Attempted loss scale: 65536, but hysteresis is 2. Reducing hysteresis to 1
OVERFLOW! Rank 0 Skipping step. Attempted loss scale: 32768, reducing to 16384
```

**原因**：
- FP16混合精度训练中梯度值过大
- Loss scale从65536降到32768再到16384
- DeepSpeed自动调整但仍然溢出

## 🚀 快速解决方案

### 方案1: 一键修复（推荐）
```bash
cd /data/xjd/SodaCoder-main/CodeReviewer/code
bash fix_overflow.sh
```

### 方案2: 手动切换配置
```bash
cd /data/xjd/SodaCoder-main/CodeReviewer/code
bash switch_deepspeed_config.sh
# 选择选项1 (稳定FP16配置)
```

### 方案3: 使用FP32（最稳定）
```bash
cd /data/xjd/SodaCoder-main/CodeReviewer/code
bash switch_deepspeed_config.sh
# 选择选项2 (FP32配置)
```

## 📊 配置对比

| 配置类型 | 速度 | 内存使用 | 稳定性 | 推荐场景 |
|----------|------|----------|--------|----------|
| 原始FP16 | 最快 | 最少 | 低 | 理想情况 |
| 稳定FP16 | 快 | 少 | 高 | **推荐使用** |
| FP32 | 慢 | 多 | 最高 | 溢出严重时 |

## 🔧 技术细节

### 修复的参数
```json
{
  "fp16": {
    "initial_scale_power": 10,    // 从16降到10 (1024 vs 65536)
    "loss_scale_window": 200,     // 从1000降到200
    "hysteresis": 1,              // 从2降到1
    "consecutive_hysteresis": false
  },
  "gradient_clipping": 0.5        // 添加梯度裁剪
}
```

### 参数说明
- **initial_scale_power**: 初始loss scale = 2^power
- **loss_scale_window**: 多少步后尝试增加loss scale
- **hysteresis**: loss scale调整的滞后性
- **gradient_clipping**: 梯度裁剪阈值

## 🧪 验证修复

### 1. 运行诊断
```bash
python diagnose_overflow.py
```

### 2. 监控训练
```bash
# 启动训练
bash sh/finetune_with_negative.sh

# 另开终端监控
tail -f save/fcl_deepspeed/logs/train.log | grep -E "(OVERFLOW|loss|step)"
```

### 3. 成功标志
- 不再出现"OVERFLOW"消息
- Loss值稳定下降
- GPU利用率接近100%

## 🔄 如果仍有问题

### 进一步降低批次大小
```json
{
  "train_micro_batch_size_per_gpu": 2,  // 从3降到2
  "gradient_accumulation_steps": 3      // 从2增到3
}
```

### 完全禁用FP16
```bash
# 使用FP32配置
bash switch_deepspeed_config.sh
# 选择选项2
```

### 检查数据质量
```python
# 检查训练数据是否有异常值
import json
data = [json.loads(line) for line in open('/data/xjd/SodaCoder-main/w_negative_train_20000.jsonl')]
print(f"数据量: {len(data)}")
print(f"样本: {data[0].keys()}")
```

## 📈 性能影响

### 稳定FP16配置
- 速度: 原来的95%
- 内存: 原来的100%
- 稳定性: 大幅提升

### FP32配置
- 速度: 原来的70%
- 内存: 原来的150%
- 稳定性: 完全稳定

## 🎯 推荐流程

1. **首先尝试**: `bash fix_overflow.sh`
2. **如果仍溢出**: 使用FP32配置
3. **如果内存不足**: 使用Stage3配置
4. **监控训练**: 确保loss正常下降

## 📞 获取帮助

如果问题仍然存在：

1. **查看完整日志**:
```bash
tail -100 save/fcl_deepspeed/logs/train.log
```

2. **运行诊断**:
```bash
python diagnose_overflow.py
```

3. **检查GPU状态**:
```bash
nvidia-smi
```

现在运行修复脚本，应该可以解决溢出问题！
