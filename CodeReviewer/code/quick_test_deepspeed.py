#!/usr/bin/env python3
"""
快速测试DeepSpeed是否能正常工作
"""

import torch
import torch.nn as nn
import deepspeed
import argparse
import json
import os

class SimpleReviewerModel(nn.Module):
    """简化的ReviewerModel用于测试"""
    def __init__(self, vocab_size=1000, hidden_size=512):
        super().__init__()
        self.embedding = nn.Embedding(vocab_size, hidden_size)
        self.encoder = nn.TransformerEncoder(
            nn.TransformerEncoderLayer(hidden_size, nhead=8, batch_first=True),
            num_layers=2
        )
        self.classifier = nn.Linear(hidden_size, 2)
        
    def forward(self, input_ids, attention_mask=None, **kwargs):
        # 简化的前向传播
        x = self.embedding(input_ids)
        x = self.encoder(x)
        x = x.mean(dim=1)  # 简单的池化
        logits = self.classifier(x)
        
        # 模拟损失计算
        if 'labels' in kwargs:
            loss_fn = nn.CrossEntropyLoss()
            loss = loss_fn(logits, kwargs['labels'])
            return loss
        return logits

def create_test_config():
    """创建测试用的DeepSpeed配置"""
    config = {
        "train_batch_size": 8,
        "train_micro_batch_size_per_gpu": 4,
        "gradient_accumulation_steps": 2,
        "optimizer": {
            "type": "AdamW",
            "params": {
                "lr": 1e-4,
                "betas": [0.9, 0.999],
                "eps": 1e-8,
                "weight_decay": 0.01
            }
        },
        "fp16": {
            "enabled": True,
            "auto_cast": False,
            "loss_scale": 0,
            "initial_scale_power": 16,
            "loss_scale_window": 1000,
            "hysteresis": 2,
            "min_loss_scale": 1
        },
        "zero_optimization": {
            "stage": 2,
            "allgather_partitions": True,
            "allgather_bucket_size": 2e8,
            "overlap_comm": True,
            "reduce_scatter": True,
            "reduce_bucket_size": 2e8,
            "contiguous_gradients": True
        },
        "gradient_clipping": 1.0,
        "steps_per_print": 10
    }
    return config

def generate_test_data(batch_size=8, seq_len=128, vocab_size=1000):
    """生成测试数据"""
    input_ids = torch.randint(0, vocab_size, (batch_size, seq_len))
    attention_mask = torch.ones_like(input_ids)
    labels = torch.randint(0, 2, (batch_size,))
    
    return {
        'input_ids': input_ids,
        'attention_mask': attention_mask,
        'labels': labels
    }

def test_deepspeed():
    """测试DeepSpeed训练"""
    print("=== DeepSpeed快速测试 ===")
    
    # 检查CUDA
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法测试DeepSpeed")
        return False
    
    print(f"✅ CUDA可用，GPU数量: {torch.cuda.device_count()}")
    
    # 创建模型
    model = SimpleReviewerModel()
    print(f"✅ 模型创建成功，参数量: {sum(p.numel() for p in model.parameters()):,}")
    
    # 创建DeepSpeed配置
    ds_config = create_test_config()
    
    # 创建参数解析器（DeepSpeed需要）
    parser = argparse.ArgumentParser()
    parser.add_argument('--local_rank', type=int, default=-1)
    args = parser.parse_args([])
    
    try:
        # 初始化DeepSpeed
        model_engine, optimizer, _, lr_scheduler = deepspeed.initialize(
            args=args,
            model=model,
            config_params=ds_config
        )
        print("✅ DeepSpeed初始化成功")
        
        # 测试训练步骤
        model_engine.train()
        
        for step in range(5):
            # 生成测试数据
            batch = generate_test_data()
            
            # 移动到GPU
            for key in batch:
                batch[key] = batch[key].to(model_engine.device)
            
            # 前向传播
            loss = model_engine(**batch)
            
            # 反向传播
            model_engine.backward(loss)
            model_engine.step()
            
            print(f"步骤 {step+1}/5: loss = {loss.item():.4f}")
        
        print("✅ DeepSpeed训练测试成功！")
        
        # 测试保存
        save_dir = "test_deepspeed_checkpoint"
        model_engine.save_checkpoint(save_dir)
        print(f"✅ 检查点保存成功: {save_dir}")
        
        # 清理
        if os.path.exists(save_dir):
            import shutil
            shutil.rmtree(save_dir)
            print("✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ DeepSpeed测试失败: {e}")
        return False

def main():
    """主函数"""
    print("开始DeepSpeed兼容性测试...")
    
    # 检查DeepSpeed安装
    try:
        import deepspeed
        print(f"✅ DeepSpeed已安装，版本: {deepspeed.__version__}")
    except ImportError:
        print("❌ DeepSpeed未安装，请运行: pip install deepspeed")
        return
    
    # 运行测试
    success = test_deepspeed()
    
    if success:
        print("\n🎉 DeepSpeed测试全部通过！")
        print("现在可以安全地使用DeepSpeed训练了。")
        print("\n使用方法:")
        print("1. 修改 sh/finetune_with_negative.sh 中的 USE_DEEPSPEED=1")
        print("2. 运行: bash sh/finetune_with_negative.sh")
    else:
        print("\n❌ DeepSpeed测试失败")
        print("请检查CUDA和DeepSpeed安装")

if __name__ == "__main__":
    main()
