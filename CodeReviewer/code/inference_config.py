import os
import json
import logging
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class InferenceConfig:
    """推理配置类"""
    # API配置
    api_keys: List[str]
    base_url: str = "https://api.deepseek.com"
    model_name: str = "deepseek-chat"
    
    # 并发配置
    max_workers: int = 8
    retry_times: int = 3
    retry_delay: float = 1.0
    timeout: float = 30.0
    rate_limit_delay: float = 0.05
    
    # 推理参数
    max_tokens: int = 1024
    temperature: float = 0.2
    
    # 日志配置
    log_level: str = "INFO"
    log_file: Optional[str] = None
    
    def __post_init__(self):
        """验证配置"""
        if not self.api_keys:
            raise ValueError("至少需要提供一个API key")
        
        if self.max_workers <= 0:
            raise ValueError("max_workers必须大于0")
        
        if self.retry_times < 0:
            raise ValueError("retry_times不能为负数")
        
        if not 0 <= self.temperature <= 2:
            raise ValueError("temperature必须在0-2之间")
    
    @classmethod
    def from_file(cls, config_path: str) -> 'InferenceConfig':
        """从配置文件加载配置"""
        config_path = Path(config_path)
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config_data = json.load(f)
        
        return cls(**config_data)
    
    @classmethod
    def from_env(cls) -> 'InferenceConfig':
        """从环境变量加载配置"""
        # 从环境变量获取API keys
        api_keys_str = os.getenv('DEEPSEEK_API_KEYS', '')
        if not api_keys_str:
            raise ValueError("环境变量 DEEPSEEK_API_KEYS 未设置")
        
        api_keys = [key.strip() for key in api_keys_str.split(',') if key.strip()]
        
        return cls(
            api_keys=api_keys,
            base_url=os.getenv('DEEPSEEK_BASE_URL', 'https://api.deepseek.com'),
            model_name=os.getenv('DEEPSEEK_MODEL', 'deepseek-chat'),
            max_workers=int(os.getenv('MAX_WORKERS', '8')),
            retry_times=int(os.getenv('RETRY_TIMES', '3')),
            timeout=float(os.getenv('TIMEOUT', '30.0')),
            rate_limit_delay=float(os.getenv('RATE_LIMIT_DELAY', '0.05')),
            max_tokens=int(os.getenv('MAX_TOKENS', '1024')),
            temperature=float(os.getenv('TEMPERATURE', '0.2')),
            log_level=os.getenv('LOG_LEVEL', 'INFO'),
            log_file=os.getenv('LOG_FILE')
        )
    
    def save_to_file(self, config_path: str):
        """保存配置到文件"""
        config_path = Path(config_path)
        config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 不保存敏感信息（API keys）到文件
        config_data = asdict(self)
        config_data['api_keys'] = ['<REDACTED>' for _ in self.api_keys]
        
        with open(config_path, 'w', encoding='utf-8') as f:
            json.dump(config_data, f, indent=2, ensure_ascii=False)
        
        logger.info(f"配置已保存到: {config_path}")
    
    def setup_logging(self):
        """设置日志配置"""
        log_level = getattr(logging, self.log_level.upper(), logging.INFO)
        
        # 配置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        # 配置根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 添加文件处理器（如果指定了日志文件）
        if self.log_file:
            log_path = Path(self.log_file)
            log_path.parent.mkdir(parents=True, exist_ok=True)
            
            file_handler = logging.FileHandler(log_path, encoding='utf-8')
            file_handler.setFormatter(formatter)
            root_logger.addHandler(file_handler)
            
            logger.info(f"日志将写入文件: {log_path}")

class ConfigManager:
    """配置管理器"""
    
    DEFAULT_CONFIG_PATHS = [
        'inference_config.json',
        '~/.config/codereview/inference_config.json',
        '/etc/codereview/inference_config.json'
    ]
    
    @classmethod
    def load_config(cls, config_path: Optional[str] = None) -> InferenceConfig:
        """
        加载配置，按优先级尝试不同来源
        
        Args:
            config_path: 指定的配置文件路径
            
        Returns:
            InferenceConfig实例
        """
        # 1. 如果指定了配置文件路径，直接使用
        if config_path:
            logger.info(f"从指定路径加载配置: {config_path}")
            return InferenceConfig.from_file(config_path)
        
        # 2. 尝试从环境变量加载
        try:
            logger.info("尝试从环境变量加载配置")
            return InferenceConfig.from_env()
        except (ValueError, TypeError) as e:
            logger.debug(f"从环境变量加载配置失败: {e}")
        
        # 3. 尝试从默认配置文件路径加载
        for default_path in cls.DEFAULT_CONFIG_PATHS:
            try:
                expanded_path = Path(default_path).expanduser()
                if expanded_path.exists():
                    logger.info(f"从默认路径加载配置: {expanded_path}")
                    return InferenceConfig.from_file(str(expanded_path))
            except Exception as e:
                logger.debug(f"从 {expanded_path} 加载配置失败: {e}")
        
        # 4. 如果都失败了，抛出异常
        raise RuntimeError(
            "无法加载配置。请通过以下方式之一提供配置：\n"
            "1. 指定配置文件路径\n"
            "2. 设置环境变量 DEEPSEEK_API_KEYS\n"
            "3. 在默认路径创建配置文件: " + ", ".join(cls.DEFAULT_CONFIG_PATHS)
        )
    
    @classmethod
    def create_sample_config(cls, output_path: str = "inference_config.json"):
        """创建示例配置文件"""
        sample_config = InferenceConfig(
            api_keys=["your-api-key-1", "your-api-key-2"],
            base_url="https://api.deepseek.com",
            model_name="deepseek-chat",
            max_workers=8,
            retry_times=3,
            timeout=30.0,
            rate_limit_delay=0.05,
            max_tokens=1024,
            temperature=0.2,
            log_level="INFO",
            log_file="logs/inference.log"
        )
        
        sample_config.save_to_file(output_path)
        
        print(f"示例配置文件已创建: {output_path}")
        print("请编辑配置文件，填入真实的API keys")

def get_default_config() -> InferenceConfig:
    """获取默认配置（用于测试和开发）"""
    return InferenceConfig(
        api_keys=["test-key"],  # 测试用的假key
        max_workers=2,
        retry_times=1,
        timeout=10.0,
        temperature=0.1
    )

# 使用示例
if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="推理配置管理工具")
    parser.add_argument('--create-sample', action='store_true', 
                       help='创建示例配置文件')
    parser.add_argument('--config-path', type=str,
                       help='配置文件路径')
    parser.add_argument('--test-load', action='store_true',
                       help='测试加载配置')
    
    args = parser.parse_args()
    
    if args.create_sample:
        ConfigManager.create_sample_config()
    elif args.test_load:
        try:
            config = ConfigManager.load_config(args.config_path)
            config.setup_logging()
            
            print("配置加载成功:")
            print(f"  API Keys数量: {len(config.api_keys)}")
            print(f"  模型: {config.model_name}")
            print(f"  最大并发: {config.max_workers}")
            print(f"  重试次数: {config.retry_times}")
            print(f"  日志级别: {config.log_level}")
            
        except Exception as e:
            print(f"配置加载失败: {e}")
    else:
        print("请使用 --create-sample 创建示例配置或 --test-load 测试配置加载")
