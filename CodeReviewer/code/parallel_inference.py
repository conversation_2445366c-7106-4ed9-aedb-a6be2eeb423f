import threading
import time
import queue
import logging
import argparse
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional, Callable
from openai import OpenAI
import json
import random
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class InferenceRequest:
    """单个推理请求的数据结构"""
    request_id: str
    messages: List[Dict[str, str]]
    model: str = "deepseek-chat"
    max_tokens: int = 1024
    temperature: float = 0.7
    stream: bool = False
    extra_params: Optional[Dict[str, Any]] = None

@dataclass
class InferenceResponse:
    """推理响应的数据结构"""
    request_id: str
    success: bool
    content: Optional[str] = None
    error: Optional[str] = None
    api_key_index: Optional[int] = None
    response_time: Optional[float] = None

class APIKeyManager:
    """API Key管理器，支持轮询和负载均衡"""

    def __init__(self, api_keys: List[str], base_url: str = "https://api.deepseek.com"):
        self.api_keys = api_keys
        self.base_url = base_url
        self.current_index = 0
        self.lock = threading.Lock()
        self.key_stats = {i: {"requests": 0, "errors": 0, "last_used": 0}
                         for i in range(len(api_keys))}

        # 为每个API key创建OpenAI客户端
        self.clients = []
        for api_key in api_keys:
            client = OpenAI(api_key=api_key, base_url=base_url)
            self.clients.append(client)

    def get_next_client(self) -> tuple[OpenAI, int]:
        """获取下一个可用的客户端（轮询策略）"""
        with self.lock:
            # 简单轮询策略
            client_index = self.current_index
            self.current_index = (self.current_index + 1) % len(self.clients)

            # 更新统计信息
            self.key_stats[client_index]["requests"] += 1
            self.key_stats[client_index]["last_used"] = time.time()

            return self.clients[client_index], client_index

    def get_best_client(self) -> tuple[OpenAI, int]:
        """获取最佳客户端（基于错误率的负载均衡）"""
        with self.lock:
            # 计算每个key的错误率
            best_index = 0
            best_score = float('inf')

            for i, stats in self.key_stats.items():
                if stats["requests"] == 0:
                    # 优先使用未使用过的key
                    best_index = i
                    break

                error_rate = stats["errors"] / stats["requests"]
                # 考虑最近使用时间，避免某个key被过度使用
                time_penalty = max(0, 60 - (time.time() - stats["last_used"]))
                score = error_rate + time_penalty * 0.01

                if score < best_score:
                    best_score = score
                    best_index = i

            # 更新统计信息
            self.key_stats[best_index]["requests"] += 1
            self.key_stats[best_index]["last_used"] = time.time()

            return self.clients[best_index], best_index

    def report_error(self, client_index: int):
        """报告API调用错误"""
        with self.lock:
            self.key_stats[client_index]["errors"] += 1

    def get_stats(self) -> Dict[int, Dict[str, Any]]:
        """获取统计信息"""
        with self.lock:
            return self.key_stats.copy()

class ParallelInferenceEngine:
    """并行推理引擎"""

    def __init__(self,
                 api_keys: List[str],
                 base_url: str = "https://api.deepseek.com",
                 max_workers: int = 10,
                 retry_times: int = 3,
                 retry_delay: float = 1.0,
                 timeout: float = 30.0,
                 rate_limit_delay: float = 0.1):
        """
        初始化并行推理引擎

        Args:
            api_keys: API密钥列表
            base_url: API基础URL
            max_workers: 最大并发线程数
            retry_times: 重试次数
            retry_delay: 重试延迟（秒）
            timeout: 请求超时时间（秒）
            rate_limit_delay: 请求间隔延迟（秒）
        """
        self.api_manager = APIKeyManager(api_keys, base_url)
        self.max_workers = max_workers
        self.retry_times = retry_times
        self.retry_delay = retry_delay
        self.timeout = timeout
        self.rate_limit_delay = rate_limit_delay

        logger.info(f"Initializing parallel inference engine: {len(api_keys)} API keys, {max_workers} workers")

    def _single_inference(self, request: InferenceRequest) -> InferenceResponse:
        """执行单个推理请求"""
        start_time = time.time()

        for attempt in range(self.retry_times + 1):
            try:
                # 获取客户端
                client, client_index = self.api_manager.get_next_client()

                # 准备请求参数
                params = {
                    "model": request.model,
                    "messages": request.messages,
                    "max_tokens": request.max_tokens,
                    "temperature": request.temperature,
                    "stream": request.stream
                }

                # 添加额外参数
                if request.extra_params:
                    params.update(request.extra_params)

                # 发送请求
                response = client.chat.completions.create(**params)

                # 解析响应
                content = response.choices[0].message.content
                response_time = time.time() - start_time

                return InferenceResponse(
                    request_id=request.request_id,
                    success=True,
                    content=content,
                    api_key_index=client_index,
                    response_time=response_time
                )

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"Request {request.request_id} attempt {attempt + 1} failed: {error_msg}")

                # 报告错误
                if 'client_index' in locals():
                    self.api_manager.report_error(client_index)

                # 如果不是最后一次尝试，等待后重试
                if attempt < self.retry_times:
                    time.sleep(self.retry_delay * (2 ** attempt))  # 指数退避
                else:
                    # 最后一次尝试失败，返回错误响应
                    return InferenceResponse(
                        request_id=request.request_id,
                        success=False,
                        error=error_msg,
                        response_time=time.time() - start_time
                    )

        # 理论上不会到达这里
        return InferenceResponse(
            request_id=request.request_id,
            success=False,
            error="Unknown error",
            response_time=time.time() - start_time
        )

    def batch_inference(self,
                       requests: List[InferenceRequest],
                       progress_callback: Optional[Callable[[int, int], None]] = None) -> List[InferenceResponse]:
        """
        批量并行推理

        Args:
            requests: 推理请求列表
            progress_callback: 进度回调函数，接收 (completed, total) 参数

        Returns:
            推理响应列表，顺序与输入请求一致
        """
        logger.info(f"Starting batch inference, {len(requests)} requests total")

        responses = [None] * len(requests)
        completed_count = 0

        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            # 提交所有任务
            future_to_index = {}
            for i, request in enumerate(requests):
                future = executor.submit(self._single_inference, request)
                future_to_index[future] = i

                # 添加速率限制延迟
                if self.rate_limit_delay > 0:
                    time.sleep(self.rate_limit_delay)

            # 收集结果
            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    response = future.result(timeout=self.timeout)
                    responses[index] = response
                    completed_count += 1

                    # 调用进度回调
                    if progress_callback:
                        progress_callback(completed_count, len(requests))

                    logger.debug(f"Completed request {response.request_id} ({completed_count}/{len(requests)})")

                except Exception as e:
                    logger.error(f"Error occurred while processing request: {e}")
                    responses[index] = InferenceResponse(
                        request_id=requests[index].request_id,
                        success=False,
                        error=str(e)
                    )
                    completed_count += 1

                    if progress_callback:
                        progress_callback(completed_count, len(requests))

        logger.info(f"Batch inference complete, successful: {sum(1 for r in responses if r and r.success)}/{len(requests)}")
        return responses

    def get_statistics(self) -> Dict[str, Any]:
        """获取引擎统计信息"""
        api_stats = self.api_manager.get_stats()

        total_requests = sum(stats["requests"] for stats in api_stats.values())
        total_errors = sum(stats["errors"] for stats in api_stats.values())

        return {
            "api_key_count": len(self.api_manager.api_keys),
            "total_requests": total_requests,
            "total_errors": total_errors,
            "error_rate": total_errors / total_requests if total_requests > 0 else 0,
            "api_key_stats": api_stats
        }

def create_code_review_requests(code_diffs: List[str],
                              request_template: Optional[str] = None) -> List[InferenceRequest]:
    """
    为代码审查创建推理请求

    Args:
        code_diffs: 代码差异列表
        request_template: 请求模板，如果为None则使用默认模板

    Returns:
        推理请求列表
    """
    if request_template is None:
        request_template = """请你检查代码审查意见的质量。请检查#代码变更#和#审查意见#，如果审查意见的内容是无意义的语句或闲聊，例如“Good job!”, “Awesome work so far”，则输出“1”。否则输出“0”。

#代码变更#：
{code_diff}
#审查意见#：
{review_comment}

#Response#:
"""

    requests = []
    for i, code_diff in enumerate(code_diffs):
        messages = [
            {
                "role": "user",
                "content": request_template.format(code_diff=code_diff)
            }
        ]

        request = InferenceRequest(
            request_id=f"code_review_{i}",
            messages=messages,
            model="deepseek-chat",
            max_tokens=512,
            temperature=0.3  # 较低的温度以获得更一致的结果
        )
        requests.append(request)

    return requests

@dataclass
class CodeReviewData:
    """代码审查数据结构"""
    idx: str
    old_code: str
    new_code: str
    diff: str
    file_path: Optional[str] = None
    commit_msg: Optional[str] = None
    msg: Optional[str] = None  # 添加msg字段用于存储review comment
    original_diff: Optional[str] = None  # 保存原始diff

    def __post_init__(self):
        """数据预处理，基于CodeReviewer项目的思路"""
        # 保存原始diff
        self.original_diff = self.diff

        # 进行预处理
        self.old_code = self.clean_code_content(self.old_code)
        self.new_code = self.clean_code_content(self.new_code)
        self.diff = self.clean_diff_content(self.diff)
        self.msg = self.clean_text_content(self.msg) if self.msg else ""
        self.commit_msg = self.clean_text_content(self.commit_msg) if self.commit_msg else ""

    @staticmethod
    def clean_code_content(content: str) -> str:
        """清理代码内容，完全按照CodeReviewer的预处理逻辑"""
        if not content:
            return ""

        # 按行处理，应用remove_space_clean（类似第749-750行逻辑）
        lines = content.split('\n')
        cleaned_lines = [CodeReviewData._remove_space_clean(line) for line in lines]

        # 过滤空行（类似第753-754行逻辑）
        cleaned_lines = [line for line in cleaned_lines if len(line) > 0]

        return '\n'.join(cleaned_lines)

    @staticmethod
    def clean_diff_content(diff: str) -> str:
        """
        完全照搬CodeReviewer项目的diff预处理逻辑
        基于utils.py第577-598行的MsgDataset.convert_examples_to_features方法
        和第721-778行的align_and_clean方法的组合逻辑
        """
        if not diff:
            return ""

        import re

        # 第580行：移除@@头部行（完全照搬）
        difflines = diff.split("\n")[1:]  # remove start @@

        # 第581行：移除空行（完全照搬）
        difflines = [line for line in difflines if len(line.strip()) > 0]

        # 移除"\ No newline at end of file"行（基于align_and_clean第726行逻辑）
        difflines = [line for line in difflines if line != r"\ No newline at end of file"]

        # 第582-587行：标签映射（完全照搬）
        map_dic = {"-": 0, "+": 1, " ": 2}
        def f(s):
            if s in map_dic:
                return map_dic[s]
            else:
                return 2

        # 第588行：提取标签（完全照搬）
        labels = [f(line[0]) for line in difflines]

        # 第589行：去掉+/-符号并清理（完全照搬）
        difflines = [line[1:].strip() for line in difflines]

        # 第590-597行：用<add>、<del>、<keep>标记替代+/-符号（完全照搬）
        inputstr = ""
        for label, line in zip(labels, difflines):
            if label == 1:  # + 添加行
                inputstr += "<add>" + line
            elif label == 0:  # - 删除行
                inputstr += "<del>" + line
            else:  # 空格 上下文行
                inputstr += "<keep>" + line

        return inputstr

    @staticmethod
    def _remove_space_clean(line: str) -> str:
        """基于CodeReviewer项目的remove_space_clean方法"""
        if not line:
            return ""

        # 移除行首行尾的空格、制表符、回车符
        rep = " \t\r"
        totallen = len(line)
        i = 0
        while i < totallen and line[i] in rep:
            i += 1
        j = totallen - 1
        while j >= 0 and line[j] in rep:
            j -= 1

        if i <= j:
            return line[i:j+1]
        else:
            return ""

    @staticmethod
    def clean_text_content(content: str) -> str:
        """清理文本内容，完全按照CodeReviewer的预处理逻辑"""
        if not content:
            return ""

        # 应用remove_space_clean（类似第752行逻辑）
        content = CodeReviewData._remove_space_clean(content)

        return content

    def is_valid_for_processing(self) -> bool:
        """检查数据是否适合处理，基于CodeReviewer的数据质量标准"""
        # 检查必要字段是否存在
        if not self.idx or not self.diff:
            return False

        # 检查diff是否包含实际的代码变更
        if not self._has_meaningful_changes():
            return False

        return True

    def _has_meaningful_changes(self) -> bool:
        """检查diff是否包含有意义的代码变更"""
        if not self.diff:
            return False

        lines = self.diff.split('\n')
        has_additions = any(line.startswith('+') and len(line.strip()) > 1 for line in lines)
        has_deletions = any(line.startswith('-') and len(line.strip()) > 1 for line in lines)

        # 至少要有添加或删除的内容
        return has_additions or has_deletions

    def get_change_summary(self) -> dict:
        """获取变更摘要信息，用于统计和分析"""
        if not self.diff:
            return {"additions": 0, "deletions": 0, "context": 0}

        lines = self.diff.split('\n')
        additions = sum(1 for line in lines if line.startswith('+') and len(line.strip()) > 1)
        deletions = sum(1 for line in lines if line.startswith('-') and len(line.strip()) > 1)
        context = sum(1 for line in lines if line.startswith(' ') and len(line.strip()) > 0)

        return {
            "additions": additions,
            "deletions": deletions,
            "context": context,
            "total_changes": additions + deletions
        }

@dataclass
class CodeReviewResult:
    """代码审查结果数据结构"""
    idx: str
    old_code: str
    new_code: str
    diff: str
    generated_comment: str
    success: bool
    error: Optional[str] = None
    response_time: Optional[float] = None
    api_key_index: Optional[int] = None
    file_path: Optional[str] = None
    commit_msg: Optional[str] = None
    original_diff: Optional[str] = None  # 保存原始diff
    msg: Optional[str] = None  # 保存原始msg
    meaningless: Optional[int] = None  # 从generated_comment中提取的meaningless值

class CodeReviewCommentGenerator:
    """代码审查评论生成器"""

    def __init__(self,
                 api_keys: List[str],
                 base_url: str = "https://api.deepseek.com",
                 max_workers: int = 8,
                 model_name: str = "deepseek-chat"):
        """
        初始化代码审查评论生成器

        Args:
            api_keys: API密钥列表
            base_url: API基础URL
            max_workers: 最大并发数
            model_name: 使用的模型名称
        """
        self.engine = ParallelInferenceEngine(
            api_keys=api_keys,
            base_url=base_url,
            max_workers=max_workers,
            retry_times=3,
            rate_limit_delay=0.05
        )
        self.model_name = model_name

        # 定义不同类型的prompt模板
        self.prompt_templates = {
            "comment_generation": {
                "english": """You are an expert code reviewer. Please analyze the following code changes and provide a constructive review comment.

# Code Changes:
```
{diff}
```

# Instructions:
- Focus on code quality, potential bugs, security issues, and best practices
- Provide specific and actionable feedback
- Be constructive and professional
- If the code looks good, acknowledge it briefly and suggest minor improvements if any
- Keep the comment concise but informative

# Review Comment:""",

                # 原中文prompt（注释掉）
                # "chinese": """请对以下代码变更进行审查，提供建设性的审查意见。
                #
                # 代码变更：
                # ```
                # {diff}
                # ```
                #
                # 要求：
                # - 关注代码质量、潜在bug、安全问题和最佳实践
                # - 提供具体和可操作的反馈
                # - 保持建设性和专业性
                # - 如果代码看起来不错，简要确认并建议小的改进（如果有的话）
                # - 保持评论简洁但信息丰富
                #
                # 审查意见："""
            },

            "quality_check": {
                "english": """Please check the quality of the code review comment. Review the #Code Changes# and #Review Comment#. If the review comment contains meaningless statements or casual chat like "Good job!", "Awesome work so far", output "1". Otherwise output "0".

# Code Changes:
{code_diff}

# Review Comment:
{review_comment}

# Response:""",

                # 原中文prompt（注释掉）
                # "chinese": """请你检查代码审查意见的质量。请检查#代码变更#和#审查意见#，如果审查意见的内容是无意义的语句或闲聊，例如"Good job!", "Awesome work so far"，则输出"1"。否则输出"0"。
                #
                # #代码变更#：
                # {code_diff}
                # #审查意见#：
                # {review_comment}
                #
                # #Response#:"""
            },

            "negative_generation": {
                "english": """Below is a code change and its ground truth review comment. Your job is to generate a negative review comment based on the original review comment. 

Your negative review comment include at least one of the following types of problems:
1. Unclear or ambiguous feedback: If the original review comment is clear and specific, your negative review comment should be unclear or ambiguous. For example: if the original review comment is "Change the h to height", your negative review comment can be "h need to be changed".
2. Meaningless statements: If the original review comment contains meaningful feedback, your negative review comment should contain meaningless statements. For example: if the original review comment is "Change the h to height", your negative review comment can be "Good job!".
3. Opposite feedback: If the original review comment contains suggestion, your negative review comment should contain opposite feedback. For example: if the original review comment is "The change from a to b will cause lots of error", your negative review comment can be "b is more applicable".
4. Other types of problems: Your negative review comment can be a totally different review comment, which is not related to the original review comment.

Do not explain the problems in the review comment.
Do not mention this is an incorrect review comment. Just pretend you are writing the comment and still not recognizing the problems. 

# Code Changes:
{code_diff}

# Ground truth review comment:
{review_comment}

# Response:
""",
            }
        }

    def generate_review_comments(self,
                               code_review_data: List[CodeReviewData],
                               prompt_type: str = "quality_check",
                               language: str = "english",
                               start_idx: Optional[int] = None,
                               end_idx: Optional[int] = None,
                               only_processed: bool = False,
                               progress_callback: Optional[Callable[[int, int], None]] = None) -> List[CodeReviewResult]:
        """
        生成代码审查评论

        Args:
            code_review_data: 代码审查数据列表
            prompt_type: prompt类型 ('comment_generation', 'quality_check', 'detailed_analysis')
            language: 语言 ('english', 'chinese')
            start_idx: 开始索引（包含），None表示从头开始
            end_idx: 结束索引（不包含），None表示到末尾
            only_processed: 是否只返回处理范围内的数据，False返回所有数据（包括占位符）
            progress_callback: 进度回调函数

        Returns:
            代码审查结果列表，根据only_processed参数决定是否包含占位数据
        """
        if prompt_type not in self.prompt_templates:
            raise ValueError(f"Unsupported prompt type: {prompt_type}")

        if language not in self.prompt_templates[prompt_type]:
            raise ValueError(f"Unsupported language: {language} for prompt type: {prompt_type}")

        template = self.prompt_templates[prompt_type][language]

        # 应用范围过滤
        original_data = code_review_data
        if start_idx is not None or end_idx is not None:
            start = start_idx if start_idx is not None else 0
            end = end_idx if end_idx is not None else len(code_review_data)

            # 确保索引有效
            start = max(0, min(start, len(code_review_data)))
            end = max(start, min(end, len(code_review_data)))

            filtered_data = code_review_data[start:end]
            logger.info(f"Processing range: [{start}:{end}], total {len(filtered_data)} samples")
        else:
            filtered_data = code_review_data
            start = 0

        # 创建推理请求
        requests = []
        data_index_map = {}  # 用于保持原始顺序

        for i, data in enumerate(filtered_data):
            original_index = start + i  # 计算在原始数据中的索引
            data_index_map[i] = original_index  # 记录映射关系

            # 根据prompt类型格式化内容
            if prompt_type == "quality_check":
                # 质量检查需要额外的review_comment参数
                content = template.format(
                    code_diff=data.diff,
                    review_comment=data.msg or ''  # 直接使用msg字段
                )
            else:
                # 其他类型使用diff和file_path
                content = template.format(
                    diff=data.diff,
                    file_path=data.file_path or 'unknown'
                )

            messages = [{"role": "user", "content": content}]

            request = InferenceRequest(
                request_id=f"review_{data.idx}_{original_index}",  # 包含原始索引
                messages=messages,
                model=self.model_name,
                max_tokens=800 if prompt_type == "detailed_analysis" else 400,
                temperature=0.2  # 低温度确保一致性
            )
            requests.append(request)

        # 执行批量推理
        logger.info(f"Starting code review comment generation, {len(requests)} requests, type: {prompt_type}")

        responses = self.engine.batch_inference(requests, progress_callback)

        # 处理结果，保持原始顺序
        if start_idx is not None or end_idx is not None:
            # 如果指定了范围，创建完整的结果列表，未处理的部分为None
            full_results = [None] * len(original_data)

            for i, (data, response) in enumerate(zip(filtered_data, responses)):
                original_index = data_index_map[i]
                generated_comment = response.content if response.success else ""
                result = CodeReviewResult(
                    idx=data.idx,
                    old_code=data.old_code,
                    new_code=data.new_code,
                    diff=data.diff,
                    file_path=data.file_path,
                    commit_msg=data.commit_msg,
                    generated_comment=generated_comment,
                    success=response.success,
                    error=response.error if not response.success else None,
                    response_time=response.response_time,
                    api_key_index=response.api_key_index,
                    original_diff=data.original_diff,
                    msg=data.msg,
                    meaningless=extract_meaningless_value(generated_comment)
                )
                full_results[original_index] = result

            # 为未处理的数据创建占位结果
            for i, data in enumerate(original_data):
                if full_results[i] is None:
                    full_results[i] = CodeReviewResult(
                        idx=data.idx,
                        old_code=data.old_code,
                        new_code=data.new_code,
                        diff=data.diff,
                        file_path=data.file_path,
                        commit_msg=data.commit_msg,
                        generated_comment="",  # 未处理
                        success=False,
                        error="Not processed (outside specified range)",
                        response_time=None,
                        api_key_index=None,
                        original_diff=data.original_diff,
                        msg=data.msg,
                        meaningless=1  # 未处理的设置为1
                    )

            # 根据only_processed参数决定返回什么
            if only_processed:
                # 只返回处理范围内的数据
                results = [r for r in full_results if r.error != "Not processed (outside specified range)"]
            else:
                # 返回所有数据（包括占位符）
                results = full_results
        else:
            # 处理全部数据，直接按顺序创建结果
            results = []
            for data, response in zip(filtered_data, responses):
                generated_comment = response.content if response.success else ""
                result = CodeReviewResult(
                    idx=data.idx,
                    old_code=data.old_code,
                    new_code=data.new_code,
                    diff=data.diff,
                    file_path=data.file_path,
                    commit_msg=data.commit_msg,
                    generated_comment=generated_comment,
                    success=response.success,
                    error=response.error if not response.success else None,
                    response_time=response.response_time,
                    api_key_index=response.api_key_index,
                    original_diff=data.original_diff,
                    msg=data.msg,
                    meaningless=extract_meaningless_value(generated_comment)
                )
                results.append(result)

        # 记录统计信息
        processed_count = len(filtered_data)
        success_count = sum(1 for r in results if r.success and r.error != "Not processed (outside specified range)")

        if start_idx is not None or end_idx is not None:
            logger.info(f"Code review comment generation complete: {success_count}/{processed_count} successful (within range), total data: {len(results)}")
        else:
            logger.info(f"Code review comment generation complete: {success_count}/{len(results)} successful")

        return results

    def get_engine_stats(self) -> Dict[str, Any]:
        """获取推理引擎统计信息"""
        return self.engine.get_statistics()

def generate_output_filename(base_filename: str,
                           start_idx: Optional[int],
                           end_idx: Optional[int],
                           total_data_count: int) -> str:
    """
    生成带有处理范围信息的输出文件名

    Args:
        base_filename: 基础文件名
        start_idx: 开始索引
        end_idx: 结束索引
        total_data_count: 总数据量

    Returns:
        str: 带有范围信息的文件名
    """
    import os

    # 分离文件名和扩展名
    name, ext = os.path.splitext(base_filename)

    # 计算实际的处理范围
    actual_start = start_idx if start_idx is not None else 0
    actual_end = end_idx if end_idx is not None else total_data_count

    # 确保范围有效
    actual_start = max(0, min(actual_start, total_data_count))
    actual_end = max(actual_start, min(actual_end, total_data_count))

    # 生成范围字符串
    if start_idx is None and end_idx is None:
        # 处理全部数据
        range_str = f"_all_{total_data_count}"
    else:
        # 处理部分数据
        range_str = f"_{actual_start}-{actual_end-1}"  # end_idx是exclusive的，所以减1显示

    # 组合新文件名
    new_filename = f"{name}{range_str}{ext}"

    return new_filename

def extract_meaningless_value(generated_comment: str) -> int:
    """
    从生成的评论中提取meaningless值

    Args:
        generated_comment: 生成的评论内容

    Returns:
        int: meaningless值 (0或1)

    规则:
    - 如果generated_comment中只有0或1，则meaningless为该值
    - 否则从字符串后面往前，第一个出现的0或1作为meaningless的值
    - 如果不存在0或1则meaningless设置为1
    """
    if not generated_comment:
        return -1

    # 去除首尾空白字符
    comment = generated_comment.strip()

    # 如果整个字符串只有0或1
    if comment == "0":
        return 0
    elif comment == "1":
        return 1

    # 从后往前查找第一个0或1
    for i in range(len(comment) - 1, -1, -1):
        if comment[i] == '0':
            return 0
        elif comment[i] == '1':
            return 1

    # 如果没有找到0或1，返回1
    return -1

def create_sample_data():
    """创建示例代码审查数据"""
    sample_data = [
        {
            "idx": "0",
            "oldf": "def process_data(data):\n    return data.upper()",
            "newf": "def process_data(data):\n    if not isinstance(data, str):\n        raise TypeError('Expected string input')\n    return data.upper()",
            "patch": "- def process_data(data):\n-     return data.upper()\n+ def process_data(data):\n+     if not isinstance(data, str):\n+         raise TypeError('Expected string input')\n+     return data.upper()",
            "file_path": "src/utils.py",
            "msg": "Good improvement! Adding type checking prevents runtime errors."
        },
        {
            "idx": "1",
            "oldf": "password = request.form['password']",
            "newf": "password = request.form.get('password', '')\nif not password:\n    return {'error': 'Password required'}, 400",
            "patch": "- password = request.form['password']\n+ password = request.form.get('password', '')\n+ if not password:\n+     return {'error': 'Password required'}, 400",
            "file_path": "src/auth.py",
            "msg": "Great job!"
        },
        {
            "idx": "2",
            "oldf": "query = f\"SELECT * FROM users WHERE id = {user_id}\"",
            "newf": "query = \"SELECT * FROM users WHERE id = %s\"",
            "patch": "- query = f\"SELECT * FROM users WHERE id = {user_id}\"\n+ query = \"SELECT * FROM users WHERE id = %s\"",
            "file_path": "src/database.py",
            "msg": "This change fixes a potential SQL injection vulnerability by using parameterized queries."
        },
        {
            "idx": "3",
            "oldf": "for i in range(len(items)):",
            "newf": "for i, item in enumerate(items):",
            "patch": "- for i in range(len(items)):\n+ for i, item in enumerate(items):",
            "file_path": "src/loops.py",
            "msg": "Nice optimization! Using enumerate is more Pythonic and efficient."
        },
        {
            "idx": "4",
            "oldf": "if x == True:",
            "newf": "if x:",
            "patch": "- if x == True:\n+ if x:",
            "file_path": "src/conditions.py",
            "msg": "Awesome work so far!"
        }
    ]
    return sample_data

def test_filename_generation():
    """测试文件名生成功能"""
    print("=== Testing Filename Generation ===")

    test_cases = [
        # 测试用例: (base_filename, start_idx, end_idx, total_count, expected_pattern, description)
        ("output.jsonl", None, None, 100, "output_all_100.jsonl", "处理全部数据"),
        ("output.jsonl", 0, 50, 100, "output_0-49.jsonl", "处理前50条"),
        ("output.jsonl", 10, 20, 100, "output_10-19.jsonl", "处理10-19范围"),
        ("output.jsonl", 50, None, 100, "output_50-99.jsonl", "从50开始到结尾"),
        ("output.jsonl", None, 30, 100, "output_0-29.jsonl", "从开始到30"),
        ("results.json", 5, 15, 50, "results_5-14.json", "不同扩展名"),
        ("data", 0, 10, 20, "data_0-9", "无扩展名"),
        ("output.jsonl", 0, 1, 100, "output_0-0.jsonl", "只处理一条数据"),
        ("output.jsonl", 99, 100, 100, "output_99-99.jsonl", "处理最后一条"),
        ("path/to/output.jsonl", 10, 20, 100, "path/to/output_10-19.jsonl", "带路径的文件名"),
    ]

    passed = 0
    failed = 0

    for i, (base_filename, start_idx, end_idx, total_count, expected, description) in enumerate(test_cases):
        result = generate_output_filename(base_filename, start_idx, end_idx, total_count)
        if result == expected:
            print(f"  ✓ Test {i+1}: {description}")
            print(f"    Input: {base_filename}, range: [{start_idx}:{end_idx}], total: {total_count}")
            print(f"    Output: {result}")
            passed += 1
        else:
            print(f"  ✗ Test {i+1}: {description}")
            print(f"    Input: {base_filename}, range: [{start_idx}:{end_idx}], total: {total_count}")
            print(f"    Expected: {expected}")
            print(f"    Got: {result}")
            failed += 1
        print()

    print(f"Test Results: {passed} passed, {failed} failed")
    print("Filename generation testing complete\n")

def test_meaningless_extraction():
    """测试meaningless值提取功能"""
    print("=== Testing Meaningless Value Extraction ===")

    test_cases = [
        # 测试用例: (输入, 期望输出, 描述)
        ("0", 0, "只有0"),
        ("1", 1, "只有1"),
        ("", 1, "空字符串"),
        ("   0   ", 0, "只有0带空格"),
        ("   1   ", 1, "只有1带空格"),
        ("This is a good review. 0", 0, "以0结尾"),
        ("This is a good review. 1", 1, "以1结尾"),
        ("The code looks good. Rating: 0", 0, "包含0"),
        ("The code looks good. Rating: 1", 1, "包含1"),
        ("Good job! 1 out of 10. Final: 0", 0, "多个数字，最后一个是0"),
        ("Good job! 0 out of 10. Final: 1", 1, "多个数字，最后一个是1"),
        ("This is a great improvement!", 1, "不包含0或1"),
        ("Nice work! Keep it up.", 1, "不包含0或1"),
        ("Rating: 2", 1, "包含其他数字但不包含0或1"),
        ("0 is good, but 1 is better", 1, "多个数字，从后往前第一个是1"),
        ("1 is good, but 0 is better", 0, "多个数字，从后往前第一个是0"),
        ("   ", 1, "只有空格"),
        ("Good work! 10", 1, "包含10但不包含单独的0或1"),
        ("Score: 10/10", 1, "包含10但不包含单独的0或1"),
        ("Result: 01", 1, "包含01，从后往前第一个是1"),
        ("Result: 10", 0, "包含10，从后往前第一个是0"),
    ]

    passed = 0
    failed = 0

    for i, (input_text, expected, description) in enumerate(test_cases):
        result = extract_meaningless_value(input_text)
        if result == expected:
            print(f"  ✓ Test {i+1}: {description} - Input: {repr(input_text)} -> {result}")
            passed += 1
        else:
            print(f"  ✗ Test {i+1}: {description} - Input: {repr(input_text)} -> Expected: {expected}, Got: {result}")
            failed += 1

    print(f"\nTest Results: {passed} passed, {failed} failed")
    print("Meaningless value extraction testing complete\n")

def test_range_functionality():
    """测试范围功能"""
    print("=== Testing Range Functionality ===")

    # Create test data
    sample_data_list = create_sample_data()
    code_review_data = []
    for data in sample_data_list:
        code_review_data.append(CodeReviewData(
            idx=data["idx"],
            old_code=data.get("oldf", "") or data.get("old_code", ""),  # 支持两种字段名
            new_code=data.get("newf", "") or data.get("new_code", ""),  # 支持两种字段名
            diff=data.get("patch", "") or data.get("diff", ""),         # 支持两种字段名
            file_path=data.get("file_path", ""),
            msg=data.get("msg", "")
        ))

    print(f"Created {len(code_review_data)} test samples")

    # Create test generator (using fake API keys)
    generator = CodeReviewCommentGenerator(
        api_keys=["test-key"],
        max_workers=1
    )

    # Test different range settings
    test_cases = [
        {"start_idx": 1, "end_idx": 3, "desc": "Process indices 1-2"},
        {"start_idx": 0, "end_idx": 2, "desc": "Process indices 0-1"},
        {"start_idx": 3, "end_idx": None, "desc": "Process from index 3 to end"},
        {"start_idx": None, "end_idx": 2, "desc": "Process from start to index 1"},
    ]

    for case in test_cases:
        print(f"\n--- Test: {case['desc']} ---")
        try:
            # Only test data structure, don't actually call API
            results = generator.generate_review_comments(
                code_review_data,
                prompt_type='comment_generation',
                language='english',
                start_idx=case['start_idx'],
                end_idx=case['end_idx']
            )

            print(f"Result count: {len(results)}")
            processed_count = sum(1 for r in results if r.error != "Not processed (outside specified range)")
            not_processed_count = len(results) - processed_count
            print(f"Processed samples: {processed_count}")
            print(f"Unprocessed samples: {not_processed_count}")

            # Show status of each result
            for i, result in enumerate(results):
                status = "✓ Processed" if result.error != "Not processed (outside specified range)" else "- Not processed"
                print(f"  Index {i}: {status} (idx={result.idx})")

        except Exception as e:
            print(f"Test failed: {e}")

    print("\nRange functionality testing complete")

def test_preprocessing_functionality():
    """测试数据预处理功能"""
    print("=== Testing Data Preprocessing Functionality ===")

    # 测试diff清理功能，特别是@@头部行的移除
    test_cases = [
        {
            "name": "CodeReviewer style diff processing",
            "input_diff": "@@ -1,3 +1,3 @@\n def function():\n-    old_code\n+    new_code\n\\ No newline at end of file",
            "expected_contains": [" def function():", "old_code", "new_code"],  # 注意空格前缀保留
            "expected_not_contains": ["@@ -1,3 +1,3 @@", "\\ No newline at end of file", "-    old_code", "+    new_code"]
        },
        {
            "name": "Clean code content (CodeReviewer style)",
            "input_code": "def function():\t\r\n\t    return    True\t\r\n   \t\r\n\t\r\n",
            "expected_code": "def function():\nreturn    True"
        },
        {
            "name": "Clean text content (CodeReviewer style)",
            "input_text": "  Good   change!  \t\r\n  Keep    it   up.  \t\r\n   \t\r\n",
            "expected_text": "Good   change!\nKeep    it   up."
        },
        {
            "name": "Remove space clean method test",
            "input_line": "   \t  some content  \t\r  ",
            "expected_line": "some content"
        }
        ,
        {
            "name": "Space normalization test",
            "input_text": "def    function():\n\t\treturn     True\n\n   \t",
            "expected_normalized": "def function(): return True"
        }
    ]

    for case in test_cases:
        print(f"\n--- Test: {case['name']} ---")

        if "input_diff" in case:
            # 测试diff清理
            cleaned_diff = CodeReviewData.clean_diff_content(case["input_diff"])
            print(f"Input diff: {repr(case['input_diff'])}")
            print(f"Cleaned diff: {repr(cleaned_diff)}")

            # 检查应该包含的内容
            for expected in case["expected_contains"]:
                if expected in cleaned_diff:
                    print(f"  ✓ Contains: {repr(expected)}")
                else:
                    print(f"  ✗ Missing: {repr(expected)}")

            # 检查不应该包含的内容
            for not_expected in case["expected_not_contains"]:
                if not_expected not in cleaned_diff:
                    print(f"  ✓ Removed: {repr(not_expected)}")
                else:
                    print(f"  ✗ Still contains: {repr(not_expected)}")

        elif "input_code" in case:
            # 测试代码清理
            cleaned_code = CodeReviewData.clean_code_content(case["input_code"])
            print(f"Input code: {repr(case['input_code'])}")
            print(f"Cleaned code: {repr(cleaned_code)}")
            print(f"Expected: {repr(case['expected_code'])}")
            if cleaned_code == case["expected_code"]:
                print("  ✓ Code cleaning successful")
            else:
                print("  ✗ Code cleaning failed")

        elif "input_text" in case:
            # 测试文本清理
            cleaned_text = CodeReviewData.clean_text_content(case["input_text"])
            print(f"Input text: {repr(case['input_text'])}")
            print(f"Cleaned text: {repr(cleaned_text)}")
            print(f"Expected: {repr(case['expected_text'])}")
            if cleaned_text == case["expected_text"]:
                print("  ✓ Text cleaning successful")
            else:
                print("  ✗ Text cleaning failed")

        elif "input_line" in case:
            # 测试remove_space_clean方法
            cleaned_line = CodeReviewData._remove_space_clean(case["input_line"])
            print(f"Input line: {repr(case['input_line'])}")
            print(f"Cleaned line: {repr(cleaned_line)}")
            print(f"Expected: {repr(case['expected_line'])}")
            if cleaned_line == case["expected_line"]:
                print("  ✓ Remove space clean successful")
            else:
                print("  ✗ Remove space clean failed")

        elif "expected_normalized" in case:
            # 测试空格标准化
            normalized_text = " ".join(case["input_text"].split())
            print(f"Input text: {repr(case['input_text'])}")
            print(f"Normalized text: {repr(normalized_text)}")
            print(f"Expected: {repr(case['expected_normalized'])}")
            if normalized_text == case["expected_normalized"]:
                print("  ✓ Space normalization successful")
            else:
                print("  ✗ Space normalization failed")

    # 测试完整的数据预处理
    print(f"\n--- Test: Complete Data Preprocessing ---")
    test_data = {
        "idx": "test",
        "oldf": "def function():\t\r\n\t    return old_value\t\r\n   \t\r\n",
        "newf": "def function():\t\r\n\t    return new_value\t\r\n   \t\r\n",
        "patch": "@@ -1,2 +1,2 @@\t\r\n def function():\t\r\n-    return old_value\t\r\n+    return new_value\t\r\n\\ No newline at end of file\n   \t\r\n",
        "msg": "  Good change!  \t\r\n  This is better.  \t\r\n   \t\r\n"
    }

    # 创建CodeReviewData对象（会自动进行预处理）
    review_data = CodeReviewData(
        idx=test_data["idx"],
        old_code=test_data["oldf"],
        new_code=test_data["newf"],
        diff=test_data["patch"],
        msg=test_data["msg"]
    )

    print(f"Original diff: {repr(test_data['patch'])}")
    print(f"Saved original diff: {repr(review_data.original_diff)}")
    print(f"Processed diff: {repr(review_data.diff)}")
    print(f"Original msg: {repr(test_data['msg'])}")
    print(f"Processed msg: {repr(review_data.msg)}")

    # 验证原始diff是否正确保存
    if review_data.original_diff == test_data['patch']:
        print("  ✓ Original diff saved correctly")
    else:
        print("  ✗ Original diff not saved correctly")

    # 检查CodeReviewer预处理效果
    checks = [
        ("@@ -1,2 +1,2 @@", "@@ header line"),
        ("\\ No newline at end of file", "No newline warning"),
    ]

    for check_content, check_name in checks:
        if check_content not in review_data.diff:
            print(f"  ✓ {check_name} removed successfully")
        else:
            print(f"  ✗ {check_name} still present")

    # 检查是否正确处理了diff内容
    # 应该去掉-/+前缀，但保留空格前缀的上下文行
    if "-    return old_value" not in review_data.diff and "+    return new_value" not in review_data.diff:
        print("  ✓ -/+ prefixes removed successfully")
    else:
        print("  ✗ -/+ prefixes still present")

    if "return old_value" in review_data.diff and "return new_value" in review_data.diff:
        print("  ✓ Diff content preserved correctly")
    else:
        print("  ✗ Diff content not preserved correctly")

    # 检查数据有效性
    if review_data.is_valid_for_processing():
        print("  ✓ Data is valid for processing")
        summary = review_data.get_change_summary()
        print(f"  Change summary: {summary}")
    else:
        print("  ✗ Data is not valid for processing")

    print("\nData preprocessing functionality testing complete")

def main():
    """主函数 - 演示代码审查评论生成功能"""

    # 硬编码的API keys
    API_KEYS = [
        "sk-eb93cdc660424a3da557bc4c0f0cf608",  # 替换为实际的API key
        "sk-4fb414ba14b6476b9bdb584a4b4270b6",  # 替换为实际的API key
        "sk-e5e4b80d36ac4c9bb7b65ebb8a22b175",  # 替换为实际的API key
        "sk-f1e89e927afc4d0187da85228e394743",  # 替换为实际的API key
        "sk-f8b1b5c9bfa84bdc87bfb932aaa39658",  # 替换为实际的API key
        "sk-5954a951841e47188ed9a3aee266783e",  # 替换为实际的API key
        "sk-75198ad5e8a0497995f680b7eff36178",  # 替换为实际的API key
        "sk-1b7b6f862cc648f6bbbbeb9306d1a24c",  # 替换为实际的API key
    ]

    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )

    parser = argparse.ArgumentParser(description="Parallel Inference System - Code Review Comment Generation")
    parser.add_argument('--input-file', type=str, required=True,
                       help='Input JSONL file path')
    parser.add_argument('--output-file', type=str, default='generated_comments.jsonl',
                       help='Output JSONL file path')
    parser.add_argument('--prompt-type', type=str, default='quality_check',
                       choices=['comment_generation', 'quality_check', 'detailed_analysis', 'simple_generation'],
                       help='Prompt type')
    parser.add_argument('--max-workers', type=int, default=8,
                       help='Maximum number of concurrent threads')
    parser.add_argument('--max-samples', type=int, default=-1,
                       help='Maximum number of samples to process, -1 for all')
    parser.add_argument('--start-idx', type=int,
                       help='Start index (inclusive), starting from 0')
    parser.add_argument('--end-idx', type=int,
                       help='End index (exclusive)')
    parser.add_argument('--range', type=str,
                       help='Processing range, format: start:end or start: or :end')
    parser.add_argument('--test-range', action='store_true',
                       help='Test range functionality (does not require real API keys)')
    parser.add_argument('--test-preprocessing', action='store_true',
                       help='Test data preprocessing functionality (does not require real API keys)')
    parser.add_argument('--test-meaningless', action='store_true',
                       help='Test meaningless value extraction functionality (does not require real API keys)')
    parser.add_argument('--test-filename', action='store_true',
                       help='Test filename generation functionality (does not require real API keys)')
    parser.add_argument('--skip-preprocessing', action='store_true',
                       help='Skip data preprocessing and quality filtering')
    parser.add_argument('--show-preprocessing-stats', action='store_true',
                       help='Show detailed preprocessing statistics')

    args = parser.parse_args()

    # 解析范围参数
    start_idx = args.start_idx
    end_idx = args.end_idx

    if args.range:
        try:
            if ':' in args.range:
                range_parts = args.range.split(':')
                if len(range_parts) == 2:
                    start_str, end_str = range_parts
                    if start_str.strip():
                        start_idx = int(start_str.strip())
                    if end_str.strip():
                        end_idx = int(end_str.strip())
                else:
                    print("Error: Range format should be start:end")
                    sys.exit(1)
            else:
                print("Error: Range format should be start:end")
                sys.exit(1)
        except ValueError:
            print("Error: Range indices must be integers")
            sys.exit(1)

    # Special mode: test range functionality
    if args.test_range:
        test_range_functionality()
        return

    # Special mode: test preprocessing functionality
    if args.test_preprocessing:
        test_preprocessing_functionality()
        return

    # Special mode: test meaningless extraction functionality
    if args.test_meaningless:
        test_meaningless_extraction()
        return

    # Special mode: test filename generation functionality
    if args.test_filename:
        test_filename_generation()
        return

    # Check input file
    if not args.input_file:
        print("Error: Input file is required")
        print("Usage examples:")
        print("  python parallel_inference.py --input-file data.jsonl")
        print("  python parallel_inference.py --input-file data.jsonl --range 10:20")
        print("  python parallel_inference.py --input-file data.jsonl --start-idx 5 --end-idx 15")
        print("  python parallel_inference.py --input-file data.jsonl --range 10: (from index 10 to end)")
        print("  python parallel_inference.py --input-file data.jsonl --range :20 (from start to index 20)")
        print("  python parallel_inference.py --test-range (test range functionality)")
        print("  python parallel_inference.py --test-preprocessing (test data preprocessing)")
        print("  python parallel_inference.py --test-meaningless (test meaningless value extraction)")
        print("  python parallel_inference.py --test-filename (test filename generation)")
        sys.exit(1)
    # Load data from input file
    if args.skip_preprocessing:
        print(f"Loading data from file (skipping preprocessing): {args.input_file}")
    else:
        print(f"Loading and preprocessing data from file: {args.input_file}")

    try:
        raw_data = []
        code_review_data = []
        invalid_json_count = 0
        invalid_data_count = 0

        with open(args.input_file, 'r', encoding='utf-8') as f:
            for i, line in enumerate(f):
                if args.max_samples > 0 and i >= args.max_samples:
                    break

                try:
                    data = json.loads(line.strip())
                    raw_data.append(data)
                except json.JSONDecodeError as e:
                    logger.warning(f"Skipping invalid JSON line {i+1}: {e}")
                    invalid_json_count += 1
                    continue

        # Process data
        print(f"Processing {len(raw_data)} raw data entries...")

        for i, data in enumerate(raw_data):
            try:
                if args.skip_preprocessing:
                    # Simple data loading without preprocessing
                    review_data = CodeReviewData.__new__(CodeReviewData)  # Skip __post_init__
                    review_data.idx = data.get('idx', str(i))
                    review_data.old_code = data.get('oldf', '') or data.get('old_code', '')
                    review_data.new_code = data.get('newf', '') or data.get('new_code', '')
                    review_data.diff = data.get('patch', '') or data.get('diff', '')
                    review_data.file_path = data.get('file_path', '')
                    review_data.msg = data.get('msg', '') or data.get('comment', '')
                    review_data.commit_msg = data.get('commit_msg', '')
                    review_data.original_diff = review_data.diff  # 保存原始diff
                    code_review_data.append(review_data)
                else:
                    # Create CodeReviewData object (preprocessing happens in __post_init__)
                    review_data = CodeReviewData(
                        idx=data.get('idx', str(i)),
                        old_code=data.get('oldf', '') or data.get('old_code', ''),
                        new_code=data.get('newf', '') or data.get('new_code', ''),
                        diff=data.get('patch', '') or data.get('diff', ''),
                        file_path=data.get('file_path', ''),
                        msg=data.get('msg', '') or data.get('comment', '')
                    )

                    code_review_data.append(review_data)


            except Exception as e:
                logger.warning(f"Error processing data entry {i}: {e}")
                invalid_data_count += 1
                continue

        # Print statistics
        if args.skip_preprocessing:
            print(f"Data loading complete: {len(code_review_data)} entries loaded")
        else:
            print(f"Data preprocessing complete:")
            print(f"  Raw entries: {len(raw_data)}")
            print(f"  Valid entries: {len(code_review_data)}")
            print(f"  Invalid JSON: {invalid_json_count}")
            print(f"  Invalid data: {invalid_data_count}")
            if len(raw_data) + invalid_json_count > 0:
                print(f"  Success rate: {len(code_review_data)/(len(raw_data)+invalid_json_count)*100:.1f}%")

            # Show detailed statistics if requested
            if args.show_preprocessing_stats and code_review_data:
                print(f"\nDetailed preprocessing statistics:")
                total_additions = sum(entry.get_change_summary()["additions"] for entry in code_review_data[:min(10, len(code_review_data))])
                total_deletions = sum(entry.get_change_summary()["deletions"] for entry in code_review_data[:min(10, len(code_review_data))])
                total_changes = sum(entry.get_change_summary()["total_changes"] for entry in code_review_data[:min(10, len(code_review_data))])
                sample_size = min(10, len(code_review_data))

                print(f"  Sample statistics (first {sample_size} entries):")
                print(f"    Total additions: {total_additions}")
                print(f"    Total deletions: {total_deletions}")
                print(f"    Total changes: {total_changes}")
                print(f"    Average changes per entry: {total_changes/sample_size:.1f}")

    except FileNotFoundError:
        print(f"Error: File {args.input_file} not found")
        sys.exit(1)

    if not code_review_data:
        print("Error: No valid code review data")
        sys.exit(1)

    # Create code review comment generator
    print(f"Initializing generator: {len(API_KEYS)} API keys, {args.max_workers} concurrent threads")

    try:
        generator = CodeReviewCommentGenerator(
            api_keys=API_KEYS,
            max_workers=args.max_workers
        )

        # Generate code review comments
        if start_idx is not None or end_idx is not None:
            range_info = f"Range: [{start_idx if start_idx is not None else 0}:{end_idx if end_idx is not None else len(code_review_data)}]"
            print(f"Starting code review comment generation, type: {args.prompt_type}, {range_info}")
        else:
            print(f"Starting code review comment generation, type: {args.prompt_type}")

        def progress_callback(completed, total):
            progress = completed / total * 100
            print(f"Progress: {completed}/{total} ({progress:.1f}%)")

        start_time = time.time()

        results = generator.generate_review_comments(
            code_review_data,
            prompt_type=args.prompt_type,
            language='english',
            start_idx=start_idx,
            end_idx=end_idx,
            only_processed=True,  # 只返回处理范围内的数据
            progress_callback=progress_callback
        )

        processing_time = time.time() - start_time

        # Generate output filename with range information
        output_filename = generate_output_filename(
            args.output_file,
            start_idx,
            end_idx,
            len(code_review_data)
        )

        # Save results (only processed data)
        print(f"Saving {len(results)} processed results to: {output_filename}")

        with open(output_filename, 'w', encoding='utf-8') as f:
            for result in results:
                result_dict = {
                    'idx': result.idx,  # 编号
                    'diff_original': result.original_diff,  # 原始diff
                    'diff_processed': result.diff,  # 处理后的diff
                    'msg': result.msg,  # msg
                    'generated_comment': result.generated_comment,  # generated_comment
                    'meaningless': result.meaningless,  # 从generated_comment中提取的meaningless值
                    'success': result.success,
                    'file_path': result.file_path
                }
                f.write(json.dumps(result_dict, ensure_ascii=False) + '\n')

        # Statistics (results now only contain processed data)
        success_count = sum(1 for r in results if r.success)
        error_count = len(results) - success_count

        print(f"\n=== Processing Complete ===")
        if start_idx is not None or end_idx is not None:
            # Calculate original range info for display
            original_total = len(code_review_data)
            actual_start = start_idx if start_idx is not None else 0
            actual_end = end_idx if end_idx is not None else original_total
            not_processed_count = original_total - len(results)

            print(f"Original total samples: {original_total}")
            print(f"Processing range: [{actual_start}:{actual_end}]")
            print(f"Processed samples: {len(results)}")
            print(f"Not processed (outside range): {not_processed_count}")
        else:
            print(f"Total samples: {len(results)}")

        print(f"Successful: {success_count}")
        print(f"Failed: {error_count}")
        print(f"Success rate: {success_count/len(results)*100:.1f}%")
        print(f"Total time: {processing_time:.2f} seconds")
        if len(results) > 0:
            print(f"Average per sample: {processing_time/len(results):.2f} seconds")

        # Show sample results
        print(f"\n=== Sample Results ===")
        for i, result in enumerate(results[:2]):
            print(f"\nSample {i+1}: {result.file_path}")
            print(f"Original diff: {result.original_diff[:100] if result.original_diff else 'N/A'}...")
            print(f"Processed diff: {result.diff[:100]}...")
            if result.success:
                print(f"Generated comment: {result.generated_comment}")
                print(f"Response time: {result.response_time:.2f}s")
            else:
                print(f"Generation failed: {result.error}")

        # Engine statistics
        engine_stats = generator.get_engine_stats()
        print(f"\n=== Engine Statistics ===")
        print(f"API Key count: {engine_stats['api_key_count']}")
        print(f"Total requests: {engine_stats['total_requests']}")
        print(f"Total errors: {engine_stats['total_errors']}")
        print(f"Error rate: {engine_stats['error_rate']:.2%}")

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

"""
Usage Examples:

1. Basic usage (process all data):
   python parallel_inference.py --input-file data.jsonl

2. Process specific range (indices 1 to 3, excluding 3):
   python parallel_inference.py --input-file data.jsonl --range 1:3

3. From index 2 to end:
   python parallel_inference.py --input-file data.jsonl --range 2:

4. From start to index 5 (excluding 5):
   python parallel_inference.py --input-file data.jsonl --range :5

5. Using separate parameters to specify range:
   python parallel_inference.py --input-file data.jsonl --start-idx 1 --end-idx 4

6. Test range functionality (no real API keys needed):
   python parallel_inference.py --test-range

7. Test data preprocessing functionality (no real API keys needed):
   python parallel_inference.py --test-preprocessing

8. Test meaningless value extraction functionality (no real API keys needed):
   python parallel_inference.py --test-meaningless

9. Test filename generation functionality (no real API keys needed):
   python parallel_inference.py --test-filename

10. Process specific range from file:
   python parallel_inference.py --input-file data.jsonl --range 10:20

11. Skip data preprocessing:
   python parallel_inference.py --input-file data.jsonl --skip-preprocessing

12. Show detailed preprocessing statistics:
    python parallel_inference.py --input-file data.jsonl --show-preprocessing-stats

Notes:
- Indices start from 0
- end_idx is exclusive (like Python slicing)
- Output filename automatically includes processing range (e.g., output_10-19.jsonl for range 10:20)
- Full data processing uses filename like output_all_100.jsonl (for 100 total samples)
- Unprocessed data will be marked as "Not processed (outside specified range)" in output
- Saved result file maintains original data order
- Statistics distinguish between data within and outside processing range
"""
