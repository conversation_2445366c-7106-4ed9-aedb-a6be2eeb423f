# 训练配置选择
# 设置 USE_DEEPSPEED=1 使用DeepSpeed加速，设置为0使用传统DDP
USE_DEEPSPEED=0  # 改为1启用DeepSpeed

# GPU配置
export CUDA_VISIBLE_DEVICES=0,1  # 使用双GPU

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

# 分布式训练配置
MASTER_HOST=localhost && echo MASTER_HOST: ${MASTER_HOST}
MASTER_PORT=23334 && echo MASTER_PORT: ${MASTER_PORT}
RANK=0 && echo RANK: ${RANK}
PER_NODE_GPU=2 && echo PER_NODE_GPU: ${PER_NODE_GPU}  # 恢复双GPU
NODES=1 && echo NODES: ${NODES}
WORLD_SIZE=$((PER_NODE_GPU * NODES)) && echo WORLD_SIZE: ${WORLD_SIZE}
NCCL_DEBUG=INFO

# 根据配置选择训练方法
if [ "$USE_DEEPSPEED" -eq 1 ]; then
    echo "=== 使用DeepSpeed训练 ==="

    # 检查DeepSpeed是否安装
    if ! python -c "import deepspeed" 2>/dev/null; then
        echo "错误: DeepSpeed未安装，请运行: bash install_deepspeed.sh"
        exit 1
    fi

    # 使用DeepSpeed启动
    deepspeed --num_gpus=${PER_NODE_GPU} --master_port=${MASTER_PORT} ${mnt_dir}/run_finetune_fcl_deepspeed.py \
        --train_epochs 15 \
        --model_name_or_path /data/xjd/SodaCoder-main/CodeReviewer/model \
        --output_dir save/fcl_deepspeed \
        --fcl_data_file /data/xjd/SodaCoder-main/w_negative_train_20000.jsonl \
        --dev_filename /data/xjd/SodaCoder-main/Comment_Generation/msg-valid.jsonl \
        --max_source_length 512 \
        --max_target_length 128 \
        --learning_rate 3e-4 \
        --mask_rate 0.15 \
        --save_steps 1800 \
        --log_steps 100 \
        --train_steps 30000 \
        --seed 2233 \
        --raw_input
else
    echo "=== 使用传统DDP训练 ==="

    # 使用torch.distributed.run启动
    python -m torch.distributed.run --nproc_per_node ${PER_NODE_GPU} --node_rank=${RANK} --nnodes=${NODES} --master_addr=${MASTER_HOST} --master_port=${MASTER_PORT} ${mnt_dir}/run_finetune_fcl.py \
        --gpu_per_node ${PER_NODE_GPU} \
        --node_index ${RANK} \
        --train_epochs 15 \
        --model_name_or_path /data/xjd/SodaCoder-main/CodeReviewer/model \
        --output_dir save/fcl_ddp \
        --fcl_data_file /data/xjd/SodaCoder-main/w_negative_train_20000.jsonl \
        --dev_filename /data/xjd/SodaCoder-main/Comment_Generation/msg-valid.jsonl \
        --max_source_length 512 \
        --max_target_length 128 \
        --train_batch_size 3 \
        --learning_rate 3e-4 \
        --gradient_accumulation_steps 6 \
        --mask_rate 0.15 \
        --save_steps 1800 \
        --log_steps 100 \
        --train_steps 30000 \
        --seed 2233 \
        --raw_input
fi

echo "=== 训练完成 ==="