#!/bin/bash

# 独立DeepSpeed训练脚本 - 避免参数冲突
export CUDA_VISIBLE_DEVICES=0,1

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

# DeepSpeed配置
NUM_GPUS=2
MASTER_PORT=23338

echo "=== 使用独立DeepSpeed脚本训练 ==="
echo "使用GPU数量: ${NUM_GPUS}"
echo "主端口: ${MASTER_PORT}"
echo "脚本路径: ${mnt_dir}/run_fcl_deepspeed_standalone.py"

# 检查DeepSpeed是否安装
if ! python -c "import deepspeed" 2>/dev/null; then
    echo "错误: DeepSpeed未安装，请运行: bash install_deepspeed.sh"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "${mnt_dir}/deepspeed_config.json" ]; then
    echo "错误: DeepSpeed配置文件不存在: ${mnt_dir}/deepspeed_config.json"
    exit 1
fi

# 检查数据文件是否存在
if [ ! -f "/data/xjd/SodaCoder-main/w_negative_train_20000.jsonl" ]; then
    echo "警告: 训练数据文件不存在，请检查路径"
fi

# 使用DeepSpeed启动训练
echo "开始训练..."
deepspeed --num_gpus=${NUM_GPUS} --master_port=${MASTER_PORT} \
    ${mnt_dir}/run_fcl_deepspeed_standalone.py \
    --model_name_or_path /data/xjd/SodaCoder-main/CodeReviewer/model \
    --output_dir save/fcl_deepspeed_standalone \
    --fcl_data_file /data/xjd/SodaCoder-main/w_negative_train_20000.jsonl \
    --dev_filename /data/xjd/SodaCoder-main/Comment_Generation/msg-valid.jsonl \
    --max_source_length 512 \
    --max_target_length 128 \
    --train_batch_size 3 \
    --learning_rate 3e-4 \
    --train_epochs 15 \
    --train_steps 30000 \
    --save_steps 1800 \
    --log_steps 100 \
    --seed 2233 \
    --mask_rate 0.15 \
    --fcl_beta 1.0 \
    --fcl_alpha 1.0 \
    --fcl_weight 1.0 \
    --raw_input

echo "=== DeepSpeed训练完成 ==="
echo "检查点保存在: save/fcl_deepspeed_standalone/"
