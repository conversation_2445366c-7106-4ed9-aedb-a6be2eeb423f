#!/bin/bash

# 简化的DeepSpeed训练脚本
export CUDA_VISIBLE_DEVICES=0,1

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

# DeepSpeed配置
NUM_GPUS=2
MASTER_PORT=23337

echo "=== 使用简化DeepSpeed脚本训练 ==="
echo "使用GPU数量: ${NUM_GPUS}"
echo "主端口: ${MASTER_PORT}"

# 检查DeepSpeed是否安装
if ! python -c "import deepspeed" 2>/dev/null; then
    echo "错误: DeepSpeed未安装，请运行: bash install_deepspeed.sh"
    exit 1
fi

# 使用DeepSpeed启动训练
deepspeed --num_gpus=${NUM_GPUS} --master_port=${MASTER_PORT} \
    ${mnt_dir}/run_finetune_fcl_deepspeed_simple.py \
    --deepspeed=${mnt_dir}/deepspeed_config.json \
    --model_name_or_path /data/xjd/SodaCoder-main/CodeReviewer/model \
    --output_dir save/fcl_deepspeed_simple \
    --fcl_data_file /data/xjd/SodaCoder-main/w_negative_train_20000.jsonl \
    --dev_filename /data/xjd/SodaCoder-main/Comment_Generation/msg-valid.jsonl \
    --max_source_length 512 \
    --max_target_length 128 \
    --learning_rate 3e-4 \
    --train_epochs 15 \
    --train_steps 30000 \
    --save_steps 1800 \
    --log_steps 100 \
    --seed 2233 \
    --mask_rate 0.15 \
    --raw_input

echo "=== DeepSpeed训练完成 ==="
