#!/bin/bash

# FCL (Focal Contrastive Learning) Training Script for CodeReviewer
# This script demonstrates how to train the model with FCL loss using correct and incorrect code examples

# Set environment variables
export CUDA_VISIBLE_DEVICES=0,1,2,3
export TOKENIZERS_PARALLELISM=false

# Training parameters
PER_NODE_GPU=4
NODES=1
RANK=0
MASTER_HOST=localhost
MASTER_PORT=23456

# Model and data paths
MODEL_PATH="microsoft/codereviewer"
OUTPUT_DIR="../../save/fcl"
FCL_DATA_FILE="../../data/fcl_msg_data.jsonl"

# Training hyperparameters
TRAIN_BATCH_SIZE=8
EVAL_BATCH_SIZE=8
LEARNING_RATE=5e-5
TRAIN_STEPS=10000
WARMUP_STEPS=1000
SAVE_STEPS=2000
LOG_STEPS=100

# FCL specific parameters
FCL_BETA=1.0
FCL_WEIGHT=1.0
MAX_SOURCE_LENGTH=512
MAX_TARGET_LENGTH=256

# Create output directory
mkdir -p $OUTPUT_DIR

echo "Starting FCL training with the following parameters:"
echo "  Model: $MODEL_PATH"
echo "  Output: $OUTPUT_DIR"
echo "  FCL Data: $FCL_DATA_FILE"
echo "  FCL Beta: $FCL_BETA"
echo "  FCL Weight: $FCL_WEIGHT"
echo "  Batch size: $TRAIN_BATCH_SIZE"
echo "  Learning rate: $LEARNING_RATE"
echo "  Training steps: $TRAIN_STEPS"

# Run distributed training
python -m torch.distributed.launch \
  --nproc_per_node ${PER_NODE_GPU} \
  --node_rank=${RANK} \
  --nnodes=${NODES} \
  --master_addr=${MASTER_HOST} \
  --master_port=${MASTER_PORT} \
  ../run_finetune_fcl.py \
  --model_name_or_path ${MODEL_PATH} \
  --output_dir ${OUTPUT_DIR} \
  --fcl_data_file ${FCL_DATA_FILE} \
  --fcl_beta ${FCL_BETA} \
  --fcl_weight ${FCL_WEIGHT} \
  --max_source_length ${MAX_SOURCE_LENGTH} \
  --max_target_length ${MAX_TARGET_LENGTH} \
  --train_batch_size ${TRAIN_BATCH_SIZE} \
  --eval_batch_size ${EVAL_BATCH_SIZE} \
  --learning_rate ${LEARNING_RATE} \
  --train_steps ${TRAIN_STEPS} \
  --warmup_steps ${WARMUP_STEPS} \
  --save_steps ${SAVE_STEPS} \
  --log_steps ${LOG_STEPS} \
  --gradient_accumulation_steps 1 \
  --weight_decay 0.01 \
  --adam_epsilon 1e-8 \
  --max_grad_norm 1.0 \
  --seed 42

echo "FCL training completed!"
