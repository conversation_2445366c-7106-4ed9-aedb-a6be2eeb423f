#!/bin/bash

# 测试分布式设置
export CUDA_VISIBLE_DEVICES=0,1

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

MASTER_HOST=localhost
MASTER_PORT=23335
RANK=0
PER_NODE_GPU=2
NODES=1
WORLD_SIZE=$((PER_NODE_GPU * NODES))

echo "测试分布式设置..."
echo "MASTER_HOST: ${MASTER_HOST}"
echo "MASTER_PORT: ${MASTER_PORT}"
echo "PER_NODE_GPU: ${PER_NODE_GPU}"
echo "WORLD_SIZE: ${WORLD_SIZE}"

python -m torch.distributed.run \
    --nproc_per_node ${PER_NODE_GPU} \
    --node_rank=${RANK} \
    --nnodes=${NODES} \
    --master_addr=${MASTER_HOST} \
    --master_port=${MASTER_PORT} \
    ${mnt_dir}/test_distributed.py
