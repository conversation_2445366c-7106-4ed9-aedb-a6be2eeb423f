#!/bin/bash

# DeepSpeed训练脚本 - 显著加速大模型训练
export CUDA_VISIBLE_DEVICES=0,1

mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

# DeepSpeed配置
NUM_GPUS=2
MASTER_PORT=23336

echo "=== DeepSpeed训练配置 ==="
echo "使用GPU数量: ${NUM_GPUS}"
echo "主端口: ${MASTER_PORT}"
echo "工作目录: ${mnt_dir}"

# 使用DeepSpeed启动训练
deepspeed --num_gpus=${NUM_GPUS} --master_port=${MASTER_PORT} ${mnt_dir}/run_finetune_fcl_deepspeed.py \
  --train_epochs 15 \
  --model_name_or_path /data/xjd/SodaCoder-main/CodeReviewer/model \
  --output_dir save/fcl_deepspeed \
  --fcl_data_file /data/xjd/SodaCoder-main/w_negative_train_20000.jsonl \
  --dev_filename /data/xjd/SodaCoder-main/Comment_Generation/msg-valid.jsonl \
  --max_source_length 512 \
  --max_target_length 128 \
  --learning_rate 3e-4 \
  --mask_rate 0.15 \
  --save_steps 1800 \
  --log_steps 100 \
  --train_steps 30000 \
  --seed 2233 \
  --raw_input

echo "=== DeepSpeed训练完成 ==="
