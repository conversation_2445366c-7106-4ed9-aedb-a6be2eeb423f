{"train_batch_size": 12, "train_micro_batch_size_per_gpu": 3, "gradient_accumulation_steps": 2, "optimizer": {"type": "AdamW", "params": {"lr": 0.0003, "betas": [0.9, 0.999], "eps": 1e-08, "weight_decay": 0.0}}, "scheduler": {"type": "WarmupLR", "params": {"warmup_min_lr": 0, "warmup_max_lr": 0.0003, "warmup_num_steps": 3000}}, "fp16": {"enabled": true, "auto_cast": false, "loss_scale": 0, "initial_scale_power": 16, "loss_scale_window": 1000, "hysteresis": 2, "min_loss_scale": 1}, "zero_optimization": {"stage": 3, "offload_optimizer": {"device": "cpu", "pin_memory": true}, "offload_param": {"device": "cpu", "pin_memory": true}, "overlap_comm": true, "contiguous_gradients": true, "sub_group_size": 1000000000.0, "reduce_bucket_size": "auto", "stage3_prefetch_bucket_size": "auto", "stage3_param_persistence_threshold": "auto", "stage3_max_live_parameters": 1000000000.0, "stage3_max_reuse_distance": 1000000000.0, "stage3_gather_16bit_weights_on_model_save": true}, "gradient_clipping": 1.0, "wall_clock_breakdown": false, "steps_per_print": 100, "dump_state": false}