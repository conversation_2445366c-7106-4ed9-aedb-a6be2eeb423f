#!/usr/bin/env python3
"""
独立的DeepSpeed FCL训练脚本
避免参数解析冲突
"""

import os
import torch
import logging
import argparse
import random
import json
import multiprocessing
import time
from torch.utils.data import DataLoader, SequentialSampler
from torch.utils.data.distributed import DistributedSampler
from models import build_or_load_gen_model
from utils import FCLDataset
import deepspeed
import numpy as np

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s -   %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


def set_seed(seed):
    """设置随机种子"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)


def get_fcl_loaders(data_files, args, tokenizer, pool):
    """创建FCL数据加载器"""
    def fn(features):
        return features
    
    for data_file in data_files:
        dataset = FCLDataset(tokenizer, pool, args, data_file)
        data_len = len(dataset)
        if args.global_rank == 0:
            logger.info(f"FCL Data length: {data_len}.")
        
        sampler = DistributedSampler(dataset)
        dataloader = DataLoader(
            dataset, 
            sampler=sampler, 
            batch_size=args.train_batch_size,
            num_workers=args.cpu_count, 
            collate_fn=fn
        )
        yield dataset, sampler, dataloader


def main():
    # 创建参数解析器
    parser = argparse.ArgumentParser(description="DeepSpeed FCL Training")
    
    # 基本训练参数
    parser.add_argument("--model_name_or_path", type=str, required=True,
                       help="Path to pretrained model")
    parser.add_argument("--output_dir", type=str, required=True,
                       help="Output directory for checkpoints")
    parser.add_argument("--fcl_data_file", type=str, required=True,
                       help="FCL training data file")
    parser.add_argument("--dev_filename", type=str, required=True,
                       help="Development data file")
    
    # 模型参数
    parser.add_argument("--max_source_length", type=int, default=512)
    parser.add_argument("--max_target_length", type=int, default=128)
    parser.add_argument("--train_batch_size", type=int, default=3)
    parser.add_argument("--eval_batch_size", type=int, default=8)
    
    # 训练参数
    parser.add_argument("--learning_rate", type=float, default=3e-4)
    parser.add_argument("--train_epochs", type=int, default=15)
    parser.add_argument("--train_steps", type=int, default=30000)
    parser.add_argument("--save_steps", type=int, default=1800)
    parser.add_argument("--log_steps", type=int, default=100)
    parser.add_argument("--seed", type=int, default=2233)
    parser.add_argument("--mask_rate", type=float, default=0.15)
    
    # FCL参数
    parser.add_argument("--fcl_beta", type=float, default=1.0,
                       help="FCL beta parameter")
    parser.add_argument("--fcl_alpha", type=float, default=1.0,
                       help="FCL alpha parameter")
    parser.add_argument("--fcl_weight", type=float, default=1.0,
                       help="FCL loss weight")
    
    # 其他参数
    parser.add_argument("--raw_input", action="store_true",
                       help="Use raw input format")
    parser.add_argument("--local_rank", type=int, default=-1,
                       help="Local rank for distributed training")
    
    # 让DeepSpeed添加其参数
    parser = deepspeed.add_config_arguments(parser)
    
    # 解析参数
    args = parser.parse_args()
    
    # 设置其他必要参数
    args.cpu_count = multiprocessing.cpu_count()
    args.gradient_accumulation_steps = 2
    
    # 初始化分布式训练
    deepspeed.init_distributed()
    
    args.local_rank = int(os.environ.get("LOCAL_RANK", 0))
    args.global_rank = int(os.environ.get("RANK", 0))
    args.world_size = int(os.environ.get("WORLD_SIZE", 1))
    
    logger.warning("Process rank: %s, global rank: %s, world size: %s",
                   args.local_rank, args.global_rank, args.world_size)
    
    torch.cuda.set_device(args.local_rank)
    set_seed(args.seed)
    
    # 加载模型
    config, model, tokenizer = build_or_load_gen_model(args)
    pool = multiprocessing.Pool(args.cpu_count)
    
    # 初始化DeepSpeed with hardcoded config
    deepspeed_config_path = "/data/xjd/SodaCoder-main/CodeReviewer/code/deepspeed_config.json"

    # Load DeepSpeed config
    with open(deepspeed_config_path, 'r') as f:
        ds_config = json.load(f)

    model_engine, optimizer, _, lr_scheduler = deepspeed.initialize(
        args=args,
        model=model,
        config_params=ds_config
    )
    
    logger.info("DeepSpeed initialization completed")
    
    # 准备训练数据
    train_file = args.fcl_data_file
    if os.path.isdir(train_file):
        train_files = [
            os.path.join(train_file, f) 
            for f in os.listdir(train_file) 
            if f.startswith("train") and f.endswith(".jsonl")
        ]
        random.seed(args.seed)
        random.shuffle(train_files)
    else:
        train_files = [train_file]
    
    # 开始训练
    global_step = 0
    
    for epoch in range(1, args.train_epochs + 1):
        # 设置epoch种子
        save_seed = args.seed
        args.seed += epoch
        set_seed(args.seed)
        args.seed = save_seed
        
        model_engine.train()
        nb_tr_examples, nb_tr_steps, tr_loss = 0, 0, 0
        
        for _, _, train_dataloader in get_fcl_loaders(train_files, args, tokenizer, pool):
            for step, examples in enumerate(train_dataloader, 1):
                if step == 1 and args.global_rank == 0:
                    logger.info(f"Epoch {epoch}, batch size: {len(examples)}")
                
                # 准备批次数据
                source_ids = torch.tensor(
                    [ex.source_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                correct_ids = torch.tensor(
                    [ex.correct_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                incorrect_ids = torch.tensor(
                    [ex.incorrect_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                
                source_mask = source_ids.ne(tokenizer.pad_id)
                correct_mask = correct_ids.ne(tokenizer.pad_id)
                incorrect_mask = incorrect_ids.ne(tokenizer.pad_id)
                
                # 计算FCL损失
                fcl_loss = model_engine(
                    fcl=True,
                    input_ids=source_ids,
                    attention_mask=source_mask,
                    correct_ids=correct_ids,
                    correct_mask=correct_mask,
                    incorrect_ids=incorrect_ids,
                    incorrect_mask=incorrect_mask,
                    beta=args.fcl_beta,
                    alpha=args.fcl_alpha
                )
                
                loss = args.fcl_weight * fcl_loss
                
                # DeepSpeed反向传播
                model_engine.backward(loss)
                model_engine.step()
                
                tr_loss += loss.item()
                nb_tr_examples += source_ids.size(0)
                nb_tr_steps += 1
                global_step += 1
                
                # 日志记录
                if args.global_rank == 0 and global_step % args.log_steps == 0:
                    train_loss = round(tr_loss / nb_tr_steps, 4)
                    logger.info(
                        "step {}/{}: Train loss {}".format(
                            global_step, args.train_steps, round(train_loss, 3)
                        )
                    )
                
                # 保存检查点
                if args.global_rank == 0 and global_step % args.save_steps == 0:
                    output_dir = os.path.join(args.output_dir, f"checkpoints-{global_step}")
                    model_engine.save_checkpoint(output_dir)
                    logger.info(f"Save checkpoint at step {global_step}")
                
                # 检查是否达到最大步数
                if global_step >= args.train_steps:
                    if args.global_rank == 0:
                        output_dir = os.path.join(args.output_dir, "checkpoints-last")
                        model_engine.save_checkpoint(output_dir)
                        logger.info(f"Reach max steps {args.train_steps}.")
                    return
    
    logger.info("Training completed")


if __name__ == "__main__":
    main()
