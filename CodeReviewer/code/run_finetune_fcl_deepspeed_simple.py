import os
import torch
import logging
import argparse
import random
import json
from tqdm import tqdm
import multiprocessing
import time
from itertools import cycle
from torch.utils.data import Data<PERSON><PERSON><PERSON>, RandomSampler, SequentialSampler
from torch.utils.data import ConcatDataset
from torch.utils.data.distributed import DistributedSampler
from transformers import AdamW, get_linear_schedule_with_warmup
from models import build_or_load_gen_model
from configs import add_args, set_seed
from utils import FCLDataset
from evaluator.smooth_bleu import bleu_fromstr
import deepspeed


logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s -   %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)


def get_fcl_loaders(data_files, args, tokenizer, pool, eval=False):
    """Create FCL dataloaders for multiple files."""
    def fn(features):
        return features
    global_rank = args.global_rank
    for data_file in data_files:
        dataset = FCLDataset(tokenizer, pool, args, data_file)
        data_len = len(dataset)
        if global_rank == 0:
            logger.info(f"FCL Data length: {data_len}.")
        if eval:
            sampler = SequentialSampler(dataset)
        else:
            sampler = DistributedSampler(dataset)
        dataloader = DataLoader(dataset, sampler=sampler, batch_size=args.train_batch_size if not eval else args.eval_batch_size, \
                                num_workers=args.cpu_count, collate_fn=fn)
        yield dataset, sampler, dataloader


def main():
    # 简化的参数解析
    parser = argparse.ArgumentParser()
    
    # 基本参数
    parser.add_argument("--model_name_or_path", type=str, required=True)
    parser.add_argument("--output_dir", type=str, required=True)
    parser.add_argument("--fcl_data_file", type=str, required=True)
    parser.add_argument("--dev_filename", type=str, required=True)
    parser.add_argument("--max_source_length", type=int, default=512)
    parser.add_argument("--max_target_length", type=int, default=128)
    parser.add_argument("--learning_rate", type=float, default=3e-4)
    parser.add_argument("--train_epochs", type=int, default=15)
    parser.add_argument("--train_steps", type=int, default=30000)
    parser.add_argument("--save_steps", type=int, default=1800)
    parser.add_argument("--log_steps", type=int, default=100)
    parser.add_argument("--seed", type=int, default=2233)
    parser.add_argument("--mask_rate", type=float, default=0.15)
    parser.add_argument("--raw_input", action="store_true")
    
    # FCL参数
    parser.add_argument("--fcl_beta", type=float, default=1.0)
    parser.add_argument("--fcl_alpha", type=float, default=1.0)
    parser.add_argument("--fcl_weight", type=float, default=1.0)
    
    # DeepSpeed参数
    parser.add_argument("--local_rank", type=int, default=-1)
    
    # 让DeepSpeed添加其参数
    parser = deepspeed.add_config_arguments(parser)
    args = parser.parse_args()
    
    # 设置其他必要参数
    args.cpu_count = multiprocessing.cpu_count()
    args.train_batch_size = 3  # 每GPU批次大小
    args.eval_batch_size = 8
    args.gradient_accumulation_steps = 2
    
    # DeepSpeed处理分布式初始化
    deepspeed.init_distributed()
    
    args.local_rank = int(os.environ.get("LOCAL_RANK", 0))
    args.global_rank = int(os.environ.get("RANK", 0))
    args.world_size = int(os.environ.get("WORLD_SIZE", 1))
    
    logger.warning("Process rank: %s, global rank: %s, world size: %s",
                   args.local_rank, args.global_rank, args.world_size)
    
    torch.cuda.set_device(args.local_rank)
    set_seed(args)

    config, model, tokenizer = build_or_load_gen_model(args)
    pool = multiprocessing.Pool(args.cpu_count)
    
    # 初始化DeepSpeed
    model_engine, optimizer, _, lr_scheduler = deepspeed.initialize(
        args=args,
        model=model
    )

    global_step = 0
    train_file = args.fcl_data_file

    # 处理训练文件
    if os.path.isdir(train_file):
        train_files = [file for file in os.listdir(train_file) if file.startswith("train") and file.endswith(".jsonl")]
        random.seed(args.seed)
        random.shuffle(train_files)
        train_files = [os.path.join(train_file, file) for file in train_files]
    else:
        train_files = [train_file]
    
    # 开始训练
    for epoch in range(1, args.train_epochs + 1):
        save_seed = args.seed
        args.seed += epoch
        set_seed(args)
        args.seed = save_seed
        model_engine.train()
        nb_tr_examples, nb_tr_steps, tr_loss = 0, 0, 0

        for _, _, train_dataloader in get_fcl_loaders(train_files, args, tokenizer, pool):
            for step, examples in enumerate(train_dataloader, 1):
                if step == 1 and args.global_rank == 0:
                    logger.info(f"batch size: {len(examples)}")

                # 准备批次数据
                source_ids = torch.tensor(
                    [ex.source_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                correct_ids = torch.tensor(
                    [ex.correct_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)
                incorrect_ids = torch.tensor(
                    [ex.incorrect_ids for ex in examples], dtype=torch.long
                ).to(args.local_rank)

                source_mask = source_ids.ne(tokenizer.pad_id)
                correct_mask = correct_ids.ne(tokenizer.pad_id)
                incorrect_mask = incorrect_ids.ne(tokenizer.pad_id)

                # 计算FCL损失
                fcl_loss = model_engine(
                    fcl=True,
                    input_ids=source_ids,
                    attention_mask=source_mask,
                    correct_ids=correct_ids,
                    correct_mask=correct_mask,
                    incorrect_ids=incorrect_ids,
                    incorrect_mask=incorrect_mask,
                    beta=args.fcl_beta,
                    alpha=args.fcl_alpha
                )

                loss = args.fcl_weight * fcl_loss

                # DeepSpeed反向传播
                model_engine.backward(loss)
                model_engine.step()

                tr_loss += loss.item()
                nb_tr_examples += source_ids.size(0)
                nb_tr_steps += 1
                global_step += 1

                if args.global_rank == 0 and global_step % args.log_steps == 0:
                    train_loss = round(tr_loss / nb_tr_steps, 4)
                    logger.info(
                        "step {}/{}: Train loss {}".format(
                            global_step, args.train_steps, round(train_loss, 3)
                        )
                    )

                if global_step >= args.train_steps:
                    if args.global_rank == 0:
                        output_dir = os.path.join(args.output_dir, "checkpoints-last")
                        model_engine.save_checkpoint(output_dir)
                        logger.info(f"Reach max steps {args.train_steps}.")
                    return
                    
                if args.global_rank == 0 and global_step % args.save_steps == 0:
                    output_dir = os.path.join(args.output_dir, f"checkpoints-{global_step}")
                    model_engine.save_checkpoint(output_dir)
                    logger.info(f"Save checkpoint at step {global_step}")


if __name__ == "__main__":
    main()
