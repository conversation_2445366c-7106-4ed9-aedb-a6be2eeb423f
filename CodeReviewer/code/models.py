import os
from linecache import cache
from typing import Any

import torch.nn as nn
import torch
import torch.nn.functional as F
from torch.nn import CrossEntropyLoss, BCEWithLogitsLoss
import numpy as np
from utils import MyTokenizer
from transformers import (
    <PERSON><PERSON>on<PERSON>g,
    <PERSON>Model,
    <PERSON><PERSON>oken<PERSON>,
    Bart<PERSON>onfig,
    BartForConditionalGeneration,
    BartTokenizer,
    T5Config,
    T5ForConditionalGeneration,
    T5Tokenizer,
)
import logging

logger = logging.getLogger(__name__)


class ReviewerModel(T5ForConditionalGeneration):

    def __init__(self, config):
        super().__init__(config)
        self.cls_head = nn.Linear(self.config.d_model, 2, bias=True)
        self.init()

    def init(self):
        nn.init.xavier_uniform_(self.lm_head.weight)
        factor = self.config.initializer_factor
        self.cls_head.weight.data.normal_(mean=0.0, \
            std=factor * ((self.config.d_model) ** -0.5))
        self.cls_head.bias.data.zero_()

    def forward(
        self, *argv, **kwargs
    ):
        r"""
        Doc from Huggingface transformers:
        labels (:obj:`torch.LongTensor` of shape :obj:`(batch_size,)`, `optional`):
            Labels for computing the sequence classification/regression loss. Indices should be in :obj:`[-100, 0, ...,
            config.vocab_size - 1]`. All labels set to ``-100`` are ignored (masked), the loss is only computed for
            labels in ``[0, ..., config.vocab_size]``
        Returns:
        Examples::
            >>> from transformers import T5Tokenizer, T5ForConditionalGeneration
            >>> tokenizer = T5Tokenizer.from_pretrained('t5-small')
            >>> model = T5ForConditionalGeneration.from_pretrained('t5-small')
            >>> # training
            >>> input_ids = tokenizer('The <extra_id_0> walks in <extra_id_1> park', return_tensors='pt').input_ids
            >>> labels = tokenizer('<extra_id_0> cute dog <extra_id_1> the <extra_id_2>', return_tensors='pt').input_ids
            >>> outputs = model(input_ids=input_ids, labels=labels)
            >>> loss = outputs.loss
            >>> logits = outputs.logits
            >>> # inference
            >>> input_ids = tokenizer("summarize: studies have shown that owning a dog is good for you", return_tensors="pt").input_ids  # Batch size 1
            >>> outputs = model.generate(input_ids)
            >>> print(tokenizer.decode(outputs[0], skip_special_tokens=True))
            >>> # studies have shown that owning a dog is good for you.
        """
        if "fcl" in kwargs:
            # FCL loss computation
            assert all(key in kwargs for key in [
                "input_ids", "attention_mask", "correct_ids", "correct_mask",
                "incorrect_ids", "incorrect_mask"
            ]), "FCL loss requires input_ids, attention_mask, correct_ids, correct_mask, incorrect_ids, incorrect_mask"

            beta = kwargs.get("beta", 1.0)
            alpha = kwargs.get("alpha", 1.0)  # Weight for generation loss L_CSL
            return self.fcl_loss_optimized(
                input_ids=kwargs["input_ids"],
                attention_mask=kwargs["attention_mask"],
                correct_ids=kwargs["correct_ids"],
                correct_mask=kwargs["correct_mask"],
                incorrect_ids=kwargs["incorrect_ids"],
                incorrect_mask=kwargs["incorrect_mask"],
                beta=beta,
                alpha=alpha
            )

        if "cls" in kwargs:
            assert (
                "input_ids" in kwargs and \
                "labels" in kwargs and \
                "attention_mask" in kwargs
            )
            return self.cls(
                input_ids=kwargs["input_ids"],
                labels=kwargs["labels"],
                attention_mask=kwargs["attention_mask"],
            )
        if "input_labels" in kwargs:
            assert (
                "input_ids" in kwargs and \
                "input_labels" in kwargs and \
                "decoder_input_ids" in kwargs and \
                "attention_mask" in kwargs and \
                "decoder_attention_mask" in kwargs
            ), "Please give these arg keys."
            input_ids = kwargs["input_ids"]
            input_labels = kwargs["input_labels"]
            decoder_input_ids = kwargs["decoder_input_ids"]
            attention_mask = kwargs["attention_mask"]
            decoder_attention_mask = kwargs["decoder_attention_mask"]
            if "encoder_loss" not in kwargs:
                encoder_loss = True
            else:
                encoder_loss = kwargs["encoder_loss"]
            return self.review_forward(input_ids, input_labels, decoder_input_ids, attention_mask, decoder_attention_mask, encoder_loss)
        return super().forward(*argv, **kwargs)

    def cls(
        self,
        input_ids,
        labels,
        attention_mask,
    ):
        encoder_outputs = self.encoder( \
            input_ids=input_ids,
            attention_mask=attention_mask,
            output_attentions=False,
            return_dict=False
        )
        hidden_states = encoder_outputs[0]
        first_hidden = hidden_states[:, 0, :]
        first_hidden = nn.Dropout(0.3)(first_hidden)
        logits = self.cls_head(first_hidden)
        loss_fct = CrossEntropyLoss()
        if labels != None:
            loss = loss_fct(logits, labels)
            return loss
        return logits

    def _decoder_pass(
            self,
            encoder_hidden_states: torch.Tensor,
            encoder_attention_mask: torch.Tensor,
            decoder_input_ids: torch.Tensor,
            decoder_attention_mask: torch.Tensor
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """
        一个辅助方法，执行一次完整的 Decoder + LM Head 前向传播。

        Args:
            encoder_hidden_states: Encoder的输出。
            encoder_attention_mask: Encoder的注意力掩码。
            decoder_input_ids: Decoder的输入ID。
            decoder_attention_mask: Decoder的注意力掩码。

        Returns:
            A tuple containing:
            - lm_logits (torch.Tensor): 语言模型头的输出 logits。
            - token_losses (torch.Tensor): 每个 token 的交叉熵损失 (reduction='none')。
        """
        # 1. 准备 Decoder 输入
        decoder_inputs = self._shift_right(decoder_input_ids)

        # 2. Decoder 前向传播
        decoder_outputs = self.decoder(
            input_ids=decoder_inputs,
            attention_mask=decoder_attention_mask,
            encoder_hidden_states=encoder_hidden_states,
            encoder_attention_mask=encoder_attention_mask,
            output_attentions=False,
            return_dict=False
        )
        sequence_output = decoder_outputs[0]
        if self.config.tie_word_embeddings:
            sequence_output = sequence_output * (self.model_dim ** -0.5)

        # 3. LM Head 计算 logits
        lm_logits = self.lm_head(sequence_output)

        # 4. 计算每个 token 的损失
        lm_loss_fct_none = CrossEntropyLoss(ignore_index=0, reduction='none')
        # Reshape logits and labels for loss calculation
        token_losses = lm_loss_fct_none(lm_logits.view(-1, lm_logits.size(-1)), decoder_input_ids.view(-1))
        # Reshape token_losses back to (batch_size, seq_len)
        token_losses = token_losses.view(decoder_input_ids.shape)

        return lm_logits, token_losses

    def fcl_loss_optimized(
            self,
            input_ids: torch.Tensor,
            attention_mask: torch.Tensor,
            correct_ids: torch.Tensor,
            correct_mask: torch.Tensor,
            incorrect_ids: torch.Tensor,
            incorrect_mask: torch.Tensor,
            beta: float = 1.0,
            alpha: float = 1.0
    ) -> torch.Tensor:
        """
        Focal Contrastive Learning (FCL) loss 的高效实现版本。
        通过复用 Encoder 输出和 Decoder 的中间结果来避免重复计算。
        """
        # Step 1: 执行一次 Encoder 前向传播，并复用其结果
        encoder_outputs = self.encoder(
            input_ids=input_ids,
            attention_mask=attention_mask,
            output_attentions=False,
            return_dict=False
        )
        hidden_states = encoder_outputs[0]

        # Step 2: 计算一次分类分数 S_cls
        # 这个分数只依赖于输入 x，因此对于 correct 和 incorrect 样本对是共享的
        first_hidden = hidden_states[:, 0, :]
        cls_logits = self.cls_head(first_hidden)
        s_cls = F.softmax(cls_logits, dim=-1)[:, 1]

        # Step 3: 对 "correct" 样本执行一次 Decoder pass，同时获取 L_csl 和 S_fcl_correct
        correct_logits, correct_token_losses = self._decoder_pass(
            encoder_hidden_states=hidden_states,
            encoder_attention_mask=attention_mask,
            decoder_input_ids=correct_ids,
            decoder_attention_mask=correct_mask
        )

        # 从中计算 L_csl (标准生成损失)
        lm_loss_fct_mean = CrossEntropyLoss(ignore_index=0)  # reduction='mean' by default
        l_csl = lm_loss_fct_mean(correct_logits.view(-1, correct_logits.size(-1)), correct_ids.view(-1))

        # 从中计算 S_fcl_correct
        valid_tokens_correct = (correct_ids != 0).float()
        sequence_losses_correct = (correct_token_losses * valid_tokens_correct).sum(dim=1) / valid_tokens_correct.sum(
            dim=1)
        s_fcl_correct = torch.exp(-sequence_losses_correct)

        # Step 4: 对 "incorrect" 样本执行一次 Decoder pass，仅获取 S_fcl_incorrect
        _, incorrect_token_losses = self._decoder_pass(
            encoder_hidden_states=hidden_states,
            encoder_attention_mask=attention_mask,
            decoder_input_ids=incorrect_ids,
            decoder_attention_mask=incorrect_mask
        )

        # 计算 S_fcl_incorrect
        valid_tokens_incorrect = (incorrect_ids != 0).float()
        sequence_losses_incorrect = (incorrect_token_losses * valid_tokens_incorrect).sum(
            dim=1) / valid_tokens_incorrect.sum(dim=1)
        s_fcl_incorrect = torch.exp(-sequence_losses_incorrect)

        # Step 5: 组合计算对比损失 (与原始函数完全相同的数学逻辑)
        eps = 1e-8
        log_ratio_correct = torch.log(s_fcl_correct + eps) - torch.log(s_cls + eps)
        log_ratio_incorrect = torch.log(s_fcl_incorrect + eps) - torch.log(s_cls + eps)

        contrastive_term = beta * log_ratio_correct - beta * log_ratio_incorrect
        sigmoid_output = torch.sigmoid(contrastive_term)
        contrastive_loss = -torch.log(sigmoid_output + eps).mean()

        # Step 6: 组合最终的总损失
        total_loss = alpha * l_csl + contrastive_loss

        return total_loss

    def review_forward(
        self,
        input_ids,
        input_labels,
        decoder_input_ids,
        attention_mask,
        decoder_attention_mask,
        encoder_loss=True
    ):
        encoder_outputs = self.encoder( \
            input_ids=input_ids,
            attention_mask=attention_mask,
            output_attentions=False,
            return_dict=False
        )
        hidden_states = encoder_outputs[0]
        decoder_inputs = self._shift_right(decoder_input_ids)
        # Decode
        decoder_outputs = self.decoder(
            input_ids=decoder_inputs,
            attention_mask=decoder_attention_mask,
            encoder_hidden_states=hidden_states,
            encoder_attention_mask=attention_mask,
            output_attentions=False,
            return_dict=False
        )
        sequence_output = decoder_outputs[0]
        if self.config.tie_word_embeddings: # this is True default
            sequence_output = sequence_output * (self.model_dim ** -0.5)
        if encoder_loss:
            # print(self.encoder.get_input_embeddings().weight.shape)
            cls_logits = nn.functional.linear(hidden_states, self.encoder.get_input_embeddings().weight)
            # cls_logits = self.cls_head(hidden_states)
        lm_logits = self.lm_head(sequence_output)
        if decoder_input_ids is not None:
            lm_loss_fct = CrossEntropyLoss(ignore_index=0)      # Warning: PAD_ID should be 0
            loss = lm_loss_fct(lm_logits.view(-1, lm_logits.size(-1)), decoder_input_ids.view(-1))
            if encoder_loss and input_labels is not None:
                cls_loss_fct = CrossEntropyLoss(ignore_index=-100)
                loss += cls_loss_fct(cls_logits.view(-1, cls_logits.size(-1)), input_labels.view(-1))
            return loss
        return cls_logits, lm_logits

def get_model_size(model):
    model_parameters = filter(lambda p: p.requires_grad, model.parameters())
    model_size = sum([np.prod(p.size()) for p in model_parameters])
    return "{}M".format(round(model_size / 1e6))


def build_or_load_gen_model(args):
    config_class, model_class, tokenizer_class = T5Config, ReviewerModel, RobertaTokenizer
    
    config = config_class.from_pretrained(args.model_name_or_path)
    tokenizer = tokenizer_class.from_pretrained(args.model_name_or_path)
    model = model_class.from_pretrained(args.model_name_or_path, config=config)

    tokenizer.special_dict = {
        f"<e{i}>" : tokenizer.get_vocab()[f"<e{i}>"] for i in range(99, -1, -1)
    }

    tokenizer.mask_id = tokenizer.get_vocab()["<mask>"]
    tokenizer.bos_id = tokenizer.get_vocab()["<s>"]
    tokenizer.pad_id = tokenizer.get_vocab()["<pad>"]
    tokenizer.eos_id = tokenizer.get_vocab()["</s>"]
    tokenizer.msg_id = tokenizer.get_vocab()["<msg>"]
    tokenizer.keep_id = tokenizer.get_vocab()["<keep>"]
    tokenizer.add_id = tokenizer.get_vocab()["<add>"]
    tokenizer.del_id = tokenizer.get_vocab()["<del>"]
    tokenizer.start_id = tokenizer.get_vocab()["<start>"]
    tokenizer.end_id = tokenizer.get_vocab()["<end>"]

    logger.info(
        "Finish loading model [%s] from %s",
        get_model_size(model),
        args.model_name_or_path,
    )

    if args.load_model_path is not None:
        model_path = os.path.join(args.load_model_path, "pytorch_model.bin")
        logger.info("Reload model from {}".format(model_path))
        try:
            model.load_state_dict(torch.load(model_path, map_location="cpu"))
        except RuntimeError:
            saved = model.cls_head
            model.cls_head = None
            model.load_state_dict(torch.load(model_path, map_location="cpu"))
            model.cls_head = saved
        model.to(args.local_rank)

    return config, model, tokenizer


