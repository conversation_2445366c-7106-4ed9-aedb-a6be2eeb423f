#!/bin/bash

# DeepSpeed配置切换脚本
mnt_dir="/data/xjd/SodaCoder-main/CodeReviewer/code"

echo "=== DeepSpeed配置切换工具 ==="
echo "当前可用配置："
echo "1. deepspeed_config.json - 默认配置（FP16，可能有溢出）"
echo "2. deepspeed_config_stable.json - 稳定配置（保守的FP16设置）"
echo "3. deepspeed_config_fp32.json - FP32配置（最稳定，但速度较慢）"
echo "4. deepspeed_config_stage3.json - Stage3配置（最大内存节省）"

echo ""
echo "选择要使用的配置："
echo "1) 稳定FP16配置（推荐，解决溢出问题）"
echo "2) FP32配置（最稳定，无溢出风险）"
echo "3) Stage3配置（内存不足时使用）"
echo "4) 恢复默认配置"

read -p "请输入选择 (1-4): " choice

case $choice in
    1)
        echo "切换到稳定FP16配置..."
        cp ${mnt_dir}/deepspeed_config_stable.json ${mnt_dir}/deepspeed_config.json
        echo "✅ 已切换到稳定配置，解决梯度溢出问题"
        ;;
    2)
        echo "切换到FP32配置..."
        cp ${mnt_dir}/deepspeed_config_fp32.json ${mnt_dir}/deepspeed_config.json
        echo "✅ 已切换到FP32配置，完全避免溢出但速度较慢"
        ;;
    3)
        echo "切换到Stage3配置..."
        cp ${mnt_dir}/deepspeed_config_stage3.json ${mnt_dir}/deepspeed_config.json
        echo "✅ 已切换到Stage3配置，最大内存节省"
        ;;
    4)
        echo "恢复默认配置..."
        cat > ${mnt_dir}/deepspeed_config.json << 'EOF'
{
  "train_batch_size": 12,
  "train_micro_batch_size_per_gpu": 3,
  "gradient_accumulation_steps": 2,
  "optimizer": {
    "type": "AdamW",
    "params": {
      "lr": 3e-4,
      "betas": [0.9, 0.999],
      "eps": 1e-8,
      "weight_decay": 0.0
    }
  },
  "scheduler": {
    "type": "WarmupLR",
    "params": {
      "warmup_min_lr": 0,
      "warmup_max_lr": 3e-4,
      "warmup_num_steps": 3000
    }
  },
  "fp16": {
    "enabled": true,
    "auto_cast": false,
    "loss_scale": 0,
    "initial_scale_power": 12,
    "loss_scale_window": 500,
    "hysteresis": 1,
    "min_loss_scale": 1
  },
  "zero_optimization": {
    "stage": 2,
    "allgather_partitions": true,
    "allgather_bucket_size": 2e8,
    "overlap_comm": true,
    "reduce_scatter": true,
    "reduce_bucket_size": 2e8,
    "contiguous_gradients": true
  },
  "gradient_clipping": 1.0,
  "wall_clock_breakdown": false,
  "steps_per_print": 100
}
EOF
        echo "✅ 已恢复默认配置"
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo ""
echo "当前配置内容："
cat ${mnt_dir}/deepspeed_config.json | head -20
echo "..."

echo ""
echo "现在可以重新运行训练："
echo "bash sh/finetune_with_negative.sh"
