#!/usr/bin/env python3
"""
诊断DeepSpeed梯度溢出问题
"""

import torch
import json
import os

def diagnose_overflow():
    """诊断梯度溢出问题"""
    print("=== DeepSpeed梯度溢出诊断 ===")
    
    # 检查CUDA和GPU信息
    print("1. GPU信息:")
    if torch.cuda.is_available():
        print(f"   GPU数量: {torch.cuda.device_count()}")
        for i in range(torch.cuda.device_count()):
            props = torch.cuda.get_device_properties(i)
            print(f"   GPU {i}: {props.name}")
            print(f"   内存: {props.total_memory / 1e9:.1f} GB")
    else:
        print("   ❌ CUDA不可用")
        return
    
    # 检查DeepSpeed配置
    print("\n2. DeepSpeed配置分析:")
    config_path = "/data/xjd/SodaCoder-main/CodeReviewer/code/deepspeed_config.json"
    
    if os.path.exists(config_path):
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        # 分析FP16设置
        if 'fp16' in config and config['fp16'].get('enabled', False):
            fp16_config = config['fp16']
            print("   FP16设置:")
            print(f"     启用: {fp16_config.get('enabled', False)}")
            print(f"     初始scale: 2^{fp16_config.get('initial_scale_power', 16)} = {2**fp16_config.get('initial_scale_power', 16)}")
            print(f"     hysteresis: {fp16_config.get('hysteresis', 2)}")
            print(f"     loss_scale_window: {fp16_config.get('loss_scale_window', 1000)}")
            print(f"     min_loss_scale: {fp16_config.get('min_loss_scale', 1)}")
            
            # 判断是否容易溢出
            initial_scale = 2**fp16_config.get('initial_scale_power', 16)
            if initial_scale >= 32768:
                print("   ⚠️  初始loss scale过高，容易溢出")
            else:
                print("   ✅ 初始loss scale合理")
                
            if fp16_config.get('hysteresis', 2) > 1:
                print("   ⚠️  hysteresis > 1，可能导致频繁调整")
            else:
                print("   ✅ hysteresis设置合理")
        else:
            print("   FP16未启用或配置错误")
        
        # 分析批次大小
        print("\n   批次设置:")
        print(f"     总批次大小: {config.get('train_batch_size', 'N/A')}")
        print(f"     每GPU批次: {config.get('train_micro_batch_size_per_gpu', 'N/A')}")
        print(f"     梯度累积: {config.get('gradient_accumulation_steps', 'N/A')}")
        
        # 分析梯度裁剪
        if 'gradient_clipping' in config:
            print(f"     梯度裁剪: {config['gradient_clipping']}")
            if config['gradient_clipping'] > 1.0:
                print("   ⚠️  梯度裁剪值较大，可能需要降低")
            else:
                print("   ✅ 梯度裁剪设置合理")
        else:
            print("   ⚠️  未设置梯度裁剪")
    else:
        print("   ❌ 配置文件不存在")
        return
    
    # 提供解决方案
    print("\n3. 解决方案建议:")
    print("   方案1 (推荐): 使用稳定配置")
    print("     bash switch_deepspeed_config.sh")
    print("     选择选项1 (稳定FP16配置)")
    
    print("\n   方案2: 使用FP32训练")
    print("     bash switch_deepspeed_config.sh") 
    print("     选择选项2 (FP32配置)")
    
    print("\n   方案3: 手动调整参数")
    print("     - 降低initial_scale_power (如改为10)")
    print("     - 设置hysteresis=1")
    print("     - 减小loss_scale_window (如改为200)")
    print("     - 启用梯度裁剪 (如0.5)")
    
    print("\n   方案4: 减小批次大小")
    print("     - 减小train_micro_batch_size_per_gpu")
    print("     - 增加gradient_accumulation_steps保持总批次大小")

def test_fp16_stability():
    """测试FP16数值稳定性"""
    print("\n4. FP16数值稳定性测试:")
    
    # 创建测试张量
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # 测试大数值
    large_values = [1e4, 1e5, 1e6, 1e7, 1e8]
    
    for val in large_values:
        try:
            # FP32
            tensor_fp32 = torch.tensor(val, dtype=torch.float32, device=device)
            
            # FP16
            tensor_fp16 = torch.tensor(val, dtype=torch.float16, device=device)
            
            # 检查是否溢出
            if torch.isinf(tensor_fp16) or torch.isnan(tensor_fp16):
                print(f"   ❌ {val:.0e}: FP16溢出")
                break
            else:
                print(f"   ✅ {val:.0e}: FP16正常")
        except Exception as e:
            print(f"   ❌ {val:.0e}: 错误 - {e}")
            break

if __name__ == "__main__":
    diagnose_overflow()
    test_fp16_stability()
    
    print("\n=== 诊断完成 ===")
    print("建议使用稳定配置解决溢出问题：")
    print("bash switch_deepspeed_config.sh")
