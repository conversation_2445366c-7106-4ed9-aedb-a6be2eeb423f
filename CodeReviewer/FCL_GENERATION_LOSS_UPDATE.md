# FCL Generation Loss Update

## 概述

根据用户提供的公式，FCL损失不应该忽视正样本的生成损失。本次更新将标准生成损失 L_CSL 集成到 FCL 损失计算中。

## 公式参考

```
L_CSL = -E_(x,y)~D_CSL ∑_{t=1}^T log S_cls(y_c^(t)|y_c^(<t), x)
```

其中：
- L_CSL 是标准的生成损失（Cross-Entropy Loss）
- S_cls 是基于分类器的分数
- y_c^(t) 是正确样本在时间步 t 的token
- y_c^(<t) 是时间步 t 之前的token序列

## 主要修改

### 1. 模型层面 (`models.py`)

#### 新增方法：`compute_generation_loss()`
```python
def compute_generation_loss(self, input_ids, attention_mask, decoder_input_ids, decoder_attention_mask):
    """
    计算标准生成损失 L_CSL，对应公式中的 -E log S_cls(y_c^(t)|y_c^(<t), x)
    """
```

#### 修改方法：`fcl_loss()`
- 添加 `alpha` 参数控制生成损失权重
- 集成生成损失：`fcl_loss = alpha * l_csl + contrastive_loss`
- 更新文档说明新的损失构成

#### 修改方法：`forward()`
- 支持传递 `alpha` 参数到 FCL 损失计算

### 2. 训练脚本层面 (`run_finetune_fcl.py`)

- 在模型调用中添加 `alpha=args.fcl_alpha` 参数
- 支持从配置中读取 alpha 值

### 3. 配置层面 (`configs.py`)

- 添加 `--fcl_alpha` 参数，默认值为 1.0
- 参数说明：控制生成损失 L_CSL 在总损失中的权重

### 4. 训练脚本层面 (`finetune_with_negative.sh`)

- 添加 `--fcl_alpha 1.0` 参数到训练命令中

## 损失构成对比

### 修改前
```
FCL Loss = contrastive_loss
         = -log(sigmoid(β * log_ratio_correct - β * log_ratio_incorrect))
```

### 修改后
```
FCL Loss = α * L_CSL + contrastive_loss
         = α * generation_loss + contrastive_loss
```

其中：
- `α` (alpha): 生成损失权重，默认为 1.0
- `L_CSL`: 标准交叉熵生成损失
- `contrastive_loss`: 原有的对比学习损失

## 参数说明

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `fcl_alpha` | 1.0 | 生成损失 L_CSL 的权重 |
| `fcl_beta` | 1.0 | 对比学习的温度参数 |
| `fcl_weight` | 1.0 | 整体 FCL 损失的权重 |

## 使用方法

### 训练命令示例
```bash
python run_finetune_fcl.py \
  --fcl_data_file data.jsonl \
  --fcl_alpha 1.0 \
  --fcl_beta 1.0 \
  --fcl_weight 1.0 \
  # ... 其他参数
```

### 参数调优建议
- `fcl_alpha = 1.0`: 平衡生成和对比学习
- `fcl_alpha > 1.0`: 更重视生成质量
- `fcl_alpha < 1.0`: 更重视对比学习

## 测试验证

创建了测试脚本 `test_fcl_with_generation_loss.py` 来验证：
1. 生成损失计算是否正确
2. 不同 alpha 值对总损失的影响
3. 各个损失组件的数值范围

## 理论优势

1. **完整性**: 现在包含了完整的 FCL 公式，不再忽略生成损失
2. **平衡性**: 通过 alpha 参数平衡生成质量和判别能力
3. **灵活性**: 可以根据任务需求调整生成损失的重要性
4. **一致性**: 与原始 FCL 论文的公式保持一致

## 向后兼容性

- 所有现有参数保持不变
- 新增的 `fcl_alpha` 参数有合理的默认值
- 现有的训练脚本只需添加一行参数即可使用新功能
