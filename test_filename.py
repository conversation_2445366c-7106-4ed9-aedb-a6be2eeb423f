#!/usr/bin/env python3
"""
测试文件名生成功能的独立脚本
"""

def generate_output_filename(base_filename: str, 
                           start_idx, 
                           end_idx, 
                           total_data_count: int) -> str:
    """
    生成带有处理范围信息的输出文件名
    
    Args:
        base_filename: 基础文件名
        start_idx: 开始索引
        end_idx: 结束索引
        total_data_count: 总数据量
        
    Returns:
        str: 带有范围信息的文件名
    """
    import os
    
    # 分离文件名和扩展名
    name, ext = os.path.splitext(base_filename)
    
    # 计算实际的处理范围
    actual_start = start_idx if start_idx is not None else 0
    actual_end = end_idx if end_idx is not None else total_data_count
    
    # 确保范围有效
    actual_start = max(0, min(actual_start, total_data_count))
    actual_end = max(actual_start, min(actual_end, total_data_count))
    
    # 生成范围字符串
    if start_idx is None and end_idx is None:
        # 处理全部数据
        range_str = f"_all_{total_data_count}"
    else:
        # 处理部分数据
        range_str = f"_{actual_start}-{actual_end-1}"  # end_idx是exclusive的，所以减1显示
    
    # 组合新文件名
    new_filename = f"{name}{range_str}{ext}"
    
    return new_filename

def test_filename_generation():
    """测试文件名生成功能"""
    print("=== Testing Filename Generation ===")
    
    test_cases = [
        # 测试用例: (base_filename, start_idx, end_idx, total_count, expected_pattern, description)
        ("output.jsonl", None, None, 100, "output_all_100.jsonl", "处理全部数据"),
        ("output.jsonl", 0, 50, 100, "output_0-49.jsonl", "处理前50条"),
        ("output.jsonl", 10, 20, 100, "output_10-19.jsonl", "处理10-19范围"),
        ("output.jsonl", 50, None, 100, "output_50-99.jsonl", "从50开始到结尾"),
        ("output.jsonl", None, 30, 100, "output_0-29.jsonl", "从开始到30"),
        ("results.json", 5, 15, 50, "results_5-14.json", "不同扩展名"),
        ("data", 0, 10, 20, "data_0-9", "无扩展名"),
        ("output.jsonl", 0, 1, 100, "output_0-0.jsonl", "只处理一条数据"),
        ("output.jsonl", 99, 100, 100, "output_99-99.jsonl", "处理最后一条"),
        ("path/to/output.jsonl", 10, 20, 100, "path/to/output_10-19.jsonl", "带路径的文件名"),
    ]
    
    passed = 0
    failed = 0
    
    for i, (base_filename, start_idx, end_idx, total_count, expected, description) in enumerate(test_cases):
        result = generate_output_filename(base_filename, start_idx, end_idx, total_count)
        if result == expected:
            print(f"  ✓ Test {i+1}: {description}")
            print(f"    Input: {base_filename}, range: [{start_idx}:{end_idx}], total: {total_count}")
            print(f"    Output: {result}")
            passed += 1
        else:
            print(f"  ✗ Test {i+1}: {description}")
            print(f"    Input: {base_filename}, range: [{start_idx}:{end_idx}], total: {total_count}")
            print(f"    Expected: {expected}")
            print(f"    Got: {result}")
            failed += 1
        print()
    
    print(f"Test Results: {passed} passed, {failed} failed")
    print("Filename generation testing complete")

if __name__ == "__main__":
    test_filename_generation()
