# CodeReview 测试结果合并工具

这个工具用于将 `finetune_CodeReview.sh` 脚本生成的 `test-preds.txt` 和 `test-golds.txt` 文件与测试集合并，只保留测试集中的 code change 部分（`oldf` 和 `patch` 字段）。

## 文件说明

### 核心脚本
- **`merge_test_results.py`**: 主要的合并脚本，支持多种参数配置
- **`merge_codereview_results.sh`**: 专门为 CodeReview 任务设计的便捷脚本
- **`run_merge_example.sh`**: 使用示例脚本，展示不同的使用方法

### 文档
- **`README_merge_results.md`**: 本说明文件

## 快速开始

### 1. 运行训练/测试脚本
首先运行 CodeReview 的微调脚本：
```bash
cd LLM4CodeChange-main/SLM/scripts
bash finetune_CodeReview.sh -e  # 运行评估模式
```

### 2. 合并结果
使用便捷脚本合并结果：
```bash
# 使用默认设置
./merge_codereview_results.sh

# 或指定自定义路径
./merge_codereview_results.sh -d /path/to/results -o my_results.jsonl
```

## 详细使用方法

### 方法1: 使用便捷脚本（推荐）

```bash
# 基本用法
./merge_codereview_results.sh

# 指定结果文件目录
./merge_codereview_results.sh -d /data/xjd/SodaCoder-main/outputs/models/fine-tuning/CodeReview

# 指定测试集文件
./merge_codereview_results.sh -t /path/to/test.jsonl

# 指定输出文件名
./merge_codereview_results.sh -o merged_results.jsonl

# 查看帮助
./merge_codereview_results.sh -h
```

### 方法2: 直接使用 Python 脚本

```bash
# 指定具体文件路径
python merge_test_results.py \
    --test-file /data/xjd/SodaCoder-main/Dataset/fine-tuning/CodeReview/test.jsonl \
    --preds-file /data/xjd/SodaCoder-main/outputs/models/fine-tuning/CodeReview/test-preds.txt \
    --golds-file /data/xjd/SodaCoder-main/outputs/models/fine-tuning/CodeReview/test-golds.txt \
    --output-file merged_results.jsonl

# 自动搜索结果文件
python merge_test_results.py \
    --test-file /data/xjd/SodaCoder-main/Dataset/fine-tuning/CodeReview/test.jsonl \
    --search-dir /data/xjd/SodaCoder-main/outputs/models/fine-tuning/CodeReview \
    --output-file merged_results.jsonl
```

## 输出格式

合并后的文件为 JSONL 格式，每行包含以下字段：

```json
{
    "index": 0,
    "oldf": "def hello():\n    print(\"Hello\")",
    "patch": "@@ -1,2 +1,2 @@\n def hello():\n-    print(\"Hello\")\n+    print(\"Hello World\")",
    "predicted_msg": "Update greeting message (predicted)",
    "actual_msg": "Update greeting message"
}
```

### 字段说明
- **`index`**: 数据索引
- **`oldf`**: 原始代码（code change 的原始部分）
- **`patch`**: 代码变更（code change 的差异部分）
- **`predicted_msg`**: 模型预测的提交消息
- **`actual_msg`**: 实际的提交消息
- **`cmtid`**: 提交ID（如果测试集中包含）
- **`y`**: 标签（如果测试集中包含）

## 常见问题

### Q1: 找不到结果文件
**A**: 确保已运行 `finetune_CodeReview.sh` 脚本，并且脚本成功生成了 `test-preds.txt` 和 `test-golds.txt` 文件。

### Q2: 数据长度不一致
**A**: 脚本会自动检测并处理长度不一致的情况，使用最短长度进行合并，并给出警告信息。

### Q3: 测试集文件格式错误
**A**: 确保测试集文件为 JSONL 格式，每行包含 `oldf`、`patch`、`msg` 等字段。

### Q4: 权限错误
**A**: 给脚本添加执行权限：
```bash
chmod +x merge_codereview_results.sh run_merge_example.sh
```

## 自定义配置

### 修改默认路径
编辑 `merge_codereview_results.sh` 文件，修改以下变量：
```bash
CURRENT_DIR="/your/path/to/SodaCoder-main"
OUTPUT_DIR="${CURRENT_DIR}/outputs/models/fine-tuning/CodeReview"
TEST_FILE="${CURRENT_DIR}/Dataset/fine-tuning/CodeReview/test.jsonl"
```

### 支持的结果文件名
脚本会自动搜索以下文件名：
- 预测文件: `test-preds.txt`, `test_cache-preds.txt`, `preds.txt`
- 标签文件: `test-golds.txt`, `test_cache-golds.txt`, `golds.txt`

## 示例演示

运行示例脚本查看完整的使用演示：
```bash
./run_merge_example.sh
```

这个脚本会展示多种使用方法，并使用本地测试数据进行演示。

## 注意事项

1. 确保所有路径正确，特别是测试集文件路径
2. 运行合并脚本前，确保已成功运行训练/测试脚本
3. 合并后的文件只包含 code change 相关信息，不包含完整的测试集数据
4. 脚本支持中文路径和文件名，但建议使用英文路径以避免编码问题
