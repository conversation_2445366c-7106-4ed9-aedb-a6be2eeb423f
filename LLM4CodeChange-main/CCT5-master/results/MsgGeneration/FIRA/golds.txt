Added error codes
generate config only if external build is enabled for the project
CommandDecoder should print all replay body on error
Fix quality flaw
Add missing wallpaper service lock
Recycle the comet flag pretty large glitch
Rendering modality if it is non default
add sort with Order enum
Handle loss of supplicant events at start
Remove unused session key prefix
Resolve a null pointer in PhoneStateChangedReceiver
enter app then immediately swipe to refresh fix
Fix retrieval of DeletionRetentionStrategyConfig in ConfigurationManagementPeriodical
Improve JavaDoc for Object Pool example
Investigated conflict into MemberNameCheck
prevent PageUp capture when popup visible
Not recycling bitmap on rotate and crop
Fix leak in LayoutTransition
Prevent edit name mode when not in a group conversation
Add isEnabled to PhysicsControl interface
Fixed Test for DataSinkTask
Turn CLOSURE_PASS on by default in the debugger
Only persist task bounds for freeform tasks
Making alphanumeric alphaNUMERIC
Setting an index in iterator correctly
Add a comment explaining why rewriteRequires needs to be where it is
throw AssertionError instead of RuntimeException
Fix unit tests
Remove obsolete tests
updated a javadoc ref
js parameter info should show types
configure initial roots
Fix DaemonicParserState toString
Fixing a compile error by removing a superflouous semicolon
Add build id to Android binaries
Fix wrong index in ColorSpaceTransform
added additional shortcut
Better naming scheme for generated patterns of inner classes
Remove gratitious gson usage
Let status bar alone
avoid ColumnIdent allocation in AlterTableAddColumnAnalyzer
Add debug message for default message output selection
allow speedrf to be inspected
Added mi command line option when invoking fruitstrap to run iOS apps on a device
Improved Javadoc for config parameter sdk
Clarified javadoc for searchEditText String in Solo and Searcher
Fixed syntax error in Javadoc
MINOR Remove stdout and stderr
Clowns may check their sense of humor somewhere else
Remove duplicate ProjectLock
Add validation for adapter stable ids in builder
integrate perf degradation fix
do not allocate empty strings
Fixed invalid traffic class in test
Removed unused parameter
Added the ability to specify a display message as well as the announcement
Reenable ignored test in AppleTestIntegrationTest
Remove mac heuristics for findJavaHome
separating isComparable and isComparableKey queries
Fix small bug
fix shortcut problem caused by svn commit dialog
adding missing test covering XSD check for label truncation
Fetch the correct number of suggestions from HBase
Fix an exception when logging packet that happen we close a call
Add a test to check Data Format property in the exchange
removed assignment in PSurfaceJOGL
Fix regression which lead to leak buffers when nothing could be read from the Channel
Remove usage of obsolete junit framework Assert class
Unknown relationship types in a dense node should not cause relationship loading
Add support for positive RSSI values
fixing pesky CS error
Removed support for attribute map parameters in callback functions
Better help text for command line client
Hide new congestion constant for now
fix typo setting syncperiod
annotate stacktrace fix tooltips for multiline commit messages
could not add components to root pane with constraints
StringLogger SYSTEM has automatic line flushing just like the file based StringLogger has
Log number of channels on server in case of DEBUG level logging
Add the support libraries to the platform documentation demos
made Jid class parse otr session ids
Update SUN JRE download link
Fix error page headers
Fixed log import
fixed bug with extra warning for css containg EL in jsp
Fix checkstyle errors
Fix color of ROC curve on x y axis
Fixed visibility of SimpleListener
Added missing delay
make guard final
Publicize some ByteSink ByteSource creators in FileSystemUtils
Pass project sdk to PythonProjectGenerator beforeProjectGenerated
Use singletonList insteadof ArrayList
Removed blank line
Print block hash in debug log message for unconnectable orphan
disable notification scorer by default
Make the header section addressable in messageView for later hiding
add onUserVisible to JsFacade
Fixed line length from previous commit
Fix resource leak
Rebalance bumps priority
Added TV density value
Throw SASL exception if authentication failed
Removed final modifier again to fix problem with PowerMock tests
Managed the interrupted exception to skip the retry logic
Add missing feature declaration in YouTrack repository
Make method name plural
preselect first JDK if no selection was provided
Change EntityBinding getAttributeBindingClosureSpan and getAttributeBindingClosure to return data for declared attribute bindings
Catch all asset bundle exceptions
fixing compile issue
do not call method twice
Fix ClassInstanceCreation s copy constructor
Added a little debug logging to better know which scheduler is being in use by quartz component
Try fix issue where distances in serach history tab are not updated when origin is changed
remove mysql validChecker log
Remove legacy methods Package getRules getFiles
allow piddir to be null in topologycontext for testing
Fix writting tick trigger in compiler
Fix sip tests
update sql standard authorization configuration whitelist more optimization flags Thejas Nair reviewed by Gunther Hagleitner
just process received rtp packet without trying to figure out the sequence number to drop
disable jsp test as jsp file type is not offered in structural search dialog anyway
removing unnecessary Collections unmodifiableMap System getEnv and EnvironmentUtil getEnvironmentMap are already unmodifiable maps
Add comment about why this is implemented
Native bullet will no longer ignore changes to the broadphase type
Add all should only add shows not added already
detect offline state
Display error message if movie could not be loaded
repopulate deployments even w no content path for plots
Clearing the buffer before returning it
Print SQL when query does not compile
Added explicit note on thread safety to JdbcTemplate variants
Increased assertion time out
Fix some quality flaws
Add GTFS routetype to leg
Fix parameter description of class prefix and class suffix
avoid NullPointerException in toStringBuffer
Revert NioEventLoop hasTasks which is not really true
Fix a bug when using self loops
Fix logger name
adjust scope call from change list manager
Explicitly request the focus in the call panel in single window mode
Fix the broken genrule example
enlarge sleep between ping and pong
Introduce Ordered NOT_ORDERED
Added liquibase tables to ignore list
New Groovy JDK methods to improve consistency newReader for URL
Fixing the property names although commented
Changing preview customize to use customizer correctly
Record waiting on mes pushed at in NowBriefed
Reduce the severity of a log message
Add a comments
Correctly handle fill array data instruction when building instruction list
Fix build error
added missing HashTable and Log
generate wrong string for log from revision
Remove MessageBody Reader Writer from ResourceConfig in RestApi
Adding TEXT BINARY to root generator map
enable selection of Linkify annotated text
Fixed merge problem
Hide Manage dialog on create
don t use selection model if block selection is enabled
Preserve visitBeanDefinition stacktrace in BeanDefinitionStoreException
Remove debugging line
Improves List partition Predicate
adding missing license header
Made method final required by Lucene
Adding deprecation annotation and javadoc
reduce the timeout from crazytime
smaller sleeping time
Fix incorrect synchronization on map values
ignore enum vals references from switch statements
don t set default cursor if empty text is invisible
Change Javadoc to get around brokenness
Java Removed redundant public qualifier
Fix RowIcon alignment for bookmarked directory
allow passing all sequence types for array and array like parameters
added additional check
Remove the LGPL license of the generated GTFS RT protocol buffer class
Correctly restart loader if another search is performed
remove debugging statement
Remove unused import
Fix potential NPE in DefaultWorkerProcess toString
Do not set docked divider as IME target
Remove the sign from number contacts when searching for a corresponding image Fixed
use safe invokeLater
Fix bugs spotted by IDE
toString for deserialized classes
Improve our EGL management when pausing resuming
Tweak snoop example to send a full HTTP request
Clicking on the new mail notification gets you to the default folder if any else to the folder list
Fix ALSA initialized wait for input only USB Audio devices
don t show strange no suggestions of type boolean after instanceof
Oops old update I forgot to commit
Display selected items in white
fixed scheme to use prefix
fixed Sonar issues
Fix GPS icon not showing for secondary users
Always update preference state
Fix a comment in AndroidBinary
Don t log a message if the connection is already closed
allow to set context file for jsp even if jsp isn t included in context file
Create should use the same index name as the index request in tests
Send Device Vendor ID after address allocation
clone the drawable while setting it to a SubActionBuilder in case it might be used by multiple buttons
Set focus back to table after displaying a message
Fix a bug in suspend read
simplify background setting
add license header to ThriftTopologyUtilsTest
Add additional debug
highlighting unresolved references
Improved creation of class from remote
Hide column header
also overwrite getAlpha to retrieve the correct alpha value
Add convenience constructor for default instance
Don t go all the way to Object class
improve test wait for green state post master node startup
Remove todo comment
updated deletion end point
added javadoc headers
ConnectionPool get should return failed future in case of free connection absence
Fix bug with preloading recent tasks
Removed nonexistant parameter from javadoc
Remove unused method
Remove unused open function from OpenCameraInterface
Turn off the CSV reporter
java lang ClassFormatError running groovy script from shell bash minor refactoring
Clarify Fragment onHiddenChanged documentation
TEST produce valid symlinks in tests
remove javadoc warning
Fix JavaClassRefLiTest hopefully
Add a setContentLength method to the exchange
leaving httpcode as int
add KeyEvent handling for ENTER key default
add timeout exception message
Fix template value caching
Tweak AMQP sample slightly
FPSCounter now using System nanotime instead of System currentTimeMillis
Fix preference dialog double creation
add showPopup to SpeedSearchBase
Fixed the CS errors of camel paxlogging
Add helper to get Cache Control response header
Skip empty lines in test url list
Added getNames to HealthCheckRegistry and MetricRegistry
Refine last segment calculation
AbstractRetryEntryPoint always uses RetryWithHttpEntryPoint logger
no need to optimize on the bool query parser we already do that in the optimizeQuery method
removed unnecessary imports
Delete enries from ofUserProp when user is deleted patch by Peter Johnson
Add some missing file headers
get auth id
Fix WebSocket regression added by this changes
Remove incorrect execution of OP_0
Remove forgotten commented code
Fixed typo in docs
Fix incorrect looping limits
Fixed ArrayIndexOutOfBoundsException and StringIndexOutOfBoundsException while logging
SopremoServer checks request for formal validity
Remove duplicate import
fixed params from Condition to SQLCondition
Do not use privacy provider for content provider start
Remove redundant modifiers from interfaces
don t log ProcessCanceledException
don t let guice by mistake inject two instances
Fix intermittent SlaveHostControllerAuthenticationTestCase failures
Tuned some configuration parameters
commit dialog fix restored splitter position
Add missing JMX export for RemoteTaskFactory
Make the ProjectFileSupport stateless pass in required parameter when called
Fix the window close database shutdown behaviour that I broke in my eager refactoring
Fixed java util jar reference cycle
Soften up result tolerance
Fixed index out of range in web view hook
replaced a thrown exception with a logger warning
Add annotations to a new API
messed up slashes for input output fields
Reduce log spew
updated copyright notice
Removed author name in code
Changed port number to see if that s what s breaking the build server
remove redundant images once again
Add firework api to get and set Firework ItemMeta
Add missing PreferenceLayouts when querying item type
Provide size hint inside UNSPECIFIED measure spec
set feedfiletype in DownloadStatus contructor
Remove unnecessary method in AllocationService
added a method to get the GraphDatabaseService from Neo4jGraph
removed now unused subject coloring code
Open any application file for import store as json mime type
add helper method to get or create a client with a custom config class
Keep attachment until called
Fix cache record creation with expiry time
Fix expected output of test needs checking
Do not do token refresh when trying to refresh token
Fix Math tests Firebird
Log a warning in ClientCall cancel if no detail is provided
add type source when appropriate
Fix variable name
Removed headless check in testcase
authorize logging added
removed dependency on groovy added by mistake
Rename a local variable too after last commit
Resolved read repair bug
Use the possessive version of activity
Removed renderMessage from API of DescriptorRenderer
Don t recycle action button views
Add sleep time to give GitHub build another try
Fix crash when selecting text on a non editable TextView
Don t consider timer elapsed seconds to be an insignificant detail in recently modified
Add constructor validation to EIntentService
cache generated file
Get AMQPRouteTest work in my Windows vista box
Test Fixes Use Transactional because this class is dirtying database without removing its data
Fix issue with site specific display density getting stuck
Expose Apple platform name to Skylark
create event tracker directory if not exist
update dox for FILL_AND_STROKE style
WPNetworkImageView now enforces a max size to avoid loading large images into memory
Delete leftover of war file from tempDir when removing invalid FileMessageFactory
Add the ability to specify a path in a cookie
Improve documentation for abstract contract
remove ExtendedApi dependency
Remove accidentally unterminated comment which has made Deprecated SingletonServiceBuilderFactory SERVICE_NAME unusable
Removed unused imports
Fix typo nbChannels channelMask
Rename configuration option to configscript
Add a setting to enable cellular on boot
Added bottles as a water container
avoid mandatory properties for optional things on the plugin
don t hold Project in statics
Added oneline if else template
Ignore a test case with timeout
Fix an edge case in computing click location in accessibility mode
fixed transient object issue
Add information about RequestFactory in Rest JavaDoc
better walk reluctance for bike trips
CS schedule throws UE instead of TOE
added a log for keepalive tasks
Sets the User Agent header to be sent with each REST request
Missed a test
Updated Javadoc for slaveOk to refer to ReadPreference secondaryPreferred
Remove Id keyword from java files in java javax
debugging null ptr
Remove moduleKey as it s not returned by the WS
Add a proper comment for SortActivity
Fix the log message when Fetch is disabled
Fix some formatting in Closer javadoc
add check for remote file clean in tests module
Fix restriction caching for UI
Fix plugin command name in remove plugin command
Fix not well format packet exception when set property for packet
clarify requirements of detach
Fixed bug in CameraFactory
Removed unused imports
Fix collision detection of Danmaku
Corrected default values for Camel model
Housekeeping added toString
Keep lazily creating meters and timers
removed unneeded attribute work in ScriptContext
Fix Emulator boot
Fix Dependant color bug
Gauge render tweak
Fix blocked call at incoming state whereas other peer is connected
Remove sticky broadcast when read in UploadListActivity
Updated javadocs of Player setPlayerListName to reflect that the char limit is based on length without colours
Updated the description of LoggedInputStream
test Remove BulkProcessorRetryIT s generic threadpool setting
missed one more context menu to fix
Just fixing a typo
fixed remove from favorites operation
Fix annotations on getters and make the boolean get versions deprecated
Allow message for healthy result
NewGeometry set default mode to Triangles
Remove redundant if check
Replacing boxed with primitive adding validator
Don t apply information level inspections to decompiled code
Only allow access to declarations from under a read action to prevent dead locks
Fix crash setting null seekbar thumb
Removed unused method
Remove extra space
Increased training size
ByteString misses encountered during forward port
Set claim sequence when the claim happens and not on publish
Rename targetFolder to sourceFolder and add dependency resolution
update demo and readme
edit button does nothing after first use
Fix JavaDoc comment
Fix incorrect order of arguments
Fixed a style error
Only allow opening files that are whitelisted serial port devices
Removed unused variables
ImageCache extends PMetadata
Generalize the javadocs on CacheBuilder ticker
Fixing the path and method implementation
Added textui TestRunner ResultPrinter
Set default locale for Robolectric s test runner to English to avoid number localization issues
Move p tags to the beginning of the line
Make the incompatibility between dynamic configurations
Corrected label on second column of histogram log file to indicate interval length instead of interval end time stamp
Normalize file output to use n style newlines before comparing
Fix documentation issue
Fix quality flaws
Don t call requestDisallowInterceptTouchEvent false
Fixes the choice of the MediaFormat to use for sending media when we accept a
Adds license header
Fix compilation error for TestShardEjector
remove useless code in UltimateViewAdapter
Fixed typo in comment
remove depricated show method
Fixed potential NPE in doStop
Remove permission check in registerAdapter
added file referesh before showing merge dialog
Revert GeoPointField stored default back to false
eliminate unused warnings
Fixing broken link
Fix NPEs in mlast commit
Fix Slow Non deterministic Test
Fix configuration factory test on windows
do not refresh svn mappings if change list manager is not running in tests
Users can now add Bundles to sliders if they want to access additional information
Added last and moveAbsolute cursor move methods to DatabaseResults
Fix LeveledCompactionStrategyTest testGrouperLevels with compression
Fix bug with password storage where some characters would lead to
Fixed some hard coded strings replaced with PostStatus methods
remove debug output
Organised some imports
Reflect renaming of base test class
Deprecate AbstractValidator no arg constructor
Fix memory leak also delete the best model
Propagate NotNull annotation to implementation
Fixe the case where the breakout room document conversion is triggered before the actor is brought to life
Make ThreadCpuStats a daemon thread
provided active profiles feature for placeholders
made Expr types immutable
Add expression string to failed expression evaluation exception message to help clarify the context
Fixed skin in GWT again
Fix compilation error
When opening a database the DatabaseFileProvider is checked first then we fallback to the context getDatabasePath call
Adding timeout to the Ldap test
Included the user number in the User Opening Closing Messages
Add minimal width and height logging to AM dump
do not include board cost in optimistic patternAlight traversal
extends base class instead of interface
Added a getter for the current config to AppCompiler
remove dead code from boolean operand
Add reserved check back
Fix a couple of idea inspection warnings
Changed the example to use the new zero length path possibility in Cypher
fix cls resolve test expectations
Use the default locale when initializing SimpleDateFormat
Fixed bug in WHERE condition on UPDATE when REMOVE is used
Use of and inside code confuses the validation parser
Add additional preconditions to UIWebServer
Add a hard check to ensure we are running with the expected lucene version
Add errors setting to stress help ninja
removed sysout code causing Travis build termination
Log SIGAR loading failure on INFO
Allow placeholderReplacement property to be specified when using the commandline client
Clarify warning message
install var callsite for AFunctions only
Add IQTypeFilter GET_OR_SET
Added unit test
Oracle tablespace resolve fix
do not detect nested copies by default
Add empty ArrayList check
Fixed compile problem
Fixing a log statement
Improve message for alert Add Datasource info
Only opens a file once for writing
Remove unused import
Fixed a test to look in the correct log
Added missing annotations to MapListenerTest
Fixing typo in ApplicationInfo documentation
Prove placeholders work correctly
Add information to be logged about underlying exception on Object DBObject conversion
Add a set operation to SimpleTensor
Adds argument checks for null value in getExistingChatRooms in MUC
When removing asserts preserve the assert tightened type on the remaining expression
Add a test for PropertyBeanSetter getPropertyNames
Adding author name
Actually overwrite SpongeImplFactory createTileEntity
Temporarily disable cobol maif plugin from integration tests
Prevent a null pointer exception
Remove unused methods
tweak replay status message
Removed unused imports
Don t reevaluate disconnected networks
Remove the dodgy ColorFilter on the RatingBar
ClassDescriptor now creates MethodDescriptor
Add ShadowCanvas setBitmap
Fix potential NPE when saving fragment state
Change ImmutableClassToInstanceMap getInstance to reject null Class
Fixed an incorrect relationship in a class diagram
zip file closed exception
does not insert end of tag for single html tag
Fix small issue with previous CL
move next button to left side of console debug context toolbar
Make the placeholders in WorksetIteration non final
To fix the build
Remove the log statement from Or operator close operation
Prevent Lucene queries from starting with a conjunction
Fixed evaluation of instance types from strings
disable sm logging
Make this type internal for now
Added onAnimationUpdate to Animation3DListener
set app settings after project initialize
Get rid of stupid compiler warning
Fix json serialization for rnn specific vertices in CG
added catch to getting style and theme
Makes test compatible with earlier JDK version
improved embeddable interface tests
only hide the console interrupt button when a new prompt occurs
add raw header copy function
added a reset method
Set modification date for abstract playlists
added filtering for min max parameter pairs
Remove group_split flag
Remove unused class
make the cached compile scripts a soft map
correctly determine the context that does evaluation
Made PyTargetExpression a PyQualifiedNameOwner
Write annotations with RetensionPolicy SOURCE to light classes
Repaired source name logging
Fix bug in AttributeEqualBuilder init
Fix ReplicaShardAllocatorTests when unassigned reason is ALLOCATION_FAILED
Fix NPE when query fails during parsing planning
Removed stacktraces from logging when creating database schema in tests
Fix warning use enum
Fix bugs in isDistinctFrom code generator
Make RecyclerView requestDisallowInterceptTouchEvent true
Don t load a fragment not reachable from the drawer
enchancing stripDelimiter function to work properly with special uinicode characters like umlauts etc
Fix else keyword completion
remove auto commit interval ms from Manual Offset Control example
Allow CordovaChromeClient subclasses access to CordovaInterface and CordovaWebView members
tweak delay for help
Added missing documentation
added TODO at the code where it breaks
Fixed wrong imports
remove heavy weight tooltips problems on Mac
remove unused single choice logic for now
add docs to load pointing to parseResourcesAnySyntax
Fix locking issues for CreateBlockMeta downgrade upgrade readlock
Don t crash sysui when advisory calls fail
Remove validate method from Utils
fixes default color
Handle empty current GOPATH when setting GOPATH
Improve rolling update logging
Fix disconnect if already no connection
made constructor private in Account entity
Adds deactivate life cycle method so binding can be safely stoppped restarted also cleans up properly when a IOException occurs
use existing logic of getting XmlAttributeDescriptor by querying XmlAttribute
catching connection exceptions during xml validation silently
Removed wrong code
add more buffering
Added toString in SimpleBuilder
improved test case
Removed double decoding for the VariantURLPath module
Force the removal of the AtmosphereResource when idle fires
Deprecate fillColor and add some Javadoc explaining the current behaviour
Fix possible deadlock in DefaultChannelPipeline destroyDown
Fixed the CS errors of CamelNamespaceHandler
Fix a crasher in SystemUI
Improve JavaDoc for Lazy Loading example
Added code to make ShadowMediaPlayer reset reset the current position
Make AvatarView compatible with layout editor preview
Fix strings highlighting within console
Use same sleep length in waitForActivity as the activitySyncTimer
Improved test to double check that the bridge method is not needed
AbstractJdbcMessageIdRepository should use the ManagedResource and ManagedOperation from Camel
Remove duplicate initialization from bad merge
add info to getItemId within the Adapter
SMALLFIX Fix javadoc param description
Fixing Unit test issue caused by moving into other package
method always has parameter list even if it s incorrect
Remove incorrect ordering check
Add comments to apptype and authtype constants
Add more Yarn related javadocs
fix a unit test
Made the shyHeightAlreadyCalculated to equal true when the shy height is calculated
Fix docs build failure
use standard UnsupportedOperationException instead of NotImplementedException from internal packages
Send OnChunkGenerated when a new chunk gets generated
Remove ShutdownHook from jar tool to prevent memory leak when running under nailgun
Move observeOn rightside
Fixed a bug regarding moving away a branched db directory
Added proper synchronization and not null check to SimpleMessageListenerContainer s doShutdown
Changed comment in JetMethod flags
small lookups should have non even height too
Added method to create an explosion in world
Minor Fix CS errors in camel netty
Set WebChromeClient to the android s WebView during instantiation
Remove isCertificateEntry check
MethodValidationPostProcessor provides protected createMethodValidationAdvice template method
Make NullAction Serializable
Add helper for URLConnection setUseCaches
do not log PCE
r Peter Gromov tolerate null key for get and containsKey
Updated comments w r t return values
Explain relationship with juf Function
Added unicode support
Adding timeout if the current printer or its capabilities disappear
Can t use keySet on ContentValues
Closes messages log on shutdown
Renamed ServiceActivingActiviyBehaviour to ServiceInvocationActivityBehaviour
Remove publicId field from SchemaResolver class
Allow bytes as well
Do not stop QueuedApnsService on unsuccessfull attempt to submit a message
take out whitespace
Don t stop foreground notification explicitly
Made field final and added an assertion
Fixed null body header after transform called bean that throws exception
add flush to gateway
Improve logging in SuperConsole
Adds command line options ga_hadoop_ver and ga_opt_out
Reduced visibility of some properties of Delete
Added getLayerCount to Scene
expose saveNow saveLater to subclasses
Return correct application context
remove some tests
indicate refresh when usage changes
add comments to Tachyon ls method
Fixed Attribute equals
debugging blinking GroovyCompilerTest
Add test for qualified table name
Revert Ignores test while we are using the naive raft log
use the language from the URL not German
Missing added test
Fix IFR parsing
Don t swallow cause if Store stats can t be build
Added getContexHolderStrategy method to SecurityContextHolder
Remove getHttpClient from AndroidUtils
revert added getLanguage for embeddedTokenTypesProviders
Fix documentation of EXTRA_PROVISIONING_DEVICE_ADMIN_PACKAGE_NAME
dynamic findBy method executes sql query twice when more than one result found
NioDatagramWorker ChannelRegistionTask should handle ClosedChannelException gracefully
eliminate eclipse warnings
Fixed a bug where channelClosed event is not propagated in ReadTimeoutHandler
Fix minor issue in JettyRequestUpgradeStrategy
Don t include email followers in getFollowers query
Activated unit test for CoGroupOperator
Fix the build
fixed compile issues by the merge
fixed problem on inheritedRoles
Improved logging client readhandler i case of not beating
Add rport argument for a reinvite request
fix materialization of RowN it now has the proper size and also copies the array
Make PhoneWindow aware of layout direction
Fixed the CS error of camel lucene
Revert synchronization of methods which cause blocking of UI thread
Fix javadoc link
Avoid duplication of AppWindowToken in created Task
Remove duplicated the from javadoc
don t red highlight unmatched brace pairs
Convert mmap assertion to if throw
restore button mnemonics
Skip a new isolated world test
leave marker to inflate a view
Check isBreakChar in getWrappedBounds
Add toString to ItemCounter
Fixed Husks rendering as standard zombies
Gradle module misses options
avoid deprecated method
A double tap starts selection when done on text only
Fixed a bug in TitleParserTest
Fix missing subprotocol
Fixed NullPointerException in ImageDiskCache
Removed dead code in AbstractNearCacheRecordStore
Added missing Override
Increased retry length of copying store from master
Replace existing files when unpacking
Fix formatted sql precondition not expanding changelog parameters
reduced message level to debug since this happens quite frequently now as of the binding re use
Adjust wallpaper restore acceptance criteria
Add javax rmi api to deployment
Add a test case to check consistency between CipherSuiteConverter and the known informaion in the Cipher enumeration
Added more exception handling for invalid server lists
Revert update comment on consume
Fixed sourceDirectory expression
fixed the wrong focus behavior after fade in
Fix TextView potential NPE in isLayoutRtl
Switched path back for travis
Fixed javadoc typos
Fix an issue where the app wans t correctly update the notes ribbon on Badge reset PNs
Add setAdapter to compat dialog
implement equals and hashCode to make two LocalQuickFix instances equal if they refer to one and the same IntentionAction
Fixed JetJavaLibCompletionTest on non windows machines
Fix NPE in integration tests when starting the broker
asynchronously update versions in listeners update node interpreters dialog
Fix Watchlist showing search results
Modified AggregateGroupedExchangeTest replacing deprecated
properly escape string
Remove seemingly unnecessary double work
Fixed bug that issued warnings on registered activities when their package was not the application package
Add javadoc to ExtendedUserPermissionDto
Pushing casting into the getter
updated since tags
FIxed bug in default deserializer
Fix Graph HTTP result set OHttpGraphResponse
Moved blueprint init
Change tell command to whisper
The BigTextStyle should never render mContentText
Alter the HISTORY_PROJECTION and BookmarkColumns
Use the provided cluster state instead of fetching a new cluster state from cluster service
exec a general ASTStatement
removing redundant null check from DelegatingMethod equals
Fix NPE which accours when Netty was used in an Applet
Prevent NaN in corner case of nDCG calculation
Removed generic return value from CoreFunctions filter
added missing final to moco exception
Fixed case sensitivity bug in repository name enumeration
Fix phase which was set before INSTRUCTION_SELECTION
Remove unnecessary Intent mocking
allow reflowComment in Markdown mode to just reflow text
corrected heuristic of test src root criterion
Added unique column constraint query for SybaseASADatabase
Fixed compilation error in test case and fixed size of double typed constants
Fix the exception javadoc in BlockDataManager
adds extra null check
Added VPC check into the SimianArmy Context
remove extra check now that the bug has been fixed
implement set String through Vec Writer
Move SquidCursorFactory into the adapter package
Don t announce the chunk provider disposal when there are still threads creating chunks
Fix stack overflow
Fix check style
remove unused generic type
Use a new class loader for each GStringTemplateScript class
Debug SVGExporter progress
Move Transactional down to method level in DefaultTokenServices
revd lesya Make update actions on setTargetComponent async since it otherwise is being called on partially initialized UI classes
Remove new asserts in CMFD
simplified expression in doc comment util
Add unit to maxContentLength javadoc of HttpObjectAggregator
Adding thread safety annotations for tachyon replay in common module
Added support for HeaderListViewAdapter in SpiceListView
TestNGOptions parallel and TestNGOptions threadCount have no effect
Improve javadoc on client context init reset
Added PluginManager getPlugins
Do not use unix specific signals on windows
Remove unused import warning
fixed write access allowed on clearing RO attribute
Don t bring stack forward when moving tasks
Add logging of NBHM resizing to Log info for now until the heuristics are proven
findbugs Create temporary ticket after setting up the response page in EditTicketPage
Fix TabLayout when used in a HorizontalScrollView
start and end indexes
Refactored OOB checking in fbook api
Fix touch handling in the ZoomableDraweeView
removing close method as shutdown does that
Adds wait conditions that were missing in TransactionConstraintsIT and caused failures
Remove ParcelConverter from default mapping
Added support to streaming for the file flag to distribute files out to the cluster if they don t exist
remove placeholders if file was hidden from student
remove redundant sanitize
Fix trees destroying pipes
detect change language level to default
Minor delete outdated comments and refine type
remove sync method to prevent deadlock
Fixed native function declaration toString
Remove alpha filler
Modified finishOpenedActivities and added an additional back press
Deprecate sticky header constant
Reduce the default memory cache size
fix test flakyness in another one of many cases
vcs log linear bek slightly more correct behavior
don t leak stuff via myPreferredFocusedComponent after dialog closing
actually count evaluated stream rules
Using for hashcode in objectname instead of dot
adds missing break
Remove docs for non existent constructor parameter
Fix issue with arrow not enable when setting maxDate
Fix deployment rollback
remove debug statement
TEST Fix ClusterStatsTests testValuesSmokeScreen to wait for yellow to get reliable FS stats
should fix the RedundantThrowTest
Remove method indirection to value field
Write packages list when granting permissions
Send connection preface and setting when the okhttp client transport starts this is required for talking to netty server
add warnings until we make it better
don t add symlink tree to rule key
reverting a faulty commit to ACG to avoid ClassCastExceptions
Spring Batch Improved producer logging
Added extra constructor which allows setting of the config attribute name
removed forgotten AwaitsFix annotations
Remove and forbid use of com google common collect ImmutableCollection
Fix reporting of non DH multi stream security events
Remove getter functions from FunctionInfo
Fix a typo
disable smart type completion inside views
Updated comments to clarify recent changes in code
tweak html formatter default options
Fix binary inspector
add explicit retention and target annotations to Ignore and Index annotations
Log error and try to answer an error packet if authentication fails because no session was found
Remove Javadoc errors
added zero padding of MCC qualifiers
Added missing license header
Fix NPE on binary get
Remove TODO comments
Check regexp intention action language detection fix
Fixed a potential crash when trying to upload attachments for an unsynced task
Fix IndexOutOfBounds exception
Make effectively final fields final
Activate RS by a single CTRL TAB in SQL editor
add getters for Title Content
fixed npe in error message handling
remove author comment
Fix COMPLEMENTOFDEFAULT alias
Don t save null project if load failed
Fix android tests
Also use special LIST path in ImapResponseParser for LSUB response
Fix NullPointerException bug
Added a comment about buffer ownership
Update the scale while updating the drawGL functor
Fixed test case
MailEngine send not sending attachments
Adding note about where authenticator activity is from
Added REPEATING_BILINEAR TextureOptions
No match found exception
removing useless imports
added the required first option of package to the invocation of aaptcommandbuilder
Fix things that should be static
Discovery Removed METADATA block
delete unused implementations of processDeclarations
REFACTOR Fixed a typo of the TourGuideDemoMain java
Moved IdGenerator and implementations from core to baseServices
Remove redundant exception handling code in CommandLineJob
Fix typo in KeyInfo Javadoc
Follow system font size in Reader post detail
Added clocks as a dependency to the NeoServerWithEmbeddedWebServer
build targets from a cycle in predictable order
Removed incorrect merge
deleted unused function
Set svn properties
Add more debug info for assertReadAccessAllowed
Replace rawQuery by query onUpgrade updateDownloadedFiles
in the call panel show the participant address instead of the participant name
Remove unused import
Adding a missing license header
remove deprecated assertion
Add a comment
Restrict account name to one line
Add Jelly Bean detector method to Utils
avoid wrong double clicks on mac
Removes redundant kafka enable property
Add API for contextPath
Fixed a bug where notification where invisible on the lockscreen
remove final keyword
Add defautl position to NodeDataImpl different than zero
Add license info
Correct an Cipher name in OpenSSL
Add missing import
do not add annotations jar to compiler classpath
fix merge conflict indicator
Fixed ignored test case
sync engine Make debug level trace
Implemented setSpeed for animationTrack as it needs special treatment
removing extra import
Documented return value of getRestrictBackgroundStatus
removed drawing of conditional sequence flow when source is gateway
Corrected config comment on doChunkLoading
reword the ctrl arrows hint don t show it for one line editors
Remove unused private method in TextInputLayout
Fix the build
speed up todo
Flip the permissions checks for video capture
Fix test assertion remove check of total count
Set maxPreTransitTime to max after initialization
add a todo
Unnecessary code removed that caused PSI changes during PSI events handling
Fix checkstyle violations
add isPowerOfTwo check
do not copy self in jar resources
Fixed generic signature
Document the lack of thread safety in CQLSSTableWriter
Remove unused field webArtifactName
Fix timeline bounds dialog accuracy problem
TaskStackBuilder use the correct package for looking up parents
remove irrelevant comment now that we pass the reuse strategy
Hook up nextMessage to ack mutation
Added processing opengl
Improved yet performance with huge datasets
Add JUnit assertions for the number of processed rows
Added null route chaos type to enabled chaos types
Add only one space before unique keyword
Add Batoo exclusion
Prevent double encoding
These tests don t mean anything
don t load props if already set
add error for isolated rule refs
when removing LDAP settings also reset the LdapConnector
make image color preview work in template data
Don t store test data in field
Fix javadoc errors
Only update widget settings UI if still added
Fixed MetricsLogger usage
On device startup be in touch mode
remove unused method don t display merge param in toString until it is used
allow to reset weights for new sentence iterator
Removed this reference
Added support for canceling and compiled statement
AbstractBaseJavaLocalInspectionTool getProblemElement should not return PsiTypeParameter
Avoid NPE if bundle is missing
Fix Spring deployment test
restore default copyright
Adding table creation in arguments transformation
Fix NPE if source is released having never been prepared
removing log lines
onewire refresh service got a name now
Add resource Id button API
Do not expose forced compat functionality yet
fixed TableInputFormat returns no records when re opened with a new split
Fixed problem on creation database with nested path via URL
Fix ExponentiallyDecayingSample clear
made argument parsing easy
Add java util regex Pattern to the list of well known immutable types
Add a routine to return a diff between two dates in seconds
Replaced invocation of deprecated factory methods in favor of newer counterparts in JacksonConverterFactory
Fix double counts when count is executed on two aliases pointing to the same index
TEST Assert against minCompatVersion instead of a static string
Use longs for time related constants
Added some new lines here and there
Fixed inverse of listener assignment
Fixed possible delay when TLS is not supported by the server
add LOG field to eliminate compile error
Disable failing Jetty SSE tests for now
Quieten a noisy debug line when downloading the block chain
Fixed bug on size against OMultiCollectionIterator
changed client network error heandling in case of IOException
Java Improve validation of valueRef on fields
Fix an error breaking the documentation generation
Fix HTML decoding bug reported by Find Bugs
A Finest log that printed all multimap values on add was reducing the performance as the value set was growing
Fix compile issues following rebase
Don t relayout based on a window that isn t visible
Add some logging when initizializing contact list of tester agent as test build fails
Fixed javadoc comments
Postponed Profile creation After time
Add setter for defaultPersistenceUnitRootLocation
Change some log level from info to debug
Fix reversed ShingleFilter constructor arguments
Add better docs for watched flag
Fix test failure due to change error message text
Fix ArrayReflection emulation
Deprecated Throwables propagateIfInstanceOf and propagateIfPossible
Effectively realise the project model when resolving a cross project native library dependency
Made RtspMethods and RtspResponseStatuses final
Avoid NPE if event don t has EventOrigin annotation
Bukkit Added shim method for setBlock foundation Block
avoid concurrent modification of merged data
Add console reporterto metrics enabled check
Remove redundant brackets
Removed unused code
Removing outdated TODO about streams
Fix processBulKReply against TCP fragmentation
Use a constant for the publishing extension name
Fixed missing format call for certificate authentication logging
Added better support for per database type object custom persisters
Add ActivityController launch which calls all the lifecycle methods that Android calls when launching an Activity and making it visible
properly throw exception
Fix a wrong ISE message
Fixed doc typo and removed warning
Fixed game breaking power bug
Add a OldestInflightDuration and OldestInflightExchangeId attribute to route MBeans
Added the copyright notice
getting literal value from parsed AST
Temporarily suppress flaky test LocationManagerProximityTest until test harness support can be rolled out
Fixed potentialStarter parse issue
Catch BadTokenException and continue in clearInsetofPreviousIme
Add TextView textSize as a ViewDebug for hierarchyviewer
Don t mix ant gradle compiler implementations when doing joint groovy java compilation
Improve Javadoc for HandlerAdapter and HandlerMapping
save and pick up sdk from default project
update the current recvoerd files size in peer recovery
added dimension service key to fetch dialog
Remove unsupported option O from ls command
remove isM2compatible from in shouldUseMavenMetadata as its always true
Remove v N A log output
RequestMappingHandlerAdapter properly invokes handler method in synchronizeOnSession mode again
Do not log messages with no body and no subject
Synchronize Execution complete so listeners will reflect current values
add list judge
Removed unused method
Added Override and improved Javadoc
Throw RuntimeException if a leader cannot be found for a partition
Fix source formatter
Make default audio capabilities public update extractor list
Fix the option default and add a line of doc
Make DefaultSecurityManagerProvider a singleton
Removed unused field
Deprecate getter and setter for unused collapsingEnabled property in Collapser Setter
Explicitly set Weld shutdown hook behavior in Camel CDI test runner
Fix x coordinate of the ACTION_UP event in TouchUtils drag method
unhide the feature for beta testing
remove unsed var pattern
New Add screenname to the activity notification title
Fixed bug reported in ML when a MAP is un marshalled to JSON
Added better javadoc
Make SimpleSessionDescription locale safe
Add getter for cullingArea in Group
fixing a test issue
moved AwaitsFix to class level
Was over nuking the response col by mistake
Fixed ArrayIndexOutOfBoundsException in SequenceModifier
Fix race in DeregisterTest
Returns the database name if it does not exists
save changes before previewing
Don t use p in javadoc
fixed search error
SourceFormatter Improve performance by moving XMLSourceProcessor to other thread
Added method to convert mm to px
Fixed a typo on the double quotes
Cleaning up AbstractInvocationHandler as the SpringBus property does not exist anymore
Fixed writing of the unknown byte at the wrong location
Improve the previously fixed byg
Refresh the log on external events
updated key types
Remove trailing spaces in filenames
jdk sdk rename refactoring
Fix the array data SmalideaMethod test
Fix incorrect condition for sub id check
Relax logging tests for CI
Match after to allow app filters to examine modify the request
isMonkeyTime false should execute normal MonkeyCalendar logic
Fixed sonortype reported issue
Consider other special chars
fix bug in early cutoff still faster
fix a bug in TachyonURI
Fix timing issue in integration test
Fixing possible race condition
reset main and minor parameters when handler is called again
don t use selection for find when newlines are included
Fix type warning
Ignore rest of ReaderWriterLock tests with intermittent failures
Ignoring exceptions thrown from chained callbacks
Made text flush
Fix Flashlight tile animation
keep periodicGraphUpdater from crashing when tomcat restarts
handle the case when flushCodec is called while reiniting the decoders
Allow components to have empty assets fields
Added an extra test for one of the find methods
TEST Fix offsets in BaseXContentTestCase testBinaryValueWithOffsetLength
changing method signature Status wasRetweetedByMe Status isRetweetedByMe
Dropped CVS additonal options since they do not work in new dialog anyway
Adding handling of Search Button for the jsPrompt
Fix code snippet
use only usr lib go for linux
update URL for Gradle documentation
Remove unnecessary warning when applying empty invocation policy
remove debug output
Renamed Asserter assertLowMemory to assertNotLowMemory
Colors methods count chart
Fixed checkstyle violations
add additional null check
Remove unnecessary call of persist for post processing after a timeout for a ejb timer
Add LayoutLibDelegate for new Bitmap method
Remove unneeded API compatibility check fallback exceptions
fix bug in changebounds with reparent mode
Made vagrant command executor more explicit about failed retries
Updated removeActiveAdmin documentation
Make RequestImpl toString also print params
Fix crash in Object defineProperties peephole optimization
Added missing comma in code snippet
Invalidate TextView on change of elegantTextHeight
Modified an error message
fixed entries in nav drawer
Fix UPDATE EDGE
Removed Ignore from one of the tests
Fixed Checkstyle error
Fixed incorrect tiles def
Revert remove no longer used method
Avoid reading the entire file into memory when hashing its contents
Revert the last modification
I like it when I can visit stops in a bidirectional manner
Fix possible NPE
Camel catalog should report missing javadoc option for components also
rename the class too
remove incorrect started check from isVersionedDirectory
Make sure the connection always closes no matter what
Fixed exception during stopping about concurrent access to hash map
add cluster name back to cluster state API
add missing test
Add license header
Changed method name to match Activity s method name
Fix possible string index out of bounds exception
Increase default reserved memory
Revert SourceFormatter Ignore files in modules when checking for unused variables for now to make the tests pass
Added default case for fixing findbugs problem
Stop IllegalFormatConversionException thrown if Mod has flagged client only or server only
Don t recycle accessibility event after sending
add getTimer method
Add count distinct tests
fix quality flaw
Fixed introduced crash
Removed unused imports
Replace dexter listener with an empty implementation to avoid memory leaks
add a day to make sure we don t have issues with timezones
Fix focus issues with dialpad in the call dialog
Relax the test
Don t use deprecated API in contact header widget
Fixed bug in opening dribbble shot URL when logged in
Log more information when Buck jettisons cache due to environment change
Make Gservices setting constants public
fixed a toc generating
Promote GuardedBy from experimental
DynamicPartitionPruning lacks a fast path exit for large IN queries Gopal V reviewed by Jesus Camacho Rodriguez
avoid redundant isDirectory call javadoc
Changed the XML plugin descriptor reader to read the plugin xml directly rather than awesome a relative path
Check against length of copy not length of original
Display the account name in preferences
read madvoc annotation result
not skip bad init
Validate a view only if it is visible and enabled
Handle overflow in StarTreeManager file system watch
Remove extraneous debug output from UserSubject
Fixed unit test
Remove origin header due to bad feedback
Fix javadoc typo for TypeLiteral class
check only iterators in Iterator equals
fix odd notification
Missing close cursor call
Use correct scheme protocol when generating clone URL
Removing a debug check when logging fstrim events
pass engine options to the constructor
Remove leftover forbidden suppression in ZD
fixed failing JMS test
Added some trace for int tests that are failing on CI machines
TEST only bump replicas if we have enough nodes in the cluster
Fix merge conflicts Use constants defined in BuildConfig java
changed AbsListView with ListView
Fix bug on SendKeyAction
Add a version of getInstructionIndexAtCodeOffset that defaults to exact
Moving checkSideEffects before transpilation
Fixed test error
after real connection closed closePoolableStatementError
add shard header with failures to flush operation
Incorporating review comments
Fixed crash in initialization of FileObserverService
add active display for physics debug view
Make sure the new Broadcaster i snot null
Improve getTimestamp documentation
Fixed typo in class name and changed double comparison
Ignore components which does not support mnemonics
Return an empty PluginList instead of null for the
Remove unused field
handle date conversion better
Make Dlugosz int packer null safe
Updated schema version
Removed an unnecessary empty line
Clarified javadoc for getCurrentSpinners in Solo and ViewFetcher
Fix a bug where right side drawers would incorrectly open close
Removed unused method
Add StreamSourceFrameChannel discard method that can be used to discard the whole frame
Fix termination related log message
Fixed JGLFW command key on Mac
Fix CME in EditorFactory
compare types by equals
Add ServletFilter UrlPattern getUrl
Fixed doc link for addUser etc
remove old typo
fixed hunspell test to clean up properly this time for realz
Removed unused code
Added default identity state to matrices
Grouped spacing blank lines wrapping panels to a single panel with tabs for each type of settings
added getParent method for CodeItem
add unit test for mismatch description of hasItems
Fix minor typo
Test awaits Lucene snapshot upgrade
Fix test when running under proxy
Fixed minor compilation errors
Corrected a non fatal exception if contentView has a parent already
Handle case where there are no GUI hud elements
Include the faulty URI in exception message
Log root cause of exception
fix broken integration test
Added a TODO
Remove hard coded Allow from OPTIONS
started compiler test cases for functions
Fixed resend error message to include exception
Increase external proxy test timeout
PartitionIteratingOperation toString included factory name
Fixed broken refactoring when cleaning up stack strack filter API
makes the observable warm not hot
Use shorter flavor names for cxx rules
Remove outdated TODOs
add new catalog at top of the list
Removed old commented code
removed unnecessary canceled call
QDigest throw on negative values and Long MAX_VALUE
use the new FreeQ refine queue instead of the old gridworks one
Rework batch mode buffering in websocket
Improved the error message for apk duplicated files
Fix import bug from refactoring
Updating unit tests of the annotation processor
Fix sample project for api changes
Fixed bug in PactString append
use project default charset for running ant script from within IDEA
fix GroupItems require special treatment for TOGGLE command as well
Reduce lwjgl backend performance
Move DeclaredGlobalExternsOnWindow before transpilation
Fixed a crash when viewing gtasks lists
Categorize expression analysis errors
Fixes a potential NPE in View
correct unit avoid overflow
Don t prohibit deletion of the changelist with name equal to Default
make JavaTypeHandler work equally in Java scratch files
Addressing review comment
Fixed an error the command parser that resulted it in handling commands it did not really handle
Removed debug print
Added stupididty exception for common issue with getMinecraftLogger
Moved cold fields to the end
fixed Stage clear didn t clear the focus
Fix issue when interpreting binary logical expression with null operands
remove spurious dependency
Fixed the Serialization of Subclassed Enumerations
provide a bit more diagnostics for internal groovyc error
skip empty server config
Prevent NPE on null session used to indicate ZAP is closing
Resolve a minor NPE problem for virtual properties
correctly reference foreign property value
Java Removed TODO
remove double transfer protection from simpleTransfer use parser
Make FRIENDS default audience rather than ONLY_ME
Rearranging image and video sources to improve loading sequence
Remove ThreadSafe annotation from OperatorContext and DriverContext
Fix oops on the saving of searches
shadows in hint autopopup update the size correctly
Fixed refresh of details fragment in landscape tablet when upload finishes
Don t define DEBUG for fastbuild
Specify the version number before serializing instance information
Fix new ConfigUtils handling of Path that does not exist
Fix racy tests as Caffeine is async by default
add another ctor for FunctionInfo
Display Home as Up
null for classLoader does not imply default loader
Moved the creation of the guiFont into a separate
removed replyAddress from sendAsyncresultFailure message
also add this support to the compiler
Adjust test to not worry about the sub deployments
Fixed potential problem with immutable points list in Line model
Add Smile annotation to BrokerServerView
Added error handling for invalid RDF parameters Faunus Titan Hadoop
remove unused constants
Fix typo just because
Fix delivery confirmation display
catches beacon config read error
Improve default table naming for entities
Also treat the trailing matches as wildcard matches
r m equals override
Run new VisibleGraphImpl
Fixed robots not picking up redstone ore and possibly others after GT ore fix
Fixed bug with context variable used in commands
default imports come first lowest priority
Replaced unnecessary inheritance with delegation
Categorized error for too many dots
Fix issue where latitude was used instead of longitude
Fix compilation problems by implementing stub methods
Put license header in correct spot
Add listener nodes to nodes list if sniff is true
Moved most exceptions in com mongodb package from driver compat to driver
Clarity about batching request
Remove empty return annotation
Don t move the fast scroll thumb with a list on overscroll
Call the correct method that removes the separator from main tool bar
moved TODOs to readme
Fix Javadoc formatting
revert removal of default grailsHome repo
fix IdentifierSplitter add possibility to split by
add method to return envelope param as human readable string
i hate this constructor
Code generate io vertx core AsyncResult methods in io vertx core Future
accept cast cases
Fix NPE when Maven model is unresolvable
Don t re parse config xml in onResume
avoid CCE when collecting variants in from import
Reverted change on logging aspect
Made Node getPosition final and fixed some whitespace in LocalWorld
Fix leak in SIM ready registrants
Adds footnote from bensigelman
Removed unused return type in ExecutorServiceProxy
added getTimouet to HttpRequest
Hide mark as un read and un flag in the message list context menu
add set and get of ItemAnimator
Added license header to PassCodeManager
Don t delegate dispatcher type to wrapped request as it may well be different
Fix lost singleton provider after force stopping user or package
removed unnecessary line of code
removed unused var
Removed unnecessary null checking
fix some minor javadoc errors in AccountManager
Fix usage of clob free on MsSQL
Set proper status when resetting ZQL
PsiJavaParserFacade no longer extends PsiParserFacade
allow navigation on depends config as well as test
Add exception handling to TranslationRequestHandler fix NullPointerException in AbstractBeamInferer
Fixed a problem where HttpTunnelingClientSocketChannel setInterestOps returns a wrong future
volatile should ensure double locking to work properly
Fixed routing of Ivy messages via our logging system
Fix a few small javadoc warnings
Don t log the failures to recreate an exception at the receiver at info level
Fix snippets in MavenPublication so that they configure artifacts and not tasks
Display track distance in save track option in settings
Added a helper method for path like env variables
Sent all existing enqueued invalidations
Fix more typos
Remove obsolete TODO
android revert getScreenWidth getScreenHeight cache
Clarify ItemTouchHelper and SimpleItemAnimator docs
CFRW no longer loses mutations
Don t show watchlist menu if not connected to trakt
fixed swingspy wrong png name
disable intention if nested if contains also elif else clause
Fix string resource
Fix Javadoc warnings
Changed exception that is thrown when a class cannot be loaded
Added missing iOS device
pre add new systrace TRACE_TAG_REACT_VIEW
Removing unused undoChanges method
do not log cleanup failure
fixed bug with services shutting down by itself
Fixing an integer overflow in getSleepTimeMs
Tweak liquidcontainer API for sidedness capability
don t replicate rows that don t exist yet
Only hide sensitive content when Keyguard is showing
Adds more comments
spew out compiler error message when kopi dies
Tame the monkey
Fix typo in FileCollection minus JavaDoc
Fixing app crash when clicking Menu or Back buttons while splashscreen is being shown
Also generify the zip type parsing from the JSON
Create context service for components that need it
Adds colorFluid Helper to FluidRenderer
add comment to Migration not applied log message
Use a ConcurrentMap thanks to Donghwan
add default breakpoints to new project
Fixed a bug in cloning that prevented a null from being usable
Recycling source bitmap after rotating and cropping
Added support for files ending in
Fixed embedded percolator benchmark
Allow right shifts
Add javadoc to Violation getCommitter
Fix too wide splitter
Added Any as fallback to expressions that has no guessable types
Fixed superfluous newlines in QueryCache accumulator package
corrected expected error message
add comment to simple concrete vertex
Fixed crash on tablets
fixed stupid NPE
Add tests to check returning null to pool
Try change task order in AppInitializer to restart NavigationService
Fixed test on windows
Fix oops when keeping alive a gracefully closed Comet connection
added comment on a possible protobuf message based in put row optimization
NewPropertyAction checks empty property keys
Enabled two Karaf component tests
setting backround color if image comes directly from cache
Add missing Override decorator
Deprecated NPT ignore Member added ignore Method overload as the new preferred method
add comment how to use the compactStyle
Change link to OrderingExplained to GitHub location
fixed duplicated test expectations
Drops extra Math floor
Fix doc link in InputDevice
Replaced void teleportTo Location with boolean teleport Location
Remove unused code
remove toString on request in both places
ensure AceRender icon destruction scoped to that editor
Remove unused import
Transform script into script instead of replacing all into
NTI Make functions that don t return infer a return type of TOP instead of BOTTOM
Remove unnecessary debug log entry
Added the correct annotations to MBeanTest
Check NPE condition
Fix some typos
fixed bogus release
Fix exc handling
remove null check
finish the same action that was started cleanup and generify
Fix minor grammatical error in Javadoc for Cache initialize
Reduced size of like scale animation to avoid clipping
removed system out
Added debug and info log messages that I found missing
Remove unused line from PhoneWindowManager
Don t run RemoveClassProperties with heuristic renaming
SourceFormatter Make the method format in JSPSourceProcessor recursive
exiting Rename mode moves focus to the Available Elements panel
properly save custom JRE path specified in run configuration
Fix dx checking on Windows
Fix reverse sorting when in sort by date mode
Implemented Component start
use different index and index type for TokuMX and vanilla MongoDB
add API function
Remove some unused code
lets try jpeg for a while
Fix bugs in scriptGroup
Fixed PutDesignTest putWasInserted
Adding link back to Firebase auth docs that include snippets from this file
Add defensive null checks
Delete extra spaces
add ActivityManager getMemoryClass
removed unnecessary cast
Use hidden API to validate APK chains
Remove FD optimization case for decodeStream
Do not show includes in dumb mode
Fix netty closure check
Remove Math min around PARALLEL_BACKEND_OPS
Turn off logspew
Remove obsolete final keywords
Remove deprecated upgrade path for relatedGroupId
Remove uneeded null check
I can t gen dbDoc from changeLog like this
Fixing compilation issues
Force update the art view when configuration changes
Added null check for checking if package is sealed
Filtering schemas fix generic driver
removed uneeded clases in GremlinFluentUtilityTest
Removed obsolete todo comment
Re throw the exception
Fix video view s time invalid situation
Temporarily increase timeout for devices with buggy driver
go back to haversine makes little practical difference
Fix crash in setAdditionalInputMethodSubtypes
Changed method to load UPOS data file
fixed up test case
properly persist accepted crypto targets
git remove unused constant
Include message history in stacktraces from error handler making it easier to know where the problem was
removed swing imports from cross platform code
Fixing issue where AppWidgetHost onProvidersChanged was not being called
add line highlighter not range if lambda body fully contains the current line
Avoid an IllegalArgumentException
jdk sdk rename refactoring
remove jalopy import line
Log the exception properly
rename setDivider to withDivider to stay conform to the rest of all method names
Disable ProxyControllerTestCase pending kabir s work
Remove System out
fixed javadoc references
reformat created groovy class
Fixed a minor typo in ConfigurationTest
Fix EeSubsystemTestCase EAP tests for community users
Fixing message reading on history loading and on incorrect zero counter
Changed the type parameter of the methods BreakPanelToolbarFactory setBreakRequest Boolean and setBreakResponse Boolean to boolean
delegate loadClass to super classloader before delegating to parent classloader
Ensure synchronized access to Flags guardLevels in CommandLineRunner
Fix docs build
added Polyline setVertices float
set default of disconnect to true
remove master password form
Get global naming resources working if naming is enabled
Fixed new resolve for augmented assignments
Fix FormField Option toXML to use correct element
grow the destination buffer more aggressively to avoid excessive array copying in escapeSlow
Remove deprecated methods
Add onHover events to TouchEvent
Forcing software in header button background ripples
ditched setRetainInstance in favor of good practices as the config change rotation in this case is not important
Disable CxxBinaryIntegrationTest testInferCxxBinaryWithDeps timing out in CI
Fix incorrect Activity startLockTask doc
Fixing some debug stuff
improve javadoc make constructor private
fixed stupid mistake comparing strings
Fixes missing targetClass variable
define the default focused component
Adds an onLoad method to Plugin
Remove unreachable code
Convert line separators when loading files with builtins
Suppressed unnecessary event object creation in OneToOneEncoder
Handle padding in end of carousel scroll limit
set log level to all
Disable strangely flaky test for now
sync engine Make eclipse compiler happy
fix quality flaw
do not run user actions when interrupted update
Fix Tabs disappearing
Delete unused method in TypeDescriptor
replaced unnecessary loop with an if statement
fixed NPE when cache folder is empty
Mute failing assertions in IndexWithShadowReplicasIT until fix
Remove contains method from data object
Throwing exception if hashed password is not supported
Improve pattern to detect unprintable characters
Added some JavaDoc
Fix WalletTemplate now that checkpoints are included
add gpx track default color
Fix broken max min pane buttons
Removes debug printing
Fixed BitmapLruCache sizeOf bug that was causing OOM errors with Volley
add explicit serialVersionUID to Token subclasses
Use correct Guava API in stress test client
Fixed the error message for json parsing error for sites users request
Removing unnecessary wait
Fix a compilation error
added support for empty arrays
Add principal in GlassfishRequestUpgradeStrategy
Temporarily ignore int test
Remove obsolete comment
Remove the need for a synthetic accessor method
Removed final identifier from AbstractInvokable getEnvironment
fixed an issue with missing material color when material has a non used texture
include address in operation result
Adjust performance timing due to new environment
We shouldn t add new line character here
don t discard unfinished messages that are not old
clarify text in join session button when editor is dirty
use implicit default
find tests in background
Fix bug with map markers coloring
Remove obsolete comments
Make LOGGING_SYSTEM_BEAN_NAME public
Fix PrivateDataManager getInstanceFor always returns null
try screen refresh
Fix Frame toString NA issue
remove pointless exception
Replace p with p in a Javadoc comment
when A groovy depends on B groovy make change to A and make doesn t fail
Fixes compile error due to stale imports
Use selected params
Fix minor Java incompatibility
hide the general data source command for the moment
Fix logging statement
Java Fix warning
do not recreate tree when exclude action is invoked
Fix minor issues using Filters programatically
Change Headers Builder to be final
Pass old and new volume to CEC without rounding up
Add missing comments lic header
also remove force option from logger trace
Fix typo in TopImagesListActivity
Fix a bug on SwitchToLastInputMethod
Make springIntegrationPublicMetrics conditional on bean by name not type
Made exception public to allow reflective use
added support for typing notifications
Fix loadMetadata initialization
Fix Finish button not working on last slide
create namespace declaration reformats whole spring xml file
Boo I missed the private field in the last commit
Add some useful imports to Mvc scripts
Added varargs method for stringContainsInOrder
Flip chained method order
hide call panel by default
fix no access to LastAnimationListener from children in different packages
Add a few unit tests for constant folding for problematic cases
Fix END_DOCUMENT handling in XMPPTCPConnection
Fix javadocs based on recent case consistency change
better for commit dialog resizing to be fixed
Fix javadoc typos
Fix english in exception message
Handle write errors when the client drops the connection in the same way read errors are handled
Fix a silly bug
by default ignore POS tags
avoid NullPointException on launch task
Use correct generics for TCP_KEEPIDLE TCP_KEEPINTVL and TCP_KEEPCNT in EpollChannelOption
set reuseaddr on tcp sockets
Change propertyMissingException to return exception instead of throw it
Add a convenience method for getting the media provider version
exposing db and coll stats and adding ability to check if collection is capped
Add throwable cause to Table PartitionAlreadyExistsException
Adds shutdown notification in log sets level of exceptions during shutdown to warn
Add GraphController ServiceProvider annotation
Add more tag ids
fixed intercept adjustement according to set prior probability adjust all submodels
Remove volatile where not needed
Fix POJO Preview exception
Don t require permissions for UsbManager getCurrentAccessory
Use injected AnnotationRuleParser
Add not null precondition check in BulkRequest
now shows more helpful mapping exception
fixing bug in GraphHopperWeb
Granted creation of a new client object with a new socket when the network connection is lost
Made SelectBox account for list background size
using TimeUnit instead of Thread sleep because it s more sexy
groupBy having does not make sense for entities
Fix line length violation in SetProcessor
Remove Nullable from PreDexMerge getSecondaryDexDirectories
Trivial align comment
Fix conversion from nanoseconds so that it actually targets milliseconds
Fixed visit of declaration expressions in trait
try action up
do not show progress messages when there is nothing to do
add missing error logging
Fix a couple of tests which claimed to deal with pre increment pre decrement but were actually the post versions
Fix being able to place items in architect table output
Fix graceful shutdown aborted log message in ThreadPoolTaskRunner
Use a more precise diagnostic position for MethodCanBeStatic
Fixing a bug where applied armeture has no skeleton attached
Fix NPE in NewViolationsDecorator
Added upgrade statement
Add param Javadoc to silence an Eclipse warning
Ensure ViewRootImpl setAccessibilityFocus doesn t crash when reentrant
Fixed SQLite tests
Fixed concurrenct modification exception potentially thrown on high load when performing onCompletion
Fix method name typo
Closing the flusher instead of just disconnecting after a GO_AWAY
highlight speed search result in usage tree
Fix RevoluteJoint getLocalAnchorB
Change constructor visibility
Ignore Pong messages in StompSubProtocolHandler
dump a memory snapshot if PsiEventsTest fails
Add annotations for Table and Column
Fix timeline background
minimize softkeyboard only if it has been in fullscreen mode
JetVariableDescriptor is a named declaration
remove extra import
Supports missing Load Balancing policies
Fix line wrapping
Fix AbstractMethodExtractor to properly deal with packageless classes
add XML validation for batch subsystem
Ctrl C in Keymap settings should not copy internal toString
Fixed broken link
Remove Escape key mapping from InputManager defined in SimpleApplication when using Android platforms
add missing checkResultByFile
Fix auto boxing warnings
modified decPattern a little
Skip the testMetricUsage test on Windows
Fix ArrayIndexOutOfBoundsException when using autoplay
Fixed memory leak
Move org sonar api measures AverageComplexityFormula to sonar deprecated
Make code uglier to keep PMD happy
Remove obsolete references to AliasFor in Javadoc for AnnotationAttributes
Added missing Stratosphere header
Fixed infinite recursive loop
delete unused imports
Ignore non grouped tribe settings
made test runnable with other default locale
Added one more Props test
removed ALREADY_DRAWING_ERROR and NO_BEGIN_DRAW_ERROR
add a toString method for flickr photo objects
Fixed CS error of JaxbDataFormat
Send an unavailable presence to the user that left the room
Log Selector instance when Selector needs to be rebuild
Properly stop the services we create on our own
ignore ctor errors in stuctures manually validated by wolftobias
update javadocs in DelayedClientTransport2
Changed method curly brace placement
move some JS modules to debug only
add toString s to help when debugging custom HystrixPropertiesStrategy classes
To fix unit test on slower servers
Tweak doc comment
Fix benchmark timing
Fixed sample code now freeing autocomplete predictions buffer after reading
remove adding timstamp in recording filename as it screws up ingest and processing
Added a loop that retries getLocationOnScreen until it gets a value
fix emitter compile error
Add SuppressWarnings serial to an Exception
Fix test following ResolvableType hashcode change
Fix Facade names
Stop debug process
do not disable run goal action when leaving maven tool window
Rx uses io scheduler
Add some print code to investigate the test error in bamboo
Add a note that the output s ordering is still missing
Add ability to clear out customParameters
Remove an unused variable from the WifiReceiver class
Removed unused random
Enable Treat test for EclipseLink
Set browser download version from
Fix join re analysis to consider coercions
JDO rollback can throw pointless exceptions Sergey Shelukhin reviewed by Ashutosh Chauhan
Prevented IllegalArgumentException if service was not registered
Create mock connection if the node is not shut down
Put printing of every record as debug output not info output
Added logging when removing orphan constraint indexes on startup
vcs log fix log text filter for strings containing or characters
Ignore flaky test
yole Always use system L F in startup dialogs
Handle structural nodes in findNode
skip conflict signature checks in case of one conflict
Finished BLNInference apply
Removed System out println used for debugging
make the client version info logged only when online
Fix accessibility issues
Make Push condition inside call intention available on entire conditional expression
Disable scratch in PyCharm
Don t display usage twice when help is used
Move action bar and view init to separate methods
Improve message for mismatched insert column types
Implement toString for hll
Clarifying which receiver method to call
Fix Eclipse warnings remove unused code
Usage view should not provide navigatable if one is not valid thus not capable to navigate
add heap engien test
Restores tracking notifications detail view
Bleeding Added Block breakNaturally and Block getDrops
DEFINER remove fix
downloading files do account specific dir to avoid disambiguity
update excluded files
fixed doc typo
do not create dialog in headless mode
Fixed race condition when closing connections
UDF shouldn t log errors at ERROR change to DEBUG
Add correct type for view
Disable those tests as they fail as soon as they are ran globally but always pass when executed single
fixed two CS errors in camel core
do not save configs with meaningless empty data
Add alias information to the node
Throwing exception when instantiation fails
fix compile error
remove unnecessary calls to invalidate
We don t have resource leaks in interceptor crashes anymore
Removing debugging change from unit test
Fix the NPE that was causing AdminServiceBasicTest to fail
Allow loopback traffic in lockdown mode
Skip unreleased versions in RestoreBackwardsCompatIT
Remove unused imports
fixed scrolling delays
Specify UserAgent for ManifestFetcher in HlsRendererBuilder
Reduce field visibility
add support for cleaning JAVA SOURCE in Oracle DB
RefreshBlogContentTask signature changed fix tests in ApiHelperTest
Dump rel type creation fail stacktrace
removed breaking lines
fixed name of property key containing invalid character
add deprecation warning to recid reuse
Fix intermittent failure in endpoint publish
Removed system out println
Avoid potential NPE in createIterator if delimier is null
Clarify JavaDoc of WebSettings setAllowFileAccessFromFileURLs
Fixed selections when text doesn t fit
Add intent extra for determining if apps are system apps
add max argument to the pump call to only pump length bytes
Fix theme change not applying to ShowsActivity immediately
handle case where intent getData is null
Fixed crash when a folder is deleted while some of its contained files is downloading
Add DEMOTED and UNDEMOTE TO ContactsContract
Fix bug of throwing error when saving model with none field
Add NotNull annotation
Cli doesn t call getBroadcastAddress needlessly avoids loading yaml
FK create fix quote source column names
erasing the first backtick could erase the pair
adds log and warn methods
Turn doc links to non static inner classes into code because javadoc doesn t expose non static inner classes
Fix typo in Javadoc for TestContextBootstrapper
tab enter in hint mode should choose the first item
Beeline s silent option should suppress query from being echoed when running with f option Naveen via Xuefu
Not properly functioning routes plugin
Fixing a flaky test with ClassLoader voodoo
Don t print empty lines to SimpleConsole
Fix missing problem
Fixed NPE on destroy of invalid Servlet error
Fix doc image refs
invokeLater s in AbstractTreeUi caused running tasks on disposed tree
skip collection conversion early if empty
remove merge remnants
Reduce cache memory usage for classes with no annotations
Don t keep dead window with borrowed app token visible
allow devs to turn off charging notifications
Changed to WARN log for non internal camel convertions
Switch SSTableDeletingTask failingTasks to a ConcurrentLinkedQueue
allow cleanup to echo rows again
Don t store externally mutable object in Getopt argv findbugs
StorageBasedAuthorizationProvider masks lower level exception with IllegalStateException Eugene Koifman via Ashutosh Chauhan
Fix FindBugs warning
Fix ApplicationInfo copy ctor
Fix exchange rescheduling logic
revert undo timeout to a more reasonable value
Fixed wrong type of parameter in
Fix off by one error in parseLocaleString
Updated TODO comments
Make MemoryPoolInfo final
improve exception message
Fix resource leakage in example
avoid image disappearance after upload
Fixed Servlet flush
Fixed import error
Avoid downloaded equinox for each test
If we re doing a folder involved search and an account has no folders
Shows contact address for contacts with display name in call conference and call transfer windows
Cleaned up some timing tests
avoiding stub to AST switch safe
Rewrite PN type c as comment
Fix compile failure from bad merge after renaming of
Convert println to logging
Removed unused code
Add constructor GenericLocation Coordinate
add setAlarmHandler for otter
Add human error message
Implement audio video extension
Fix an NPE when launching an activity that s not found
Add DIV to list of allowed tokens in MagicNumber check
Fix dictionary data lookup in SpanishVerbStripper
fixed refresh problems
remove unused imports
remove tests for now
Add API to allow plugins to request players switch to a texture pack
add missing license headers
Fix bug with AbsListView position scrolling
Give the lock a bit more time
avoid null characters in generated code
disable login requirement for AllowAllAuthenticator
Fix print statements
Not degrading quality when calling smooth again
Added Player get setListName to change the name of the player on the player list
Include task name in cache key log message
Suppress some invalid compiler warnings
Fixed UDP connector disconnect
corrects error code
Fix unnecessary relayouts
added default animation to Container
Add NO OP for pong messages to handle unsolicited pongs
Don t whitelist routes when recycling them
Fix potential NPE on getting Markdown content
Disable auto away status while on do not disturb
Made the Layout leading
Clarifying arbitrary choice to return BEFORE for equal vector clocks
Added few missing values that were omitted while writing data to parcel
fix build breakage
don t paint text fields rect in case of graphite
fixed an NPE exception that occured during flat textures merging
Add a new comparison result type for when neither side has it
Do not generate classpath index if nothing is changed
Fix doc breakage
don t access indices in dumb mode
undo previous change
Fixed busted filename name
Fixed the unit test error of camel osgi
Fixed regression in moving down to the last empty line that ends with n
Created ContextAreaBuilder to encapsulate access to action pane
Renamed OnBeanCondition java to OnMissingBeanCondition java
Removed selectAid from IsoDep
git Don t report git process start error during unit test disposal
Only skip BuildConfig generation for dependencies of an AAR project
skip separators when adding actions from action group
Fixed wrong null check
use the new short method syntax inside the IconicsFactory
add toJavaObject method
Improve the name of the system secure client
reverted myScheme null in release
Add a test for the standalone elytron xml generated configuration file
log Don t schedule a refresh task if there is nothing to refresh
eliminate eclipse warning for unused commands
Make nextSequence a local variable again
Remove unnecessary public modifiers
Use strict policy by default
fixed rid copy on indentity change
Increase max parallel replica sync limit in partition lost listener stress tests
Change AsyncParser getType to Type and not class
returning editor to previous state after test
Fix typo in Registry javadoc
Send UpdateLock broadcasts to manifest receivers
npe in replaceMnemonicAmpersand allow null arguments
Removing test noise
FileStatusManagerImpl add NotNull annotation for added listener
Expand contract for Futures makeChecked
In push active branches dialog added possiblity to push if there is no roots with errors
Add convenience methods for setting airplane wifi mode
Disable old unit tests
Remove debug printout
removed topology level stat
fix expected object type
moved flushing into filter so that updates are committed if no errors occur
Fix new IP prefix restrictions
added check if index name is missing
Remove leap year code as partials cannot determine this accurately
Fix bug of concat operation in some cases
made the test safer
Add singleton MissingFileSnapshot
Fixed bug on type conversion on index lookup
Change EmptyArray System identityHashCode to Object hashCode
call mergeInit in the serial merge scheduler case to get proper stats for it
do not replace invalid duplicates
Enhance the VirtualModelTest to ensure that persisting a virtual table model from a template with setId works
Made popup menu actions on contexts for Spidering and Flagging as Form based Auth Request active in History tab
Revert previous modifications
Document maps don t allow null anywhere
Fix up crash around weak ref usage
remove legacy way of specifying TX manager that conflicts with JTA_PLATFORM
Reduce memory requirements for MockWebServerTest disconnectHalfway
remove debug output
Fix a minor bug with respect to logging information
Also call Lookup refreshDefault in SmackAndroid
Corrected the typo
Disable failing test timebomb
added doc on the canScrollHorizontally method
Add a guard against NullPointerException in RestrictedLockUtils
does not belong in license header
Fix refresh bug
Shorten our menus by one more obsolete item
Only throw exception in EBT helper if parent bounds are untouched
Fix TwoDimTable switch
Have to set the fallback URL
update Ctrl P location size after controllers modifications
remove unused code
Made fix to prevent null pointer exception on OAuth2Authorization toString when token has not been built yet
Make native_release methods in AudioTrack and AudioRecord JNI callable
Fix synthetic shadow in MixinCommandExecuteAtSender
Set live region property on AccessibilityNodeInfo
Deprecated MongoInterruptedException constructors
Increased code coverage of ManagementCenterService by increasing log level
Added missing processInstanceBusinessKeyLike parameter
fixed a bug where the program does not sweep latest version consistent keys correctly
Removed commons lang dependency
Make release behaves the same way as other part of media framework
Add calls to loseFocus and gainFocus when activity pauses and resumes
Added missing dot in javadoc
Remove invalid todo
Fixed potential bug in not recycling bitmaps that I spied from BS
Revert the work around for Frame deletion now that it s fast
another fixing attempt
de add missing examples
create IocVfs initWithoutPassword Context for when no password is set
Fixed a array out of bounds in ViewDragHelper
Fixed NPE when changing class in TriView
add final to EP_NAME
Load a truststore if client certificate authentication is enabled
Removed unused variable
Add javadoc to HeaderViewListener constants
Added ability to saddle a pig to the API
Websocket write implementation now uses SocketWrapperBase write boolean ByteBuffer
fix another NPE
Add Z suffix to override ws agent conf on RHEL systems
Do not report usage data for internet storage
Fixed leading zeroes issue in HMS Picker
Remove unused code
Increase the level of JUL logging calls in the tests to try to fix CI
Add missing last padding Comment
TEST when the test fail have the exception message as the reason
Polished the javadoc of camel cxf DataFormat
Add type annotation checkbox naming
Added screen tagging to all currently instrumented activities
Fixing StreamResequencer doStop stopping services correctly
remove debug sleep statement
removed dead code
Missed an import
avoid NPE if editor event comes at an unexpected moment
revert pool size change since some groupon tests now hang because of it
Set fill color if drawable is not supported
updated Javadoc to tell users to subclass AbstractModule instead of directly implement Module
add a type check before doing a cast
add quotes around name
never attach null functor
Stop execution when we get destroyed
Fixed small tiny error that no one reported yet
add function isCurrentlyRunning int taskId
Fix the test of ManagementExampleTest
use clojure lang Util classOf instead of getClass to avoid NPE in case nil inside of constantType
Fix NPE when no FMLAT is defined
Add Family Guy info
Set isChange to true when calling deleteRange from ctrl w handler
add and as a cherry on a cake
oops forgot these
Reduce maximum number of concurrent requests in BulkProcessorIT
Remove use of operator in status hash table
add missing copyright
added javadoc comments
changed protocol name for ssl
save tile to correct place
Intercept HttpServletResponseWrapper setStatus int String to fix response status tracking
Fixed a bug where DefaultChannelFuture isSuccess returns true even if the future is not done yet
fix incorrect import
createSmartList uses THashMap
Still use ro monkey
remove indetermined progressbar until it wont be fixed in actionbarsherlock
Add a returns clause to the matchesInterruptionFilter javadoc
Fix stress tests
Updated Realm sample
Check connection errors more reliably in CqlRecordWriter
Make node executable for all users
added delete with hadler test for rest
Modifies XML output to be handled the same for a native java array as well as a JSONArray
don t send statistics about default setting value
add a turn reluctance for weighing turn costs
Remove unused variable
Hide useless error in error checker
Removed usefulness WAR in log
Throw exception if not enough points
Make Printer constructor public
Bleeding Help command should properly wrap command text at least for english
Renamed start point
prevent another IndexOutOfBounds exception
Disassociate imageview from any pending downloads on set
ORC ETLSplitStrategy should use thread pool when computing splits Rajesh Balamohan reviewed by Sergey Shelukhin
Extended testPrototypeCreationWithOverriddenAutowiredPropertiesIsFastEnough s deadline since that test repeatedly failed on the CI server
Reset the visibility binding properly when the in game state is left
Fix up some conflicts between ccomp and parataxis
Test the database connection more thoroughly
Add a missing javadoc tag to LaunchConfig
add map size to exception message
Fixed bug when turning tablet with no file in the right fragment
Adds startup banner to Development Server
Fix local variable reference detection
Fix errorous test
jmp code palette changed a normalize to normalizeLocal
Rename static DiscoveryNode ingestNode Settings to isIngestNode
Added a little bit more context to some info level messages
ensure temp test unicode named library deleted
make notifiaction plugin map a concurrent one to safegaurd against concurrent access edit
Backs out ensureUDFContext from getContextProperties
Minor Change login fail logging
Remove TextUtils join Iterable CharSequence
Refine AbsListView transcript mode behavior
Add build dependency on binary spec instead of build task of the binary spec
Fix warnings about deprecated methods for DelegatingMetaClass ProxyMetaClass and MockProxyMetaClass
Revert Fix parentheses in logical expression for ignoring const or columns with high N As
Fix failing test
Fixes call transfer to a filtered contact
Expose scheduling time of a job
Ensure compatibility with the Android gradle plugin
Use proper thread names for REST API execution handlers
Add the ability to pass a KeyStoreParameters Object to the DefaultKeyAccessor
changed gzip default to match webdefault xml
Add a java property for ctags for junit
Removed deprecated methods
determine fileOrDir copy output kind based on the content at the file path
GraphServiceBeanImpl always returns the same graph even with routerId
Make sure test fixtures clean up GradleLauncher properly
Remove hack for testing
Don t save signature to identity header if identity doesn t use a signature
Changed GradleBuild to extend Model
Fixed unit test display creation on my linux pc
disable fork mode if module was chosen successfully
Trace current active requests info
Java Close PositionReporters when a connection closes
Added comment header
remove unnecessary transient
don t allow renaming nodes not declared in sketch
add node setting to send SegmentInfos debug output to System out
Reordered plugin registration on startup so that distributed mode OrientDB also creates databases specified in server config file
Added int to ShadowBundle
fixed typing after
SelectedValueComparator defensively handles null values in exhaustiveCompare
Moved org bukkit Vector to org bukkit util
Add method to check if the action produces any outputs
Update the docs to clarify the ordering constraints
update comments to reflect new Stage ANTIENTROPY name
Handle BadTokenException exception more reliably
Reduce garbage in AbstractUnsafe flush0
Ignored test case
remove unused code
Fixed isPackage check for module files
don t cache count queries
Install button not active for incompatible
provide title in the assertion about empty content
Fix inconsistency with selfPreservation check and how it s reported as servo metrics
AnnotationsToHTML needs Graph level DEBUG
Fix metric name
Karma auto rerun tests shouldn t activate Run toolwindow
Fix wrong map being used for put
Fixed binder intent
Temporary ignore test
Added a comment about method signatures
activating the disabled tests as we have a new cxf
Updating EditTextUtils getText method instead of creating new one
Uncommenting temporarily commented out code in VertexBufferObject
Replace questionable logic in PathTypeCoercer
Moved the spawn egg entry in ItemType further up
don t process methods once a suitable variable has been found
Promote JUnitAmbiguousTestClass to MATURE
Fix NPE on non windows platforms
Remove unused import
TEST use length norm as the tie breaker in BlendedTermQueryTest
Add error checks to ensure switchToBubbleView is not called when there are no active tabs
Add constructor that does not take prefix
o Do not try to reset input streams that do not support mark
Fix typo in TtsSpan
creating a copy of the projection matrix so it s not altered by calling unproject function
Fixed a bug where the indication text could get too high
ensure paste events forwarded to RCompletionManager
Fix flaky test
avoid toast message when Xposed is not installed
Add getApplicationContext to the mock implementation
Use empty border for the viewport too
Fix incorrect scheduler test that sometimes produces a deadlock
Remove unused code
Fixed error in AfcClientTest testWriteSpeed
use a different package for reflectors
Fix JOINs that have ORDER BY rand
fix failing test
removed hop in RT nth caused head retention
Fix HTML entity
Remove static modifier from KeySetManagerService issuedIds
Use very light grey instead of white for message text in dark theme
Fixed wrong exception messages in AddAutoIncrementChange
Improve alarm manager docs
BeanDefinitions return isSingleton true by default again for CXF compatibility
Missing JetElementImplStub getReferences and getReference implementation
Avoid double spaces in inspection navigation panel
Handle null resolveInfo crash in ContentView
addressed gpang s comments
Remove unused methods
Keep screen on when playing video
don t vertically resize image line widget when collapsed
correctly test for R indentifer characters in input handler
Fix logic error
changing the locale from US to default
disable automatic foreground enabler
Temporarily disable overlay text bubble timeout
Fixing infinite loop in BitmapDocIdIterator
use a pattern with name information
Bump timeout if setRequestTimeout is called
removed unused method
Added dispatcher invocations
Remove google talk from error messages
Add note about getCanonicalFile check and fix IdleSession test in test hash sessions
Fix NPE in GradientDrawable constructor
Removed comments about exceptions which are no longer thrown
moved test into ParserErrorMessageTests
Fixed failing test
Fix HTML formatting in android os AsyncTask
Add position accessor to client Image
replaced StringUtils import
improve usage message thanks to Ruud Baars
Revert foreach for better performance in java
Remove extraneous Quantiles from RequestServer
Fixes a bug that would cause some characters to be lost while parsing template text
Parsing qualified type names should return class types for instances
Renamed PushTest PushCacheFilterTest
Sanitize input to isRemappedClass to use as a package seperator like the srg files
Fix ErrorManager ignoring error code in messages
Add a comment about who wrote it
Remove undefined properties java api
Use a BufferedOutputStream to write cache entries
Remove unnecessary string concatenation
Moving PolymerPass before the early transpilation
body changed to BODY in unit test
Modify PutDataPointRpc java to accept millisecond timestamps with a period as
Fix update all
Add simple exception message for cache director creation
Only write interleaving no tx separate pool if defined
Fixed invalidationMessageBatchEnabled property initialization
Remove dead store to environmentConfig
We are not using this private method so we can get rid of it
apply review comments from bleskes
leave filter in place
Refine LocalJava javadoc
remove javadoc warning
add unit test to NetworkUtils
Flushes the index updater after all updates have been applied
Remove unused code
fixed possible NPE
Fix inconsistencies in javadocs
when creating Java Command Line Application module gets CommandLine name regardless of settings
Remove unjustified assertion
Updated javadocs documentation
Synchronize creation and use of thread local looper to avoid null pointer exceptions
Restore status bar on phones
Fixed links in MipmapDrawable documentation
fie AlreadyDisposed exception
rename name key
added replaceResolveMode to CallCandidateResolutionContext
remove redundant fetch result initialization
Fix a bogus comment in checkMerkleBranch
fixed possible SQL error
changed access modifiers
Do not force lower case after colon
Needed null check
Correctly update the source of recurring subtree change accessibility events
Removed unnecessary exception
Disable don t send messages in the outbox if they re flagged users
Migrate DATA_ACTIVITY_TIMEOUT_MOBILE WIFI from Settings Secure to Settings Global
introduce acceptability for both lambda and method references
add getStringInfo to help deal with null strings
Hard code the request log locale
remove return statements if there are no side effects only
DbSrcFs fix layout
Adds comments on GitHubApi file
Handle invalid item type
Fixed missing opening brace in FacebookDialog page link
use correct snapshot build number
Fix access to poller registration
Removed unused imports
Refactored tests for quantified predicates out into their own test case
Removed superflous printlns
made action on error of the stream writer exec output handle configurable
Properly set the viewport for WebView when using GL
Marking Slovenian as complete
Handle EntityNotFoundException insted of re throwing
Removed the unused import of RabbitMQConsumer
Added new method to retrieve a property descriptor by value
remove old tests
Reverted change reading TTL from edge label type in ImplicitKey
fixed bug on parsing of nested sub query as target in TRAVERSE
Fixed misplacement of cancel X button for Face Unlock
fixed small thing in AndroidMusic
Fixed rogue character in a file
Fix Layout isRtlCharAt
Allow null child view when checking whether divider should draw
add better exception logging
Display Home as Up on UploadActivity
add CamelHttpBaseUri header constant to Exchange java
Increased BackupTest testBackupPutWhenOwnerNodeDead test timeout
Deprecate MLP ConcatenateVectorsJob and ConcatenateVectorsReducer in the codebase
Fixed last patch to support Numbers on format
Remove method specific javadoc
Retain expanded state across violent updates
recheck module element when its root model is changed via api
remove premature optimization and beg
add PLANNED to Status enum
Make public constant final
Removed unnecessary explicit locale setting for svn command line client for mac see EnvironmentUtil getEnvironmentMap for details
Put model into DKV after setting its cv error
added more conditions to any response setting
ReliableTopStressTest didn t make use of the correct ringbuffer
prevent base neural network weights from exploding on backprop
PreferenceActivity should not leave message in looper when destroyed
Rename PluginModule registerConfigBeans to addConfigBeans
Removed y inversion from screenXYZ methods
Added empty constructor for Camera for serialization
en set default language variant to en GB
Fix swapped LAC CID in card inflater
Do not set indeterminate state on progress bars
always use http proxy for http requests
Fixed a misaligned method call
Fixed possible NPE
do not load image file content into vfs for indexing use RandomAccessFile with index independent on content
Add vvm type for t mobile
Removes redundant code
Use constant instead of string literals
Fix executor propagation in CallOptions
Ignore failures when closing war files in tests
fixed possible NPE
Fixed Double and Float equals to handle objects with a wrong type
Disable the test until it works on all platform
Add AccessLog interface to JDBCAccessLog
Make getAvailability always return true
Remove an unused member in SampleMediaRouteProvider
Populating accessibility node info with supported actions
Remove unnecessary parentheses in test code
remove empty directory
Setting SSL tests to Ignore till we implement the SSL bits again
adapt name for destroyByteBuffer
Cancel scheduler tasks onDisable to prevent nag warnings
made more inlinable
Fix AttachmentProcessorFactoryTests to only check for existing fields
Remove check for missing parent calls
Fix the property name to respect the other related classLoader properties declaration
Fix multiple index reading in readOneSegment SegmentInfoProvider
Fix edge slices import in Processor
Comment detail now only requests the post associated with the comment if valid
Changed water to be considered a pass through block
Fix bug in Timestamps MICRO sleepUntil
Camel component docs now include a human readable title for the component names
Fixed highest cp
Allow Checkstyle s unused imports test to be used with the o a catalina authenticator package
Fix loading of extensions which extend abstract classes
TEST return the correct transport instance in mock transport
Throw an IllegalStateException when the folder already exists
Added semi transparency to Bitmap borders
Fix Data Layer asset key and edge case in Content Provider
Fix bug found by FindBugs
Added smarts to save state since is still needed for this sample
Add space to Portaudio Combos and remove background of frame rate checkbox
Added upside down stairs to cycle
Fixed typos in api config filters
removed not needed code as init does the same
ninja use javax annotation instead of avro
avoids BinarayExpressions for parameters with default values
Make checkstyle happy
wiped out legacy tests
Text processing stop showing an empty toast
turn restrictions configurable
Set FLAG_SHOW_LIGHTS if either on or off is nonzero
Added the connection s hashCode to toString
Remove an unused context from a private function
Server dumpStdErr throws exception if server is stopping
fixed minor javadoc errors
Allow subclasses to add their own custom GrantedAuthority s
remove useless trim
Updates the documentation for adding a texture to reflect the intent
Revert Re add performance optimization in View
Reverting internal mode state in tearDown of JetPsiChecker tests
deprecate a method not used anymore
Removing author information from test related files
Added support for passing the optional flag no locals to dx
Fix couple of tests to work when FileSystem access is available Jason Dere reviewed by Thejas Nair
optimize bool filter for single must clause or single should clause
removed unneeded log
Fix grammar and update link in Javadoc for SimpleThreadScope
Actually fixed the setSpatial null bug now
Fix non optimal route calculation
remove java awt as a default import in the StandardTypeLocator
restore analyze stacktrace
prevent bitmap auto scaling
Moved daemon hygiene action in the chain
fix outdated javadoc completion is performed in a read action
Stop listening for WiFi interface status changes
halide Fix bug in HalideLibrary getNativeLinkableInput
Add a delay before collecting memory information
rename loadDeviceOwner to loadOwners
id based equals hashCode generation
Fixed NPE in UpdatesIntexFragment
add a reset to default value on a remote network test
Added a main to SimpleTest thanks Lynn Allan
disable attach detector in headless and unit test modes
Fixed java util MissingFormatArgumentException for missing format specifier arguments
Use a private I O thread pool for failure detector
Give unit test params a payment protocol ID
Changed stripPunct function to use a pre compiled regex instead of compiling it every time it is getting called
Fixes an application freeze when PulseAudio is disabled
make MAX_ENTRIES private
Fix broken links
Add netrc support to GitCloneFunction
Don t use the unused includes attribute when adding server group
Remove useless TODO
Fix typo in targets detect test changes option
update one comment
Stats day detail no longer ignores back arrow icon
Switch the sort order code around so it saves when reversing sort with a keyboard
Fixed CustomProperty to support property containers with multiple lines
Removed unnecessary cast
Fix remote create method calls
adding serde methods for intermediate aggregation object to ComplexMetricSerde
Don t disallow ZIP by default
Fix checkstyle problems with glusterfs module
bring back show outline shortcut
Don t edit the generated files at all makes regeneration simpler
Fix javadoc warnings
Make SubGraphExporter always output numbers with English locale
Adjust more Pre Alpha Alpha
Fixed local region cache ttl check
Remove Timeout on Tests
fixed test for linux
Changed responseContainer docs
Include globs in getIgnorePaths
comment out oval line in example for now
Remove unused sameCall method
Stop the placeholder animation on errors
Adding Uv Index property to Atmosphere class
Fix overlapping of text locations with same start
fix quality flaw exception test
Added Stratosphere Header to PlanConfiguration
Fix approximate count double test
Fixed test cases
Removed doTest char since it was erroneous
Add missing Override to method addContextPanelFactory
improve exception message
Update display metrics when updating configuration
remove empty Constructor which resulted anyway always in a NPE
Doesn t include the cause since that Exception class may not exist
add ability of use TransitionSet of Transitions with different duration
Remove incorrect test comment about ALLOW FILTERING
added boolean serialization test
Fixed bug in ReaderBlog setFeedUrl
Fix arguments order in DocumentsContract moveDocument
PartitionManager should commit latest offset before close
Fix crash caused by accessibility being turned on and using a Modal
Add gwt module definition only when html included
Fix NPE when null BackupObserver is passed into BackupManager requestBackup
change some connection related log messages from INFO to DEBUG
Adding missing passthrough of task affiliate id
Make sure we times out on connect operation when the connect operation occurs on the calling thread
Fixed formatting in usage message in AppCompiler
fixed bug in ASeq toArray Object
Ignore TEMPLATE html in LayoutTest directories
Fix spacing for Struts2GuicePluginModule
IDE is not recognizing android R anim
Updating the javadoc comments
android remove final
Fixed performance value printout as recommended by indianajohn
add DruidDataSourceFactory getObjectInstance testcase test null branche
Add swagger module on ws agent
inspections settings manage button ui updated
Removed debug trace
Offer API isEmpty to check if listeners have been added
hide move param action if not available
make LayoutManagerDelegate class public
Remove unused getter
Revert Run cleanup before end actions of ViewPropertyAnimator
Updated dependency bank
Renamed Environment SKYLANDS to THE_END
Removed a System out
don t stopAsync if the peergroup isn t running and update the class description
Possibly fixed bug with incorrect package remapping of android attributes
Sets default value of enableGeolocation to true
Fixed delete mapping to return acknowledged false when ack times out
added DefaultCurved and tested both Default and DefaultCurved presets
Fix quality flaws
Flux can now handle IStatefulBolts
Stop scroll when layout manager is replaced
Java code style help topic ID
Introduce Parameter Refactoring Delegate via overloading method drops some symbols in resulted code
add missing headers to files
Fix some typos
add optDouble for JsonData
Fix substring regression in repository name panel
don t trim bodies
associate the type e op and type e in CHECK_CAST expr
remove obsolete line
Added small sample to the JavaCompile task
removed unused imports
Fix extension list war bug
Fix layout params resolution
Invoke background callback in own thread
Add convenient getters for force Encoding attributes
Add Null type to type registry
refactor Remove unnecessary declarations in observer pattern
additional existing HTML templates
Increasing visibility for Ben
commit documents in tests
Missing Switch Check compilable UT inputs
Represent timed synchronized transfers with edges instead of table entries
Avoid NPE in CLI
Removes unnecessary local variable
decouple CommentLiteralEscaper from JavaTokenType
Made the temp BoundingVolume in TempVars a BoundingBox since it s instanciated as a BoundingBox
Fix coercions in JOIN criteria
Revert getting failures in build sh Junit from two NN tests
manual mvc structure update switch community
CacheRecordStore eviction checkes the heap memory stats
Renamed Progress2 redirect to match Progress
PHP avoid dumb SOE in deeeeeeeeep trees
eliminate eclipse warnings
Improve the UI slightly by moving the suggested fix to a new line
Analyze command should support custom input formats Chao Sun reviewed by Xuefu Zhang and Prasanth Jayachandran
Only report ab test info when sharing task via email
Declare TargetAndConfiguration as final and immutable
Add Test annotation for outerjoin test
Add substring for rendering types
Add a test to confirm behavior for direct uses of an instance type
Remove Plugin onIndexService
Fix broken test
Not showing excluded tasks in Recents
Restore Timer thread on resume
Adding a sanity test in the while loop in readHistory
added null check
Add back the special Spark logic
Add content intent to autogroup summary
added SortedVertexDegree which uses MapReduces shuffle and sort to order the vertices by degree
Parse UIDs as Long in UidReverseComparator
Improve javadoc of parameters of AnnotationLocationCheck
Fixed test on windows
check correct database type
Fixing isBot flag in user and fixing find groups promice
Added BINARY datatype mapping to AbstractTransactSQLDialect the base class for Sybase and SQL Server dialects
push down Python specific constants
Fix javadoc of setHandleMethods
Remove another ServerFactory reference
SubjectDnX509PrincipalExtractor supports CN as last segement
Preserve construction exception in DistributedQueryRunner
added test for join with
Provide meta annotation support for Rollback
check LockBlockResult to be not null in RemoteBlockReader constructor
moving the polymerPass to before the later transpilation step
Fix the CoordinationPattern toString for negated nodes
Add ObjectMapper setVisibility VisibilityChecker to replace setVisibilityChecker which is non chainable
restore use of completion manager
Fix synchronization leftover from refactoring
Throw IllegalArgumentException instead of RuntimeCamelException
sync engine Save processing
create Groovy field from usage in java code
Fixed test on Windows
Fixed windowsForWordInPosition ignoring windowSize
Added logging if an unexpected exception gets thrown when handling a request
Remove some stale TODOs from the Java Bridge tests
Remove null check inside afterPropertiesSet since it s never null
Fixed NPE on asking for a plugin configured but disabled
Throw proper exception instead of printing stack trace
Don t spin up encryption unaware providers twice
Added FlatDirectoryArtifactRepository dir
Don t append noLoop in error checker
clarify rules for rate param
simplify string display
Pass exceptions thrown in mod event buses back to FML to handle apropriately
fixed model resource loading problem
TEST don t restrict index to nodes
Add routing time to message
Throw AssertionErrors from unreachable methods
Fix unknown length read for when the instream is done
exception from one listener doesn t block entire refresh operation
applied changes on backport
Fixed EditSession s Javadocs regarding notifyAdjacent
Added line feed as another possible heartbeat character
Hive logs full exception for table not found Sergey Shelukhin via Ashutosh Chauhan
fixed typo in MockMaker javadoc
Fix JMS client demo
Don t throw exception if test report directory exists
Removed unused code
change some methods non final non perf sensitive
Do not require explicit setting of source and output directories
RSV segment read fix in disconnected editor
Always save the new offset
cleaning up unit tests
use a utility method
don t return partial results from ContributorsBasedGotoByModel getNames
fix a typo in the usage message and align text
write better java
add license header to FileDownloadTaskAtom
Fix links to Bluetooth Guide
Step NPE if message encapsulation fails
Remove blank lines
Revert Defer tap outside stack until multiwindows
Suppressed event service log
Saving theme image fetching width in preferences
Allow the system in other users to call startActivityAsCaller
Print call statistics in the log file
Test BackgroundIndexer uses the wrong logger
Make the quick lookup map final
fixed logging statements
Add latest revision conflict manager to module descriptor
Remove Id keyword from all remaining files in tomcat
converting packaging related parts of javaee facets to artifacts
Print the location of atmosphere xml
Fixing comment lexing
disable thread dump action when session is inactive
Add some docs on ClassLoaderBootstrapper
Reset resend count before starting heal sets time
Log startup exception to console if needed and to file as error
suppress compiler warning
Drop deprecated Intrinsics stupidSync
Fixed group avatar orientation
add null check
Fix WS persistent cache
Change useBrowserHistory to default to true actually
Fixed bug in IndexRemoveJob
Relax conditions for including windows behind dialogs
Fix varargs on RobolectricPackageManager
fixed minor spelling and spaces
Allow Swipe to dismiss on error
Fixed typo in javadoc
Don t remove assets from file names
Cleanup diff rename output
Fix default number of threads
Have BukkitEntity getState return null for now
Introduce property value copy method to Show
Remove extra spaces after deleted statements
will add tools jar property to the classpath
Fix file header and remove unused imports
Dead transaction does not contain confidence depth and should not fail when sorting
Adds a property that can set autojoin by default on newly created rooms
Fix limit function in Vector3
temporarily show video frame ourselves
add click item long
add debug to mongo session id manager
Added postgres support for auto increment field types
fix one possible problematic place
Add mac osx support into EnvironmentUtil
Close the message logger so that the file is closed on windows
Fix description of Checkout with Rebase action
Fix OutOfBound exception in swapoutOrphanBlocks
Fix system properties core model test
Fix missing strategy constants in JSON dump generator
Remove wrong comment
Add activator logging
Add Retention annotations in ContentRecommendation
Added query parameter to filter results from geo search by name
Also check the support attribute
Log bad line when number of columns doesn t match expectation when building trees
Fix customJodaDateTimeFormat to specify time zone when creating Date
add reserved word tests for doc too
Fix quality flaws
Remove unused codes
Fix the checkstyle issues
Added putAll to ShadowBundle
Fixed string encoding regression introduced with fix for BOM
fix mln setParameters for pretrain remove code duplication
removed superfluous implementation classes
Fix custom model loader reload hook not firing during registration
propagate max walk distance to walking params
Fix absolute path handling
making notifs expandable by setting bigstyle text so error can be fully appreciated
Fixed chat example
Fixed a lot of bugs on embedded fields
Bump serialVersionUID to trigger recompilation for those using
Fixed onLongClickListener behavior when using Badges
Fixed the example s file naming convention since some files were being incorrectly read as update data files
display elapsed time
Added SuppressLint for web debugging
Fix error messages
handle long module quelifiers
Improved empty view message handling in custom date filter case
update MIME to public
we use shared event loop group now so we don t have to use shutdown hook
Report conflicting argument error at specific location in input
layouts Remove redundant code in generateDefaultLayoutParams
They say Thread yield doesn t work reliably on some platforms
getEndpointUri returns the exact uri the endpoint was given
Make finish a no op on a context mode that is not active
Fix the length of the secret key
Populate the SCI Javadoc
Fix Overscroll when we re using Mode BOTH
Update edit controls after data segment read
Fix bubble flow not expanding after long press with no move
Fixed some side effect compilations error in other modules
Quick documentation update at java NokogiriHandler
remove unused method
Disable LTI CIVIL capture devices
added javadoc header
show the top leaked object
Fix LocalStackDaoTest failing
Clarify WebSettings setGeolocationEnabled JavaDocs
restore old visitor behavior when jvmCompatible flag is off
Missing setter on spring batch job names property
Add possibility to force community format from any edition
Add back missing ContentPackLoaderPeriodical binding
Esc cancels paste
Added tearDown method which resets the context to null to prevent occasional breaking of other test classes
Wait a bit before trying to connect to server
expect errors on importing from default packet
allow override of HDMI rotation
changed return of Modifications shouldBeIgnoredByFilterIn to check for disjoint sets when invertFilter is used
getLastCommittedStamp doesn t require freezing injected documents so speed it up
Updates committed tx id
removed unused import
Fix incorrect reset of Geometry members after cloning
fix misprint myWidth myHeight
Set jid to passed parameter not null
Fix transparent translucent reference in private docs
add sneaky exception thrower usage
removed unnecessary variable
Java Remove repeated javadoc point
all add method startsWithIgnoredWord to SpellingCheckRule
Fix a bug where DnsNameResolver attempts to send extra queries
Rotate the RefreshIcon on refresh
Revert Improves List partition Predicate
Plugin doesn t merge additional PATH from Ruby plugin settings with environment of spawned process
Print log warning when stop attempt made with incorrect STOP KEY
fixing some config validation annotations
Excluded nobs from early stopping
Adding WPStats tracking for view admin
Fix another off by one in a comparison
Fixed implementation of copyWithZone
fix hover events consume issue
Use double math in DES calculations
Add Twitter and Facebook IDP SmartLock support
Removed duplicate Javadoc
Fixed a dead lock where MemoryAwareThreadPoolExecutor does not release its semaphore completely
XmlNodePrinter has protected methods that cannot be overriden
Fixed a bug found by Find Bugs
Fixed NPE with producteev notes id
even delete corrupted files
Fix a compilation error
do not check repository unless it is non null in getHistory
Fix minor formatting issue
refine server home page ui
Fix search shards count method when targeting concrete and routing aliased indices
improve icon drawing
Fixed letter case in test code
fix annotation read bug
Modify JsonFactory readResolve to hopefully work correctly wrt serialization
Fix typo in EurekaInstanceConfig
Invalidate the ProgressBar when changing animation states
Remove redundant check
remove extra call to getDataProvider
Fixed the help for editstack and editstackair
Add manageClosureDependencies option back
TEST Move SimpleNettyTransportTests to expected exception
Initialize the presence status
made Source Code Pro default shrift
removed unused imports
Corrected exception message inserts
Add missing close call
Fix checkbuild error due to javadoc reference to hidden class
backspace in lookup shouldn t undo common prefix
added old method marked as deprecated
Java Regenerate IR codecs
starting recovery for failed tasks
get provider by type made nullable
fixed a potential problem with windows script by adding apostrophes around windowtitle
SQL editor dispose fix
Fixed bug on serializing embedded collections
Switch id generator before MasterImpl
Fix NPE in Log class
rebuild list on EDT only
Delete deprecated getListViewScrollY
git log Remove unused code
turn off broken CM printing
Fix some checkstyle issues
Move cursor down on reindent of line
Drop unused imports
Remove unnecessary parenthesis
ac enable toolwindow stripes by default
Fix package name of DebugComponentOwnershipModule
Fix compilation error
Add new Analytics Event
Fix timing edge case in test
Remove code duplication in catch of exceptions in Constant class
do not return invisible gutter area for mouse events do not include right gap into line numbers area
Fix class generator npe
Implement bike switch penalties
Fixed bug on query against deleted records
Avoid unnecessary serialVersionUID warning
don t add line breaks this causes false alarms for the comma whitespace rule
Clear cached locations when location providers disabled
rebuild artifact with build on make option on project rebuild
restore the drawer state whether it is open or closed
add constructor in order not to break open API
Add requested unit test
Revert added todo
Tez in place updates should detect redirection of STDERR Prasanth Jayachandran reviewed by Gopal V
AppCompatDelegate fix cast exception
Removed resetting of hosts database
Fix the generics warnings
Use unified renderer architecture for LWJGL by default other backends pending
log fall back to avoid confusion
Fixed some docs
add missing imports
switch all exceptions that are thrown during parsing to addError addWarning
Simplify size delta calculation in TopNOperator
call stop at the end
Fix log parameters
set default dict path to valid path within models jar
Don t bubble up the error while verifying
Set theClass to the class for which this is the MetaClass rether than the class of the delegate MetaClass
corrected Javadoc comment
Added support for serverside routing InvalidMetadataException
don t leak project retrieve focus manager when it s needed and when the table is showing for sure
Make it possible to run PDE in Eclipse on Windows or on any platform
Set the scope after closing the stream
Revert Don t allocate strings and do concat if debug mode isn t enabled
Cleaning up the code
Fixing exception message
Do not remove Content Length header even for a message with empty content HEAD requires that
correctly calculate relative path for root
Don t use localization when writing events with SimpleGcWriter
added new util package to Imports for Gremlin
Add isPlaying to animation objects
Fixed unused imports
Fixed copy paste on OSX
Checking a box in Code Style Spaces makes the preview to scroll down
Annotate HeliosSoloIT HeliosDeploymentResource as ClassRule
Don t add an empty
renamed TransferTask to TransferThread
Make Compiler toSource Node n public
Added a topicAsStreamId flag to SpoutConfig
improve comments on removeDeleted
continue skin for editor tabLabel
Added support for onCheckedChange
Remove Deprecated Wakelock Refrences from AndroidApplicationConfiguration
Add test to make sure we don t check the wrong certificates
added finally clause to release memory in crossValues method
Fix unnecessarily non static inner class to static inner
fixed some accidentally overwritten change
added one simple test in client lock
use LinkedHashMap to fix blinking tests
Address some JavaDoc errors in ParameterParser
choose some nice and decent preview text
Initialize exceptions with better message and set the inner exception
camel spring boot Add default values to auto configuration
remove final from interface where they have no effect
Add some debug info
Fix the domain configs demo
Fixed problem with remote connection to distributed storage
include subContexts values in JobExecutionContext toString
Updated InjectExtra javadoc
Added support for camel spring detecting and adding InterceptorStrategy from the spring application context
eliminate eclipse warning
Fix NPE in ForwardingListener
Make layouts cancellable using LongTask
Also show progress when refreshing via button
Improve logging message
Java Put a single byte rather than an int for zeroing
Limiting versions that trigger
Don t allow foreign and normal leaves to be considered equal
fixed another write action issue
TEST improve Phrase Collate filter test
These methods never return null
missing plugin status for plugins without vendors
Modified test method name to be consistent
don t recreate editor modes on save unless mode has changed
Remove unused field in KryoSerializer
Correctly clear resizing drawable when nulling background
Turned off debugging for launching secure camera
Handle object names that end up with spaces around them
Fix exception in snippet generator
Run next chunk keyboard shortcut not working
Improved exception handling if response already committed
Hide the new posts message when the reader list is scrolled
Fix Since tag
Remove incorrect comment in FlexibleDateConverter
Fix checkstyle again
Implement Clone for Point function
Fix timing initialization bug
Slightly fix voice routing
Hide waiting list if empty
Renamed compiler options
added synchronization to AndroidGraphicsLiveWallpaper resume as in
added debug info
provide extra trim
Categorize error for InformationSchemaPageSourceProvider
Fix typos and remove incorrect comments
Fix rounding error in SeekBar tickmark drawing
Remove useless flag check executeBuild is only called if execution is requested
DskipTests doesn t skip integration tests
use PropUtils for indexer coordinator node
Handle null values better
Fix botched merge yay
fix expected wildcard types
Omit JNA AWT load test on headless systems
Log error message if there is a MongoDB connection error
Fixed Unicode string escaping to ignore escaped backslashes used in regex
fixing long vs int issue in capacity
changing stats getUsersBlogs call back to https
collect usage statistics
Fix a minor oversight in using a constant in LongMath for its intended purpose
pausing WatchOperation while processing CLIRequest
suppress JBColor warning
Added check for positions enabled state in adapter before performing click
Initialize mRetyMgr when constructing GsmDataConnectionTracker
changed cascade for many to many to save all as the sensible default
Adding method that allows to easily compare two items
gbm update hot fix
handle image resource provider which provides empty image
Add a Gservices setting to have a use location for better search
Removing typing that can be inferred
Added CookieSyncManager reset
Added ENOTSOCK error code
add note in the method BaseDownloadTask asInQueueTask to declare no need to invoke this method when user use FileDownloadQueueSet
Add reference for maven failsafe plugin classpathDependencyExcludes parameter MavenDependencyReferenceProvider
Remove superfluous variable assignment
Fixing license header in com phonegap api PluginManager
Add printout of sparsity ratio for double chunks
Added some more debug info
Fixed bug where tasks assigned while offline would be reassigned to self after sync
Temporary disable failing unit tests
Add a default flush interval for HiveBolt
Fix NPE found when pulsing and
don t auto open the choose dir dialog in existing
fixed NPE while updating poll status
parse set user id from token
Revert previous patch
fixed issue with improved selector change handling
rename mustRetry method to mayRetry
Fix Astyanax slice end boundary handling
Fix incorrect behavior for statusbar and quicksettings panel
Allow prep in RRC
Added stats action to controlbus component to get performance stats easily
expire notifications in AWT later
fixed Integer LongOps
catch all throwables when loading contacts
Fix a bug when detecting cookies name with sign at the beginning
The Android transition requires a valid CPU value
Fix null shutdown timeout in ExecutorServices
remove outdated javadoc
Avoid boxing of the number of bytes flushed
run pex with target interpreter
Started new demo
make sure stats s op count and size are in sync
Fix JmsProperties bean not found exception
added missing TargetApi annotation
Adds some important comments for users follows endpoints
Aligned import order
Remove no longer needed dependencies from DriverConductorTest
Remove redundant methods
Remove unused method
Moved HintedHandoff to voldemort store slop
Added compass to poi context menu
android update WXSDKInstance java
Adding full support for JAX RS and JSON
Fix KMeans perRow logic no point in lookin at actuals vs predicted
Fixed data type in setStreamVersion
Fix the comment
use new actionBar method
put back process into command buffer method name
Added test to verify that GenerateExportsTest doesn t crash handling arrow function
Fix namespace collision
Make method TimetableSnapshotSource applyTripUpdates synchronized to make sure updates happen atomically
Fixed ReaderTag equals implementation to take the tag type into account
android update WXSDKInstance java setSize
Fixes the fetch error string for email followers adds viewers one
Add messages for number of active sessions connections
XML editors loading fix
improve exception on invalid input
handle the typo in Hybrid Test
Removed spring xsd polluting javadoc and rolled back the deprecation of PropertyDefinition as its used in purpose for endpoint propertiese
Updated SRTM Provider URL
fixed cs issue
fixed test data paths
Make IQPEPHandler getPEPService public
Remove accidental checkin of println
amend commit documentation got messed up
Added the ability to print the result of a generateChangeLog to the specified changeLog file
Fix ShutDownTracker Already disposed on close project
Fixed a tiny Javadoc typo
Fix NPE when creating DomainNameMapping via a builder
Removed empty method
Add Google internal memory consumption tests for Immutable HashBased TreeBased Table
don t reverse timepoint and shape_dist_traveled
Remove blank line
Removing non existant resource usage
Clarify default value of reverse route generation
Undo Introduce Closure Parameter shows confusing message
Fix action bg color
Fix typo which cause stack overflow if children was called
Call initializeWithInteractionTarget each time when a BaseInteractionScreen opens
Remove unnecessary suppressWarnings
Revert DefaultMapServiceContext change since it breaks enterprise repo
Add precondition to throw more informative exception when calling request before start has been called
Add session listener to listener types list
Include all DDNs when exporting a context
Only draw AbsListView selector when focused or pressed
Suppressed NewApi lint check for EditorWebView execJavaScriptFromString
undoing last change
Add null check in BitmapFont toString
externalized Querydsl SQL serialization
Uncheck the generic converting
do not throw WebApplicationExceptions in Inputs java lolwut
removed unused call
handle highway crossing as a crossing
Add an UNKNOWN_UID constant to WifiConfiguration
don t set notification mode to background when on pause
remove debug output
Massage the tries before using them for method analysis
Never expose user dir to the web on directory listing
Slightly improve debug message
don t automatically include runtime
Updated demo to collapse the main when clicking on the main layout
highlight assigned unused variable instead of the value
Fix incorrect import for MixinFluidStack
Added error checking to liquid registration
Remove fprofile dir flag from clang flags
Fixed the CS error of camel karaf component
Fixed Javadocs for extension providers
SpringRouteBuilder should use getBean type when lookup by type
Added test for show encode UUIDs
Don t run replaceIdGenerators in simple mode
Fix typo in o e c r a d ConcurrentRebalanceAllocationDecider
ObjectEncoder should allow zero estimated length as we did in DynamicChannelBuffer
Fixes code formatting
corrects some typos
PsiFileSystemItem implements NavigatablePsiElement
Use a HashSet instead of a TreeSet
Added configurable index class and name
inject an additional newline before SweaveOpts
Fix assertion in broken test
Incorporated PR comments
Fix up option parsing code for resource id stuff to use i
Remove the CREATE_FLAG_OPT_LEVEL_0 from the Java API
Stop warning about empty blog options
Remove unused security event callback methods
Don t unbundle updated notifications
Fix timer issue
added toast when no account in selector
fixed bug on inner select traverse
Fix remaining NetworkLinker bug which I ve noticed
Simplify an expression
Added additional test for Vehicle not supported exception
Removed unused method
Initialize mTiles before constructing AutoTileManager
MB update script bug
Revert changes in sample
Avoid NPE if class interface is not present
used id constant in http setting
fixed checkstyle issue
adding one missed javadoc
Fixed typo in a log message
removed useless string conversion
Optimize RowType getTypeParameters
TEST Reduce size of random shapes
Fixed NPE in VimKeyMapUtil installKeyBoardBindings
Fix compilation after broken cherry pick
prevent use of Inet6Address when finding possible addresses
Eliminate an unchecked cast from ImmutableSortedSet that previously generated unsuppressed warnings
Throw exception not System exit change a System err print to say
Added reverse iteration from tail of PooledLinkedList
Only add divider if it is non null
Ignore the exception indicating an error
don t highlight todos in dumb mode I said
Prefer own inner classes to introduced
Fixed NPE on server due to path changes causing all segments to error out instead of coming online
Remove unused code
Poking wakelock upon fallback
log Improve javadoc
Add default value submitted by Bill Barker
do not reorder catalogs on saving
Make the grandkid GraphRelation correctly set relation
Added shin to HumanoidRagdollPreset so that Jaime works better with the KinematicRagdollControl
Added a FIXME marker for StaticChannelPipeline
remove unused method
make TextureAssetKey flipY option a proper bean property
Added getPreviousExceptions method to Element class for testing help
add parameter check
Fixed issue reported by Konrad about fixed size of cfg with large db structure hundreds of files
do not change package when chooser was canceled
Remove unused constructor
removed output from AndroidNdkScriptGenerator
SMALLFIX Removed redundant initializer in FileSystemUtils
Fix confirmation dialo
still don t load bloom filters even when Directory instance doesn t have a codecService
By default Android tooling does not compress certain assets and just puts them in as STORED
Hide Quantile Models by making their keys user hidden
remove proxies without primary or backups only backup expiration handling left
Fix potential race condition in awaitCompletion
Fix ModelMap withType being additive
Make a field static
Fixed a problem with not closing a input stream in a converter
Ignored a failing test in MapQueryEngineImpl_queryLocalPartition_resultSizeLimitTest needs further investigation
Fixed deadlock in BlockStoreContext and FileSystemContext
Removed unused imports
Include failing parameter index in exception useful for inherited classes where it s
remove an expensive Class isInstance check
Add an isSecretEstablished getter to CordovaBridge
Make public more RTL APIs
Pool should throw a SQL exception never return null
revert some changes that were left in between testing
Prevent the world generator selection from reverting to the first entry after customizing modules
stop search by name thread when panel e g directory chooser is closed
remove TODO and improve javadoc
Add another batch in the H2ExceptionSuiteTest
Fixed unit test
non blocking socket reader idle time probe now on info instead of debug
Improve error message when using DISTINCT with non comparable type
Fixed a connection still allocated log warning by not using an ivar for the DefaultHttpClient
correctly encode help URIs
Fix FindBugs warning
Implement cancelHttpRequest method
make the externs zip a bit more robust for different build environments
Don t output an info log for every block downloaded
Fix full package name for files from libraries
Also make Weighted WeightedRealVector serializable HT Justin Hayes
lower case some words
Fixes cancel operation on select screen dialog while initiating desktop sharing
Added TODO item for improvement
Fixing cross user content
do not hide non dumb aware actions just disable them
add auto makeDirs when writeFile
Change String to equals in Preference
Don t overlap appearance animation
moved scope down
always save state of stop called
adding new method forgot it in last commit
prevent focus stealing by side chat fragments
For update command only write multi or upsert field if the value is not the default
tweak theme preview to eliminate horizontal scrollbar for default font size
Improved Hibernate missing error message
Make media scanner read the jpeg orientation tag
Don t set dev bootcomplete until interacting with user
Get collected flag state in overview
Adjusted high velocity
Fix code style in LruBitmapPool
Post editor shows html entities in categories
added missing check for between
add more documentation on Session onTimeShiftSetPlaybackRate
Allow AOSP override
fix up the tests for the escaping change
Remove the debug code
Pass parent to createView
Fixed a bug where the media template wasn t transforming right
Added icon in action bar
Unroll import statements in AttachmentView java
disable permanent fns by default for now Sergey Shelukhin reviewed by Gopal V
Added more clear message about missing tags
Refactored the failureUrl lookup into a protected method to allow customization
reverted content dependent flag
Remove radial restriction for GeoDistanceQuery
Disable CalendarBasedTimeoutTestCase unless enabled via system property
Add attribute active for Selenium tests
removed debug printing
Verifying my EGit settings
compute number of local stops more correctly
Configuration class processing explicitly skips java
SuspendContextImpl setIsEvaluating resume may happen during the evaluation in the context
Added data binding support for progress
Now using STATUS_404 as the default image in stats followers
Clarify rules about encryption of emulated storage
Remove unused imports from LocalBlockOutStream
Large value formatter now can appends a specified text to the result string
Do not show soft keyboard for readonly textfields
Improved error message
Write package proto even if it s empty
check ZipFile in IO Resource not safely closed inspection
Fix bug in IME handling of pending key events
add support for adding multiple response msg parameters
hotfix tests Fix manual tests to properly start testing cluster
Ignore properties with transient fields from serialization
added marker not sure that the javadoc is correct
show the URL not just external link
fixed bookmark text
Removing STOPSHIP logs
Simplified HttpTunnelAddress compareTo
Add test for HAVING with DISTINCT aggregations
Fix exception with self loops
Fix name in legacy support in registry changes
do not ignore end of line symbols in bookmarks
Hold sync adapter instance in a static field
add an extra flush for good measure
properly implement BackedByDirectoryTrees hasDirectoryTrees in DelegatingFileCollection
Log the exception
enabled show history for directories
prevent SOE during groovy control flow building
Remove nearest filtering override old code
android sdk Handle SSL errors in FbDialog
Slightly increate the drag bezel size
Remove more unused deprecated code in Jasper
Adjust performance test
Fix bug causing ObjLoader to not load materials with dots in name
Ignore all of IIOPNamingTestCase
Fix boolean parameter handlign
Fix legacy widget
Treat moduleDependencies as the union of artifacts from all incoming dependencies
struct disallows prop creation in prototype methods
Added IDE error messages for RESULT_TYPE_MISMATCH
speeded up node shutdown
always show conversations with pending subscription requests
Fixed a mask parsing error that raised java lang StringIndexOutOfBoundsException
Send neighbors into NUD_PROBE rather than NUD_DELAY
Add default constructor to SimpleXmlConverter
Rotating screen after viewing a file would return you to the
close existing completion popup before opening a new one
moved timeout to PINCODE_TIMEOUT
Removed dead store to partitionCount
change additionalSettings to use Settings
Add a constant to fix MagicNumber checkstyle error
refactor some method name
added a check for conversion of date in date
do u even null check bro
rename CheckUtils isVoidMethod to isNonVoidMethod
enable extract class for inner classes
remove white spaces from hostname
Changed the return type of UnresolvedDependencyResult getFailure
Removed unused variables and imports
TEST Temporarily ignore transport update tests
don t suggest null after
remove unnecessary constants
Fix errors from rebase
Allow nested calls to begin
Expose the react application context as a singleton
Fixing a typo
Updated sendNotificationsSettings to use AppLog tag
fix quality flaw method rename
Avoid potential NPE in TypeResolver
Allow correct search for RefModel using branch name
Fix sending ACK on Invite OK last attempt to revert libs do nothing
fixed local binding of non primitive emitting exprs of primitive flow through type
determine tag end by doctype
Added getPlayer String name to Server
Fixed deselection of all items
skip leading whitespaces
Remove unnecessary warnings
Fix list items launching detail view twice
Fixed issue on delete of record in transaction using the remote protocol with indexes
Remove inappropriate author credit
Fix bad workaround docs in Resources getDrawable
Obey redirects in HTTP downloads
Make read only store management servlet methods public
avoid NPE which can occur under some unclear conditions
missed in command line property definition
Fix NPE in addMessageToConsole
When applying type modifiers don t ignore container types
Remove redundant override
Fix the build
Improve type safety of GtfsStopContext
Added another badDateTimeFormat to ImapResponseParser
add reference to getFullId getId
add missing group in TestAnalyticsDao annotations
Provide public getConfig
Remove unnecessary Javadoc
Adding method to save themes
Don t forget JGLFW
Consider modules without goroot as non go modules
improve toString of bundled scopes
Catched more IOExceptions to print them in DEBUG
Fixed Camel may clear attachments during routing
ActionFileHandler also needs the ReadAfterWriteConsistent feature
Removed unused imports
Remove javadoc references to code now in JXMPP
Rename getHelperTextAlwaysShown to isHelperTextAlwaysShown
Should not snapshot data by default
Replaced an integer literal with predefined sizes in Constants specifically Constants GB
commiting ignored case til we support streaming
Fixed failing test
introduce Functions compose
Fixed the camel mina build error
close the dialog by double clicking radio button
Removed unused imports
Improved java doc of EventCollector
Fix report issue
Rename a method that was confusingly similar to another method
Print full backtrace when a wallet extension fails to load
Fix send futures in FakeChannelSink to unbreak a unit test that deadlocks after a previous change
update excluded files
Revert page based scrolling on by default in browse
added an explicit unload implementation to cancel the thread
Making sure that there is no notification when focus changes from something to nothing
Removed unnecessary reset of fields from onCompleteSuccess and
readded remove not existing file test
Make ChartAxis serializable
Fix PrivateDataResult getChildElementXML
Move return out of debug conditional
Fix failing tests
remove crop handling for flowers
Incorporated PR comments
Fix potential NPE in MethodAnalyzer
fix underscore merge
Looping playing music
Polished test due user forum issue on windows
CubeMapTexture update texture bug fix
Fixed issue on remote queries
Fixed issue on scan with distributed
Removing Primary Key from IntroExample
remove unused imports
Added default mode as CORNER
Check sum test fix
Removed unnecessary description
handle bike rental in basic path parser
Don t run the WebView preparation if using the same non replaced package
do not add listeners in test mode
Prevent error while parsing self generated certificate
add debug logging to purgeIncompatibleHints
Don t show progress bar on Image Viewer for now
remove search termination as a property of the algorithm runner rather it s a property of the run
Fix test waiting time
Tidied up test failure message
Add webview tracing bit
Added errorStream null check in HttpClient used for testing purpose
Remove optimization to report config change
Remove some unnecessary imports
Make properties with predefined values editable
Correct a comment
ignore tempdir cleanup failures
Allow reading of state in StateMachine without acquiring a lock first
Changed test of addUser so that it actually work when mongod is started with auth
moved finish back to proper place for launching lock screen
Fixed little error typo in comment in Layer Interface
Changed RequestParams getEntity from package private to public to allow standalone use of RequestParams
Fixed an error comparing relationship types
remove superfluous Valid annotation
Fix code style
add hasKey methods
Do not show normal permissions in sideloading UI
Removed unnecessary frame buffer reset
added missing root cause to FileSystem Exception
Add xpack as official plugin
adding a closing paren in a comment
Increased timeout in TimeoutConcurency test to make timeout more reproducible
added get setInt to Bundle
avoiding unnecessary char creation
Fixed a compilation error returning from a void method
made session immutable
Add restOverStream switch to D2ClientBuilder
Added getEqualsToken for future use
Change back default allocator to pooled
Add fix for parsing bug
Fixed an issue with the headless server not saving the world on shutdown
let the exception handler from decoder handle the exception
Optimize number of DATA frames for unary requests
Photo viewer now uses low quality for the lo res image
Replace Log wtf by Log e
revert unnecessary name change
Supports multiple viewTypes
remove auth from source file
only one person authored this code
android add null protection
Fixed Double Item Drop
TEST Fix IndexLookupTests testCallWithDifferentFlagsFails
Removed unnecessary import
Java Remove unneeded AutoClosable interface from PublicationImage
Fix unit test using the changes function
Add some Javadoc
Add information about shards that are not closed when the filter cache gets closed
try new aggregation function
Change DrawerLayout s focus strategy to favor children
LoadBroadcaster should remove load info for nodes that leave
Align default with Context Javadoc
fixed wrong suite
Breaking Change removed ACRA setConfig
Fixed bug with invalidation in top level Views
Don t set userSetLocale from Configuration setLocale
Added Player getDisplayName and Player setDisplayName String
Retain IME assignment if no windows accept input
Fixed the NPE when using the embedded goal of camel maven plugin
Add null checking to addRequired
Reorder fields to potentially help object packing
Fix file name lower case
Use new send event method
make Nullable contract visible
null context crashed getThumbnailForWPImageSpan
Set useCaches to true for testing before removing it
Fix hang in compiler when compiling lambdas for method references taking
do not convert mouse event if nothing changed
Changed back bit offset positions to long
Make Config numTokens a final member
added failing test so i go back and fix issue with op in LR rules
set frame as a parent in evaluate dialogs it prevents it from closing when opened from another evaluate dialog
Fix indexing when creating loop from float
Improved encapsulation of Configuration
Use a privileged action to set system property
If the buildSrc gradle version state cache file integrity is violated just rebuild the buildSrc project
Fixed failing testRecoverData
Don t crash activity manager when supplying invalid URI
reveal nullability problem in generateSyntheticParts
Hide current soft input when a background user gains window focus
remove feature flag for shared project UI
do not cancel import task during auto import
Do not request focus to Editor if it s already there
save some CPU cycles
Set httpd logger additivity to false
Added IDE error message for VAR_OVERRIDDEN_BY_VAL
Adding defaultIconColor and reading it from attributes
make Wiki webview single column
Remove method local int variable
Add simple body and proper return code to SessionsResource validateSession
Fix some wrong event creations after previous change
Added redundant abstract method declarations to maybe work around problems on Nokias
Updated JacksonObjectMapper visibility
PlainText presentation rendering fix
Fix a type mismatch in COMMIT_CONTENT_FLAGS_KEY
Refix the bug
Add comments for javadoc
Add hidden minor version ID for support lib workarounds
Add CORS by default
Don t initialize the server
Move Forge init message to MinecraftForge initalize
Correctly deliver AppWidgetProvider onDeleted events
recognize xts objects as data in environment pane
TEST fix SearchScrollTests scroll doesn t support searchType count
Make nicer list of files in HTML annotations
modifying config for RAS example topology
git Fix a misprint
Fix race on StorageService doAuthSetup
Fixed compilation issue RestTest imports
Avoiding horizontal keypad navigation trapping within gallery
Replace fragments so actions update properly
removed line that prevented like approve actions to appear on wearables
Fix more copypasta it s a pasta kind of day
Fix compile error
Provide a request processing time for an error response if possible
Reset build identifiers to SNAPSHOT
Address missed comments
Add support for TD SEL as SQLSubqueryTableSource rather than SQLExprTableSource
Make methods private in RequireThisCheck
I did not save this file
Ignoring PMD warning that I believe to be spurious
Fixed bug on ordering of columns in console output
Do not allow remote peer to arbitrarily size the HPACK decoder dynamic table
Add a comment
make MultiScheme serializable
Remove airplane mode related wifi test for Wi Fi only devices
Remove redundant SourceSet extends implements clauses
Remove unused code
Object copy fix do not copy primitives
Fix AsyncQueue to actually wait
Add HashMap about Brackets and Quotes
Fix flipped captive portal bit in network conditions bcast
Remove duplicate code
pass the right settings to the listener
Fix a typo in CSSLayout toString
Fix typo in AccountManagerService
prevent default when a prefix key is pressed prevent beeps
Add number of dynamic partitions to error message Lars Francke reviewed by Prasanth Jayachandran
hotfix Improve exception message in TaskManagerTest testRemotePartitionNotFound
make single row layout by default
Adding dynamic priority blocking queue
Ignore viewport viewScale in fixed viewport mode
Add missing R file change
creating JetParameters in JetPsiFactory
Private MUC chats disabled until better implementation
changed spaces at the end of messages to dots
Fix broken MessagingStyle
Log message had params out of order
Add SEQUENCE to exception description
Track Application Upgraded and account logout events
fix some errors with a log statement that is causing compile failures
Fix close open group icons in contact list
java drops excessive logging
Consider border insets even if label doesn t have an icon
revert circular reference in generics
vcs log remove add remove Highlighter methods from VcsLogUi
Fix empty view flickering
Improve logging message and reduce verbosity from INFO to debug
Improve templateSend method to check the response code to see if it s successful or not
fixed javadoc typo
Add diagnostic groups for goog provide goog require checks
Removed debug code
Addressed more comments
set some description and presentation in CollapseOrExpandGraphAction constructor
Added move speed and rotate speed getters in FlyByCamera
Add missing Override to KStreamImpl through
ui a logic to override always show thumb property for transparent buttonless scroll bars
Forge uses the FORGE channel for packets
Reverted back the experimental workaround for SslHandler that did not work
Add some javadocs clarifying throwOnError
Cleand up imports
Changed log level of exceptions to send to the client from SEVERE to FINE
Fix deframing error handling
stabilize RemoteEJBClientStatefulBeanFailoverTestCase on Windows
removed unused private method getCodePanel int
Added ability to disable sliding
Extracted method to clarify
Fixed bug in ReplayInputChannelContext
Remove static and final modifiers on RestApi interface
Fixes call buttons behaviour if provider info is missing and preferred protocol is set
Re added missing declaration
add getter for footer header stickyFooter
Make fill Pixmap fill emulation behave like core
Exclude label width when drawLabelsEnabled false
Added alpha management
fixed stupid mistake
fix quality flaw missign license header
Fix broken JSON object comparison in test
don t show bulb in a non focused lookup
Moved count command implementation to its correct package
Add method to get overview view
Fixed typo in MapServiceContextInterceptorSupport
acceptsURL check doesn t thorw exception
Fixed bug of AES URI Pattern for hls
use correct type for writing to context
getCurrentInterruptionFilter should return the filter value not the hints
allow dynamic max height for scrollable toolbar popup menu
Fix the GWT build
Fix checkstyle warning
Remove outdated test and add new test to AllTests suite
Fix copyright dates
Add pom packaging type maven plugin
fixed NPE in injection
Remove test that seems wrong
Fix missing public decls that were breaking tests
add error detail info
adding author tags so you can blame
Fix loop observed during local testing
Add more debug of the output data type of GangliaWriter
Ignore Samsung ActivityManager context leak on L
Throw exception if adjustViewBounds is enabled as this requires an unsupported scale type
only update current scope if the filetype can show scope trees
Make actual less bind rules work again
Fixed multimap migration rollback
enable closure folding by default
Set stop token in postamble
handle last tag in file correctly
Don t add convention mappings to bridged ClassDirectoryBinarySpec instances
ThreadPool now uses daemon threads in CUDAContext
Removed unused import from LocalOperationStatsImpl
don t interrupt R if autocompletion popup visible
Fix bug in contents of metadata txt file for secondary dexes
Changing Response Status to Created instead of OK
Fix quality flaws
Added option synchronous to endpoints to control if async processing can be used by producers
Add missing finalize call to super finalize
Remove some redundant code
Add new property
Added ASYNC_WOKEN state to HttpChannelState
missed file in commit
Added dataSourceRef option to lookup DS in registry
Move first test to new test package
Prepares the generic TransportManager to accommodate ICE related utility methods
added license header
Fix AIOOBE in PyImportOptimizer in case of incomplete import statement
do not ask proxy credentials when passively check whether background connections are enabled
Don t override the hash code for the channel
Fix settings versioning after merging autofitWidth branch
Added a contentColor and contentColorRes method
Remove a comment based on review feedback
DescriptorRenderer should render visibility for constructors
Remove ISE in ResourceUrlProvider
Searching for emails in Contacts throws an exception always errors out
Prevent possible NPE in android gesture Learner
Remove unnecessary connection allocation
Removed unused inner class
Remove incorrect and unused method
Fix ItemBridgeAdapter NPE
Prune unreferenced contents faster
made the ID in PacketEntitySpawn unsigned
Add some javadoc
set mini style to narrow down
Fixed special parsing of and commnds
Fix zookeeper exception logging
Replacing return null with IllegalStateException
Make RosterStore getEntries return a List
decreasing neighbor updates to make last step faster
Dont propagate exception in sstableexpiredblockers
Add helper methods to register Transports and Codecs in plugins
uk fix the class comment
Fix poi additional search
Fix logger class
fixed minor keyboard stuff in jogl
r maxim mossienko Leaking navbar item s cache on project close
Remove unnecessary drawable code in CompoundButton
add some javadoc
Ignored test for now
remove Editor dependency from HtmlUtil
improved logging in case of error during binary serialization
Fixed missing wrapper import for primitive type literals
fixed faling searchengine unittest
changed remove to only be done if FD is readable
Improved description for Layer removeEntity
kill Harcoded string literal inspection
Remove duplicate code
ScopesAndSeveritiesTable suppress unused declaration inspection
Added missing onDestroy to close cursor and db
fixed npe in comparing results
remove duplicate semecolon
Fix for crash in gtasks widget
remove a period
compute all neg all time
Add parameter SET DEFINE OFF to prevent variable substitution
fixed template generator failing on generate controller test case it was checking the wrong path to the controller file
Amend some comments
Fix in place update of FileDataModel where PreferenceArray data has a data point removed not being updated currently
Also add clamp to Vector and Vector2
Fixed missing typeahed requests for models
Fix typo in GraphView
Invalidate cache for streamed rows
Fix CollapsingToolbarLayout pinned views with insets
Enabled profiler in OrientDB Studio app
Improve description of sonar lf logoWidthPx
Use buffered streams
fix the lock
accept Nullable parameters
Moving hardcoded ports to ephemeral range suitable for more environments
Fix Raptor ShardIterator for MySQL
Fixed bug in charset decoding logic
Added some TODOs for future work
Fix test data
Fix an assertion to avoid crash on deletions in Recent root
Remove workaround that was required by the T extends Object Iterable
TEST Remove explicit network mode not needed here
fixed type in server shutdown message
include cause of configuration exception that Shouldn t Happen
Cannot throw exceptions from reset must fail the callback instead
when setting the main window visible bring it also to front
Fix null Bitmap returned from pool in FitCenterTest
remove whitespace from ActiveSession
Fix a buffer leak in BinaryMemcacheEncoderTest
Fixes contact details appearance
correctly access the rhs frame s chunks
fixed equals method to return false for contacts from different providers
retain html preview scroll position across reloads
Fix AndroidManifestFinder with Gradle Library projects
Fixing indefinite articles
just did some heavy javadoc n of the property graph interfaces
Added getRoom method
Fix autowiring in DefaultGrailsCodecClass
On windows delete target file before moving
add some TODO comment
made public for out of package TabLabel inheritors
Javadoc not to receive a question about messy data transfer anymore
dispose erroneous module in EDT
Add repo revisionId to buck rage log
Fix removal of symbols
Add Callables throwing method to make it easier to construct Callables
Added brave to a white list
Add a hopefully useful comment
avoid null profile name from TeamCity
don t show avatar on initial account creation
Improve logging of finalize methods
Remove compiler warning
Remove Beta from Escaper and UrlEscapers
Deliberately fail test to test bamboo s reaction
added missing moco runner
Rephrased the comments on compression handlers
added currentUser property on connect
fix a strange NPE
git Fix NPE for obsolete component initialization
When doing a vertex retrieval only retrieve edges
Fixed the CxfEndpointBeanWithBusTest error
Relax DLGradientCheck tolerances some more
move AbstractAndroidMojo finalName to other similar fields
do not modify document if there is nothing to change
Fix OOME potential candidate
Fix copy paste error
Fix maven tests
Added additional tests
Fix ambient display
fixed renaming in standard schemas
Remove erroneous create method
Add description to PlayRun task
Allow ModelLoaders to return null values
always create placeholder tables for basic objects
Plant a logging trees for timber
Ignore web socket tests when using AJP
Fix exception when defaultValue null
Don t ignore flush exceptions on close
Revert changed modifier
Prevent system crash when OOM in Binder thread
Fixed sequence of stream headers after TLS was negotiated
Don t clear Global Proxy on boot
Reverts latest changes fixing build
Implement SocketTagger tag
Don t apply transformation fudge when not rotating
NEW TYPE INFERENCE Allocate fewer types in JSType substituteGenerics
another failing test
Remove circular ref when finishing activity
Removed unnecessary parameter root
ActivityManager Prevent kill a restarted process again
Fixed some typos
Gradle support cannot update IDEA projects once one of build gradle files changes
Disable HW acceleration for status bar
Remove PrintWriter wrapper
Fixed NPE when DeamonClientInputForwarder is stopped before it is started eg on failure to write command to daemon
Font size change should trigger a refresh
don t add update request if dialog has been disposed
Renamed the setOnMeasureSpec
added saving functionality for gpx file
remove eclipse generated comment
When we connect to vold explicitly mount external storage
Add test for topmost home stack activity being home
Fix test to not rely on the order
fixed unit test added null check
added Oracle specific extensions
javadoc comments should behave as simple java comments
Handle null files list in cleanup
Don t save local data using DiskCacheStrategy ALL
check all upper bounds even if there are some error types in bounds
Added a second fire reg state method
fix project file reload
Handle GET operation properly
remove a duplicate codepath for folder insert
fixed typo and formatting in Javadoc
Dump stack on test failure
Add finish button
acquire ControllerLock in the restart required handler
hide non working options
Fix deprecation Javadoc on annotations
Add a DocumentationRegistry to the GlobalServicesRegistry
Use correct mapping data for welcome files
Prevent direct instantiation of BlockingObservable via no arg constructor
Add private constructor for ExpressionExtract
Tolerate AmazonClientException not just AmazonServiceException
removed unused func
Stubbed out a method that no longer needs to do anything
RabbitMQConsumer don t use Camel ExceptionHandler BEFORE requeing message
Remove merge leftovers
Clarified javadoc for isRadioButtonChecked in Solo and RobotiumUtils
Remove private key from ECKey toString and put it in toStringWithPrivate
Add factory method for a mutable registry
suppressed IncompatibleClassChangeError in testProjectLeak
Fixed Javadoc warning in HystrixNetworkAuditorEventListener
Temporarily revert manifest s Premain Class validation
Disable full screen mode action for MacOS since it doesn t work anyway
Fix android text cts TextUtilsTest testRegionMatches
Porting fix to allow for a forward include to call getAttributeNames on the Request in a sandbox
Allow bigger images
DependencyResolver should not lowercase the dependency URI s authority Anthony Hsu via Carl Steinbach
write lock subscription
Oops had added toLowercase to wrong string
Turn off rollupstats quantiles test
Prevent erroneous empty locations log warnings
Delete unused method in AbstractQueueVisitor
Fix inefficient loop in QueryStringDecoder decodeComponent
Fix bootstrap cli tests to not clear all properties previous tests set
Support all restrictions for setRestriction
Fix another APR refactoring regression
Fix documentation of nextSpanTransition off by one
Minor Fix CS errors in camel jpa
android bump version
Add missing delegate for AnimatorInflater
Need to call getApplicationInfo from System id
prevent js exception when window focus isn t found
Fix bug in jdbc driver processor
client executor service isShutdown exception catching fixed
Rename no functional change
Remove println in test
Remove ugly etched border
dont show delete file button when outside conversations directory
Remove unused import
Avoid poential NPE when stopping jms consumer
update DlvApi Breakpoint
Fixed NPE when a Config Builder cannot be created
Add missing loginterceptor test
optimized modifier checking for Properties
Add MapTransformer toImmutableMap
Fix build break due to misuse of git add i command
Keep native callbacks when primary SQLiteConnection is recreated
Fix NPE in bringPointIntoView
Avoided unnecessary double lookup
eslint provide short descriptions for properties fix so that description is correctly combined if ref is also used
fix index not ready exception
screw the remote run
Added more to take to decrease the likelyhood that nothing is dropped
remove unnecessary log
Fix window positioning code
silence VPN notifications
do not reexplain unchecked calls
include setup chunk options when auto executing setup chunk
Check superclass and not parent
Add skipCrunchPngs to rule key
Add edge thickness to PreviewAPI
Handle the failure of compilation better so we don t find out because of a ClassCastException on a TypeElement
XML DSL Should not have default values in errorhandler
Reorder some variables in AndroidBinaryRule
Remove deprecated code
Changed some variables to be final in BetterAsyncTask
Reported exception when server cfg file is not found
add an exception if there is no master node set
Fix SDK build
Fixed travis error on an empty vehicleStr
Remove Internal error message
Temp enable the log to identify the wake up cause
Added TODO in QuadTree
change module chooser to look like in IDEA s Java run configuration
Improve watchdog explanation when system server is blocked
Added debug logs for bdb stats
tolerate nullable HighlighSeverities
Improve the column name for Binomial Model Metrics
Fix creation of remote sdk in tests
Add back support for properties type text channel mode in lexer actions
Removed calls to printStackTrace used for debugging
add validation tests
implements DomModel T
Remove unused quick setting display preferences
removed static from shared hazelcast instance
Hopefully fix intermitten failing test
Fix NPE in StrictMode handling
Fixed MCP mapping server side
corrects a typo ShutDown Shutdown
Execute request with no params in case of null
Lubricate a sticky notification panel
simplify the parsing for reducer ops
Fix my little bug
EntityEditor dispose fix
Fix ListView losing scroll position
disable intention in scala low priority
reset new boolean
Added javadoc to getChild to explain that its really getDescendant
the first character should affect hash code
Add invalidate hack
Don t paralellize VersionResponseFilterTest
fixed presence template dedup for online status
remove duplicate email title
Change printing of routing table from info to debug
do not show class icon for Find In Path usages
handle null bytes from getData request
Allow undefined reasons to be passed by notifyVideoUnavailable
Fixed log message
using different seed in foldLeft foldRight tests to show the difference clearly
Fix a crash
do not wrap RuntimeException
Explained PCFPOISSON filtering in EdgeFilterinMode enum javadoc
Fixed incorrect registration of new block families client side
Fix spelling of dimReverseExtractionNamespace
Show file that causes Cannot add resources from jar error
Don t run the normalization pass if the using transpileOnly
Adjust time in State reversedClone so minTransferTime does not cause missed trips during back optimization of itineraries
Create a new Logging constant for Profile Challenge Settings
frame key was being passed into the job key s spot fixes python test
Correct the Javadoc of ForwardingMultiset standardClear
only memory barrier pending publications at end of batch
Saving Blacklist and Hold for Moderation lists when dismissing dialog
Lightened the tab background color
Fix Text Showing on hidden LoadingLayout
Removing windows from tests that use tmp directory assumption
Changed throw exception to log severe when the received packet id is unknown
Fix broken test
New lcd contrast values
add token pre process for uima
Improved error message when file URI is mistyped
Remove lingering TODO that was for LaunchConfig
Added toString to TestStatusLine
SerDes that do not inherit AbstractSerDe do not get table properties during initialize Jason Dere reviewed by Xuefu Zhang
Fix NPE if Hystrix reset called when it s already shutdown
Remove ReadableInstant constructor
Fixed NPE when no room name was specified when registering with a room
Added CoreExternalAnnotationsManager editExternalAnnotations method to avoid compilation error on updating to newer IDEA
Fix mis merge
disable action when no project is available
Make ViewHolder class public
Removed unnecessary params from anyRequest s javadoc
Fix color for highlighting resolved method
checkCanceled in python resolve
Fix unsynchronized read of currentRequestStartNanos
find file in read action
to make sure it works with Android
speed up PathKeyFileStore by using SingleIncludePatternFileTree instead of DirectoryFileTree
Tweak text size in results
Don t trigger securityOff events if the call has been already ended
Remove no longer needed suppression method
updating shader program doc
Fixed MIUI Email apk decompilaion
Remove deprecated props from DatabaseProperties
Add debug flag to console
Make it clearer
update circut for Range interface changes
missing since tags
Corrected a command description
Fixing NPE in during client pool shutdown
Remove unused field from ModelSpec
update moved files references
Skipped tests not capable running on CI servers
Shadows Fixed issue where Edge filtereing was not properly initialized and was causing a crash when compiling the shader
clarify timeout message when shutting down kafka input
Drop all inners classes if sorting is needed
If the user performed a very quick touch sequence without a response
Removing mipmap generation during image texture loading
properly implement equals sod off Player
adding scope to group does not update containing inspections in tree view
Add MainThread executeSynchronously
Fixed EngineIron fuel acceptance problem
vcs Don t treat a root as unregistered if its VCS doesn t support root checking
Fix testA testB and testE in TestSymbolIssues
implement returning to original map view profile after GPX routing
Cast the output of _train get to a Frame
change delete file after undo file restored without content because changes were made to the cached document
Eliminate superclass callbacks
Set file buffer bytes for test
Pass only SourcePathResolver in IosPostProcessResources
Remove Volley Drawee rounded corners
Resolved a compilation error
Fix antecedent generator for new coref annotations and make coref same speaker more robust to null speaker
Remove debug logging
Addressed some comments
Tell the user about a failed delivery the first any time it fails
Method should return value
generate a new seed for SpeeDRF if one not specified
never change StanfordCoreNLP java
TEST Don t use extraFS files as legacy files in tests
Added template for class object
fix build break
Remove temp announcement listener in PeerGroup broadcastTransaction once done
Fix some quality flaws
Fixed typo when building up the deadlock detected message casuing the wrong transaction to appear in the cycle
Remove extra assignment in BlockWrapper constructor
Switch default BloomFilter strategy from the broken MURMUR128_MITZ_32 to the
Help id corrected
added show to Toast for no camera detected
removed TODO because the simplification I had in mind doesn t appear to work
fix elasticsearch connection KB help link
Fixes a NoSuchElementException in the contact list
SourceFormatter ignore init ext
improve master logging
Remove GrailsConstraintsUtil ConstraintMethod
Remove volatile memory barrier from starting
TEST mark IndexWithShadowReplicasIT with awaitsfix
add two little methods for utils
Fix numerical stability on perfect prediction
Fixed issue with rendering colors on Lollipop
Method doesn t throw an exception
Use a subtarget to avoid contaminating the container
Set touch mode to be TOUCH_DONE_MODE when calling doDoubleTap
ilya a more formal definition of Contract pure
Compacting the code for largest bst in BT
Removed invalid import
Fix comment in DaemonInfo
Fix more indents
once more improve handling of too many errors message
Remove some unused member variables
Fix javadoc markup
Fix OEM native library path bug
Stop Reload Android Webview On Same URL Before
Add set pin method in TachyonFS
Make AMBIGUOUS_FUNCTION_DECL an error by default
Fix wrong booleans which shouldn t change anything in practice but the code is wrong
Fix typo in EastAsianWidth javadoc
Optimize ModelPath child
fix skipped variable setting
Removed unchecked warning
Allow open classpath configuration anywhere inside java project
Align test with expected behaviour currently fails
ensure root node
added run helper method too
Turn misplaced type annotation warning on by default
Add default downsampler and transformations
improved error message in Fuzzy k Means
simplify null check
Restore live wallpapers if specified in the restore data
hotfix Remove noisy log message in TaskCancelerWatchDog
Iterator characteristics row fixed
Extend the examples with the optional ext
Fixed potential NPE when getting the camel context name
Fix for x window crash on Display destroy on linux
Fix number of nodes in the test
Make vCard composer handle null of Photo entry correctly
named patterns and view methods inside str literals
don t reset encryption choice to auto on archiving
Added size limitations for spilling queue elements and fixed bug related to file memory transfer
moved outDir creating from setOutDir to decode
Reverted the change with TabbedPaneWrapper for default code style panel
renamed parameter to fillIfAbsent
corrected to errors view not resolving properly
Add parsing error test to distributed queries
add missing string format specifier char
Fix discconect if connection is null
Fix a NPE in JavadocPlugin
changed FileHandle has two new constructors for use in non crossplatform tools that don t need a backend
ensure completion manager detached when AceEditor detached
Remove tests for Key Equality
don t set platform prefix if already specified
remove duplicate overload
Fix JS function to method refactoring
Include both title and URL when sharing
Use new downloadUrl helper
updating doc to reflect JF work
Changed callers of Iterators skip to call Iterators advance
Only mark all messages as read on the server if that s supported
Remove unused code
add back code that got lost in translation
Adding pause functionality for streams
don t use GlyphVector getVisualBounds to determine clip region when part of glyph vector is to be drawn
Removed superfluous SuppressWarnings unused from PutAllOperation and PutAllBackupOperation
Fix infinite loop
Catch ActivityNotFoundException in Recents
do not search for disabled queue configs
Changed EditSessionBlockDelegate getHeight s return to match what Bukkit s worldheightheight methods return
update setMode to respect new interface
Added SuppressLint NewApi
Log a warning
Location implements Cloneable
emoved Author tags
don t highlight unassigned vars in boolean checks by default
Add an API to detect if an config is valid or not
Fixing a build failure
Fixes range check bug in recordCountAtValue currentHighestValueLimitInAutoRange should be
Don t send the onServiceDisconnected callback after close
do some optimization according to the inspection from android studio
compiles fine under eclipse
Fix NullPointer in VpnServiceBinder checkStatus
Don t change state when inflating LayerDrawable
Only send master volume or mute updates if the settings have changed
Extend FlowFilter to include with for other filters
Do not cancel entire task when aborting a single buffer
Add getSelectedIndices and getRowCount to FastSelectTable
Fix modifier order in BytesStreamsTests
remove all node s children on clear to avoid blinking when expanding watch node
RBucket should implements RExpirable
Add disabled tests for order by that highlight broken semantics
Fix cursor leak in avatar hash checking
Fixed CS issues
Fix NPE in tests
Fix artifacts in clip reveal animations
remove provider cursor refresh on resume not needed
fix wrong position returned inside the setListSelection methods listener firing
remove unused import
consider the whole path when highlighting
Remove deprecated code
when a notification group suddenly gets a toolwindow capability use this capability
Support the annotation SuppressWarnings at class level
Fixed ClassCastException that prevented certificates from being imported
new added method cancelText for ActionSheetDialog
Fix the ordering of extra values in the PI
Knocking out warnings
Added TODO for possible field reduction
Fix SessionEventListenerManager serialization issue
Fix modulo override
Fix double checked locking in StandardVertex
Added test case to verify that content type is not sent when there s no request body
remove unused code
Fix missing param to system audio mode action
Fix HttpResolution to follow entire location header uri including query
Fix output stream to not use the converter in the buffer but do a simple hard coded conversion
Rethrowing SPDYException in case of stream exception
added subscriptions test
reset FileEditorManagerImpl in tearDown
Added success method for all commands
Fixed input types for email web address preference
Revert Don t create ssl configurate resource if there isn t a ssl attribute
Fix the calculation of method parameter register in MethodAnalyzer
Removed unused import
ConnectPlugin android should return error if trying to login or other stuff before calling init
Remove redundant else
Removed compiler error check in PactConnection setShipStrategy
dispose application components in tests
Made getTimeWritten public
Adding StateMachine hasMessages StateMachine hasDeferredMessages
remove bam yt
Remove unneeded set creation
Removed unnecessary listener check
fixing improper usage of optional debug API isObsolete
pt add java compound rule
Corrected concurrency advice in Reducer
prevent eternal waiting
Fixing findbugs issue in PortableUtils
Initialize attr array as empty array when passed in as null
shut down daemon immediately when autopopup completion is started to free CPU for completion threads
added logic descriptions to HeightsCalculationUnitTest
Fix poi not found
clarify variable name
Use a pooled byte buffer allocator
Revert Unbreak index range calculation
Throw exception to debug build issue
fixing null check
align bottom indent when has no fixes
Remove redundant reflective call
Add text representation of ROUTE_TYPE_LIVE_VIDEO for debugging
Fix API method typo
Change ScaleTransformer isNextToLeftBound and isNextToRightBound method implementation
Introduced XBreakpointType createCustomPropertiesPanel Project method old variant deprecated
Log warn message if leftover shard is detected
Fix typo in inline property diagnostic message
fixed CS issues
Remove heldMotionless disabling native scroll mode
Fix NPE when implementation URL is not given
Allow null as updateCount in StatementResource
remove invalid comment
Adding support for passing HTTP headers to a Browser
stubs make findTypeInVarSpec more stub tolerant if the common type exists
Set the parent group when we add a new contact to a MetaContact
Update the documentation for recoverInline to advise against
Should save entry value
Add TableCommitNode processing to PruneUnreferencedOutputs
Remove the Log info as Claus point out
Remove unneeded cast
git init now refreshes directory in order to prevent incorrect nofication about roots
Add javadoc to SmackException ConnectionException
Added assumption to make test more robust
expose meteData directory
Updated javadocs to reflect new contract for interface
Add new check that factor levels of training data match after checkpoint restart
eliminate eclipse warning
Add a missing trace to removeContainer
Simplify version test for postgres
Add headers which need to be present for the upgrade to be rfc conform
Avoid possible overflow if the text ends in a high surrogate
set ProtectionDomain for newly generated Class
Added editor to main menu
flip equals in case a language has null as a name
replace exception in SpdyHeaderBlockZlibEncoder with EMPTY_BUFFER
Added config check to prevent crashes
Added since tags
Simplified Predicates test
Cleaning up output messages
Fixed SimpleIdCache clear to not invoke onRemoval twice which can happen in rare cases
do not store empty expressions
Fixed endless loop if invalid namespace is sent in auth element
Fix missing WITH NO DATA in sql formatter
allow dynamic refs in match
context Help working
Add method that already existed on DefaultExecHandle that exposes the process state on the corresponding interface
Fixing condition in publish method
Added message on non availble attachment provider
Restore PassiveScanThread constructor
After SF is needed change the expected result in the test
fixed cast error in ResultSet
Fixed list preference not using dialogTitle
Remove statusbar height from minimum webview layout
Include key in error message when value is null
Fix paragraph iterator
don t create new auth if one exists already
remove duplicate finish
Fix NPE when creating Action elements within an extension
Add document to SpellCheckerService
remove an obsolete comment
Added ButtonGroup clear
Fix typo in ActivityWithReceiver
Qualified Utils static imports in UtilsTest
We shouldn t be checking the error message for sqlite constraint exceptions different phones do it differently
Fixed incorrect package of Document class in BsonTypeClassMap documentation
Incorrect and ancient code would accidentally clobber the wrong attachment
Deprecate ANCHOR_TYPE and IMAGE_ANCHOR_TYPE from WebView HitTestResult
combine with custom filter
Disable tests for APR that do not currently pass with that connector
Add NPE protection
Fixed OnClickListener on a button s label
Added a test case to check if CUSTOM properties in class are exported an imported correctly
Added additional constructor in TestThread for our test library
fixed error in onClick remove mode
Remove unused import in o e t UpdateThreadPoolSettingsTests
remove extra paren
added a method that returns the known blender primary types names
Turn off chatty logging for everyone
Fix appender not locking when getting log position
Remove unsafe field in BytesStreamInput
Rename hive ipc ping interval to hive dfs ipc ping interval
handle zero rowSize in throttleResolution calculation
mark matched comparators with unused symbol mark
Add methods to obtain the foreground background schedulers
Fixed manual retry of upload failed due to credentials error
Remove unused import
allow error handling override
Renamed Solo assertLowMemory to assertNotLowMemory and left the old method as deprecated
make APersistentVector SubVector public
serialize timestamp on the response
correct a typo
Don t report a validation error if there s no validation set
Use our cascading ClassLoader
Remove the obsolete unused hidden constant
Removing an extra line
Deleted trailing whitespace
add NotNull annotations
Moved the generation of the Trees object to init as it seems to be the same on every round
Removed unused imports
added config service start looks like it works
Fix absolute path in ShellStepTest
add empty constructore to AudioNode to reenable serialization temp fix
Fix bug in Scene s use of setTag
editor should display groovy rather than java in editor
Actually uses seed in RandomRule and a way to reset
Remove advanced edition from the list of possible editions
d oh really raise the visibility
highlight suppressed problems test
Removing in source files
fix broken test
Fixed an arithmetic error in a title parsing test
Add missing method docs
add labels as vocab words
Check for internal org json JSON classes when sending content
Add brackets braces and backslash to alt space character entry popup
added getBaseScale method
Fixed wrong first tab status icon reported by Damian
Revert Fixed CS
Fix javadoc build
Hopefully this nukes the config files after each test now
CookieGenerator explicitly sets secure and httpOnly flags in removeCookie as well
Correct the tag s name in the exceptions
Fixed a CS error of camel xmpp
added method to create a subset of the orginal dataset
Add two getters to retrieve the current configuration from AudioStream
Fix missing assignments for majorityEnforceBlockUpgrade majorityRejectBlockOutdated and majorityWindow
Reuse applicability criteria
Ignore HEAD when displaying interactive rebase
Allow Metric on methods and parameters
Can t get JournalArticle by resourcePrimKey
Removed unused imports from BuilcraftAPI
Prevent monkey from triggering bugreport
Turn off accidental test
Advance Bombed tests default year value
Provide local argument to the message source accessor
Remove call to rebuildWebTextView from handling selection change
remove an unnecessary cast
Move output panel help key registration to prevent NPE
Add copyright header
respect Hadoop create overwrite semantics
Fix JMX subsystem marshalling
improve comments of cassandra startup code
Add license headers
Add label to InfinispanConfiguration customListener UriParam
simone added an attribute to abstractlifecycle increasing the attribute count on influences
Added ViewPort clearProcessors to remove all processors from a viewport
Fix StorageProxy syncWriteToBatchlog
git merge conflict removed
Removed the condition for the browser so this will be raised anyway without having to use Internet Explorer
Fix a test to not require a fallback IP address
Also use last upgraded state to enable supporter features
start delegated activity after login
Fix typo and add missing import
Adding additional validation to IgnoredWhenDetachedHandler as suggested by yDelouis
Animate closing menu again
Increased robustness of initial execution graph creation
Fix error removal of the recipe file in the console log
Fix a line ending issue for building on Windows
add final to EP_NAME
Fix scrolling issue for Gutter Icons
Add a failing test for Stream groupBy map
Added ability to set text size in FontAwesomeText programatically
use a label instead of a text field
Add unit test for behavior of lexer with input position sensitive predicates
it had absolutely no effect because of a missing negation
Remove TODO that is now done
remove javadoc warning
Reduce the memory requirement of heap dump compression
Make the JSR web socket filter support async requests
Make documentation for cross profile intent filters clearer
Prevent dialog from invalid context
Remove redundant condition
Removed unused import
Fix the SDK build
Fix variable naming
test android test for circle ci
Fix clipping not being activated on children in groups
Remove superfluous not null check in ContextLoaderUtils
Mentioned the active keymap in the shortcut conflicts title
tweak search match drawing on retina
Added some missing status bar updates
SMALLFIX Follow convention of test names ending int Test
Added some comments in the BeanExpression
Add new resolvers to default resolver bug spotted by remm
Fix odd whitespace no functional change
Add programmatic filter test
Fix missing import
added missing headers for io classes
Fix logging error
default weight based sorting fix
fix broken code sample
add token dotdotdot
Display more details about SSL connection issue
Added setter for capacity of ILIquidTanks
run mojo to point to correct documentation
Don t shutdown the executor when shared
remove fields incorrectly pulled in merge
fixed the JPASpringTest issue where it wasn t properly cleaning up
add assertion that shows hooks not being cleared for each scenario
Added debugging for bf emulator trouble
Add Bukkit getAllowEnd
Avoid costly Double wrapper comparsion logic
Fix issue with min maxDate not respected on rotation
Fix a bug in getSocketError in VpnService
Add tracking to favoriting shows
Added backwards compatibility for per world permissions
vcs log use the same baseline finding method from SimpleColoredComponent used in commit cell renderer for message for labels
APK mojo does not consider exclusions when pulling in native dependencies extra guard
Fix issue with route recalculation
Catch all exceptions to be sure
Add comment about why we can t check for publication repository compatibility at configuration time
remove line breaks
Fix NPE when calling PopupWindow s default constructor
Create translucent hardware renderer if surface insets are non zero
Don t hard crash if you get a null stack trace in Android
Fix race condition in SocketSslEchoTest
added missing tests for my sanity
Ensure OSGI WebApp as Service WebAppContext can be deployed only through ServiceWebAppProvider
Avoid extraneous EGL surface allocations
keep selection order
comment a failing test temporarly won t fix until new editor
Fix bug in idle timeout conduit
Added into illegal user characters
Remove a few lines of unused code
The command line query tool must print its output on stdout not stderr
Remove test message comparison
delete dead code
Catch those exceptions
Polished and removed system out
remove unused import
Use correct default value for dividerHeight when not present in xml
Optimize imports on InputsResource
enter move statement handlers don t need gsp
Fixed visibility of ImageFileInfo and ExifInfo class members
Deprecated BLUEPRINTS_TX_MODE parameter
Fix root selection after search
do not load document to check for isReadOnly
fixed SQL phrase
mention Django in module type description
Handle broken files in parse attempts better
add unused declarations
Update JVM warning text on Linux
TEST InternalCluster restartRandomDataNode should restart a data node
Add Inherited to EnableAutoConfiguration
avoid wrong memleak assertion
Fixed two minor javadoc typos in Getter and Value
Fixed a crash issue in the image settings dialog fragment
update processing java documentation since output is no longer required
Temporarily rollback a change that s causing SetupWizard failures during restore
Tweak group splitting unsupported message for DRF regression
Add Origin header
Fix trigger sensor re registration
Fix select object test failures
Fixed Y axis inversion in preview
Fixed EditText s recycled TypedArray issue
Handle non existent files
fixed after unsuccessful merge
fix a typo in PolygonRegion
add sample how to update the badge on a item
Skip empty pages in RaptorPageSink
Allow FreeTypeFontGeneratorLoader to be used without extra
Fix possible NPE
Remove unused import
prevent null ref in setAppList
for unsaved documents take proper psi
Enable enter key for movie search GTV support
Improve Chain Javadoc
revert initial AUTH_START for Authentication
Remove deprecated methods
Adjust line numbers of TTL expressions to their correct values
Testing possible fix for changed strings
Fix blurring when Fragment is retained
Fixed exception when retreiving nicknames
Fixed missing error data in NN JSON
Rename poorly named field
fixing javadoc errors
Remove unneeded commented out SuppressWarnings
Do not display getSystemService getSettings
Fixed potential NPE
Use the remaining for the byte buffer as the capacity is the total capacity and not the limit
Adding missing license header
Change the SingleFilePageSwapperFactory implementation name from striped to single because we might want to stripe over files later
Ignore test in OCamlIntegrationTest that depends on compiler platform specific binary artifacts
Fixed possible resource leak in EphemeralCheckpointForwarder
Fix bug should return this
Reducing duration of the ringbuffer stress tests nightly is spending too much time here
Fixed a bug where the secure camera would flash when rotated
Fix NPE on Cupcake
Fix output of creating new machine in existed workspace
Remove deprecated code
removed verbose output
added support for samlp
Updated an old transaction statement
Tweak the expected result computation for TextViewCompat
Fixed the failing unit test s now all pass as expected
Changing default dynamodb url
Improve the description of the RewritePolyfills class
improved single resource matcher for rest
Fixed null handling
tweak toString map
Fixed initial metered state of new policy
Fix crash due to reverse selection
don t set syslog raw message as GELF full message
Add constants for banner location
Change a type
fix flaky blob RecoveryTests
Verifier getMetaClassField ClassNode can throw a NPE
moved double check inside null safe test
catch cancelled exception when after change lists loaded correctly
Fixed comment adapter typo missing
Prevent ClassCastException when undeploying a FailedContext
fix a bug
change cul transport to report in hex
Allow the character in phone numbers
SMALLFIX Add openAtPosition for S3UnderFileSystem
Add test of odd number of param arguments
added null check in sasl response verifier
Improve performance in MOVED and ASK response parsing
Removed the AppBarLayout LayoutParams import for easier readability in the code
Simplifies var names
Remove another generic author tag
remove unused import statements
Fix printout of confusion matrix
Add override annotation to implemented methods
Set secure user cookies and only for HTTP
Fixing new line at the end of files marked the rule to be an error
Make multiDimensionSpline Serializable
Moved abrupt connection closing messages to debug
Exclude deleted messages from search results
ensure positive notification
Make a few methods in DatabaseFactory static
Tidying up unit tests
Fix NaN bug in OnBoardDepartServiceImpl
Fix merge conflict in TestDatabaseShardManager
Java Double the length of max event length
Remove unused imports
HorizontalScrollView should delay child presses
moved JUnitIgnoreTest java from src test groovy to src test java
Added the addApp method to the interface as well
added better support for launchers that don t resize on initial placement
Set better intent flags in integration so user s activity stays on top
Fixed typo in JavaDoc
Adding GPL license to header
Added a check to avoid NullPointerException
RenameDialog retruns correct preferred focused component
Set copyWebInf to false by default
Object delete in job
Fix Watched Unwatched filters
Fix missing telephony contacts in the menu opened on call history records
Made Auto Resetting of TimerHandler dis enable able
applied Stefan s patch
Fix copy paste error and throw correct exception
Add a couple additional test cases
Moved up empty buffer check
Edited src com koushikdutta test UrlImageViewHelper
Implement RawTableMaster getProcessor
Use short type names in super function prompt dialog find usages and refactorings
ClassDefinition improved javadoc
query planner constant removed old todo
Polished javadoc and added note about done UoW from PollingConsumer
Add API to control scaled health
immediately reset current book hashes value on changing book
removing unnecessary comment
fix quality flaw NPE check
Make archive metadata idempotent
set editing to false after code cell run
Importing exception needed in a test method
Removed unused imports
fix bad GitHub response handling
remove unused import
Stop Shift Backspace from foward deleting
Fixed unit tests for empty row key listeners
Fix comparison of strings
Updated javadocs after changing one of the features
renamed addHandler to withHandler
removed TOMBSTONE from SCArrayQueue it wasn t being used anyway
Remove unused code
Suppressed event service log
Move id files as well as the store files themselves
Changes the default exposure values for the post processing bloom effect
SMALLFIX Added the usage of the delimiter parameter to the getList method of the TachyonConf class
Fix C8Chunk was allocating extra array and couldn t handle NA
make Groovy Shell action non dumb aware as it requires findClass
need this error message
FredrichO JonathanL fixed issue with views not recycling on media grid
fixing version number
removed static class reference from builtin AST transform implementation class to third party class junit framework AssertionFailedError
Remove debugging output
fixed TableLayout borders
better debug message
Add some whitespace for readability
Wildcard query on non existent field matches all documents
Adding more tests to git url validators
throw correct exception
Add override annotations to generated code
Added Javadoc for explanation
Keep endpoint state until aVeryLongTime
make clone cleaner
Showing Magic Link Sent fragment
Use a warning instead debug
Tapping connection bar now hides it then re checks connection after a brief delay
Fixed compile bug with generic signatures in input format
remove stale comment from CircuitBreakerStats
Fixed HttpOutput spin
Fixed NPE in ItemStack
move all changed values on top
Fix logging of android platform detection
Fix synchronization issue in singleton
Improve Javadoc for DatabasePopulator
added log for failover
update buildTestSSTable to compile
Fix SysUI crash when no metadata is provided
added custom strategy parameter
Added SpawnReason BUILD_SNOWMAN
Changed AuthorizeTag to use StringUtils deleteAny instead of replace
Add traces when windows are animating
Nullify listener onDetach for PeopleListFragment
ensure editable on column resize
Show the create account form if the wizard supports it
skip collections for injection
Fix broken unit test
Removed unused field
fixed link to old resource
Remove debug messages
Improve error reporting for buggy View subclasses
Fixed javadoc comment
Fix a bug where an action bar could be created when it should not be
Fixed crash when tapping comment icon in reader list
Improve the grammar of a compiler error message
Disable the MoreTypes equal optimization of returning true when TypeMirror equals is true for the specific case of ExecutableType
Removed chopping by character in the URL to get MUC Plugin running
Adds a setToken method to ColibriConferenceIQ Recording
Fix null mConfig in addMissingResSpecs
Added session destroy example
Fix broken logic in SettingsProvider parseProviderList
SearchEverywhere shows Searching if the popup is empty
Add media log to Watchdog stacks
Change zeppelin gui context from zeppelin to in dev mode
to avoid exception in JarFileSystem don t remove trailing slash from external resource URLs
Fixed a bug where the setting to silence list notifications would get turned off whenever you edited a list
Fix test failure
fixed merge mistake
Remove DeadlockHealthCheck from defaults
Add initializer to variables declaration in Scope
ignore some GUI test to let showcase test pass
Added javadoc for BlackPawnTextureAtlasBuilder constructor
Cleaning up PlayerDropItemEvent
Remove unnecessary clear in count aggregation
Fixed findbugs issues
Remove spaces reformat JavaDoc
Fix add tile
Improve javadoc on SharedElementCallback
Fix type in Java enums
fixed javadoc a bit
Data search fix
remove unneeded read action
don t assume long distance mode
Fixed log and spelling
Remove Eclair back press example
Add remainingCapacity to RingBuffer
Fixing the build
ReadTimeoutException and WriteTimeoutException must extend TimeoutException
Pass NotFoundException constructor args in the correct order
Remove code to explicitly add EOF transitions to rule stop states with no outgoing edges
Some warning message to confirm that certificate authentication really took place
Removed the modification of the input parameter that lead to a wrong axisSamples member then the cylinder was closed
Migrate all accounts to use compression
Modified axpression with arguments parsing
Reduce the potential contention caused by ResourceLeakDetector sampling
SourceFormatter missing spaces
Added missing javadoc
Made ConfigureUtil configure method that takes a resolve strategy public
Fixing incorrect log message
remove variables from activation mean listener
Added all group to the list of disco items
Fix bug when stopClusters weren t lazy initialized everywhere
Use assertNotSame x y in place of assertTrue x
Fix failing functional test
new build investigation
Properly set the EXECUTE_ASYNC_CONNECT value
check write object to be non null before calling close on it
Add newline after early return
Stop initialisation if node is dead since no more packets will be sent anyway
Fix error when association group has no members
Adds URL to RecordingStatus
Add some authoring information
Remove unneeded emulated true to fix open source GWT tests
do not create fake descriptors when override private functions
Remove unnecessary throws in JobsResource
using regex pattern on COPY did not work properly
Include goroots in scope in tests only
don t search for stubbed elements inside javadoc comments
handle case of no selected vertex in
HazelcastInstanceLoader now makes use of the new getOrCreateHazelcastInstance method
Fix planning of TableCommit to run on coordinator
enable anti aliasing for balloon line drawing again
Fix Javadoc error in JdbcOperations
set partition ids for paging predicate
Make astrid sort match astrid com
Can t call Reflect create A B null
Fix API lint warnings in CordovaChromeClient and CordovaWebViewClient
Fix default implementation of resolveIndex
Using exec paths instead of root relative paths in the JSCompile bundle files
Handle exception on ins close
always publish HTML widgets as static content
automatically annotate compiled domain classes with Entity at the byte code level
Fix bug where modules with dependencies caused errors on the world config screen
Close handling changes
Add length method and EMPTY constant to IndexRange
don t finish activity on ACTION_MAIN intent
Remove sneaky little break breaking syncing
delete obsolete method
Fix copy paste error
Fixed expected stacktrace output in tests
fixed test runner for non python modules
should fix memory leak in tests
Adds additional dynamic range to verify auto ranging during DoubleHistigram auto sizing
NTI Add testcase for types auto defined on window
made jingle connection array thread safe
Added missing license header
align order of statements
Allow the buffer cache to be null in the cached resource handler
Fix recycling bug
fixed regression in file delete detection due to missing
Allow ExtractionDimFilter value to be null
Assert null pointer warning
Make credentials error notification high priority
Autobundle all notifications don t leave the most recent one free
Fix trailing whitespace as nagged by Checkstyle
add missing processOutgoingPath
Add another safe to ignore socket error message for SslHandler
When making a ClassDataItem from scratch sort the fields and methods
hide AudioAttributes usageToString method
pass a meaningful value as settings parameter to TreeStructureProvider
remove unused variable
Fix possible NPE
Don t accept input from process after TranscriptScreen is finished
Added List swap
Don t get application info for the system
Add missing softly closed flag to clusterStateHolder
Hidden some methods of ResolveImportUtil for Python
Minor add constructors with messages and causes to KotlinReflectionNotSupportedError
add comment to illuminate why we only dismiss the dialog when launching an intent for in app search
Expose setter for configuring response timeout
Fix broken test
Reactivate backup service after device owner is cleared
activate editor before Hector popup
remove one unused method
Fixed typo and updated Javadocs for the logo fields again
improve whitespace filtering by also removing r fixes one small TODO
Fixed the bug of not setting the location field of the instruction in addInstruction i instruction method
fix erroneous mIsWpcomAccountWith2FA and mIsInvalidUsernameOrPassword init
fixed a bug comparing non latin display names
Changed default log formatter
Fix method called after upstream refactoring
Remove Xposed logging
only perform explicit save on explicit lint gesture
Remove an unused constructor of InputMethodSettings
added javadoc example
Fix silly mistake
Fix Gauge javadoc
add xml to start of output
Fixed bug in var len integer deserialization
Improve error message
fix broken readSettings logic
Always re establish kernel alarms when considering new alarm set
Switch active input after routing control
Updated Editor example project with changes to onMediaUploadProgress
Fix checkstyle issue
preserve original pagedtable option to avoid changing preview gallery thumbnail
Fix wrong String comparison
Made class package private
Adding exclusion for transient fields
Add some Nullable annotations to RecyclerView
file based storage should have an extension
Just removed dbg print I left in by accident
Added pass code protection to AuthenticatorActivity
Let s get Groovy with an apostrophe
add support for reload for case of auto merge in remote
added test for provider lookup
don t resolve qualifiers twice
Add getters for the specified timeout values
handle case of empty non null intermediateplaces
Fix NPE in RequestFutureTarget
Fixed version numbers
Adding data so that activityResult of fragment won t ingore it with the data
Removed deprecated method
fix broken UT
added fallback for integer
Fixed race condition in test
don t remove Default changelist when everything from it is shelved
Fixed findbugs issues
Remove Alert s instance variable no longer needed
Fix empty downloads list
Don t set sync freq in login
Whoops actually exclude pre HC apps
Fix wrong resource string
removed unused import statements
Fixed issue with resetting BeanInfo if bean has changed
Fixed int test for changes to worker process ClassLoader hierarchy
Add Javadoc for new FileHandlerSpec files convenience method
add example code which shows the importance of setValue usage to javadoc
removed tab character
don t auto expand Scope Type WITH
Fixed r option
Fix bug in onDetached lifecycle
do not start busy spinner on fast rediff
Compress everything on the site
removed unused code
gave test more time
Do not hold a lock when calling API to grant default permissions
Added the right logger class in CollectionFilterer
Fixed bug in get of property chain for batch inserter
Do not set waitForDebugger if the process to be debugged already started
add firing delay to fix problems in tests when events fire when file change is in progress
Reset reverse state when an animator ends
Missing setName method on OClass interface
Fix lockup if sender is used to send and empty buffer
Fixing previous commit about scrollpane view
Remove obsolete comment
Remove Test annotation in delegate class
fix quality flaw missing license header
ignore faulty configuration
Fixed error during closing of delimited input format
compare non captured types fix foreach loops
Reordered the address book fields to something a little more standard reasonable
Log package changes
Added toString to ToroDocument
added version attribute for iws files
find java source roots in new source path only for java sdk corrected
Fixed Internet Explorer compatibility
throw a descriptive exception when trying to sort on multi token or multi values field per doc
Reduced indentation on a piece of code
Fix state when importing settings only
Revert this change
remove unused replacement
Fix SpinnerCompat to use the correct PopupWindow
fixed previous check in
Simplify testUnsubscriptionCase test
Add iodotNetty noResourceLeak option to microbench
Don t include checksum in changeset description
do not change parameter types in MethodSignature
Uncommented for now previously commented out hook in DKV to remove
Remove wrong javadoc comment
Make the registrator static
remove outdated workaround lastTimeRouteRecalcAnnounced
Revert change to example file
make switch action in Find Dialog DumbAware
remove stray debug code
Removed moveData method
Using open instead of load for asset bundles
Commenting await init to warn that it gets removed by instrumentation
fix broken BUNDLE_TITLE
Adding missing binder statement
Removed obsolete type methods from authentication modules
Only ignore LinkageError on loading LibC in FilePermissionsHandlerFactory
Fixed CheckBoxStyle copy constructor
just changed some comments
fetch user from database instead of using the one in the session to avoid hibernate exception
Fix bug in airplane mode
Fix some Gmail tests
Remove non final static field
Make FragmentActivity supportInvalidateOptionsMenu public
Fix LineMarkersRenderer painting code in EditorGutterComponentImpl
Fix quality flaws
Don t log derby driver missing info
Don t finish MessageList when forwarding or replying to a message
add more debug info about discarded message
Also notify heartbeat when polling transport is used
Commented drop of database complex map
kb don t exclude instance members from completion
Added LivingEntity getEyeLocation for getting the eyes location
added test that covers validateConstraintsForNotUsedColumns
optimize keyboardlistener callback invocation
Removed unneccessary cast
remove the static method
Fixed a bug where no event was received for groupAdded
changed expected exception class to CheckstyleException
AccessibilityNodeInfo does not report checkable coreclty
Don t run every build with dry run
Add missing quote
Fix oil and fuel opacity
Try switching to blocking ProxyServlet
added Texture constructor to support TextureAtlas
Fix Typo in MergeContext java
add test for single null identifier
CASImpl ignores authentication attributes when registered service is set to ignore attribute release policy of svc registry
Provide shortcut for creating ClientOptions with default values
Remove random field as it s never used
Updated RealmMigration JavaDoc
trying to fix activity has been destroyed error
Found a KdTree bug which prevented it from compiling
reverting unintentional API change
Fix PubSub Affiliation getElementName
add extra condition to check of redirection
Java Not all errors have an error message
Commented flyweight example
Terminate instanceInfoReplicator with shutdownNow instead of shutdown
Reduced logging during task recovery
Exclude EXPRESSION nodes from all range validation in subsystem test not just min
Added one missing space to the javadoc
Remove unnecessary check from AbstractTestBlock
Remove dperecated code
Remove unnecessary comments
Fix external photo
Fix compilation issue with final assignment in lambda on windows jdk
correctly handling null actions
Fixed find bugs warnings
Fix NPE in SqlQueryExecution
Removed best conf
Fix null ptr exception in getReadingLevelScale
remove orientation spam
Fix redundant String constructor call
Make fields final
Adding filter cache dashboard cache and easier startup setup
Updated MasterClientHeartbeatExecutor java to use MasterClient disconnect instead of MasterClient close
Make some stuff in DataInput2 DataOutput2 public
Fixed issue with using wrong display id when creating context for displays
removed useless import
add mention of JOGLAppletLauncher
removed debug logger
remove some calls to BigDecimal setScale where return objects are not used
Throw exception on stop failure
handle parentheses in null thrown inspection
Tweak Timepicker line width
remove unused imports
Moved DDbDDlFormat to the editor package
Remove bigdata test file not available
Fix null pointer
Fill only the area that was modified by the previous GIF frame on dispose background
Tweak documentation of Protobuf Serializer
add CommitDataGetter getCommitData Hash
Improve logging around check pointing
Fixed the camel itest karaf test failures
removed println in test case
Added Main class to camel core
Use public API for getting type token
Moved responsibility of setting current nodeID
Avoid supported ABI list containing unknown
Add the Service Worker Allowed header to the HTTP header constants for Java Go
improved error message
Fix manual tests not finding activity plugin
Added ShadowContext fileList
Fix Mustache tests for Windows users
removing empty method
Prevent rare disastrous classloading in first call to LockSupport park
Removing old method from embedded store
need a WORK hashmap per remote jvm
Trivial Javadoc commit to trigger a CI build
The volume controller has notification policy access
Set found escape when parsing empty strings
Speed up NBHM Junit test
MemberNameCheck should not validate interface constants that s ConstantNameCheck s role
Fixed bug where first terminal was incorrectly used as first state
Add helper to set If Modified Since request header
throw JedisDataException when sending NULL values to redis as it is not a valid value in the protocol
allow specification of config file on the command line
Remove unused imports
Add null protection to ServletContextHandler doStop
use equals for number equality checking
Fix string comparisons that were using
set focus on search field and enable keyboard
Add static to some fields on BlockTransformExtentTest
SMALLFIX Removed explicit type argument in PermissionCheckerTest
Using more common way to check the file in DumpToXmlTest
do not run invalid test accidentally
Fixed test for unix pathSeparator
failing test for multiple editors for one document
added method for database
Add convenience currentIndex method to BaseActions
ignored testIndexCleanupOnMigration and testMapLoaderLoadUpdatingIndex
Fix incorrect warning message in OBJLoader
Add the proper backend to Deep Water junits
Add TODO comment in spurrious tests to check
added cleanup mechanism to launch plugins
Add some unique id to slide filename to minimize the chance of browsers caching the presentation
display the path where the graph is being read written to
Removed unnecessary ActionBar setup
Remove wildcard import
fixing deprecate Javadoc comment minor cleanup
updated following change to TagLibDynamicMethods contructor
Added permission needed to send events
Turn off the operator wrapping test until I can provide an option to
hide Balloons when some action performs
Remove dead code
Take out a debugging line
Edit connection dialog fixes
Allow PredicatePushDown optimizer to handle NullLiteral from nullInputEvaluator
Bring chinese test back
make IconicsLayoutInflator private
Added annotations for voluntarily unused methods
Drop unnecessary parameter from setActivateOnItemClick
Fix SafeTreeSet so delegate tailSet is wrapped by a new SafeTreeSet as is headSet and subSet
Removed order from openid filter
Add methods for getting axis min and max value
Make a couple more Wallet methods public
Removes listener in chat room subject panel
Removed bad tangle
Use android text util Regex EMAIL_ADDRESS_PATTERN for email address verification before account creation
Trim input username and password
Add missing annotation to openTSDB sample
show the ModelNode on validation errors
Fix issue with editors loading when tabs not showing
Add borough as city type
Fix swapping of pitch and yaw in setLocation
bean validation Upgraded Javadoc
Cleaned up some deprecated warnings
don t process anonymous class declarations while resolve it s base class
fixed text color for hyphenated words
Comparison method violates its general contract
Remove scanners that cause runtime dep conflict
Fix the fix for the fix of red dot
Add missing lower bound to Action parameter
Give InitializationError a useful message
Put NodeEntry in Supplier SkyValue passed to EvaluationProgressReceiver so it s readily accessible in the program
Added back author annotation to test renrerers
Add call to setCallbackdId in addPlugin
remove footer divider
Delete useless line
Fix Eclipse warnings
Removed scalein from google cards due to performance issues on scaling imageview
fixed off by one error
Fixed assignment operator in validateBinding
Remove illegal assert
Don t animate screen brightness if there is a pending off transition
Bump up Palette s accuracy
Fixed failing tests
added test for executeWithKey
Check the return value from HistoryCache get to avoid NullPointer
Fix bucket test
more verbose output in case the correction itself triggers an error
Added requireNonNull for setter of hostnameVerificationAlgorithm
Fix compatibility issue with WebStorm
Fix minor issue in MethodAnalyzer analyzeMoveException
switched to instance variable
fix failing ci formatting autotested javadoc code
Check am context
Added DateTimeValue Handler ReorgCheck tool
Added license header
Fixed up linked list a bit
Fixed desert list initialization
Increased delay between packets to give more time to the server for processing the packets
Fix crash by adb shell pm list permissions f s
Fix quality flaw
disable debug color
Resolve USER_ALL when inflating notification views
speed up tests
Fixed bug in constant expression evaluator
Removes debug printing
Adding supress warnings to tidy up
Fix test assertion
Fix test failure in TestRestLiResourceModels
Insert FML packet handler into Vanilla pipelines in case modders send FMLPacket to vanilla
fix failing tests
Fix R import in BottomNavigationView usage
Removed System out
Small optimization of NativeString constructor do the String writing
Added HttpTunnelAddress toString
Fix TelephonyManager to grab the best context
Add license to ActionBarTab file
Fix a bounds check problem in IndentingWriter
Renamed the destroyByteBuffer method to destroyDirectBuffer
Check NPE condition
Rename TCN_RECOMMENDED_MIN TCN_RECOMMENDED_MINOR
update test data
Handle editor reopening errors
improved error message
Fix redudant assertion in test
Fixed possible issue with cleanup of temporary entities where blocks are changed in response
Added feed column to SEL_FI_EXTRA
Switch the connectivity check to its own hostname
Fix SnackBar Crash
Fixed getSuperClass to return null for Object class
Fix inflation errors
Reenable colored bars for multiwindow
Add support for setting key listener in builder
don t merge messages over the char limit
SocketIOServer stop causes NPE
SMALLFIX Fix style
Move the point where the socket is removed from the connections Map so
tableView getSubtalbe will set origin table as parent to subtable not the view
Fixed the CS errors of camel core
revert removal of master loadstatus
Add a check for no three unary nodes in a row
Fix copy constructor
make groovy project compile editable compiler excludes configuration
Modify OneDayDecorator to be cooler
Replace Integer longValue with Integer intValue as we compare the result against an int
removed old code
added velocity test with annotated methods
Let BitSets auto adapt capacity
Optimize calculation of properties hashCode
Don t bork Safari by removing spaces in front of parens in media lines
Fixed default network mod checking to allow client side mods without the server side
Adds protected method resetValues
Dont wrap yourself
a reflect bug
add module settings action to dependencies trees
Add onload attribute to plugin in plugins xml to create the plugin at load time instead of lazy loading
fix broken validation query config port
Fixed record type truncation in generated externs
Fixed toString on private relationship type implementation
Fixed some TODO comments
remove unused import
disable type migration from multiple roots
Added public function that allows user to close the menu via swipeListView class
Make GzipFilterAutoConfiguration conditional on HttpMethod
Fix alpha keyboard shortcuts with ToolbarActionBar
Add back IcedHashMap classes to TypeMap
Added the two most common interpolation functions
Allows starting window to be shown for swipe dismiss windows
Comment the listener to make the sample as simple as possible
Remove temporary default value for k in KMeans
Removed unused lvar
Introduce GitVcs MINOR_NOTIFICATION group for minor unimportant notifications
AnimatedRotateDrawable should inflate child elements
Set core size before maximum
change word rigion to region
preferred width for left panel to keep size in check
Add JavaVersionCheckUtils transformer exclusion
added doc around new flag
added invalid configuration test
restore previous input after playing commands into the console
Fix string formatters in SettingsProvider SecurityException message
Fix intermittent JMX deregistration test failure
Fix http_jar documentation
Fix legacy disconnect callback
Don t throw an exception when parsing an unknown message type
Remove unused field
Remove programmatically disabling logs
remove unused method
override equals in BlockContainerIdGeneratorEntry
fixed merge issue
isolated check methods
removed System exit
better name missed the release
Set default locale to US in offending Scala Test Cases
add FailedToCommitException to registration
Add exception handling for XML errors
Remove some TODOs that have been implemented
Fixed bug in LZMA decompressor
Don t use CollectionUtils in DefaultClassPath because it is used in the very initial bootstrapping
Allow HTTP server port of buckd to be specifed with command line parameter
OAuthUpdate now supports PIN
We use FNV hash functions to generate a large indexable sequence of non colliding keys
anoter compressed string test
improved DataNucleus detection
Make constants static
Named rebuild index thread
use a per plugin named module json to avoid shadowing in development mode
Populate AndroidDebugBridge with adb path so it can start the adb daemon
fixed typo on warn message
update control captions to match paul s mockup
Moved float conversion above double
Fix creation of components
When copying advancedexternalizer from the legacy configuration use the
fixed an issue where calling setCursorVisible on the ChaseCameraAppstate before it s initialized was causing an NPE
TestApp now will start the default Hazelcast Instance
Do not dispatch context menu events to fragments if they are not exposing their menus
unified unix part
Use the file name directly and remove the now unused getFile method
Make the ProtobufParser timer daemon and labelled
Give the thread running in the default ScheduledExecutorService for the AbstractScheduledService a name
added small gap between modules list and facet editor
temporary restored API method to fix compatibility with Kotlin plugin
Renamed wrong sounding variable
Make TOs not strict allow unknown tags in XMLs
Fix bluetooth bonded devices
Added overridden onMouseWheel event listener
Add Google internal memory consumption tests for Immutable List Set Map BiMap SortedMap SortedSet
Fix index out of bounds in ProducerSequenceFactory
Adding test which crashes PhantomJS
Fix collection iteration index
Do not initialize DirectoryIndex in CoreProjectLoader
Fixed potential issue reported by hudson when shutting down
Fix bug in JedisCluster del command
Added Nullable to getters to be consistent
finalise scrolling before checking scroll position
Don t allow onBackPressed to be called while AstridActivity is finishing
Remove min split size for test benchmark since I have no idea if it helps
fixed issue with synchronization
Do not compare active input when updating
remove empty onSaveInstanceState
Added Message sendToTarget
Implemented addOnScrollListener and removeOnScrollListener wrappers in FilteredRecyclerView
Try also local files in remote traceback filter
Added destroy and canReuse methods
Added test for MultiMap interface
removed calls that makes no sense
Fix some quality flaws
Added ack call when the cluster state has not changed
Change layoutParams type to ViewGroup LayoutParams
Don t use deprecated api
do not create pass unless absolutely necessary
Don t cache BytesRef in ThreadLocal
adding license text
Adding test for Absolute on xml paths on command line
workaround an encoding bug
do not add source and style cells
make Response class final
Added some comments
make class non public
Prevent Spring block updates on chunk gen
Optimised attribute html
Fixed xpath builder to force layy creating default functions for thread safety
fixed text positioning when autoScale is true
Force the connection to resume in case of an unexpected connection
changed android tests use stencil by default now
Fix cache of libraries
Fixing format error on toString
RenderScript fix unhide after cherry pick to aosp
Fixed problem with default block size in HDFS binding
Fix bug where replaceAll result was being discarded
Fixed camel cometd
Fix read write nbt in builders
Fix lockcanvas exception
increased version number on master
Do not explict set default values for attributes on CamelContext as JAXB will emit those when outputting XML
Add an additional note about the relation between reportContextSensitivity and reportAmbiguity
Add NN dependency parser model to models JAR builder script
add title to log configs
add setRecylerViewBackgroundColor in demo
Fix logic thanks to review by kkolinko
Throw an exception when a Broadcaster already exists to prevent lost of Broadcaster
Makes link references weak in the LinkedList iterator to improve performance
Translate ripple mask to account for drawable bounds
Move AbstractSurefireParser to package org sonar plugins surefire api
Propagate the nested exception when an EventListener init fails
Remove fully leaded colloquialism
refactor driver compat make AggregationOutput fields private
fixed merge errors
Remove need for Java Serialization
Increase the cluster joining timeout in StandaloneClusterClientIT
Set android manifest versionCode property
More proper show tree handle fix for the project view
Remove creation of synthetic accessor methods
change expected test result
Created a separate scheduler operation to terminate jobs
JetPackageDirective made not JetExpression
Adjust SockJS scheduler core pool size
Fix checkstyle error
don t kill call on transaction timeout
Removed pluginsDirectory static variable due to possible misconfiguration if JiveGlobal homeDirectory was not initialized yet
Add since tag
Only perform one read of the hashCode field in our benign data racy hashCode implementation
Add copyright contributor
Fixing the build
Updated gwt backend emulation with Joint user data capability
take a shot at the screen rotation correction
Added SourceHttpMessageConverter as a default
Hide SQL Text Field is show in DDL
Adding a way for native code to get the apps data directory
Fix issue in GEXF export
Don t re throw exception in onThrowable
Only set lastReadTime if an read actually happened before in IdleStateHandler
make flags public just so people can hack it if needed
Don t supply a null child to the click listener
missing client Executor tests
Fixed import issue
Increase the sleep time to fix testTakePicture
Add a simple Hello World Servlet that can be used for testing
don t throw AuthentificationException on IOException
remove obsolete assertion
Remove unused throw clauses
make maxTime for subsequent searches relative to path start time
Remove incorrect comment
Removed unnecessary call site dependency for evaluating return type of a property
test creator should use tabs instead of all spaces
Moved Http post processor bean name to BeanIds class
Use the StatsHandler
Fix RoundRect radius
Improve PSQLException SQLActionException conversion
Make eventual assertion in PartitionServiceSafetyCheckTest
Fix setting selection drawable from decorator by cloning drawable
Rewrite reference to GoogleInternal FluentIterable join
Ignored flaky test while I debug to allow CI to keep running other stuff
Call onFailure when response is not valid JSON
fixed fetch issue for qualified urls
Fixed minor bug that prevented column details from getting passed on to recon service
remove AUTOCOMPLETE_ON_CLASS_NAME_COMPLETION usages in tests as they make no sense anymore
Fixed the build error of FixConverter
Fixed TERM_FACTORY usage in VersionFetchSubPhase class
remove Java dependency
changed multi touch bug in Group forgot an index
add ability to use project level deployment to create credentials
Removed some info logs
Add osrm provider
Expose the publication batches on the disruptor
correctly match suffix for completion parameter help
removed extra chars
replace hard coded hive catalog for table preview by default catalog
Override setters of EvictionConfig in CacheEvictionConfig and return its type CacheEvictionConfig for backward compatibility
Make getListView public and hidden as it is needed for WifiSettings
Print a stacks trace of internal error when in internal mode
Fix broken build
Remove space from separator
SMALLFIX Add a TODO on master side
add missing Guice module
improved ensureIndex to use default index name if null is provided instead of getting mongoexception
Removing unused imports
Fix a typo in Histogram generator
Rename Hive access control none to allow all
Fixed possible NPE
clean up duplicate code
Add additional setAnimation method
Added enumeration for the possible operations
Added a TODO
Removed unused import
Fix a bug in Android Keystore updateAAD
Test that non static data points are flagged failures
fixed broken tests
Fix wrong ClosedCaptioningAction id
Skip auth setup if AllowAllAuthenticator is used
case when in criteria always casts resulting object which fails on mysql with boolean
Fixed shadowOf ExpandableListView
Fix NPE reported on users list when HTTP session ends
Tweaked Element select documentation to reinforce CSS selector syntax
added an extra test case for StreamSource DOMSource
Added header to new class added by Tom
Make tryToExtractPackageNameFromManifest static
Fixed javadoc links to setCacheSeconds
Fix odd looking code
include default server as part of the domain describe op
optimize presentation name calc
Make MemoryPoolId final
Also inform the wallet in the single peer case in broadcastTransaction
Use a synchronized Set
Fix anyType in BaseParamValidator
Fixing calls service message
Fix handling of synthetic block in adding jscomp this for arrow functions
Remove cancel Status since it is now unused
Add a convenience method to MockTransactionBroadcaster TxFuturePair
detect rename rename conflict
wrongly added test removed correct one was added before by Zhenja
fixing log message
Use the threaded version for NN visualization
Removed MapServiceContext getNow
added constructor that takes the parent class loader
Fixing IllegalArgumentException on listing aliases
Moved localAddress to correct place in file
set the smilies selector box non opaque
Ignore the jingle initiator field in session initiate messages and use the IQs from field instead
Increased the initial heartbeat dealy in order to
Add missing license file
finish up dialog event
Fix documentation of sonar cluster activate
add Nullable NotNull annotations
Fixed null reference on backPressed in Sherpafy
Fix weaver for public enums
ensuring correct classloaders in pycharm cont d
Retract tryParse overload that accepts a radix from Guava
Actually running the tests for the win
Reduce color of background fading a bit
make annotate overridden methods fix annotate Groovy methods
Fixed HTTP Patch application start message
add comments to getByFieldInternal
Fix broken statusbar disable flags
Disable generateSubModules since it causes issues with private headers
fixed tag state identification problem
Fix shape drawable constructor
Fix total number of deployment group hosts
Added a trim
Removed Log import
use JUnit syntax to expect an exception in wontFindBook
Remove unused imports from HystrixCodaHaleMetricsPublisherCollapser
corrected typo in assertion message removed unused imports
remove circular dependency
commented print statement
move service loading away from edt
More efficiently allocate header buffer
Fix client error template
Updated Robolectric reset so that it calls the generated reset method
add watcher option
Fix wrong commit
Add event for show number
add unit test to AnnotationUtilsTest
Fix up tiny packet handler to actually work
fail parse when Text is CDATE
Fixes captured local variables within generic classes
Prevent wrong system ui visibility callback after the user swipe
Make isAvailableForSpending and markAsUnspent public
Fix SP sendToHintedEndpoints javadoc
Minor Remove unused imports
Ensure GeofenceHardware will return capabilities for old implementations
make text selectable in satellite chunks
Add one more unit test
Fix BigText transitions
removed obsolete property
WebSocket Jsr Session getRequestURI is missing scheme host port query parameters
restore login notification when session expires
Fix quality flaw
Avoid Guava s notnull precondition
Recursive refresh of folders in full synchronization does not retrieve information about shares
improved the javadoc
Added the trace logs for debugging
Fixed broken test
Modified the second route to demonstrate the problem
Moved transformer wrappers to a separate package should fix signing errors fixed debug mods not loading in forgedev string comparison
Fixed Tiles sending old school BC description packets
next prev occurrence shall leave focus in editor
Remove redundant private modifier from enum ctor
Add sand to the OreDictionary
Suppress some deprecation warnings
Add tsd search enable and tsd search plugin config option defaults
Make DEFAULT_FRAME_BUFFER_NAME as display
Added support for tracking self hosted uniques
hide packrat project init when packrat isn t available
Simplify the logic
Generate default ID property if no other integer primary key present
Removed Actor LOG
Added missing fields and imports
don t pollute logs with expected modal indexing start traces
catch the ISE
avoid costly LOG assertTrue
Doc InteractiveAuthenticationEvent doesn t extend AuthentcationEvent
Fix NPE when starting the spider
SourceFormatter auto capitalizes static java lang reflect Field and Method
updated lfw to use user home
Fix Config hasProperty bug where it was throwing an NPE if the entry did not exist instead of returning false
add SSL_OP_NO_TLSv1_1 and SSL_OP_NO_TLSv1_2
save jdk in
Delete unused imports in spring test
add socket bindings to the update list
Include the base URL in SpiderTextParser
Remove unused imports
Return the document if a stream is received
remove unused method
Fix javadoc typo
Add a ClassPath Iterable ClassProvider constructor
fix broken usage of path api
Display icon on Intercept links from dialog
Corrected typos and removed unused import
make the statements prettier
Remove local lines
Use a shorter version of the code that generates unique IDs
Improve setParentLoaderPriorty javadoc
Fix registered shows with episode uri
remove methods accessing call sounds
Support signed deployments
Class has default constructor if it has no declarated constructors and it s parent has protected constructor without parameters
Allow simple alias assignments in externs for type inference
Propagate entity link in mention annotator
Remove log for ping replies
Fix bug with transientState for listviews without stable IDs
Make isms optional support info
android revert WXComStatement updateFinish
NPE on stop only threadStop should set thread to null or it needs a sync
Create indexes on location
Remove serialisation of internal data in AssociationClass
add correlation ids to subscription messages
removed unnecessary unboxing
Fix comment to refer to InternetDomainName isValid instead of deprecated isValidLenient
use a more sensible name for the JRuby bundle
TEST declare support for groovy scripting feature in the Java REST tests runner
Added note about using the per class can cause side effects so use it with care
set DEFAULT_REPLICATE_ON_WRITE to false
Fixed start jar handling of expanded modules during add to start
Add test case for null producers
TraversalRequirements now uses canTraverse
Fixed NullPointerException if home button was pressed in AccountSetupBasics activity and no account object was created yet
android update WXpageActivity java
Pass DialogWrapper s disposable to the list table in Change Signature dialog
Fixed NPE when display name is null
reduced log level
Fixing unused import independant of current branch
Use weak references to values in cache
Added license header
Hopefully fix another NPE
Remove infrastructure type from authentication provider bean
implement unbind in AbstractItem
add a note on the rest pre processor
Get right ip from devices on parse command outputs
Remove unused import in Scheduler java
fixing issue with oracle ordering on end time
remove all double single quotes
Deprecate WriteConcern getCommand
fix some typos
Fix compiler warning
flag addProtocol String HttpUpgradeListener HttpUpgradeHandshake method public again
Fix loop that occurred with NIO and could cause issues with APR as well with a closed connection during keepalive
Theme Preview WebView needs js enabled
use full safety for the safety corner of the bike triangle
Fixed bug in setting maxWaitQueueSize
Forcing a content length to prevent chunked encoding
Add share item for custom tabs
Trunk wasnt compiling due to this bug
Unimplemented providers throw exception when used
do not request focus for About command
For clippedSubViews pass down the opportunity for children views to update their clipping if they intersect at all
remove unnecessary instanceof check
Fixed Camel commands to not show correct route status in the routes list command
Set seconds for max stale cache control flag
Prevent instantiation of more than one broker
deleted unnecessary call to WordPress getBlog as per nbradbury code review comment
This should reduce the feature expense some without hurting accuracy
Fix TextUtils commaEllipsize
Removed the or from the over and underlay masks command is now mask id id id
Remove old commented out code
Add test case for pathbuilder with validator
Gracefully handle needs init transport errors at finish
hotfix tests Remove leftover sysout logging from AccumulatingAlignedProcessingTimeWindowOperatorTest
added better index usage code
set global instance to null in one place
Added method to customize search view for light theme
Fix GPSUtilities after refactoring cloudmade was not working
Tweak pattern match
fixed powered tile detection for engines following RF migration
Remove unwanted import from Utils java
Fix major issue on PlaceRenderer
Add new image size columns for new install
Add read only setter to ace
Fixed potential NPE
Remove unnecessary args to determinePartitions
Remove redundant comma
Removed unused code in TaskListFragment
increase the clean interval of filter cache
AbstractMarshaller defensively uses DocumentBuilderFactory within synchronized block
fixup canonical view test
Dismiss the controller dialog only when a pending intent is set
Updated Javadocs to remove mention of deprecated behaviour
Fix simple error in local execution memory estimation
Print result code in test console
Fixed bugs in containsKey
select a post after refreshing the list
Added PlayerEggThrowEvent getEgg
Isolate HTTPLoggingDocIT from other tests that use the shared server
java added method for getting declared string value from annotation
fix broken test
Fix the test
Removed unused test catalog
Avoid integral division for Sorensen Dice coefficient
Remove spaces before and after and
Ensure MediaSessionCompat Callback behavior matches framework
Fix some other quality flaws
Choose CA certificate storage according to userId
Updated IPC exception support info
Added missing annotations to ClockPropertiesTest
Removed strange p
Suppress failing test
Deleting commented code in HBaseStoreManager
fixed Javadoc typos
Fix on demand category
make _parseName final
don t reset multiple selection on context popup
Implement SeekBarPreference onGetDefaultValue
add session cleanup in case of authentication failure
vcs log add a small padding on top of details panel
Do not load gnustl in NativeMemoryChunk
start Firefox using open command if no command line args are required
Removing in source files
provide a more efficient fromBytes method for ProtobufWritable
Allow the Pool to be constructed without having an internal pool created
use only move effect for tab drags
handle INRE in more actionPerformed calls
Fix circular hierarchy
Removing redundant attributes from WebServlet
Fixed sql command sequence create drop syntax
Added back real oracle keywords
Improved error message
Increasing wait for jobs timeout to prevent MSSQL QA Deadlock
revised RootBeanDefinition constructors
Fix a minor style guide violation
Replace usage of deprecated constants
return the assumption of overrideable getters as pure
Changed calculation for min scale
Added cluster name to slop mbean
Remove deep learning dependency from Model by commenting it out and breaking the
add the platform prefix
rename field to setField to match convention
allow POST of Keys
Log an error if a PluginPassiveScanner has no ID
Use the existing exception instance not a new one
Always show OTR session switcher disable for single session
Adding accidentally removed init block
remove unused variable
Touch exploration hover events don t map coordinates correctly on scrollable WebViews
Fixed wrong jdoc
Multiline initial comments in mssql were interfering with view definition snapshot
Print restore tokens in hex
Specific images override generic images
Fix wrong class name in the exception message
Add missing import android content ContextWrapper
Allow an annotation to skip dispatch to AtmosphereHandler
Fixed command line options description
Incorporated PR comments
add a mention to size limitation of RealmResults and RealmList
Remove project level granularity when generating a combined project
Removed unnecessary code
Ignore a broken test for SPDY failure recovery
Add comment explaining InjectorImpl localContext
always show module name
Add comment to TemplateLibrary
Remove unused method
Produce a simpler warning message when a StagnantRowException is thrown
Remove unnecessary condition special casing IOSTypeBinding in StatementGenerator
Remove a left over print statement
patch from flaktack eliminates exceptions when clicking on tripPatterns
Fixed seg faults
Remove helper for Host request header
SourceFormatter prevent ConcurrentModificationException
Fix a failing test
Set minimum size for the Settings tree
Change TopNQueryRunnerBenchmark to use a ByteBuffer as per OffheapBufferPool
switch off compilation of stdlib and jdk headers
Create a deep copy of the record when changing timestamps
super loadClass calls loadClass in superclass
Fix transparent color
Add RunWith annotation to AutoWindowSizingOnTest
method made public to fix Kotlin plugin
Split an unwieldy log line
removed System out statements
make qualifiedmode and qualifiedmodeset serializable so they can be used in analyst cluster
Make order of modifiers consistent
fix build break
Fix format constructor parameter order
Fix javadoc warnings
Eliminate OperatingSystem current calls in FileNormaliser normalise
don t grab focus
if isInSearchMode we expand the searchView and input the query
Remove superfluous method definition
add LoggingListener to ElasticsearchLuceneTestCase
Add missing copyright
Fixed MoveToAction with alignment
add some Chinese rules
Fixed bad import reported by Dave via ML
Fixed the cloned Letters to use the cloned StringBlock instead of the
Add a getImageURL method
create default package for library module
fixed Group fully functional now
Always update quick settings header translation in onLayout
Fix merge mistakes
Temporarily removed mouseover and implemented click method
Refactor Rename dialog calls it unknown
revert previous changes fix CCE in CssDocumentationProvider
Fix typo in LayerDrawable
Improve code quality
Fix height measure state
fixed upgrade message
Changed message to console
Remove the index columns from the blocked provider
Cleaning up fragment addition
fixed a bug that caused a build failure
made tmp output directory of DefaultExecHandleTest environment unspecific
added public isConnected method
Avoid ripple buffer creation for empty bounds
fix indexing of jar roots
move the forward button closer to the back button
fixed test to work from command line
Move finish to finally block
Only use the context xml from an unpacked directory for a WAR if unpackWARs is true
Don t kill test when descriptor is null
always use https
Suppress unchecked warning
add byte as a default serialization
Relaxed generic constraint in CompositeServerSelector and fixed a spelling error
update StubBasedPsiElementBase javadocs to the new reality
fromBarycoord taking floats
Don t use a lambda expression on this hot path to save the alloc
Fixed issue in bdb environmentStats
Remove unused method in DalvikAwareOutputStreamHelper
Initialize native rendering lazy
Allow test targets to completely disable the test security manager via
don t create callsites when no surrounding fn obj support e g top level inits
Update outdated PrimaryKey annotation documentation
Fix CLI package tangle
Moving constants to top
Remove DBPort finalize method as there is already code in place to ensure that it is closed
fix interface add update
Fix deprecation warning for Guava Objects firstNonNull
Add note regarding absolute attribute on annotation
Fix process ID check don t return true on threads
reset contained branches cache on refresh
Solved one NullPointerException
towards removing lombok
Remove unused code
Suppress HUNs from non profile users
Re order the connector destroy call to hopefully avoid the Gump
Add the java of the native setCertificateRaw
Fixed new lines issue in NamespaceComparator tests
modified logging statement
Let NBT Tag String name accept a null name
releaseLock removes LockHandler from instances to prevent memory leaks
fix other potential npe
Fix error in HTML syntax in Javadoc
To solve a issue that will return a null if a concurrent read is being
Should accept application vnd
recognize configuration scripts for ConfigSlurper
Don t duplicate RTP SAVP profile when both SDES and ZRTP are enabled
check bounds in LinkColumn click events
disable buggy test
do not inspect binary files
add REPLACE to the list of cached sql stmnts
don t do fake complete command in desktop mode qt menus don t respect fake shortcuts
Fix Notification opens unread messages
Fix Distance Comparator
moved hibernate setup specific components to com mysema query apt hibernate
Java Use termLength from RawLog when computing termOffset
remove System out
Set initial buffer for Kryo for streaming tests
Don t long press if preventDefault is called
Added Rectangle toString
Removing test noise
Fix bug related with horizontal drag and x velocity when draggable view is released
Remove AssertionError for non zero return codes in QTestUtil cleanup Siddharth Seth reviewed by Hari Subramaniyan
fixed nextval usage
Wrap a dangerously long line
Do not refresh app view if main settings
Doesn t exit this way on Macs
git Fix potential NPE
Fix logging on immediate exit on start
Do not print credentials in toString
Fix Crash at Open
Fix findbugs issue
add main method for compound splitting tests during development
add java doc
added default value
remove unused imports
fix builders databases loading empty
Only play the new vibrate hint once per volume down
Reduce contention on icon deferrer s lock
Tweak the test so it can easily be run multiple times in a row without failing
add logback property definer for machine ID
add exception as cause when re thrown
Allow empty column list in ParquetHiveRecordCursor
added support for boolean operator
add move test
Use a constant that can be re used by the unit tests for the file modification resolution
Add convenience helper to print a Vec to string via TwoDimTable
Removed validation of xml namespaces and xinclude
registered some more columntypes as varchar max and varbinary max
Raise error instead of just an assumption on the cause of it
Fix item click listeners in movie streams
Don t load anything if we are in ADT s editor
added check if directory exist and try create it
Adding thread safety annotations for tachyon conf in common module
Adds Missed call for color
fixed log message
Fix JPA tests
Disallow nick changes for individual chat rooms
encapsulate unwrap file object operation in dedicated methods
Remove extra println from InFilter
remove deprecated method usage with jackson
avoid usage of django VirtualFileUtil
Fix progress message for apksigner
Fix Android Studio render issue with CondensedTextView
reduced distance required for docking
Fix parameter to Pan Device Call
fixed typo removed unneeded parts
Fixed bug with overwriting dependsOn in configure closure
Fix isAtLeastN on builds where the codename is REL
Turn off load balancing by default since it can lead to overly high memory usage
Add SVG as an analyzed file type to ResourceShrinker java
i cannot get issues from YouTrack
Fix NativeSSTableLoaderClient to use the right ClusteringOrder class
Added support for gzip encoded downloads
Hide some fields in PartialMerkleTree
Remove unnecessary SuppressWarning annotations
handle TypeCoercionException when passing commandline option to task don t handle other internal errors here
Deprecate IncompleteFlushException as its not used anymore
Validate the start of a roundtrip
remove raw height calc
Adds viewers table to deletePeopleExceptForFirstPage
avoid NPE if no delegate is available in the closure
Fix wifi AP backup
Perform null check on super getAsyncContext
Add constant for Daydream settings
Replace an exception message check with a class check
removed unused field
conf Remove outdated ES config
JsConsole no longer references its JavasSript prototype
CheckedTextView is missing onInitializeAccessibilityNodeInfo implementation
Fixed incorrect calculation of dirty invalidation area
Made two StrictMathTest tests OSX only as hypot using NaN fails on iOS devices
Prefer AndroidDebugger over JulDebugger
Added test for recursive key mapping
Add null check before attempting to do math on the date
A better fix for race condition when starting USB accessory mode
Remove unneccessary code
Add a test item the turns on a bunch of extra icons
Include NAMESPACE_ELEMENTS in NodeUtil isStatementParent for TypeScript namespaces
Fix remaining Eclipse warnings in o a coyote
Add sanity check that RemoteSource is not locally parallelized
Fix default focus for password prompt field
restore server selector behaviour
Fix dark text appearance by using the correct resource identifier
Only dispatch context menu selection event to fragments which are not in ViewPagers or are currently selected in ViewPagers
Fix getEntityStateListeners name
AnimControl clearChannels now notify the animCycleDone on every listener for every channels before clearing the channels
Remove NotNull annotation
use static access to ImApp memTrustManager
Set non null default value for Faunus jobdir
removed showcase wizard message for now
Added second message channel
Add tab listener for feature toggles
git remove trivial javadoc
fix handling of preferences for data panel and antialias
Add length check to battery report processing
removed toString to
library Remove unused variable in TWStaggeredGridLayoutManager
Added comment to make the code easier to understand
Added check of db open in import export db
Ingest does not close its factories
fixed node border rendering in PDF export
write start element
Fix bug in unit test due to some Lifecycle refactoring
Replaces a hardcoded value in startup with one provided by SmackConfiguration getPacketReplyTimeout
Fix DB connection leak
Do not use old stack and frame sizes
isELIgnored depends on library version and web xml declaration
Arbitrarily increase the version number
Fix AndroidExopackageBinaryIntegrationTest to mirror AndroidBinaryIntegrationTest
hotfix metrics Remove leftover comment Registry
Remove an unused variable from AudioService
Fix bug when changing position
Fix ChatStateListener javadoc
Fixed since tags
Improved slightly the exception message to ease debugging
Remove deprecated GTFS RT value
added pre destroy
Fixed the failing test
Add back no op setLoader to ServerProperties
Vault uses static inflater and deflater fixed possible race condition when working with two or more projects
Fix checkstyle errors
pt Activate removed txt in Java code
Fix the build
Use new device identifier to check if a layout has been set
change getStream to be public
Slightly simplify DeclaredGlobalExternsOnWindow unit tests
add missing imports
Improve performance of RelaxedNames
Set cache dirty when re create the cache bitmap
Add simple example to XContentParser how to obtain an instance of it
Do not log empty messages in the receive pack
Fix download count for hillshade
Remove reconnectionFailed callback in XMPP action
Setting selection state when opening a note
do not send test messages to contacts not in our list
Fixed compilation error
Remove direct DateTime usage
Set ActivityInfo resizeMode to RESIZE_MODE_RESIZEABLE by default
do not throw runtime exception if config belongs to another cluster group
Add some finals where appropriate
fix rendering of balance label after coin toFriendlyString change
remove operationThreaded setter from ExplainRequestBuilder already available in base class SingleShardOperationRequestBuilder
Relax protected broadcast check to allow shell
Rename intent extra following API review
Removed uneeded declaration
Modify the way MockWebServer selects a host IP to bind to
TEST added exception for GET index API to bwc tests
Fix the doc of addCrossProfileIntentFilter
pass the correct permission to the alfresco ws
Fix flaky index sampling test
added ShadowView requestLayout
use debug logging in TransportCloseContextNodeAction
Fix method naming Make super method final
Added hasActivity check before requesting more notifications just to be safe
fixed multiple manage catalogs items issue
When exiting lock task require authentication
Allow easier usage of OTP standalone
Fix issue with views becoming visible with stale content
Refresh action bar title in FolderList when unread count was calculated
Creates new Iterator when returning the root subgroups as we are iterating it simultaneously
android allow register duplication
Remove all timeout callbacks once the connection is reported as complete
Improve javadoc for TachyonException unwrap
Restore correct line directive file name
Get name for task from containing lesson
Set dynamic bounds if null
skip digits in matching
Added a todo comment
properly initialize TripTimeSubsetCache when graph is deserialized
Simplify Subscription Chain
Added missing close cursor call
Improve FakeFile implementation
Call onChildrenLoaded properly
Fix broken build in BridgeResources
Added getters for IdGraph s vertex and edge ID factories useful when alternating IdGraph with BatchGraph
added toString method to MongoClientOptions
Use better Class forName in ZygoteInit
add copyright to new tooling classes
Do not create directories that already exist
delete the scored frame but not the appended vec
disable refactoring on inappropriate contexts
Fix instability of LogbackHelper
Added Assume fail
create keep in sync field in db on app setup
Fix fragment flickering on hiding support library
passing overrideErrors on d
Fix build by ignoring test that shows strange behavior
Fix crash switching users
Removing unused getter
Restrict scope of NPE check for Bouncy Castle bug
Beeline force option doesn t force execution when errors occurred in a script
Change oss ufs to return empty group owner instead of null
WritableScopeImpl should not check descriptor uniqueness
Add loggerCreateWasCalled check to avoid log4j PropertyConfigurator
dispose BaseFixture on EDT fight JavaAutoPopupTest blinking
Added setOnCompletionListener for MediaPlayer
in form preview mode ignore bindings for button groups
Fixed potential liquid duplication bug when filling containers with capacity greater than a bucket
Fixed post merge compile problem
Added some sanity checks to the getVideoPressVideoPosterFromURL helper method
bind server socket in blocking mode
Fix a typo in the docstring of getAllCellInfo
fixed NPE in particle editor
Introducing local preferences for default post category and format
ShutDownTracker interrupts awaited threads after several seconds
Fixed DataBufferUtils takeUntilByteCount
Fix up merge
KeyValuesInputMerger creates huge logs Rajesh Balamohan reviewed by Gopal V
remove printStackDump from assertNoJobExecutionContextAreLeftOpen
fixed DispatcherServletTests breakage
Use the SemanticGraph directly when outputting text dependencies
Allow app to stop lockTaskMode if started by manifest attribute
add subscription id in log line
add WebView FindAddress option to ignore case sensitivity
TEST enable TRACE logging for gateway on testIndexWithFewDocuments
fix compile error
Removed some necessary javadoc
Fixed a bug where non successful HEAD response is assumed to have a message body
Removing the SMS Listener
Have RecyclerView fully claim touch event streams during scrolling
Should not add dependency to org apache commons
Removed timeout in searchFor
I can t see any reason why a Realm shouldn t be re startable by default
remove dangerous CTOR not used in this driver
Kill the emulator on exit if we started it
added test case for http
Fix stopBatchedScan not working for wifi
Added better traceability for failures in the startup sequence of HA subprocesses
Added santizie option to JMX to hide sensitive information like password in URIs exposed in JMX MBean names and attributes
Fix memory leak and slowness in keyguard
Removed double masterNodeTimeout set in RestPutRepositoryAction
Fixed target prefix OOV handling
Javadocs added getOverObject
relax pattern to accept comma and dot
provider getUrl now can return null so check it and return fallback not null url in this case
fixed test failure
removed a trailing log
send empty text with statistics message to avoid producing unnecessary output by IDEA Project Runner in TeamCity
Fix auto focus on Camera1
annotation method reference shouldn t cover the reference expression when value is omitted
Implement a couple of weak hashCode methods to go with equals
Rename method to clarify which connector is being created
Fixed a bug causing mode styles to not be applied
Fix the bug that SlidingPaneLayout doesn t fling correctly
Added category and data wrapper methods to IntentBuilder
Increased code coverage of NearCacheConfig
Added validity check on RBTree
Fix a test that randomly failed by asking for negative IDs
Added a TODO item
Added BiomeDictionary support to Oil gen
added mime type for json
Added generic argument to ICollection
Incorporated PR comments
Revert this patch whilst I figure out why it breaks class loading
Remove redundant overriding methods
Fix missing recids in TxMaker
Add mappingBean command if not surrounded by quotes
do not infer from void return type
Fix user id and uid comparison
Add isSupporterChannel helper method
Do not throw an NPE if the JPA annotations do not specify a unit name
Fix NoSuchElementException when cross join is used inside IN subquery
Remove long sleep time in WifiApStress test
Adding a comment about the strange shared fields in BitmapTextPage
Completed tasks don t get weekday
Whoops add a line separator
make publish cancel button consistent between dialog and wizard
Set version from
vcs log move busy icon after the last table row
Add Matts copyright notice to TestWithPeerGroup
eliminate eclipse warnings
Updated per aaudiber and calvinjia s comments
Fixed deprecation warnings
Revert Fixes NPE when listing tasks on Gradle s own codebase
Revert two patches crosscontext hack that will be forgotten and useless NPE check
Highlight super and this as keywords
Removed redundant comment
Updated upgrade message with help from Jon
handle finishing a session before initialization gracefully
Change SearchThread to include the type of AJAX spider requests
don t use global font map for presentation mode
debug log cleanup
Fix bug in reporting of running tasks
Fix cached responses coming in with the incorrect encodings and content length
Change ImmutableList convert to return an empty list rather than null
Remove unused method
Making devkit s run configuration include extensions jar into initial classpath of the launched idea
Added workaround for NoSuchMethodError on some Nokia JVMs with a superfluous abstract method thanks Marnix
implement Logic for FooterView
Added a preview flag to GET Model so the browser can tell the backend when
Improved javadoc TransactionalTask
remove smelly isPhysical check
Remove unnecessary sysout
Remove usage of obsolete collection
rename existing test method
Added usage output when parameters are missing
Discard new cluster state if the clustername doesn t match
used time unit in file moco runner watcher for interval
Adds configuration fragment requirements for AndroidNeverlinkAspect
Keep legacy ShareMethods for compatibility
try new aggregation function
Add null checks in IntentAssert
Add PageCacheCapacity to the WebSettings
Fix NPE in bad formed GeneralCommandLine
Fixed the timestamp for the metric
Hide the new posts message whenever the post list is refreshed
provide access to the corresponding help topic
Fix handling of improperly encoded DER signatures to match OpenSSL
allow top sleeping state in app process verification
expose FunctionInfo and ReferenceInfo in Reference and Function symbol
Improved watch dog daemon output
update notnull default to false
delete dead code
Fix jumpy scroll on initial typing in console
added nicer toString for int ranges
Added missing license header
use same cache location as old worker to allow hopping between them when testing
throw an exception when var lock does not exist
Don t let operation headers break interface address criteria evaluation
strip underscore off GELF fields
Fix a broken link
Don t create columns named rowid in createTableSql
Remove redundant ms in GC pause warning message
Keep the do stmt body if the const exp is false
Templeton job does not write out log files on InterruptedException Dennis Chan via Ashutosh Chauhan
Updated some javadocs
Moved getName from Player to HumanEntity
Expose announced channel types characters
fixed compile warnnings
Removed unused method
Fix hardcoded overhead size in commit log test
Remove Component for optional encoder
Updated Version and DependencyBank for next release
prevent NPE if no action found
library Remove unnecessary semicolon in TwoWayView
Kill FileType matches
Remove uneccessary call
Commit and compact the database on startup
Fix regression with drop targets not being visible
Re enable the CommitPusher
resolve Optional annotation to the org testng annotations one
Remove static from mMaxRowHeight
fix missing dim layer surface
Don t lock on ConcurrentHashMap
Remove an unused method
moved result collector up to abstract command
Fix quality flaw
Restore icons in widget filter picker
Mangling the logger name for better configurability
Fix bug discovered during the adb startup process
fix handling of shortcuts with second stroke for local actions
Fixed problem with wrong log configuration
dump the buffer on failure
removed a few unused imports
initialize lastHeartbeatTimestamp before starting heartbeat task
Missing Fluent annotation
Fix blank right panes on boot when old UI prefs are present
Remove dependency on Tomcat internals from examples
Remove casts to ComponentSpecInternal in ComponentRules
Learning curve now has configurable metric
made JsNew qualifier mutable
Fix drag and drop URI permission grants
Fix the build
make inner implementations of Filter and Comparator static
Ignore tests which fails
fixed clone rotation and scale cloning
Set the toolbarClickListener again once we leave ReaderPostListActivity
Added some more logging in the last location finder subclasses
Fix serialization bug introduced with last commit
automatically create outputDirectory
remove unused code
Add hasBracketsAndQuotes Method
Fixed potential deadlock reported by Giancarlo
Preventing infinite loop when trying to serialize Node model
MySqlValidConnectionChecker use pingInternal replace ping
Provide way to setup default folder
Make less calls within loop
disable code coverage for debug sessions
add generation back to bucketing filtering sort
Added method that accepts a map as script parameters
Simplify logic to remove need for comments
Fix some forgotten CharacterUtil InteractionsUtil renames
added ThreadSafe annotations
Fix operator name for debugging
added a toString method to help debugging
inject inspection colors dynamically into pages that implement InspectionColorSettingsPage
stopping the preview before unlocking reportedly helps with video corruption
Fixed UTF decoding in DataInputStream emu
removing boxing unboxing
Make onDestroy public
Check shutdown status of Redisson during async command invocation
corrected test case
Fix GroovyScriptTests to not depend on the way documents are routed to shards
provide a better error message when a file doesn t exist
fix media server restart detection
remove Java Antipattern
Making textview instead of button
Add non Android method for clicking an options menu item
Add a comment
Removed waitForIdleSync in drag
Implement getInvokedBusinessInterface on session beans
add server version check
Don t use TypedArray after recycling it
Fix a security exception when checking cross profile caller id cap
Updated documentation link
Increased the size of the field vals of the table PARAM
Add unit test for behavior of HystrixRequestCache without a HystrixRequestContext
Get controller capabilities before setting SUC
fix false alarm about a one way street
Include padding in auto fit column matching
Fix the build
if we have a single value for roundrect radius manually clamp it before drawing
Add support for security handlers
Adding Unversioned V0 rest client
Improved the daemon client log message for the unexpected daemon result
Pet Clinic test suite now includes VisitsAtomViewTest
Fix bug Unsharing in maintenance mode removes the file
remove unused field
Properly stop server
Uses original config arguments when doing internal applying of transactions after a a copy store
Define charset in executor
Ensure result ready in asyncDispatch in MockMvc
Workaround to hide the TO attribute with the fake JID
Adding Override registerCapabilities method
don t advertise technologies we don t support in community edition
Remove unused import that was breaking the build
Fix test in eventsArePair
update tooltip size after setting text
use proper CachedValue for caching of Django settings values
Catch NameNotFoundException support info
Updating the test to gte more info when failed
Fixed enhance warning
Fixed an NPE in AbstractIOSTarget when building an iOS executable without specifying run
add select text to context menu
Retain img size ratio when in reader
avoiding resource conflicts
cleaned up comments for my previously commited code
Added additional tests for implicit receiver types
Fix wrong import
Change some ZooKeeperHiveLockManager logs to debug Mohit Sabharwal via Ashutosh Chauhan
avoid double DocumentImpl assertWriteAccess
Make Toast widget aware of layout direction
remove extra processing of DoNotAskAgain
removed comment parts
Remove bad Javadoc in SCTP classes
have bool json filter extend the base one
remove reference to deleted class
Add warning to CharMatcher DIGIT
fixed NPE when running test
Narrowing fractional metrics usage to non apple jvms on macs only
Undo use of Collections singletonList
Add ServerInfo to TestingConnectorFactoryContext
inline template should not cut END variable otherwise we loose some text and can get AIOOBE when counting range markers
Fix parent path build
Add FLAG_RECEIVER_REGISTERED_ONLY_BEFORE_BOOT to name changed broadcast
struts multi module web paths
Fix bug in extractValue
Removed unused method
Added missing method docs
add annotation documentation for memcached handlers
Fixed serialization bug in JobGraph java
Fix migration complete message
Add an option to hide the Voice Search icon
shorten wrap checkbox label
Avoid NPE with Status fromStatusCode
do not ignore test sources on off for global scope
Call getResponseBody0 before allocating the array
Revert Removing top level filter parameter from search API
remove debug statement update todos
Remove unused param javadoc
Adjust volume bar visibility in HDMI CEC system audio mode
Fix music auto resume
cancel waiting for second keystroke if ESC cancel any popup
Added findbugs suppression for serialization warning that is irrelevant
Prevent double calculation of containing class
resolving priority fixed
Only handle scroll events when over content
Added the missing period
Updated user agent of native reader
Modify the log levels of LocalDuplicatedRemovedScheduler java
Do not log process canceled exception
exclude non content files from analysis
Set way name for areas
remove unused clusterService from SelectStatementPlanner
camel slack should use charset when sending to slack
Fix nodetool status regression
Fix quality flaw
Improved methods comments
Added comments for the visitor example
correctly say all files up to date when there s no remote changes
Test fixes Remove unnecessary test listener
Add missing ICU_Delegate methods
Move broadcastState STATE_TURNING_ON before persistSwitchSetting
make private inner classes static
Added map position
restored all tests
Add protection for accessing images
Get worker ID first when re register to master
Just fixing an indent
Added clear information about bugged blender file
Fix a memory leak in CustomScanDialog
Move content overlay to the bottom of the screen
remove commented methods
Remove unused import and variable
Rename updateDrawerList method to more appropriate
remove stale job for workers
Changes the chat room configuration window class visibility modifiers
Revert a commit that broke things
Disable ImageIO s File based cache should speed up texturepack loading stitching
Relaxed performance test condition
create inner JBInsetsUIResource class
do not show file name in popup
removed unused import
Removed unused imports
Reformatted csharp generator to pass checkstyle and address warnings
Hide ClassBodyWriter from the public API
clear the class change queue in a finally block to avoid running again on errors
Fix MockBlock compile
Add missing Javadoc
Fix memory leak
add CanonicalNameConstants and JClass refs for PowerManager and WakeLock
explicitly return an ArrayList so that one doesn t have to cast it when putting it in a Bundle
Added a setter for mAllNotesLoaded in NotificationsListFragment so we can set it back to false after refreshing the notifications list
PackageLocation should document suppression mechanism
Make property public instead of private
fixed OSProcessUtil getProcessID
make error type from error class
Removed temporary fix from Sketch and a changed comment in
Added a TODO
Removed debugging printout from SoftCluster
made SCArrayQ padding package private
towards removing lombok
Added type for iterator
remove out filter for org restlet
Avoid printing Long MAXVALUE in the counter max box if not provided by user
Adding a searchView
ExceptionUtils throw NoClassDefFoundError
synchronize access to object specific removal
Fix constructor name
Correcting the order of expected and equals in an assertion that s
Move code not directly related to the update of the local cache outside
Fix typos in ContentPackLoaderPeriodical
Allow non AJP connectors to support SSL by default
JAXB scan skips annotated interfaces
include categories in Existing Templates speed search
do not used bitmap from memory cache if the bitmap is recycled
Fixed copyright header errors
Corrected ReaderTagTable spacing in createTables
Add check for MOE installation before allowing a project with moe to be generated
remove debug message
Protect WindowManager s PRIVATE_FLAG_KEYGUARD
to resolve failing test will ask jason what the purpose of this test is
Fixed issue on property indexing
remove file from mru list if it is deleted
Handle null directories in onDetach
Remove unused method fromJdbcValue from IsolationLevel
Fix check that source path is not a directory
Testing synchronization cancelation
Adds an INACTIVE direction to the MediaDirection enumeration
If bytes are not read send an error event otherwise fields referring to the connection could remain
Fix html entity add doc snippet so that javadoc doesn t complain about an empty param
Don t trigger log for empty views
reverted incorrect change
more shutdown exceptions
Remove call to MXNotifyShutdown wouldn t work with JUnits
BailErrorStrategy propagates the RecognitionException to the current rule context and it s parents
Fixed bug on JSON converter
Fixed double divider
add time delay for tocken check
Fix integration tests
ThrowableLogEvent now logs throwable s message too
Fixing comparator method overflow in LogFileHandler
Introduced factory method to create JDK s Selector instances
Fix Windows JDK build error
fixed typo in javadoc
Deprecate ConfigurationProperties location
Added INFO level messages to better understand resetting stats in ClientSocketStats
Fix a failing test
Fix ActionBar name
Fixed TiledSprite unnecessarily updating its TextureCoordinates
removed ajar state
improved updated Javadoc in JUnit tests
rename insertLocalMessage insertLocal
nl add TaalTik as contributor as requested by Ruud
added missing license header
last ditch extended type detection before preview
Removed a hack of using toString on AuditEvent in the default logger
Remove PacketExtensionFilter Class constructor
fixed checkstyle error
include complete BSD license text
remove assertion in AbstractSuppressByNoInspectionCommentFix isSuppressionComment
fixing formatter problems
don t load stub if there s none
inject only injectable languages without additional dependencies
Start the sync job service after primary boot
elasticsearch Restrict visibility of defaults
Added some minimal JavaDoc
more normalize identifiers routerId
Try longer waits to improve stability of integration tests on Travis
Fix import statement of Preconditions
Fixed comparison of long values in compareTo
Removed unused field
Allowing BaseBitmapTextureAtlasSourceDecorator onDecorateBitmap to throw Exception
Fix compiler crash when doing instanceof on a GETELEM
Throw away messages until version negotiation is complete rather than throwing an exception
rename startChat to openChat to avoid confusion
Fixed a typo in the DeadLetterChannel
Update an example of using query parameters
maker methods now return BTreeMap instead of Map
updated help again minor bugfix
explaining the path toString
Add election timeout and heartbeat interval to raft builder
do not perform action with cursor on empty line
git don t load refs by default e g in commits for push dialog
Fix integration test compile error
Added Troy s suggestion that we set properties on a non Script derived script
Remove System out
Fix default image not displaying prior to load
Not using default mail session anymore
Fixed table troubles
Allow place KotlinSignature annotation to methods
Fixing large frame test
Ignore FSWriteError in recover during resetUnsafe on Windows
cancel the timeout in any case
added string representations to CommentStatus
added more descriptive message
don t update remote sessions if jabberid changes create new session
Fix broken test
remove extra semi colon
Check both superperms and PEX directly to support older versions of PEX with inGroup
Commented out type locator strategies from application test to see if this fixes Travis memory problems
Updated Windows results removal of digest
Add insert trigger
Make MediaCodecUtil getMediaCodecInfo public
Log error message if not using NativeUserProvider with NativeAuthProvider
correct file write time return value on collaborative save
Changed LightningStrikeEvent to return a LightningStrike and not an Entity
Create AS method UML action
Remove rotate x y z from Reference
Add comment warning against incorrect use
Reduced the visibility of the createRegistry method to protected
Never log GONE notifications as visible
better handle rename
Protocol not found error
Performing a rebase
ignore MalformedPatternInspection in configure inspection pattern dialog
Ignore available presences of closed sessions
fixed updater tests
Moved outerFocusChangeListener check outside the floatingLabelEnabled check so a focus listener can be added without having to set a label
Added FiberAsync wrapException
removing unnecessary import
Let data rule to do their work if no navigatables found directly
Fix min max calculation for dynamic graphs in ranking
do not use Arrays asList for one element
Add warning when getContext is null
Modify emergency number detection
Add DataSourceType GAUGE to DoubleGauge and LongGauge configs
update Repository info after update command
Deprecated AbstractJExcelView since JExcelAPI is an abandoned project
Fixed Color deserialization
Fix issue in FiltersModel that avoid operators to have properties
fixed retweets param
Make sure settings import doesn t degrade connection security
Fix broken API
Avoid excessive scheduling of the maintenace task
Always assign a default value
ext backed chrome connection
Added super check
remove unneeded branch
Fix copyright headers
Ignore flaky tests
Fix uninitialized variable warning
fix empty list type handling in DataTypeTest
Added public method getGeometry on Batch class
Added set getSpawnRadius in Server
Fix a CLI text
Fix cleanupConnection when switching from wifi
implement the method which is called now
remove duplicate lines that have no effect
Add log message
Initializing the client before each test
Add variant of Preconditions checkArgument that takes int placeholder
Close the HTTP connection in the callable
fix various typos
Adding return codes to api calls
Added missing interface implementation
Fix NPE in TextToSpeech with setLanguage null
Fix resizing behavior in code browser
remove unused boneId variable in ModelNode
Removed wrong assertion
Remove unnecessary methods ParseTreeMatch getText and failed use getTree
Fix deprecation warning in ConfigurationPropertiesReportEndpoint
fix up HTML export handling
Only update item if we know we retrieved the thermostat it refers to
Change visibility to package private
Remove Constraint stuff from package info
Fix PMD violation
Fix npe caused by uninitialized variable
Remove duplicate license header
fixed issue with calc the complete range with some side effects
Fix test name inacuraccy
Support all URIs by passing them to their default activity
Removed section of test no longer used or available in Nd4j
Removed unused field
Fix small thread safety issue
Fixed a bug in statistics reporting
Fix tests on Travis hopefully
Add comment about making parser members private instead of public
Adding a space for formatting
do not complete fixed attributes if they are optional
Fix description of computation step DigestReportStep
updated OrientGraph with Lucas required change
Rename ExpirationStoreCache metrics
execute onSuccess before opening window
Revert changes to invalidateChild
Add Primary to default InMemoryMetricRepository if there is one
Fix implementation of equals for BaseTypeReference
Fix typo in AutoValue s CompilationTest that meant the expected code was incorrect
Making User model serializable
add getSocialNetwork method in SocialNetworkManager
Remove some white space
Couchdb river plugin ignore design documents
Fixed node node condition
ignore nop moves
Connect proxy socket
Error handle fix
Fix formatting for SchemaDao
process forgotten exception
commit document before import insertion
Fix layout in layout logic
Actually return identifiers so method actually works
Added more tests for preserve spaces
Do not add deprecated shows column on new installs
if dependencies aren t calculated for Multibinder yet return a dependency on Injector class instead of null
Fixed the CS error of CxfEndpointBeanTest java
Updates build number for new release
add a test for term docs version deletes
Removed completed TODOs
Fix ugly UI in Project view on GTK L F
Add hyperlink colors from a base scheme
removed unecessary logging when muc tiles update
Restore method signatures on MediaType
Fix typo in test group
add usage triggers
using auto resource management
Fix IndexIO segment validator to account for timestamp mismatches
Fixed put and timed offer implementations in Unbounded
TEST Use greaterThanOrEqualTo for testClusterInfoServiceCollectsInformation
Fix NPE when internode compression is off
added doc comment
Fix configuration url
Removed hard coded reply timeout
Hide the file size when the file is a folder
Add internal support for network url for image options
Marking functional interfaces
add a little info to Configuration class description
Fixed small bug
changed return type to be more generic
Fix a force quit by going to the Accounts page after creating an account
Fix NPE in empty SizeAdaptiveLayouts
Remove unused function which
Remove system out
Removed code that should never run and replaced it wil IllegalStateException
Avoid logging stack trace
add one test more
Reinstate convenience method for using the GroovyChain DSL against a Chain instance
Updated error msg when we can t find the jni lib
Added null check on actionListener
Fix whitespace for querydsl scala
Do not scan classes directory if it does not exist
Fix shared drawable state
Make Version serializable used by SimpleModule
Stop hexagon setup task early if it was cancelled
Fix new camera restrictions untested
add support if only frameworks selected
Handle error when user closes browser while playing snake game
Update plugin button does nothing
Revert add rmarkdown as dependency of rsconnect
Add JavaDoc to BodyParserEngineManager and TemplateEngineManager
Fix rotation issue in Android N
Channel should implement Comparable Channel
Fix SDK build
Updated to next release version
Fix NullPointerException on a null object reference
TEST Just start two nodes
when new message is received remove this message from loaded history messages
Updated ActionProcessButton progress indicator height
Align wan replication GroupProperty names with properties
Remove final from template fields
Revert Removed the equal sign which caused a FC when mCurrentPage was equal to bounds size
Using add instead of replace for the Comment Post FragmentTransactions
Support refresh token by default
Added Samsung browser to browsers white list
Preprocessing Live Template
It s an IllegalState not an IllegalArgument sigh
Do not create the cache directory unless required
If we auto config a Yahoo account set the spam folder to be Bulk Mail
Make excuses for SearchSourceBuilder parseXContent
Fix display Node ID when Name set
Selected F iles
revert wrong commit
SourceFormatter catch incorrect line breaks
Added more useful output
don t retry the request if it is canceled
fixed parameter type
do not touch super method s document text
Deny once on dialog timeout
Fix sy ls test
Added ByteString asByteBuffer
Use right StringUtil
Removed new line at the end of the file
fix converting of android modules
SessionState dropSessionPaths should use FileSystem getLocal conf to delete local files Hari Sankar Sivarama Subramaniyan reviewed by Vaibhav Gumashta
Add settings for SUW data control
Fixed potential unit test failure
Fixing a bunch of javadoc warnings
Fix plan printing of table layouts
Remove NfcFragment from activity when no longer sharing
Make sure we won t compile same modules in subsequent script runs in same VM instance
Fix activity alias encryption awareness bug
Formatted my code to follow code styling
get rid of deprecated api
Don t allow an uncreated Network to satsify requests
add support of HTTP Proxy also to ApnsFeedbackConnection
Do not use the status bar as the system decor layer if it is not visible
Cache generated group name in AbstractProject getGroup
graphhopper web fills hints from URL do not throw exception if key is duplicated inserted
Fix TextView clickable state when using onClick attribute
Removed tableRefFactory dependency in DerbyDataBaseInterface
Add info on how to drop default interceptors
Expose ObjcProvider INCLUDE to Skylark read only
Added MSDN links
Removed javadoc link to hidden class temporary fix for build
Allow non alpha prefix for dbquery keys
rename csv import locale dialog to match other ui elements
Added ability to force disable a scheduled job Important for slop pusher job if we face sudden network traffic
Fix query on MySQL
Fix version number on watchOS
Add missing synchronized
remove side effects from BackgroundTaskQueue tests
Only add the tagline when uploading a local draft
enable inner class creation
Add missing return statement
Make ControlFlowGraph public
Revert the clever runonuithread hack from the previous commit as it
Added a bogus extra method just for now
Changed level to trace
Fixed checkstyle issue and divide by zero problem for ratio in NearCacheStatsImpl
Fix wrong state saved while dragging or in drag motion
Remove useless postgresql alias
Fix name of BSONCallbackAdapter
added some Javadoc
removed sun reflect Reflection dependency
don t pass null errors to controllerErrors addAllErrors
Work around CI build failures
Do not use CXF continuation if option is disabled
run inspection cleanup in a transaction
adding editor fixes
Should fix some issues like grass
Fix EditText RenderNode invalidation bugs
modify shuffle int
Cookbook grouped quantile cleanup
Fix javadoc error
Call execute method
Fixed conversation list shown at wrong time with wrong title
Weird merge error
removed System out println
added hook for writing bundle files
Add setter to configure the request
Allow overriding startJMXServer
fixed KinematicRagdollControl for bullet
Added new methods isToggleButtonChecked int index and isToggleButtonChecked String text
Skip test on IBM jdks
fixed a broken availability of Show Usages action
increased version due to PersistentHashMap value storage compression
If the node attribute of the Disco info reponse is null set it to the node value of the request
Fix a crash
Fixing actionbar overlay
Refresh all blog content when refreshing the posts list
Allow views to setSystemUiVisibility with the same value and have it work
don t clear cache for disposed MessageBus fix tests
Move replaceInPredicates method under the usage
remove validate invocation for now
Fix issue with route recalculation crash
Fix blink when going back quickly after entering Activity
Fix apparent bug
passing project to the dialog
Fix zone routing logic for hints
Update followed status after followed blogs have changed
throw UnsupportedOperationException on newly added generateGraph String in blueprints test
corrected copyright message
Use the display id from the otr client instead of the raw id
Remove some dead code
Make the window have a somewhat sensible title
Don t bring up Launcher until after boot complete
Revert return to previous session on quit
Fix speedbump for PRIORITY_MIN intrusives
Fixes adding media from quick photo not showing up in post content
Increased timeout of DefaultStepRunnerTest testParallelCommandFailure
replaced use of a StringBuffer by a simple String concatenation as it is now optimized by the JVM
Fixed horizontal vertical LinearGradientDirections of LinearGradientFillTextureSourceDecorator
pass adjusted plugin properties in processNotification
Delete an unused line in AbstractBlockChain
Use the table name prefix
Don t show ways poi
Add new ID for HotRestart into ID Helper
Remove debug log that slipped in
Classloading change for FixedCertificates
Removed outdated TODO about limit and skip
Java Use the right type in the mask calculation
remove obsolete todo
Ensure we not log missleading errors if the promise was already failed due errors
If we end up with a null attribute cancel everything
since IdeaProjectManagerImpl went to platform use a different class to detect platform prefix
Adds mandatory initial_hosts in ForeignStoreIdIT setup code
Add tests for a bug found whilst reviewing the ELParser
Tweaked error messages to show the actual version rather than the version object
Have isIndexBuilt expunge all tombstones
drop double dropResolveCaches during batch inspections
emphasize installing label by another color
changed invalid host
improve error message from pr feedback
Fixed a broken link to PCAScore
Prevents NPE when a View was detached
Fixing failing test for duplicate variable inserts
Fix NPE during DNS reload logging
update some description of UltimateRecyclerview
Using unused constructor in BsonTypeClassMap
Removed dummy code that was breaking the tests
Compare creation times of records if access hits are same for LFU based eviction
Reorder the ideal order so that removeDeadCode is always directly after the peephole and minimizeExitpoint passes which are cheaper
check out variables
remove a space
Fix GWT for Firefox
Fix null pointer exception in setVertex
fixed bug on linux mac when the history file has been deleted
Java Remove unused method
remove unused LOG field
Injection editor should create editor highlighter by injected language not
Organization members shouldn t see Sites in Control Panel
activate pattern rules
explain reason for the empty PUT body
Made two accidentally public message classes package private
HttpFields declares IllegalArgumentException as checked exception
Throw exception if config database does not exist
add onBackPressed ot ActionBarDrawerActivity
fixed Pixmap getPixel won t work for a while
fixed it to use the Edge s class as label if defined
do not add compilation listener if it won t do anything
Remove artifact setting from manager dialog
Removed leftover DashClock reference in APIs
Remove useless undef
Fix log statements
Fix permission check in hasEnrolledFingerprints
Use correct default gradle user home
Added setBorderColorResource to allow passing color resource references
Fix ResolvableType hashCode generation
Java Remove hangover printStackTrace from closing UdpChannelTransports
Handle color state list in methods returning an int
removed obsolete imports
Log compiler path and version for debugging
Fix EditorAction id inferring
Display expiry in the cli
Fixed extract method in case of expression
fixed missing method
Expose DefaultConfiguration resolveNow to tests
Fix NPE when loading a graph with Giant Component Filter
Add a toString method to ScannerSupplierImpl
add test case
Add toString implementations to Response and Result
Lint Fix extra explicit type arguments
correctly move initializer
Add getter for Dashboard widgets
Fixed incorrect javadoc for construction parameter sleeper
Detect embedded DBs when deducing driver class
remove unused imports
Added segment tree testing code
Fixed a bug in the uptime example where uptime is reset even when it
added min max queries
improved spring model performance layered cache models
Fixed a bug where redacted notifications had the wrong height
Fix non determinist test
set plugin action support post request
Restore Javadoc to the getCoyoteResponse method
Add todo for supporting multi master cluster as resource
Don t throw exception if daemon isn t running just return false for isRPCServerRunning
Allow COORDINATOR ONLY plan fragment to remain as the source
Always write script results to stdout in daemon mode
made APersistenVector compareTo use Util compare on elements
Switch Queue to better performing Deque
Fix TabHost stealing focus on hardware keyboard keypress
remove duplicated code
Fix quality flaw
Disable SpeeDRF tests since
Added check for null servers list on the setup of the DRPCSpout
Fix bad log
Exclude FxCop plugin from ITs since it is interefing with C and VBdotNet plugins
Fix GeoPointFieldMapperTests expectations
Fix broken console env tests
InspectionProfileManager setRootProfile can t set profile if current is null
make recurring invoice items description prettier
add detected sdk from details dialog
don t load comments adapter if the activity is finishing
Removed p tags in javadoc
do not change task description tool window background in darcula
fix generate SpecialCells
sort working copies
add test case when input prefixes is null
don t display progress icon when the completion takes little time
remove unused NOTIFICATION_FOLLOW_ACTION
Only set the JspConfig if it was present in the metadata
fix bug in TransactionalBoltExecutor comments on validity of commit condition
Fixed bug in TxModule that didn t close the file channel in
Make setCaptureRate locale safe
Remove unnecessary line break
fix broken test case assertion
Bubble up exceptions caught in stop
Removes a duplicate method an unnecessary method override
fixed last java warnings
Fix maven test
Fix data activity overlay not being removed when data is disabled
Fix Bitmap Recycle Logic
use standard shortcut for add element to a list table
Add audio tag to systrace
add a long description for some processes
TEST Don t invoke RoutingNodes assertShardStats via asserts many times in tests just once on each reroute call
Fixed selector used for the isPlaying method in OALAudioTrack
Revert Temporary fix for QXDM crashes
delete index request does not support empty String
Added back old dump commands
Remove unused variable
don t take stanza id into account when deduping muc pms
do not drag out minimized views
don t show empty Python code style settings page fix Python preview text
Commeted out test case with invalid XML since some servers will just close the connection
Handle null enum values
Actually invoke method after initializing when first called
fix missing DocumentationProvider functionality
Remove that part for now as it suffer encoding issue
Remove a stray Beta annotation on a test
Adjust the name of the PImage object variable for reference
Fix the preloaded classes tool for JellyBean
Don t draw a circle via a path
br Added Fulup Jakez as contributor to the Breton version of LanguageTool
removed the truncation
Remove unnecessary logging
explicitly specify hostFilter for HeliosSoloDeploymentTest
fixed documentation for Skin
address some cr
Fix NPE in GelfChunkAggregator checkForCompletion
Handle flow where is already logged in
Added skip and limit to keywords to create a break line on
don t pass remote shell port parameter twice
Fix actionBar title upper one level than content in UploadFilesActivity when press back button
updated static imports
camel amqp Added missing topic pre configuration
fix KafkaStreams SmokeTest
provide default sensible implementation
Removed debug code that was causing a test to fail
ignore events for nonlocal files
Fix test failure
Added a Logcat entry at the very first instant when ACRA catches an Exception
Removed unused method
Improve javadoc for AlluxioURI
Added intermediate throw none event including parse listener for KPI support
Throw an exception instead of logging if mapToModel is used incorrectly
keep single mTimerThread
skip empty lines in message preview
Remove half button
use walking visitor to prevent SOE on big html files
Fixing bug where we did not leave a mark at the first backup when there were no messages to backup
only get output size hash when building locally
clicking on entry opens wrong detail editor
Ignore Frequent intermittent failures in coarse fine web session passivation tests
remove mex connection constraint
Log MongoDB query plan on TRACE level in ClusterEventPeriodical
Add a test for suppressing CompileTimeConstant
Only collect source files from attributes that don t have instrumentation data
use bookmark href to cache PDF viewer location reduce coupling
added missing fail
removed unused line
Fix NPE in com Client
Remove broken doc link to removed method
add missing import
Fix createFile journaling
Moved the startup of the memorymonitor to node start
Switched X and Y axis in the javadoc
reduce verbosity of log message in BusinessFactoryBase
Remove incorrect file separator in CommitLogArchiver on Windows
add getContext method and a tweak or two for javadoc
cleaned up proxy implementation
removed break lines
Remove unused import from OneWebApp example
tweak check box margin for windows desktop
SMALLFIX Removed explicit type argument in ClientHandler
Clear pending operation flag when move failed
Don t show menu on create account screen
Fixed typo in table name
Fixed compilation problem
Fix another Windows test issue
removed unused map
Don t check if callee is callable for Python type references
Added null type resolution
add option to disable header divider
Allow external URLs from AbstractProcessingFilter
pass correct value collection type to constructor
Fix RUnits wrt ChunkSummary
Doesn t move away messages log to branched dir
Suppress find Unsafe exception when Log Level is not set to trace
add DbTest select DbSession String
Don t check file permissions on windows
Fix Windows test failures
make DefaultTestMethod serializable
using SmartList for small lists
Fix access to undefined label attribute in computed outputs
Optimize equals and hashCode in StreamId
don t create UI in headless environment
Prevent run failures in tests from polluting LoggerContext
Java Remove debugging output that was left in by mistake
Allocate pty for remote console
Fixing build breakage
Avoid additional NPE on test failure
Fix column name
DeterministicKeyChain Builder add seedCreationTimeSecs
Clarify comparison test for a change in header table size
For dataSource inputSpec in hadoop batch ingestion use configured query granularity for reading existing segments instead of NONE
Fix Checkstyle IDE Gump nags
Fixing left string
We can call dispatch after we dispatched
remove dead code
Set a default hostname if we can t get the local hostname
Fixed compile error from bad Nullable import
Fixed the crittercism NullPointerException for quickadding a task in a list
Add e package description to InstrumentationTestRunner javadoc
Log xml in case of processing iq answer error
set strictCompare to true to remain compatibility
Fix issue in connector refactoring identified by Gump
Added an isFunctionScope and scopeRoot null check to Scope java
Put the sort and filter options back in the main menu for tablets
Perform account search on pps package
Added static ZERO UNIT_X UNIT_Y UNIT_Z and ONE fields to Vector
reduce usage of JSDebuggerSupportUtils
Use C SAMPLE_FLAG_SYNC
expose the root used for the mustache
fixes of tests for foreign record allow null integration
remove surplus code
Fixed small toString issue in Invocation unwanted comma
Fix Node AIOOBE during Quantile computation
Fix messages binding date boxing
SourceFormatter Remove unused var
Fix logic error when checking for use inventory overrides
Fix weird thumbnail scrolling behavior on webkit
Avoid formatting error messages when using the look ahead parser
fixing some tests
fixed possible sync crash
Fix type in TwitterRestClient
ensure scrolling finished before popup is shown
ImageView setImageBitmap setImageDrawable to reset view
remove commented block
Add assertions for Inf Nan for float double in GBM prediction
do not use showAndGet for modal dialogs
Added info about plugin that failed to load
Added missing Override annotations
Demote the log in ProcessState ensureNotDead from a wtf to a warning
Fixes the phrasing of an error message
Log statements made it into a release
Add prefix for LinkBubble support emails
Remove unneeded SideOnly
Removed unused import
fix example missing break statement in ReplayingDecoder
Fixed crash when trying to rotate image when bitmap null
missed a different mock signature
Ensure that even if a close statement fails during connection close that we continue to try to close additional statements
Fix docs for MenuItem setShowAsAction
never skip execution of listener s display it affects focus
commit document after modifications in a command
Can t reproduce bug that necessitated this hack so took the hack out
Added support for DatabaseFiled foreignColumnName true to map foreign to non ID
Clarified javadoc for getCurrentToggleButtons in Solo and ViewFetcher
Actually send custom message when checking in to trakt
Make CSVReader more robust to poor CSV files
Replace calls to NestedSetBuilder addAll NestedSet with addTransitive
Only send broadcasts to registered receivers
Replace throw Throwables propagate checkedEx with equivalent throw new RuntimeException checkedEx
Git Don t check user name email for create patch and shelve actions only for commit and commit push
Fix warning on BufferedOutputStream buffer size
Updated example configuration in javadoc for LdapAuthenticationProvider
fix setClosableOutside destroy the current activity bug
Added exception for outer absence
Fix missing null check in AccessControlManager
Always get a writable database might need upgrade
configured event queue capacity
fix quality flaw System out
Add helpful error message to DispServlet initializer
Add property sonar core serverBaseURL
Fix recipies for stone variants
add hardcoded contracts for apache Validate notNull
Return the top most generated JType from SchemaMapper generate
Don t read strict class loader scope flag on class load read on demand
Sanity check the item ID for loaded liquid stacks better
Corrected documentation of NodeTraversor to reflect depth first order of node visitation
Making fields which are not present in some cases Nullable
Fix a bug in TaskStackBuilder where task stack PendingIntents would
Javadoc remove author in private class
allow equality check of StringType with java lang String
Added ThreadNameDeterminer CURRENT and PROPOSED
Closed a set of quotes in JavaDocs
fixed typo in properties file extension
Fix loss of header settings on example fragment recreation
Removed the comment out of the assertEquals test
Reverted inadvertent change to the public API
Make return collection unmodifiable
disallow recur from catch finally
Fixed resource allocated problem
customView View will use themed context for inflation too
Fix the build
Java Remove duplicate zeroing of counter
reduced stress on load test
Removed unnecessary null check
Fix a wrong location of the contact list backup file
fix selecting previously selected configurable in Settings Dialog
Don t show null for partially downloaded messages with empty text body
Always create tooManyConnections
Fixed binderFor calling itself
SHow options when plugin is active
fixed graph closing on MT env
remove component annotation
Make unknown fields on Message Builder transient
add useful getNative methods for PGraphics fellers
grails force recomputation of caches in tests
Fixed registration of PlayApplicationSpec
add some threshold while processing scroll down event when header view is visible
add sanity check for test
Fix docs breakage
Fix Downsampler caclculateScaling incorrectly offsets power of two sample size
Added back counter increment when sending packets
updated call to XMLRPCUtils sanitizeSiteUrl
Remove deprecated warnings
Removed nice comment
find usages of step definition
Improve error reporting
Fixed possible NullPointerException in DiscoveryService
Fix loop reference in TemporaryJobBuilder port
remove old code
Always warn users
back out this takes a different fix
Fix server error path reference in error controller
Fix time units in ldap sync log message
Fix broken test
Make sure CommandLineRunnerTest checks the exit code
Do not log invalid parameter value unless debug logging is enabled
Rename DiscoveryNodes smallestNonClientNodeVersion to getSmallestNonClientNodeVersion
Remove connectionInitSql deprecation
adding youtube link to the code
fixing setJSONObject setJSONArray
fix merge errors to make jenkins happy
Don t play a sound when adjusting volume while telephony is active
Add missing udp option
Add test for custom SQLite bindings version
Cast filesystem to distributed filesystem to support older version of hadoop
Don t use my DNS seed
Added json msg and id parsing
Added test for different encodings
android Cancel prev
add comment to deprecated tag
Added missing type to ServerTypeUtils
fixed load text for LightVirtualFile with win line separators
Used DeploymentScannerLogger for log message per recommendations
Do not blend opaque windows
Improve FailureDetector Unknown EP Error Message
Java Removed unused variable
remove redundant imports
integer constructor for session count changed event
Invalidate cached data for new name when renaming a table
reset myChangeListManager in tearDown
Avoid duplicates in vcs roots
Added documentation for NMA action config parameters
LwjglFrame allow initialize to set size and or position
Added system property muc room maxUsers for default max number of users value
Make ScatterGatherResult getRequestInfo and
Call invalidate immediately when a frame is available
Fix refactoring snafu
Send failure response to spout instead of doing nothing for the case that acker receives FAIL before INIT
Fix index condition and handle null mimetype extensions
push down allocations to where they are needed
Get the Future so the JVM doesn t exit before the undeploy execution is complete
Support an API key in anti CSRF form generation
Change disable timeout for BT disable stress test
restored underfs for tfs
Fix issue with engine not starting after canvas reinit
Catch an exception in ProjectWorkspace when following links
Get MonitorTag annotated fields from parent classes checkstyle fixes
Remove unused method
Handle getting a null from getNewParser gracefully
Add some basic print debugging support to fragment collections
use the new String byte Charset constructor directly
set local on inspections import
remove empty method
Fix NPE during DNS config reload
Curse you unused imports
added getRootLaoder for ClassLoader
Fix KITKAT usage
Remove obsolete imports
Setting visibility to GONE when refresh is done
Fix Configuration javadoc typo
Fixed Javadoc errors
Implement GPX explorer
Fix proper exception
Fixed the buildSrc cache so that it works fine after changing the cache lock format
Adds a getter for the gen jar
made newInstance nullable
enable debug for idea s coverage configurations junit application
Deprecate screenOrientation from AndroidHarness
Renamed parseStatusResponse to parseResponseText
Added hint for findbugs
GraphJung now implements DirectedGraph
Fix avoid roads
Don t mark every servlet as the JSP Servlet
refresh lookup immediately on up end
Add tip for parsing KeyValueFormat format from ruby code
Fix merging contacts from separate parent groups
Optimized calculateSize to consider Strings
Make comboboxes in AccountInfoPanel and ConnectionInfoPanel transparent
adding another xml tokenizer test
Remove extra status wrapper method
Wildfire would have jiveVersion not ofVersion
Set provider authority from resources
Delete some old and or half baked javadoc from DexBackedField
fix broken build
ensure tokenizer reset before retokenizing
Adopt propagated verbose level
Added support for getters and setters of X and Y on ShadowView
Adds getCurrentImageViews with parent parameter
Fixed bug that caused bottom progress bar to always appear when loading posts should only appear when loading more posts
Remove hardcoded version in Banner
Fix typo in name
For debugger expression evaluation in jsp files use context element from Java tree not JSP tree
execute method to help us understand it
Dont bother logging illegal cookie names as errors this happens during scanning
fixed quotes for rtype type in options
added error message to assertion
Fix code checkstyle
Fix isFullFluidBlock fix Floodgates
Widen the standaloneSetup builder method to accept the AbstractMockMvcBuilder builder type
removed unused class
Change second view position to be always below the draggable view
Fix testOnlyReadsPartialInput on Darwin
add time method to MultiKeyPipellineBase
taking a chance here
removed identical branch
Add comment for method onOverrideUrlLoading
Prevent Iron Pipes from switching to a plugged side
Do not log the size of uncompressed CE report
Remove ReactRootView s MeasureSpec assertion
Fix quality flaw in ConvertViolationsToIssues
Add lock check missing from waitUntilFree method
still propagate log
Add exact date setting to DisplaySettings
Fix reference leak in ComparableTimSort
fix sided proxy bukkit test not used so not a biggie
Added utility method to return content as a string
Choosing NONE as default folder was opening the MessageList of the NONE folder
added check of target null on traverse
Fix compilation error in ProjectGeneratorTest
Adds Ashley support to gdx setup
Changed JTextArea to ZapTextArea so that the Undo Redo Manager is working again
Removing unused variables
Fixing abort message again
validate commutes too
Improved logging when launching on device
Remove the text about altering the classpath
Fix issues with TabLayout tab indicator stuttering incorrect view pager item being selected when pressing a tab
fixed the class name compatibilities between client and server
Fixed concurrency bug
Fix poi map cancel issue
Cancelling a future should clear the callback
Fixed parseDocument to throw any exception raised parsing the stream instead of returning a null value
do not paint mac corner for Yosemite and newer
Fix a couple exceptions to use the original path string
Fixed a NullPointerException for the context menu in the Unified Inbox
Properly honor the init param value
apply DRY to SrcFrameHandler
Don t draw spaces that are the same color as the background
Adds utility method to SourcePacketExtension
deprecate key getData storage approach
prevent NPE when missing a library
Add isICSOrHigher to Utils
Delete a property when the value is empty
Fix up rebase related errors and warnings
Skip faulty test
Added proper equals and hashCode for Context that compares based on context id
OracleAQ does not support JMSType header
Fix bug where output stream was not flushed
Fix intermittent NPE caused by session cleanup after a member leaves cluster
Recent history displayed off screen
Added IS_32BIT and IS_64BIT flags to org robovm rt bro Bro
document extracted method
Debug log instances of OpenSprinkler library not initialized
Don t use getClass in InitStaticCode
added a checked exception
Bukkit will no longer leak tears
remove temporary code
Delete unused imports
Add JavaDoc to Logger from Sample app
fixed Snapshot integration test which was flaky
Improve batch handling
add test in node allocation is there are no shards no need to do local gateway allocation
Fix minor blur doc bug
Add date string to temporary job names
Moving field to top of class
Uncomment a test that was marked as STOPSHIP
fix wrong REST response generation of cluster state
implement last sketch restore feature
Add socket timeout in MockTcpTransport
Add asserts to please Findbugs
Remove MapMaker expireAfterAccess from public API internally
Fix bad Arrays import
Removed a Sysout
Skip web fragment xml scan in WEB INF classes when scanning an unpacked WAR
Fix weird animation when SnackBar has been cleared programmatically
Add toString to GrokPatternSummary for more useful error messages
Modify the member of FileInfo from public to private
Corrected FileHandle JavaDoc
when closing db close all pending statements also
use more appropriate exception types
Remove redundant null check
Fixed failing test case where part of Main s printHelp method was
Fix PythonInPlaceBinary rule key calculation
don t count identical param types as problem tie in getMatchingParams use any
updating javadoc s external links
Replaced invalidateDataset with notifyDatasetChanged
added default exception message to avoid NPE on logging an exception without a message set
implement output input for streams
Removed unused id variable
make some logs not emit
Fix imports and remove useless test class
fixed negative check on string emptiness
improved error report when using erroneous list attribute format in annotations
Fix Android Keystore key gen for keys requiring user auth
Added unbind call
Execute continuation in tests
use HttpURLConnection HTTP_NOT_MODIFIED
Fix outdated javadoc comment
Fix bug in the logic for determining if a rolling update is timed out
encode a binary e g byte as per the spec
Give preference to adding new instances
Move defaultInstance s frameBufferManager assignment to getDefaultInstance
Allow default SMS app to get self phone number
Fix rawtypes warning
mockito verify fix
Bleeding Add new TargetReasons to EntityTargetEvent
don t create anchors for invalid elements
Changed threadID to threadId
reinstated setting of mContext variable in constructor
Add comments to IndexedSet
remove unused code
Removed unused rsIndex property
Added needed support to exclude certain databases from support for allowGeneratedIdInsert
Fix whitespace issue from pull request
Fix transport layer
Add back CordovaWebView getUrl needed by tests does make sense to have
Added headers to set ttl eternal etc when adding updating to cache
Added Buck project generation event to external interface
Oops usersFile should use u as short form
Fix regression in camera transition
Improved the exception message after being bit
Fix accessibility state callbacks
remove obsolete copyright
Remove unnecessary override
Prevent TextureView from ever setting a null layer paint
Add method to recursively delete dir for testing
do not throw exception if not presnet in domainngramfreq text
Fix CDMA BCD unit test
Executor should log the commands executed
fix bug in info bar visibility management
remove System err prints from Receiver
Removed unnecessary ternary expression
Add isGranted to isDisplayablePermission
ExamplePaymentChannelClient should not send PeerGroup to the extension since it uses WalletAppKit
Fix wrong MessageHandler type for Reader
Don t remove the slash if it is the only one
Remove unused variable
Tapping in a empty textEdit shows the handle
Do not proceeed next frame at send failure of previous frame
preserve library grouping in packages pane
checked empty failover file to avoid warning
Fix emmet wrapping
Fix broken AStarPathFinder
Fixed JUnit test
Adds a comment which explains why we pick the first role from network response
Refresh quicksettings location tile text when language changes
Fix emmet tests
Don t start UsbDebuggingManager when data is encrypted
Fix initialization of Hadoop native code
Removed empty block warnings in TextView
Removed debug text from title
Revert Remove unused ApplicationContext fetch
Fix wakelock leak in PowerManagerService sendNotificationLocked
applied patch by TonyHoyle
Fix transport layer rotation bug
Break loop on finding the first non printable character
Upgrade your JDK to use FileRegion
Revert whitespace changes
Fix typo in example in javadoc
Fixing a NPE in DevicePolicyManagerService
Remove legacy javadoc
injected files do belong to project scope
Switch animator to fixed rate scheduling
Increased the timeout value for commands to accommodate Travis slowness
AsyncNCSARequestLog blocks JVM exit after failure
Remove hack for early Tomcat version
Fixed Checkstyle errors
Remove redundant test comment
remove Throwable declaration
remove debug line
Added link urls to the msdn descriptions
Fix display of null string shown in Wifi Settings for AP
add missing and order in terms aggregation
change setDisplayedChild to be public
Use ActivityLauncher to switch sites
Do not call alarmManager cancel with null intent
Fix flaky testQueryNameFromSysOperations
make counts and sums in histogram non final for serialization and deserialization
Updated docs fixed cs errors
Verify fiber target only if verifyInstrumentation is turned on
Removed unnecessary methods added standard constructor
Mercurial Tests missing status
Improved title for taskbar
allow configuration during starting state
Fixes a couple of warnings introduced in a recent revision
Fixed small bug in rotating shapes
Fix Android tests
Skip processing annotation when holder is null
fixed editing test
Fix a small bug
Fix test bug
increased wait time of commit and rollback operations to Long MAX_VALUE
should run oncomplete function when error stop tasks and remove surfaceview
Fix tree border
made http and https args constructor private
Load lockscreen disabled setting on database create as well as upgrade
Fix failing tests
Move load fence to public method as not need on private usage
augmenting max inactive time
removed extra space on entity declaration in the main docbook xml
Fixed javadoc warning
handle reading exception more gracefully
use AnnotationUtil to find VisibleForTesting
align TurnPathHelper color with NextTurnInfoControl color they depict identical type of information
Fixed bug where tapping the Comment context menu item in ViewPostsFragment didn t do anything
interrupt queries on incremental indexer
Add hack for failing js completion tests
Removed unncessary parenthesis
Improve detection of model variables
Reduce the frequency of annoying git gc messages in dev environment
brace matching util check for each brace type once
Remove unnecessary parameter
Don t accept remote connections
add NavigationListener to sample
Make a slight tweak to the way we calculate means in StatsAccumulator
UNSAFE throwException null arg crashes JVM
Reduce visibility of field with public getter
fix selected algorithm
Fixed bad imports
equals method modified and hashcode added to ActivityLifecycleCallbacksWrapper
Fix failing precondition on actions that don t start the PeerGroup
annotate stacktrace do not show hand cursor for nonprocessed lines
Remove finals conflicts with CGLIB generated classes
fix sdk build breakage remove invalid note tag
Multi query results results UI
Fix checkstyle violations
log prohibit repaint during collapse expand all branches
Throw exception if timestamp has incorrect format
fixed NPE in PasswordSafe
Fixed the CS errors in camel core
Properly cancel animators when View doesn t exist in starting state
reverted Sergey Vasieleiev change that skips removal of duplicated usages
add utility method for tree renderers
Fix failing test
Add getMenuInflater to Activity
Removed unneeded info in module s quickdoc
Turn off MORE_DEBUG logspam
Reuse bindProgram in OGLESShaderRenderer
Remove fixed TODO comment at least in this branch
Fixed a bug in ListViewUtilsTest and added commons collections to pom
Fix a String format locale issue
SNAPSHOT Trigger retry logic also if we hit a JsonException
remove deprecated method
clarified comment for method inCodeBlock
Fix finding recipe by tags
Removed a TODO that didn t actually show big improvement
IndexRangeComparator should take begin and indexName into account
don t index blobs
save projects in list instead of set
Remove leading trailing spaces from search string
Fix NullPointer when deleting the last entry from a group
Changing callers of deprecated methods ImmutableSet of E and
Fixed focus problem when escape from dialog
Fix an NPE when there is a clinit with no codeItem
Add support for bitcoin URIs
capture the handler registration for resizing in TerminalPane
remove deprecated constructor
Fixing harmless NPE seen during testing
defer arming breakpoints in Shiny files for single file apps
Removed HeapByteBuffer address field check
Add missing import manual back merging fail
remove unused var
enable apply button for top level configurables
Fix inverted scrolling condition in lists
Fixed setDeliveryPersistent javadoc
Make module maps generation conditional for native Swift rules
Reduce multi part upload threshold
Fixes commenting out a missed import
Add rsbrowser rbrowser to list
add TODO for builder conversion
Use groovy syntax highlighter
preferred focused component for authentication dialog
Adding license headers to new files
Fix broken performance test
fixed imports and removed not needed log message
Traded places copyright package info
ftp producer should force reconnection attempt if sending noop fails in pre write check
don t show change version button for app server library
don t create the destination folder when copying messages
don t use deprecated method
Exclude camel ribbon from PrepareCatalogMojo docs presence phase
Query the correct Extra for BT intents
Added a getCurrentActivity in getViews in order to make sure that finalize will finish all openeded actvities
Detect empty result paths
ignored bad test
Dismiss QSB dialog when doing an in app global search
added call to canvas setFocusTraversalKeysEnabled in initSurface to
avoid assert on getDebugProcess
Check setName for null string typo fixes
Fix quality flaw
Add a new deployment phase
using auto resource management
fixed check for SVG file if header is missing
Fix non portable check for BlockMissingException
FIxed broken tests
ensure thread suspend policy for logging breakpoints
Skipping font objects with no embedded font file
add log message on hte concurrent streams used
don t call getAbsoluteTop for slide navigation menu in IE
Remove unused import
remvoed curly brackets
apparently firebird does not support ddl in transactions
Improve the sql query in deletePeopleForLocalBlogIdExceptForFirstPage
Improved performance of writing a string to an output stream
fixed copyright notice typo
Fix merge error
remove long running test
fixed javadoc issue
Ignore deprecation of setAlpha in MovieDetailsFragment
Use correct starting size for clip reveal transition
reload the model on rebuild
Dependency parser should provide dependencies
Adapted junit test to new serialization format
store the otr data files in sdcard Downloads for simplicity
Fixed retrieval of context variables in document fields
insert single quotes when in double quoted context
Accept other messages when testing canceled requests
Fix a compilation error
tweak resolve logic to work better with PyGObject
Removed nearest config beacon methodology
pass _nums and not numNums
don t skip empty lines in the line number count for config files
Added a commit to write transactions on TorodbSafeRequestProcessor
Removed a sys out
As part of a refactoring move the start callback into the subthread
Added isFullJID helper in StringUtils
Implement new BroadcasterFactory API
Allow lockscreen orientation to be overridden with a system property
Fixed broken test
Revert MAX_HIDDEN_APPS experiment
Added Wunderground total precipitation
Fixed duplicate registration of execution vertex listener
Hazelcast idempotent repository Should use locks
avoiding bad filenames
Added additional check for proxy filter by address
added workaround to maven compiler bug we may want to throw an exception though if its really a problem
add cache method for some block store
Insert comment with indentation
Don t force SSO on DEBUG builds
Added bind unbind Stickers
Introduced a workaround for the runaway daemon hanging around after gradle was executed in a single life daemon mode
Removing redundant imports
Fix a typo in IpConfigStore
Fixed glmva validation for multi chunk datasets
Fixed possible NPE
Fix problem where PhoneWindowManager waits for keyguard to draw
Increased command latency in RollingCommandMaxConcurrencyStreamTest
removed dependency on platform
Remove some dead code
Add a check that when extending an interface the interface being extended should be goog require d
Add missing preRequest
Added explanation about class adapter vs object adapter in the adapter
Change super setProperty to use super set type Property in example comments
if showing live templates in completion show them in explicit one as well
Prefer Editor apply over commit
modify the main method
Fix bearing when we calculate projection
removed debug info
fixed header layout issue causing it to receive double padding
Remove unused PsiJetFunctionStub getAnnotations
added missing script usage
Adds the update to the list of available params
Don t notify windows of resize during PIP animation
fixed Line emission with no width
Improve error message when a key is undefined
Use correct ChannelInputStream
Fix a NPE
add volumeupbutton volumedownbutton events
Remove unnecessary variable
Changed indent printer field access level from private to protected final easier sub classing
Fix the build I ve broken with my change to GroovyDoc
update inspection tool state if options were modified
Remove Wearable API warning messages when API is unavailable
Prevent JLine from expanding
Handle error responses from Clearspace during chat transcript updates
Fix link in MetricName comment
HashLoginService does not stop its PropertyUserStore
Added support for links at any point in transcript
Allow Debug filters to print an identifier
Fix me analytics names to follow the action at the end convention
Fix CompletableEntityStore upsert operation
Make Groovy method public escalating shouldn t put explicit public modifier
Removed use of HttpHeaders entrySet
Fix new Javadoc
Declare two fields as final in BsonBinaryReader Mark
Replace getActivity with context
Added fallback when accessing the Mail session when systems deny access to the default instance
amend bug description comment with latest test results
Set rebased class initializer method to be public if the class initializer is defined on an interface type
Fix regression in APR refactoring
Change CachedAuthenticatedSessionMechanism to return NOT_ATTEMPTED rather than NOT_AUTHENTICATED if the identity manager returns null
only send new visiblerect and size on onscrollchanged if we re not in overscroll mode
autoscroll from source shouldn t request focus
Add CarrierConfig for metered roaming
Don t log bad surface size before layout is done
Use correct Utils version
Added in the end of multi file namespace internal names to prevent them from having Test suffix
correct the MySQL version check in DateTimeType
fixing merge problem
fixed Expressions signature issues
Fixed spaces instead of tabs
Updated sample to have badges
Added scrolling check for HSQL Matt review this
Fixed JavaDocs in pact common
on second basic completion suggest class names everywhere
Remove accidental Ignore
assure toCollect doesn t return null
Revert resetting scroll position on refresh
Improve memory accounting in PagesIndex
Fix build with latest SDK tools
Fix the build
Fixed a NotYetConnectedException in closeOutboundAndChannel
Added synchronized method to GenericPool that allows shuffling of poolitems
for IDEA level ignored folders keep show in viewer only folder
Remove unneeded code
Protect the foo
Deprecate the annotation RequiredMeasures
add a missing javadoc tag since
Don t require an exact match for the welcome file list just make sure
fixed performance problem
Fix bug when a class referenced by both
Fixed a bug with tag context menu intent names
Add a very forward looking comment
fixed travis bug
always set original file for all psi roots
Add brutal hack to prevent stale icon displaying on Settings
Changed param name for Table matchRows to sync with Table matchRow
Initialized the Nether Fortress chest loot
Fix MSSQL case sensitivity issue on sys indexes
Fixed bug reported in ML by Alex on wrong cluster used to store records in a graph
Fixed NullPointerException when initing Tellstick binding on startup
tried new postprocessing no luck yet
Use the correct method
loading an invalid rule file printed the error only to stdout instead of showing a dialog
Fix HeliosSoloDeployment pullIfAbsent
Construct exception message only if the exception will be thrown
added string representation version of the property
delegating call to the new typo free method
Pre register the inner class as well
editors should only handle bulk changes of their own documents otherwise main editor constantly scrolls during test run
Remove some duplicated code in DocumentsUI
reset ReadVerbHandler buffer between uses
Fix UserManager isUserAGoat
Fixed package documentation to not reference Factory
Do not update the progress if it is the same as the last given progress
Added IQSharedGroupHandler module
add a tracing tag for video
Fix force local scheduling for Hive
added to completion group
Fixing javadoc errors
Add PSK as option for second element in handshake algorithm pattern
Avoid retrying unrecoverable alluxio exceptions
GlobalInspectionContextImpl must to reset view state
Don t create new object
Toggling Recents from home should actually toggle Recents instead of launching the first task
only allow single selection in Existing Templates dialog
Add tests that labels cannot have single or double quotes
Add missing since tag
actually use the resolved address
Rename set methods to reflect state
Added missing function in ArrayUtils
Add a warning about the SSID format in CaptivePortalTracker
Set desired size parameters
Use implicit aliases in TransactionalEventListener
Added vcard query parameter for not attaching photo
Fix potential NPE when excluding last change author
vcs Handle all exceptions during annotation similarly
Rename parameters to match names in overridden methods
Do not eat those exceptions that require user interaction during the discovery phase
revert incorrect check in RenameUtil
Added cluster state version to the debug logging of shards instances used in search
add fileItem Param
Do not process annotations twice in LAZY mode
Do not run in app purchase query if on beta or X
Cut the conformity field value when saving to SimpleDB to avoid failure
Set ListView Header Views to not be selectable
add new plugins icons
remove PlatformTestCase initPlatformLangPrefix calls
Fixed bug in getType
Only declare Bitmap as cached if it is in hard cache
Set user repository prefix from setting
remove BINARY from DROPPABLE_VERBS
log Fix NPE in the roots column graph not fully loaded yet
Clarify the service lookup magic
do not cancel popup if already disposed
Fixed some item durability values
adding support to drop scheduled jobs
don t set symlink mode when zipping
Set additional template cache devtools properties
show prompt when no file found on Cmd Click
change logging level nodes FD will detect it as well
Fix constructors for new clocked TimerMetric
change the failing test so that it works closer to the real life and doesn t fail
Added parsing of Cython structs and unions
Do not index dummy documents in ExistsMissingTests
remove left comma from previous commit
Let users control SSE stream completion
revert JdbcDataSourceStat changes
Move calendar group init code back to main thread
PUTs don t have a content type don t NPE on them
Fix access modifier of some RecyclerViewSwipeManager methods
Fixed a bug where DuplicateChannelBufferTest doesn t test DuplicateChannelBuffer
make size static and return JBDimension
Fixed image loading issue
Fix build failure and don t tie NIO connector to a single SSL implementation
used seperator constant in rest ids
SourceFormatter imporove check for wrong line break
Fixed delete repository from repository panel
remove wildcard imports
Fix TouchableNativeFeedback state propagating to children
Add a categorical column explicitly to weather junit for PDP
Support Hibernate CGLIB modified domain objects
Don t handle backspace on empty field
Always explicitly use a Locale
removed dead code
includeAll within jar causes NullPointerException
Added boolean assertArrayEquals
remove useless code
Add singleton scope to navigator
Add additional diagnostic
add a few comments onto the image parser and serializer
Use extended error description for exceptions during installation of framework packages
SMALLFIX Move precondition message into a PreconditionMessage constant
Could not convert java type to Hibernate type
Remove unnecessary log
Fixed issue with recent change
Remove LayoutTest http tests xmlhttprequest simple cross origin progress events html from skipped list
Log more of the value to checksum
Actually send the ScenarioResult to the ruby call
Fixed a few null item checks
SMALLFIX Disable failing integration tests for now with TODOS
Included the partition id into the operation toString as well
RefDirectory merge fixed
Fixing a bug with RestService
Fixed custom json serialization declaration needs to be final or static
Enable Show Progress option only if the right scrollbar type is selected
Removed unnecessary synchronized modifier
getImportedTargets doesn t return duplicates in case of importing graph of any complexity
Added UriParams for camel twitter
Properly handled overflows when attempting to move buffer position forward
Remove fragment as well as the query from URLs when resolving MIME type
Add support for arrays in AbstractBuildConfigFields
added console message about studio URL
Fix isEmpty implementation
Adding tests for bean violations
Make sure the angle is in the correct range required because the angle is divided
Removed forced frame expansion
Fix bug in HorizontalBarChart
Fixed a bug where longpress was not possible in landscape
Changed providerJars from List to Collection
Leave zen none on manual ringer mode stream volume changes
Add a TODO in PeerGroup
Fix Squid plugin to correctly build the relation between a Java file and its package
Fix import controller importer matching code
Fixed issue in which DashChunkSource didn t make use of the most recent
Fix wrong cast
Removed unnecessary logging
Added missing check if permissions are supported on the filesystem
escaping char value in groovy stub generator
Revert Change the flamegraphs flag so that it needs to be set to true
Ignore JS test back
Disable Show in History Tab if message not valid
Should reset visibleLimit on clear messages
remove unused comment
Declares RajawaliScene internal collections as final to protect the safety of synchronization blocks and enforce the intention
removed unnecessary changes
Add CheckReturnValue as an alt name of ReturnValueIgnored
Prevent page tracking checks from spreading across tests
fix bad comparison of IColumn to ByteBuffer
Remove unused code
Always put current user first in user switchers
Remove hardcoded groovy grape report downloads true from DependencyResolver Anthony Hsu via Ashutosh Chauhan
NPE fixed that caused by plugin that violates NotNull constraint
Fix AttributeDefinition flags parsing
Fixed a bug in SmoothCamera not calling super onUpdate which prevented HUD updates and a ChaseShape not to be chased
Removed untrue TODO
The progressStart now also reset the current offset
added QueuedThreadPool getQueueSize
remove double count of panel open
Remind myself how to run the postgres unit tests
Fix override of API withCancellationToken
Make database URL handling more robust
Removed debug message in GroupRestrictionsManager java
remove deprecated warnings
integrate ven s change
Disables Jabber telephony until it s actually ready
Add accessor for variable importances for DL
remove unused import
Ignore a couple of tests
GetGlue Check In task execs on thread pool
make new welcome screen available in Rider and all others JetBrains IDEs
Add a method for getting the known states from a model
remove unused getters
Correctly handle interestedOps changes
add LWJGL renderers to list of known renderers
process cancel action
fixed compilation error related with generics usage
Fix mongo db compilation problem
Make Path Builder visible
don t infer method contracts when there is already an explicit Contract annotation
Replace some hardcoded values by MessageDialog constants
Changed the result handling from isNull getResult to be getResult wasNull
add adler to checksum faster and is good enough with length check for our use case
Enforced plain text file type manager NPE fix
Adding thread safety annotations for tachyon mesos in integration module
added getNotification method to RestClientUtils in networking lib
added useful methods to VertexAttributes class to find VertexAttribute for a specified usage
Eliminate race condition around backup completion resumption
Steer users away from dereference
Improving fixing comments
Addressing review comments
Only show Reverse order if there is more than one active marker
Fix CommandExecutorTest java test on Windows
Allow the ClientAuthenticationHandler to become extensible
Fix the MalformedPolicy error code when passing a null to the optional ipRange variable in getCookiesForCustomPolicy method
remove bogus Override s
getFunctionInfo should register jars under a check Reviewed by Amareshwari
fixed test compilation
do not break layout if we don t know revision number
updated and fixed unit tests
fixed display name
added some more docs
Fix NPE in setSurfaceTexure
use the proper token stream to return
library Don t update items if the adapter is null
Move the unit test to the same package as the class it is testing
Add missing head tag to last commit
ChooseItemAction should handle default shortcut char
update asm in coverage
Fixed strong tag closing in javadoc documentation in RepositoryService
toolwindow should focus content when clicking on its header
do not allow start void variable migration
Fix broken test sorry about that
remove unnecessary switchUser call
fix some javadoc warnings
don t declare them as Nullable
add ensureGreen to testSelectStarWithOther
Fix crash while editing QS
Improve username en de coding for urls
Fixed compilation error in ExternalExtensionsDialog
Add missing nextLine
ToggleButton could keep a reference to a previous drawable
Minor Fix typo
Remove unneeded and possibly problematic isDone
Really fix the build one more change to SearchDialog which shouldn t have
removed unneeded imports
deleting unused field
fixed localplayer getViewDirection
Added test comment
Fix NPE during polling
fixed exception message for task listener event names
HiveServer2 dynamic service discovery should add hostname instead of ipaddress to ZooKeeper Vaibhav Gumashta reviewed by Thejas M Nair
Fix http scheme
reverting back dumb service message propagation
Inserted test for LONG types
Fix incorrect return impl in SmileXContent createParser
Optimize removeGroup method by running in reverse
Remove unused code
handle empty arrival departure times
Don t allocate more memory than necessary if normal allocation is already aligned
Fix import in SharedClusterSnapshotRestoreIT
adds workaround for Context leak in Crouton
Fix javadocs of NameResolver
Fix crash support info
just catch everything
fixed eval tests on windows
Fix problem with duplicate directory entries in the database
borders fix oops
Ignored Examples java tests since they re not regression tests
Add lookup key to callerInfo
Don t use deprecated hibernate scanner anymore
Also remove any unavailable extensions to avoid saving them again
don t remove recent project panel on speed search
Removed CommandCallable getValueFlags
Fixed invalid argument exception in error message
Remove unused field updater from SubjectSubscriptionManager
fix the problem when the recyclerview is not set parallax header
do not build mirror tree for getText and getTextLength on a
added getter function for DBM for future use
override correct super getters
Remove debugging output
Remove obsolete NoopSearchRequestBuilder setNoStoredFields
Fix navigation service when come back to Map
Wizard proposes wrong Project location
eliminate eclipse warnings
depricated method removed
RSRR doesn t actually throw DigestMismatchException
Remove always empty ObservableHorizontalScrollView Callback method
Collect all TextureRegion with indexes for a particular regionName
Allow a custom Executor to be passed to the TerrainLodControl
Fixing log message for star tree generation
prefer java util Date to java sql Date
deprecate unused code
Fix exception type
MavenIndices don t run case insensitive test on case sensitive FS
reduce NPEs in CQE
Fixed NPE in AppCompiler when building console apps
Do not merge Backport a fix for InputMethodManager java
Added support for setting background colors for tabs
HttpTransportOverSPDY add newline
Remove FLog isLoggable String int
Revert change to short circuit WidgetGroup hit detection
Added back removed method
Added a condition to get the correct comments row
Modified the setAttribute method to remove the attribute
Remove useless comment
Fix NPE in Transport applyBatch
Add missing GroovyObjectSupport to PgpSignatoryFactory
Remove upper bound from timing tests for slow cloud CI
Add guard around debug statement
remove explicit WriteConcern
Register the JSONP filter in the standalone Grizzly server
Avoid NPE when bitmap s config is null
Delete exit button
Fixed debug message
Fix XML format
Fix null pointer exception
Hide keyboard before typing
Fix test timing issue
wrapped adapter notifydatasetchange not working bug
Fix issues with noticing that a VPN interface is gone
Makes ClosureRewriteModule respect suppressions
Fixed a string comparison
Add hierarchy view to downloads
android fix list LayoutParams cast error
Removed a TODO note and added key count implementation rough for now
use validating XML parser
adds a method to allow configuring the default configuration for the compiler by using properties
Fix items rendering too low in first person
Remove obsolete TODO
Also handle unexpected authentication terminations where no exception is present e g age restriction
Remove unused variable from animation objects
Remove comment about generic param that does not seem to apply
updated the FlickrApi to use https
Unpack the moduleSource when delegating from caching repository to external resource resolver
Create children cache on demand
Suppress some warnings
Fix pipelining performance problem
removed timeout against remote connections
Define our own jgroups stacks
Prevent crash if last row goes missing
removing improper warning status of a log message
Fix key leak
Fix that sample app crashes on older Android version when initializing ListView with header
rest binding should inheirt the options
Added getter for authoritiesPopulator
Having a serialVersionUID generates a warning if the class is not
Initialize TimeSurface cutoff so isochrones work
change the position of the stream after reading
Add the name of the class that gets the anonID regenerated
remove chatty debug output
Removed incorrect manual link
Fix merge issue
Disable the PreconditionsExpensiveString check since it has a bug
remove finality of getOutputContext
Remove redundant public modifiers
Add missing defaults
Added API TODO
add annotators only once per language
Add missing import
added helper method for builder implementations
add hidden method to draw entire page into a canvas with no view chrome
add aync opening camera while start
Change client created file permissions
groovy preserve order of methods in class by using linked hash set
display currently configured wake up interval
Fix a TODO
Fixed the variable name for retrieving people list from db
WebView Remove fallback call to WebViewFactoryProvider no arg constructor
Fixing failed tests
Always show hidden tasks in gtasks to match website
Made some AbstractAtomicLongOperation methods final
Preventing a NPE
keep consistency with the debug log for ShardingService
make getDafaultLaf method public
Added implementation of SQLiteDatabase isDbLockedByCurrentThread to the ShadowSQLiteDatabase
Also remove boxing for reads and method calls
Create PercentLayoutInfo on demand if not already set
Added reference to Gray paper
Fix network name not showing in QS sometimes
Made MessageFactories abstract
Adds support for undefined Jingle reason codes
Ruling out document editor dependencies
Handle multipart paths that begin with
Removed unnecessary null pointer check
make POSDictionaryBuilder public
Documented one more thing TODO
RmStep should resolve path passed to rm
Log messages printed on stderr by command ine tools containing warning on
fixed possible NPE
added the ability to set a custom meta class on scripts
Only send touch events to gesture detector
Ignore associated domains when comparing entitlements
compare strings correctly
Removed some redundant stuff from the JUnit rule
improve MathJax sizing behavior on Windows
removed password log message
Ignores this test
allow lower chart scale values
Removed unused getEndpoints method
Fix copy paste error in Javadoc
Try loading the class first using the AtmosphereFramework
delete obsolete TODO
Fix header names not getting written back
remove null response when processing line by line with autoOutput enabled
improved debug handler output
Catch numerical instability earlier
reverted non public proxy interface change
Add note on unintuitive part of code
Fix precondition error message to use s instead of d which is not supported by the preconditions formatter
Add a space between recipients when doing toString
Fix the case sensitivity of the Cache Control header
Treat screen on while pausing as pause timeout
JdbcTokenRepositoryImpl updateToken should use lastUsed arg
Remove unnecessary log line in common join operator Gunther Hagleitner reviewed by Prasanth Jayachandran
Avoid NPE in constructor
Improve docs for MetaClass getMethods vs getMetaMethod
Fix crash when restoring SearchView
update clc WebRTCRuntimeProvider
ignore failing tests
Fix occasional NPE from reflection initialization
fixing a list members examples trivial bug
Prevent home task from being set as ontopofhome
fix second application project components error highlighting
Remove unused import in o e p PluginManagerIT
Fix compilation error
Fix the missing command in the background execution and update the vertx id option
Fixed bug that caused scroll position to be lost in reader post list after returning from post detail
Fix NPE in ConnectivityService
Add MONSTER to CreatureType
Add missing assertion to util class ctor
Handle VEVENT with malformed GEO
Fix test Windows
killed most of the old checkin implementation
Remove redundant onBackPressed
Public JavaDoc for ParseException should not mention WebAddress as this is not public
Remove some System out println
Add check for null InputStream to prevent infinite loop
Removed unused field
Change some rolling update logs to DEBUG not INFO
Fixed copy paste mistake in docs
Added VoldemortApplicationException handling inside GetCallable GetAllCallable
Add license header
Remove racy but unnecessary assertion
remove unused code block
TEST Only use the clients from the node
reverted tag name
fixed paths to updater log files
debugging the test
implementing kcooney s suggestion
disable welcome screen
warning was repeating token index twice
Fix broken java doc
Using isNullOrEmpty method
Configuration should return legacy parser by default
Add an ignored View only if it is not allready ignored
Don t flush each time if we use a buffered output stream
Check tag deletion status when creating new links
Make the helios client AutoCloseable
Removed improper check on mCalledExitCoordinator
Cleaned up unused code
Updated the null check which was missing
Removed check on name
