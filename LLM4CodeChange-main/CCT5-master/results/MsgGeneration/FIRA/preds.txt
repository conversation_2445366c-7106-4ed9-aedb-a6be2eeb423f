Added some error codes to MessageDeliveryFailedEvent
do not generate maven project system dir for non process builds
Improve exception message
Remove redundant local variable
Fix a race condition when restoring wallpaper settings
Remove comet flag
Render modality constants in DescriptorRendererImpl
Add SearchSourceBuilder sort String Order
Disconnect supplicant before reconnecting
Remove unused RedisSessionModule getSessionKeyPrefix
Fix NPE in PhoneStateChangedReceiver
don t start swipe refresh if scan is already running
rename DeletionRetentionStrategy to DeletionRetentionStrategyConfig
Improved JavaDoc of OliphauntPool
Added test for naming of member name extended class
don t capture keys when completion popup is visible
Removed recycle of ImageView bitmap
Fixed a bug where LayoutTransition setupChangeAnimation does not fire
Don t show edit mode for group conversations
Added isEnabled to PhysicsControl
Fixed infinite input iterator
allow closure pass by default
Don t persist task bounds for static stacks
Add a random digit to the ALPHANUMERIC_CHARS constant
Fixed bug in VariableSizeList
Add a comment
Replaced RuntimeException with Asser<PERSON><PERSON>rror
Add static instance to TimeUUIDSerializer
Remove broken tests
Fix javadoc link
remove unused method
do not add duplicates
Remove unused variable
Remove extraneous semi colon
Add a deterministic build ID to Android builds
Fix typo in ColorSpaceTransform
add shortcut for next window
Fix Patterns class name derivation
Remove unnecessary GsonBuilder from ModelBuilder
Don t hide system ui
Fix bug in alter table add column analysis
add debug logging to MessageOutputBindings
Add SPDRF view to Inspect
Addmi to iOSDeviceTarget
Added documentation for the sdk parameter
Clarified javadoc for searchText in Solo and Searcher
Fix documentation of firebase recycler
Changed SparkInterpreter to use logger instead of System err println
Added author tags
Remove ProjectLock from ProjectTaskContainer
Throw IAE if the adapter doesn t have stable ids
don t assert isDispatchThread if it is the current thread
do not show empty mnemonic
Fix the test
Remove unused parameter
Bump the maximum notification prompt time
Fix broken AppleTestIntegrationTest
remove searchJavaHomeInDir for Linux
Add isComparableKey to ValueAttribute
Fix bug in refreshMap
Call super onUnload in commit dialog
Add another test
Fix NPE in UniqueId
Don t log RTP packets if local address is not set
Fixed the test error of camel cxf
do not set caps twice
Fix a buffer leak in AbstractNioByteChannel
Replace deprecated method
Fix NPE in DenseNodeChainPosition
Fix negative RSSI values in WifiStateTracker
Fixed test on windows
Remove legacy code
Added more help to the usage message
Hide GATT_CONNECTION_CONGESTED flag
Fix commit log sync period
annotate stacktrace doesn t include br tags
Add constraints to the root pane container
Log messages to stdout
Add a debug log message to ServerChannelLister
Fix broken build
added Jid fromSessionID
Fix typo in default link
Fix quality flaws
Moved Log_OC to lib common utils
do not validate inner jsp
fixed checkstyle issues
Fix AUC example
make SimpleOnTabSelectedListener static
Add a delay to the pipe transport items
add final to static guard
Make FileSystemUtils asByteSource public
do not generate project generation for project specific settings step
replace ArrayList with Collections singletonList
removed empty line
Improve debug message
Disable contact scorer by default
Added header container to MessageView
added onUserVisible method
Fix checkstyle error
Close the file handle after reading
Fix compilation error
Added TV density constant
Throw an exception if the user fails to authenticate
Make RecordReader non final
rethrowing InterruptedException while waiting for request
you track state upDATING
Fixed typo in method name
do not set empty indices
Reverted the previous fix to EntityBinding
Catch all exceptions when extracting a bundle
remove dead code
vcs log use the structureFilter instead of the rootFilter
added missing copy of hasRetainedResult
Add debug logging to quartz component
Fix search history location
add catch for InvocationTargetException
Remove unused methods
Fix NPE inTopologyContext
Don t register tick triggers in debug mode
Fix SIP tests
Add compressed files to the list of allowed files
Remove unnecessary rtp packet processing
disable test for now
Fix OC issue
Added a comment
Fix broadphase creation
Only include shows that are not already added
do not show empty notifications
Display offline message for crew members
Fix checkstyle issues
ByteBufferPool acquire should call BufferUtil clearToFill
Make TestLogicalPlanner fail on invalid SQL
Added note to JdbcTemplate
Removed unnecessary parallel test
Deprecate BootstrapModule constructor
Add routeType to Legs
Clarify help text for generated classes
fixed NPE in toStringBuffer
Remove unnecessary method
don t show arrows for self loops
Fix logger name
adjust scope before actual update
Fix call panel focus
Add missing dot in BazelGenRuleRule
added timeout to pingPongTest
Added NOT_ORDERED to Ordered
AddACT_DATABASECHANGELOGLOCK to clean check
Added newReader URL String charset
removed unused code
Change the URL to wp admin customize
Fix nowbriefed bug
Log duplicate symbols at FINEST instead of info
Add a comment
Don t analyzeFILL_ARRAY_DATA instructions
Remove unnecessary code
Added missing imports
use a tip instead of a string concatenation
Remove unused classes from RestApiService
Added binary text frame generators to WebSocket generator
Added canSelectArbitrarily to LinkMovementMethod
Remove deprecated code
Hide create button in registrarations dialog
don t highlight intention with block selection
Include exception in BeanDefinitionStoreException
Remove a System out
fixed partition predicate
add missing copyright header
Added Lucene version filter
Deprecate unused OnMenuTabSelectedListener
increased timeout time
increased sleep time
Fix an Eclipse nag
do not highlight switch label statements
status text no longer uses the default cursor if status is visible
Fix broken Javadoc
Remove redundant modifier
align bookmarks at top
Allow size parameter to be specified as a parameter
Improved exception message when class not found
Fix license header
Fix SearchFragment not restarting loader
Remove extraneous Log statement
Removed unused imports
Fixed toString method in DefaultWorkerProcess
Ensure the Input Method is above the dock divider
Fixes a bug in the TreeContactList where numbers were being parsed as numbers
use application invokeLater instead of SwingUtilities
Add missing break in Http2Parser
Add toString to DeserializedClassDescriptor
Terminate the EGL context when pausing
HttpSnoopClient should use DefaultFullHttpRequest
Fixed a bug where FolderList wouldn t start up
CheckAlsa file status before waiting for alsa file
don t suggest suggestion for instance of expression
use a regular expression to split comma
fixed text color
Fixed the CS error of camel mail
Added NOSONAR to new Alias class
Add receiver as user
Fix setDisabledByAdmin bug
Fix typo in AndroidBinary java
Don t close the connection if it s already closed
Add missing parameter
TEST Fix shards size test
Send Vendor ID command for CEC device
Fixed a bug where SubActionButton s backgroundDrawable could be null
Request focus in history view when header message is received
Fix a race condition in DefaultChannelHandlerContext
vcs log make commit message opaque
Add missing Apache license header
Improve logging of special network not available
fixed unresolved description highlighting for built in constants
Fixed bug on UPDATE THE SCHEMA
hide header in PropertyTable
add getAlpha to Drawable
Added default constructor to WireConverter
don t serialize Object
Fix race condition in MinimumMasterNodesTests
Removed obsolete TODO
Fix NPE when deleting a registered service
added copyright notice
ConnectionPool throws RedisConnectionException
Don t cancel preloading of recent apps on long press
SMALLFIX Removed unnecessary param tag in Javadoc
Remove unused method
Remove unused OpenCameraInterface open
Disable csv csv module
Fix bug in huntForTheScriptFile
Fix documentation for onHiddenChanged
Fix broken test
added code tag
Fix NPE in MockProject
Added a method to set the response content length
Fix MashapeResponse getCode
Fixes a bug where the OK button was not clicking the OK button
add wait millis to GetConnectionTimeoutException
Fix null pointer exception
Rename rabbitTemplate to amqpTemplate
Fix logging of FPS
Fix PreferenceDialogFragment to return the correct fragment
add showPopup to speed search
Fix license header
Add helper to get cache control
Skip empty URLs
Add getNames to MetricRegistry
Fix off by one error
Make logger field protected
Remove unused code
removed unused imports
Delete user flag when deleting a user
Add missing license header
Added getAuthId to Messenger
Remove unnecessary code
Remove OP_0 from Script
Remove commented out code
Fixed compilation problem
Fix bug in switchUserLocked
Remove unused code
Fix NPE in SopremoServer
Remove unused import
fixed method name
Fix restricted check
remove public modifier from interface
do not log PCE
Add Transport to LocalTransportManagement
Fix test failure
increase lazyUpdates default value
calculate total size instead of comp size
Fix compilation error
Added working directory parameter to getProjectFile
Disposed the main window now calls System exit
Add weak reference to JarEntry parentJar
Add more randomness to threaded evolutionary test
Fix index error
Suppress mapping deletion warning
Add NonNull to shouldShowRequestPermissionRationale
Removed unnecessary toSystemIndependentName
Reduce amount of log spew that apps do in the non error condition
Updated license header
update the comment
fixed test on windows
Fix wizard icon path
Added setFireworkMeta methods
Fix PreferenceLayout equals
Fix a bug where we were not setting resultSize to zero
set file type in DownloadStatus constructor
Remove unused method
Added getGraphDatabaseService to Neo4jGraph
Remove unnecessary code
Changed the Intent type to application json
Added getNamedClient String Class
Fix bug in NettyAsyncHttpProvider
AbstractCacheRecordStore createRecord now parameter
Fix broken test
Fix trakt refresh issue
Fix RAD operator
Log a warning when ClientCall cancel is called without any detail
When building a tar tar archive append type as source if needed
Fixed a bug where HttpSnoopServerInitializer does not remove HttpObjectAggregator
removed check for diagram creation
Add query params and headers to log
preserve order of modules in move to module fix
Rename batchSerConfig to serializationConfig
Fix bug in GetReadRepair
Fix a typo in the documentation
Make DescriptorRendererImpl renderMessage protected
Don t recycle ActionMenuItemViews
increased polling source test timeout
Fix NPE in Editor
Remove dead code
Add validation for abstract or has empty constructor
Record artifact before creating a new step
Fixed unit test
add TransactionalExecutionTestListener to company service test
Fix default zoom scale in WebViewCore
Add a stub to Platform getNameInPlist
Create event tracker directory if it doesn t exist
Clarify PaintILL_AND_STROKE documentation
Enforce a maximum size to reduce memory usage
delete the file if it exists
Add missing delegate method
Added missing javadoc
remove usage of IconUtilEx
Remove deprecated SERVICE_NAME
Removed unused imports
Fix AudioRecord native_setup signature
Rename configurator to configscript
Add a new setting for enabling cellular on boot
Added potion and glassBottle to the list of liquids
Removed required tags
make constructor public
Add if else to keywords
Ignore test that randomly fails on Travis CI
Fixed a bug where the bounds were not updated properly
Make service ticket lock transient
Added requestFactory javadoc
Speed up bike walking
Catch UnavailableException in schedule
Added trace logging for jabber provider not found exception
Set the user agent to wp android
Fix typo in test
Fixed javadoc warnings
Remove Id keyword
Fix NPE in C2Chunk
Remove unused method
Changed setNewList to set
Removed obsolete comment
Fix Javadoc formatting
Fixed issue on remote test
Disable method restrictions by default
Fix error message in RemovePluginCommand
Added rightAngelBracket to properties
Improve Context detach documentation
Fixed height calculation
Removed unused import
Fix DanmakuUtils equals
added metadata annotation
Add toString to DefaultModuleVersionSelector
Fix potential NPE in MetricsRegistry
Remove unused methods
Fix NPE in resetConnections
Fix color resolution for dependent colors
Fix drawTexturedModalRect for combustions
Fixed a bug where RTPConnectorInputStream does not set the receive buffer size
Remove sticky broadcast when activity receives intent
Clarify Player setName Javadoc
Added JavaDoc for LoggedInputStream
Remove threadpool generic max from tests
Fix context menu
Fix typo in closure rewrite error message
fixed book removed event
Deprecate some unused methods in CodeNarc
Added healthy method
Fix default mode
Remove unnecessary check
add validator for user password bcrypt salt size
do not collect inspections for non project intention elements
Prevent deadlocks in KotlinCacheManager
Fix NPE in SeekBar
Removed unused method
SMALLFIX Fix indentation
Fix a bug in protobuf serialization
Allow NULL header values
Fix Sequencer claim and forcePublish
added missing dependency resolution
added missing import
get settings from editor settings
Fix Javadoc in ThreadContext
Fix camera rotate method
Removed extra space
Throw IAE on invalid serial port
Removed unused field
Add ImageCache delete
Fix CacheBuilder javadoc
Add registration method to NameResource
Removed unnecessary call to setPrinter
Set default locale to English
Fix line length
Add error message for dynamic configurations
Fix histogram legend output
Fix tests on Windows
Remove outdated comment
Fix quality flaws
Remove unnecessary requestDisallowInterceptTouchEvent call
Fixes a bug where Jingle calls to JingleMediaHandler would throw an exception if the media format is not supported
Added missing license header
Add RaptorNodeSupplier to TestShardEjector
add javadoc for setCustomHeaderView
Fixed a typo in a comment
Make sure wizard dialog is visible
Fixed potential NPE
Don t enforce BLUETOOTH permission
refresh merged files after adding them
remove unused field
eliminate eclipse warnings
Fix broken link in package info
Remove unnecessary casts
Added sleep to OperatorObserveOnTest
Fix test on windows
do not refresh mappings in tests
add bundle method to BaseSliderView
Added some methods to DatabaseResults
Fix test for compression
Set the default encoding for AESCrypto decryptText
Fix post status comparison
Turn off debug output
Remove unused method
rename ElasticsearchSingleNodeTest to ESSingleNodeTestCase
Deprecate the default no arg constructor
Delete best model after spirals test
add NotNull annotation
create meeting before creating new one
make thread a daemon thread
do not convert numbers to strings
Fixed bug in MultiComparator
Improved error message
Use the class name instead of the simple name
add method to set initializers
Improve performance of SQLiteDriver openDatabase
Don t allow Object
Improved message for partition events
Print minimal width height in toString
remove optimization traversal for depart after searches
Make go highlight exit point handler factory base class
Add getConfig to AppCompiler
Removed check for Clob
Fix failing test
SMALLFIX Removed explicit argument types in GenModel
Fixed ACL example
fixed failing test
Don t use LocaleENGLISH
Fixed bug on UPDATE with WHERE
Fix Javadoc for expires filter
Add null checks to WebServer constructor
Add lucene version check to Bootstrap
Add option to handle errors during stress
Remove debug output
Improve logging when sigar fails to load
Added Flyway placeholderReplacement
Improve error message
fixed bug in var call site generation
Add GET SET to IQTypeFilter
add windows style path test
Oracle tablespace resolve fix
disable debug output
Fix crash in WindowManagerService
Fix the build
Added LoggingFilter to TestServlet
Improve logging of no servers found error message
Removed unnecessary fs create
Remove unused imports
Fixed test on windows
Ignore MapListenerTest for now
Fix typo in ApplicationInfo javadoc
Add test for missing mandatory digest value
Improved error logging
added set double to SimpleTensor
Fix NPE in MUCServiceImpl
Set the JSType of the first argument in ClosureCodeRemoval
Add test for getPropertyNames
Added author tag
Add createTileEntity to MixinSpongeImplFactory
Disable maifcobolplugin plugin for integration tests
Fix NPE in WebView setSelection
Remove unused code
Remove extra space in log message
Remove unused imports
Check connectivity state for disconnected networks
Remove color filter from rate movie
Added convenience method to ClassDescriptor
Added setBitmap to ShadowCanvas
Fix NPE in FragmentManager
Remove Nullable from ImmutableClassToInstanceMap getInstance Class
Fixed javadoc warnings
Don t close JarFiles when using URL caching
don t complete html tags
Don t set widget height for non locked widgets
add debug step button to secondary toolbar
Remove unnecessary modifiers
comment out unnecessary check
Removed log message
Fix query builder
Fixed type parsing
disable sm logging
Move BufferedWriteStream to internal package
Call onAnimationUpdate in onAnimationStart
git Fix Git tests
avoid compiler warning
Fix json property name in DuplicateToTimeSeriesVertex
Ignore exceptions in calligraphy attributes retrieval
Fixed a failing test
added test for embedded interface
don t hide console when interrupted
Add a copy method to RawHeaders
Add a method to restore original display
Add date modified time to media media provider
added min max to parameter name folding manager
Remove group_split parameter from GBMV2
Remove unused code
use MapMaker to make the cache
Fix EvaluatingContext for suspend manager
PyTargetExpression extends PsiQualifiedNameOwner
don t generate annotations for light classes
Fixed a bug in the log message
fixed AttributeEqualBuilder initialization
TEST Fix UnassignedInfo constructor
Fix NPE in QueryMonitor findFailedTask
Disable drop for now
Fix javadoc for getDomain
Fix bug in IsDistinctFromCodeGenerator
Disallow touch events when recyclerView starts scrolling
Fixed a bug where the new episodes fragment was not being loaded
improve sql stripping
Fix keyword completion
remove obsolete comment
Make CordovaChromeClient appView protected
tweak completion timeout
Added missing documentation
Added a TODO
Fix wrong import
Remove tool tip heavy weight
Removed unused interface method
Clarify ConfigFactory load String javadoc
Fix race condition when trying to free a temp block
add try catch to setNotificationsShown
Remove unused method
Fix default style background color
Use the correct gopath when building the test run
Improve logging of rolling update step
Fix NPE in unregister
remove unused constructors
Fix alarm decoder binding shutdown
do not retrieve descriptor from parent
Do not log ConnectException
remove unused code
use buffered input stream
Added toString to SimpleBuilder
Added tests for SQL INSERT
Remove unused code
Remove the AtmosphereResource from the Broadcaster
Deprecate methods that have no effect
Fix race condition in DefaultChannelPipeline
Fixed the CS error of camel spring
Fix NPE in closeRecents
Added missing javadoc
Fix ShadowMediaPlayer not resetting startOffset
Don t paint image in edit mode
fixed bug in console process handler
Reduced sleep time
added test for direct call andbridged call
Fixed the CS errors
Remove redundant initialization of mPowerManagerInternal
added some comments
Addressing review comments
Fix broken test
don t rollback parameters list when parsing errors
Remove unnecessary toString calls
Add comments to TelephonyManager
Add missing documentation
Temporarily comment out patch level check
Fixed a bug where the shy height was not calculated properly
Fix the build
replace NotImplementedException with UnsupportedOperationException
Fixed bug in local chunk provider
Remove shutdown hook from tool main
Fix AndroidObservable bindActivity
Fixed a bug in the previous commit
Fixed potential NPE
Fix javadoc for method flags
don t show lookup list if it s too small
Added World createExplosion
Fix checkstyle issues
Add WebChromeClient to ReactWebViewManager
Remove duplicate code
Allow Validator to be added to MethodValidationPostProcessor
Make NullAction serializable
Add useCaches method
do not log PCE
fixed NPE in RefHashMap
improve javadoc for parse String
Clarify Function s javadoc
Fixed regex for numbers
Declare printer as unavailable if capabilities go away
Fixed bug in restoreTransitory
Fixed a bug where the directory of the store was not being closed
Renamed a class
Remove unused field SchemaResolver publicId
Add body method
ignore unexpected messages
Remove extra space
Remove unnecessary call to stopForeground
Made field final
Catch Throwable instead of Exception
Add a flush method to Translog
Log exceptions thrown by SuperConsoleEventBusListener render
Add support for ga_opt_out and ga_hadoop_ver
Make getters protected
Added getLayerCount to Scene
Make saveLater protected
Make ForApplication Documented
comment out test for now
Fixed bug in VertexBuffer setUsage
add javadoc for ls and mkdir
Fixed Material equals
debugging test failures
Add unit test for table names
Removed Ignore from test
use the same language for the quick check
added test for tuple arg
Fix H264Reader to handle scratch slice correctly
Include exception in exception
Allow retrieval of the context strategy
Remove unused AndroidUtils getHttpClient
remove unused getLanguage
Clarify DeviceAdminReceiver extra documentation
removed unnecessary try catch
Fix NPE in NioDatagramWorker
eliminate eclipse warnings
Fix a bug in ReadTimeoutHandler
Fix NPE in JettyRequestUpgradeStrategy
Exclude email followers from getFollowers
disable failing test
Fix the build
fixed cs issues
Fixed ORole setParentRole ORole iParent
added logging for readevent when not heartbeating
Add rport argument in SIP request to fix some SIP servers
fixed bug in RowN materialize
Fix title direction in PhoneWindow
fixed checkstyle issue
Remove unnecessary synchronization
Add ResizeMonitor to the javadoc
Don t add app tokens to new tasks
Fix typo in Header javadoc
Fix brace highlighting
throw IOException instead of assert
don t use mnemonic for buttons
Fix DumpRenderTree tests
Don t inflate views
Fix bitmap font break char detection
Added toString to ItemCounter
Fix husk zombie
add custom options for new project wizard
remove unnecessary parameter
Fix Double Tap detection
Fixed test failure
Fix javadoc link
Ignore null URLs in ImageDiskCache
Remove unnecessary null check
Added missing Override annotation
Shorten the time we try to load the database
Add a REPLACE_EXISTING option
Fix parsing of sql check
reduced log level for override reserved variable warning
Fix minimum width for wallpaper backup
Add javax rmi api to JacORB dependency
Fix typo in comment
Improved error message when invalid server list file is received
Improve Javadoc for IntStream consume
Removed unnecessary project basedir from sourceDirectory
Reset primary actions only if the selected row was the controls row
Fix NPE in TextView isLayoutRtl
Commented out unused code
Fix typo in documentation
Fixed a bug where notifications were not updated when there are no active notifications
Add setAdapter ListAdapter to MaterialDialogCompat Builder
implement equals for ExternalAnnotatorInspectionVisitor
Fix broken test
added random routing table builder
add UNKNOWN SemVer parseFromTextNonNullize
Add missing break statement
Fixed the test error of AggregateGroupedExchangeTest
escape literal expressions
remove dead code
Fix AndroidManifestFinder activityQualifiedName
Add comment to ExtendedUserPermissionDto
Make Environment getJerseyProperty return T
Fixed since tag
Fix bug in DefaultDeserializer
fixed bug on serialization of edges
Initialize defaults for block properties
Rename tell to whisper
Hide the big text view
Add thumbnail column to bookmark history
Fix compilation error
Fix logic for handling else and for statements
Removed unnecessary null check
Fix NPE when getting system property os name
Avoid divide by zero error when idealized gain is zero
Removed return value from core function
added final to constructor
Fix repository names sorting in GitBlit
Make StaticCompileTransformation in INSTRUCTION_SELECTION
Fix intent comparator test
use onReflowComment instead of reflowText
do not show test roots in new project wizard
improved unique constraint snapshot generation for SybaseASADatabase
Fixed size of double constant
Remove TachyonTException import
Fix NPE in PackageManagerService
Add InstanceInVPC rule
remove unnecessary check
add set String to Vec
Remove unused import
Call ChunkMonitor fireChunkProviderDisposed in LocalChunkProvider
Fix ShadowView finishedAnimation
Fix checkstyle violation
Remove unnecessary type parameter
don t use a new class loader by default
added progress bar
Add Transactional annotations to DefaultTokenServices
do not update actions for non visible toolbars
Remove assertions in CFMetaData
fixed python doc comment parsing
Fix documentation for HttpObjectAggregator maxContentLength
Annotate ReplayCache with NotThreadSafe
Fix SpiceListView getAdapter
Add parallel and thread count to TestNGTestClassProcessor
Add some documentation to ClientContext reset and init
Added PluginManager getPlugins
don t show notifications on exit on windows
remove unused import
refresh file only on write action
Don t move stack twice
Turn down NBHM resize logging
Fix NPE in edit ticket page
Don t re measure TabLayout tab max width
Fixed bug in tokenizer
Replaced OOB check with Precondition check
Fix return value of onTouchEvent in zoomable drawee view
Remove unnecessary close
Fixed a race condition in TransactionConstraintsIT
Remove unused code
Add file option to StreamJobPatch
remove highlighters from task file when answer placeholder is empty
Remove sanitize from rename
Fixed a bug where pipes were being replaced by leaves
do not show compiler output for default language level
added getOriginal to ConstructorDescriptor
Remove unnecessary synchronization
Fix NPE in DebugASTPrinter
remove alpha filler from blendColor
Added a constant for MINISLEEP
Deprecate sticky behavior
Reduce the size of the cache
Fix InformationSchemaServiceTest on windows
fix linear bek bug
dispose preferred focused component
increment the count of stream rules
Camel naming strategy should use the same prefix as the component name
Add missing break
removed unused param
Fixed a bug where the month wouldn t update the ui
Fix mssc verification listener
remove debug print
Fix flaky testClusterStatsTests
fixed checkstyle issue
Remove unused field value
Flush packages list when GID memberships
Add connectionface and settings to AsyncFrameWriter
Add comment about undefined symbols in IntelliJ
Remove unnecessary annotation
fix failing tests
Add debug logging to Spring Batch producer
Added missing constructor
TEST remove AwaitsFix from SimpleIndexTemplateIT
ChangedSimilarities listFactories to return a Collection
Set the default session cipher to null if no DH mode session is active
Remove unused code
Fix Rapids help endpoint
don t complete class name completion for multiple files per document view providers
Add a note about the fact that the version strings are considered compatible
adjust doNotAlignChildrenOfMinLines default values
Commented out test cases
Added missing annotations to Ignore
If no session was found then answer an error packet
Remove misleading documentation
Fix MCC qualifiers
added missing license header
Fix NPE in JsonObject getBinary
Removed TODO that shouldn t depend on other fields
Replace getTextRange with getRangePair
Fixed a potential NPE
Fix off by one error in JarBinaryRules
Make holderHead final
add NotNull annotation
Added missing methods to EditorFragment
Fix NPE in MessageParser
remove outdated comment
Fix complement of DEFAULT
Fix NPE in ProjectManager
Fix failing test
Added lsub response type
Fix JDBC error message
Updated the comment on BSONDocumentBuffer
Add scale parameter to nativeUpdateDrawGLFunction
fixed test case
Pass attachments to mail engine
add originally from
Added TextureOptions REPEATING_BILINEAR
Fix NPE in UDFJson
remove unused imports
Fix AarMojo to use packageResources
Added NoopTask create
Remove unused code
remove unused method
Fix tooltip text
Move IdGenerator to internal id
Fix handling of InterruptedException in CommandLineJob
Incorporated PR comment
ReaderWebView should use fixed font size as default
Added RealClock to bootstrapper
Preserve order of modules in inc project build targets
Removed redundant null check in BatchUpdateResponseBuilder
remove unused method
Removed hardcoded values
improve error message
Fix content provider
Fix display name of CallParticipant in call panel
Remove unused imports
Add missing license header
remove unnecessary check
add a comment
Don t create a new line in the enter key screen
Add utility method for JELLY_BEAN
do not focus navigation bar on button1
Remove condition on kafka enabled
Add context path setter
Fixed a bug where notifications could overlap
Make CompoundProcessor non final
added x y toNodeDataImpl
Add missing license header
Fix the build
Add missing import
Remove annotation jar from test classpath
Change rebase conflict indicator to be more accurate
Revert Ignore broken test
sync engine Add trace logging to sync event processor
add setSpeed to AnimationTrack
removed unused import
Add javadoc for getRestrictBackgroundStatus
don t draw sequence flow if it s a gateway
Changed the description of doChunkLoading to keep chunks it is working on loaded
don t show completion shortcut in one line mode
Remove unused method
Fix javadoc warnings
don t call reflectFilesForElement if child is a file
Fix typo in permission check
Remove unused import
set maxPreTransitTime to Integer MAX_VALUE
Add a TODO
comment out PSI changes
Fix checkstyle issue
made isPowerOfTwo more efficient
Fix zip file name
Fix FlavorContainer flavors type
Add note about multiple instances in parallel
Remove unused web artifact name tag
Set maximumFractionDigits for custom bounds
Use the correct package name for task stack targets
Remove outdated comment
Fix NPE in sharded distributed message queue
Add processing opengl to core imports
Fixed bug on max memory
Add assertion to DLIrisTest
Add NullRoute chaos to list of chaos types
Fix typo in DefaultUniqueDelegate
Add NoBatooJPA annotation
Fix MessageSizeInterceptor to filter already filtered messages
Fixed the test
Don t create Props if already loaded
re enabled isolated rule check
apply settings after setting deletion
don t show preview images for non java files
Move expectedResolveData field in AbstractLazyResolveTest
Remove IOException from BurnCpuChaosType
Don t show settings fragment if activity is not attached
Use MetricsLogger action instead of MetricsLogger visible this
Set mInTouchMode to true
git remove unused method
added resetWeightsOnSetup method
Fix JoddBean thisRef bug
Added cancel method to AndroidCompiledStatement
do not highlight type parameters
Avoid NPE if bundle is null and treat it like an MRE
Fixed failing test
fixed copyright loading
Added locale to StepDefinitionMatch
Fix NPE in framework sample source
Removed some logging
Add a default constructor to OneWireRefreshService
Add a method to replace showcase button with a layout which isn t a button
Make static final flag private
Reset HBase endReached after creating new HBaseResult
Fixed bug on post database creation
Fix bug in ExponentiallyDecayingSample
Fixed bug in Execution
Add Pattern to WellKnownMutability
Added secondsBetween method
Fix JacksonConverterFactory bug
Fixed bug in alias mapping
Fix assertion in ZenDiscoveryTests
Change GD_SECOND to long
Add missing line break
Fix typo inExitTransitionCoordinator
Release lock after TLS is negotiated or we are not insterested in TLS
add logger to GraphMetadata
Disable SseIntegrationTests for Jetty
Reduce log level of blockChainDownload log message
Fixed bug on multi iterator
Fixed bug on opening a connection to remote server
Fixed error message for invalid enum value name
Improved javadoc for GenericWhiteSpace
Fixed a bug in decoding HTML entities
removed debug log
Remove unused imports
Don t animate windows that aren t visible
Handle exception when initializing tester agent list
Fix javadoc link
Fixed bug on profiling
Add setter for default persistence unit root location
reduced log level
Fix line length
Add documentation for EpisodeFlags
Fix test data
Fixed array get method
Deprecate Throwables propagateIfPossible
Fixed a bug where the model was not closed when a project is resolved
Made RtspMethods final
Fix NPE in propagation policy
Deprecate BukkitWorld setBlock
synchronize access to merged data
enable console metrics
Remove unnecessary parens
Removed unused method
add comment explaining stream provider usage
fix a bug in protocol read
Added NAME to PublishingExtension
Add missing MessageFormat format
Added support for Object type in DatabaseResults
Add launch method to ActivityController
Improve error reporting
Fix error message in MediaBrowser getRoot
Fix VelocityTracker javadoc
Fix power query bug
Fixed a typo
Add license header to HttpUrlConnectorTest
unquote literal values
Suppress LocationManagerProximityTest for now
added support for PotentialStarter
Handle BadTokenException in clearInsetOfPreviousIme
Expose TextView getTextSize
Allow groovyOptions useAnt to be disabled
Fix HandlerAdapter handle Javadoc
do not create sdk for default project
Initialize currentFilesSize in OnGoingRecovery
git add size key to fetch dialog
Fix typo in JnaUtils
Don t check isM2compatible for Maven metadata
Fix startup info logging
Fix issue with missing completion stage
Only log messages that have subject and body
Make AbstractExecution complete synchronized
Fix SQLEvalVisitorUtils visitExpression
Remove unused method
Fixed the CS error of camel spring
throw exception if no leader found for partition
SourceFormatter source formatting
Make default audio capabilities public
Add a bit of documentation
bind DefaultSecurityManager as eager singleton
Removed unused field
Deprecate HystrixCollapserProperties that are no longer used
Disable shutdown hook for beans
Fixed a bug in the TouchUtils
enable app locking feature
remove unused pattern field
Add screen name to notification title
Fixed bug on parsing of strings
Add javadoc for JsonSchemaValidator matcher
Add explicit locale to String format call
Added getCullingArea to Group
Don t copy the payload if it doesn t exist
Add AwaitsFix annotation to DeleteByQueryTests
Fix a small bug in DRF
Fixed infinite loop
Fix flaky DeregisterTest testJobsArePreservedWhenReregistering
Fixed error message
ensure save before render rmarkdown
Fix javadoc formatting
encode search query params
SourceFormatter Make use of multiple source processors
Add a helper method to convert a float to px
Fix user name example
Revert Fix SpringBus issue
Fix missing byte in AssetRefTable
Fix ADB device detection
refresh Git log on repository change
WorldDevIndicatorReducer now extends Reducer
fixed file name generation
Use project sdk instead of project JDK
Fix SmalideaMethodTest on Windows
Fix default sub id validation
Fix broken test
Fix the build
Simplified isMonkeyTime method
Fixed the CS error of camel core
Fix JSONP interceptor
Fix heuristic heuristic for onboards
Fix URI constructor
Fixed a timing issue in the test
Fix deadlock in WMThread
reset parameters after parsing url
don t show find text for new lines
Add type parameter to SavpOption combo box
Ignore test that intermittently fails on CI
Improved error message
added flush method to FeatureVectorEncoder
Don t allow flashlights to change state
Stop the updater thread and interrupt it
Fix an issue where exoplayer does not reset the codec state
Handle empty string in AssetTypeHandler
Added a test for the root group
Fix XContent test case
Fix typo in method name
do not create additional options for checkin project
Fixing back button behavior
Fix code snippet
Simplify if statement
Fix link to Kotlin with gradle plugin
Remove unnecessary warning message
Remove stack trace from MissingResourceException
Changed assertLowMemory to assertNotLowMemory
Add theme to chart
Added missing license header
prevent null pointer exception
Timer task should not persist timer twice
Add missing delegate for copyAshmem
Remove unnecessary logs
Fix change bounds
Don t swallow command exceptions
Add a note that the operation is not synchronous and the admin might still be active
Add headers and params to toString
Fix a bug in PeepholeFoldConstants
Fix comment typo
Don t change elegant text height when it s changed
Updated error message
Add exp to grid view animation list
fixed bug on update edge
Removed Ignore from test
Added private constructor to ClusterFixture
Fix mailx typo
Add a convenience method for building time format
Use a byte stream to hash the file
Clarify the default expiration value for subscription requests
Fix a bug in the NetworkLinkerLibrary
Fix NPE in deodexerant
PrepareCatalogMojo now checks component properties correctly
Renamed TestPrintableInterface to TestClusterInterface
Fix a bug in isVersionedDirectory
Remove unnecessary else
Fixed bug in stopDirectComponent
add cluster name to cluster state rest response
add override test to all tests suite
Added missing license header
Fix typo in method name
Fixed bug in StringUtils getRelativePath
increased default reserved system memory
SourceFormatter Remove unnecessary code
Added default case to MemberAttributeEvent
Fix typo in mod info log
Don t recycle accessibility events
add method to get timer
Add tests for count distinct
Remove hardcoded ES HTTP port
Fix NPE in ContextMenuAdapter
Remove unused imports
Add empty listener
Fixed date calculation
Fix call dialog focus issue
Improve assertion in TestStandardContextResources
Use the correct class name
Fix NPE in DribbbleShot
Don t invalidate cache on environment change
Make calendar sync related settings public
fixed possible NPE
Make GuardedBy Checker a MATURE
remove unnecessary code
allow symlinks aware FileUtils delete
Fixed bug reading plugin xml
Fixed bug in Vertex removeEdge
Display the name of the current account if there is any
Read the result from the annotation
Remove unused code
Validate views only if they are visible and enabled
remove debug log
Remove some debug code
Added a method to clear the routes cache
Remove unused code
Fix typo in TypeLiteral javadoc
removed unnecessary dependency on java util Iterator
don t show notifications in read state
Fix test failure
use the correct protocol
Remove unnecessary check for isDebugGable
Pass the TTransportFactory to the server
Remove unnecessary SuppressForbidden
Fixed unit test
Added some logging to TestResources
TEST Only bump replicas if we have at least one node
Changed Config to BuildConfig
Removed AbsListView dependency
Fix a bug where HdmiSendKeyAction does not call sendKeyDown
Add getInstructionIndexAtCodeOffset to InstructionOffsetMap
Moving the checkSideEffects before the transpilation
Added support for named modules to ClassFileLocator
Fix NPE in closePoolableStatement
add broadcast shards header to flush action
Increase REST client shutdown timeout
Fix NPE in FileObserverService
fixed bullet rigid body rendering
Fix possible NPE
Clarify audio track timestamps behavior
Fix a typo in a test
don t show mnemonics for stripe button
Fix PluginList returning null
remove unused code
Don t use java sql Timestamp for dates
fixed NPE in DlugoszVarLenIntPacker
fixed schema version number
Fix checkstyle issue
Clarified javadoc for getCurrentSpinners in Solo and ViewFetcher
Fix DrawerLayout left position
removed unused method
Remove unused import
Add excess workers to log message
Fixed a bug in isKeyPressed
validate editors are released in for loop
compare types with
Add getUrl to ServletFilter
Fix manual reference tags in DB java
Fix quality flaws
Fix HunspellServiceTests afterClass
Removed status stream attribute from auditor
Initialize the matrices to identity
set priority for pyCharm
Added a getter for the parent MethodIdItem
added test for mismatchable matcher
Fix typo in DeploymentDescriptorMethodProcessor
Mute OldIndexBackwardsCompatibilityIT testOldIndexes
Don t throw exception on persistent connection
Make close public
Don t add a parent view to the canvas view
Fix NPE in StateIngame
Include URI in exception message
added exception to log statement
Add expected events count to test
Added a TODO
Removed unnecessary code
Added a test for inline boolean functions
include exception in Cassandra write buffer logging
Use a longer timeout for proxy test
Add toString to PartitionIteratingOperation
enable cleaner by default
Made the observable HOT for the purpose of the demo
sanitize file name in CxxFlavorSanitizer
Remove TODOs from AnalyzerFacade
Fix duplicate network ids
Removed commented out code
remove unnecessary code
Throw IAE if offer takes a negative value
update freeq url
Increase the buffer size of SimpleTextMessage
Improved error message
Fixed imports in PipeTransportPower
Fixing the build
use new ImageManager Builder
fixed bug in append char sequence
set default charset for build command line
don t show TOGGLE command for groups
Removed disableAudio from LwjglApplicationConfiguration
Moving declaredGlobalExternsOnWindow to after transpilation
Removed unnecessary groupBy call
Replace UnsupportedOperationException with SemanticException
Fix NPE in View getLocation
Fix a compilation error
remove changelist action
Java typed handler fix
Make MultiMount final
Fixed a bug with double slash in search command
removed debug print
Throw a runtime exception if you try to get minecraft logger
Rename bindSocketAddress to bindSocketAddress in UdpChannelTransport
Fixed Stage clear
Fix NPE when left right is null
remove dependency on PeerFactory
Fixed serialization of Enum objects
improve error message when groovyc cannot be found
Fix NPE in ApplyRemoteMasterDomainModelHandler
Fix NPE in sessionChanged
Avoid NPE in getPropertyType
Fix model container retrieval
Java Remove obsolete TODO
remove SimpleTransfer traverse
Use FRIENDS by default
Reorder media sources
Remove ThreadSafe annotation
Fix search query
correctly adjust hint position
Don t show the file detail fragment if the file doesn t exist
Remove debug flag from FastBuildOptions
Fix a bug where the current version was not set
Fix NPE in ConfigUtils set
enable read count by default
added constructor with default constructor
Display home as up in PhotoSelectionActivity
Fix NPE in ThriftUtils getTypeRef
Allow custom fonts to be set to the guiFont field
Remove unused variable
Handle _ initerable
Validate model test case
Fixed null values in Line
add smile annotation
throw exception if RDF format is not found
Remove unused constants
Fix typo in Ordering toString
set delivery icon on query complete
Add a quick fix for invalid beacon data
Remove class prefixes from entity type names
Fix security path matching
Remove equals from CFMetaData
use linear graph for visible graphs
Fixed an issue with redstone ore dropping
fixed bug on command transformer
Don t add default imports to importDirectives
make CompilerConfiguration extend UserDataHolderBase
Fix error message for too many dots in table name
Fix latitude longitude in PlaceActivity
Fix compilation error in NodeCorrectlyIndexedCheckTest
Fixed the CS error of camel http
do not ignore listed nodes
Fixing the build
Fix a bug in OperatorMerge
Remove unnecessary return tag
Pin the fast scroll thumb to the top bottom during overscroll
Fix a bug in View removeMainToolbarSeparator
Remove TODOs in EvaluationTests
Fix javadoc formatting
Add repositories to core dependencies
added support for dot notation in IdentifierSplitter
Add a human readable string for ResultEnvelope
remove addConsole from createFooter
Add missing methods to Future
fixed NPE in anonymous can be lambda inspection
Avoid NPE in resolveSourceLocations
Don t reload the config on resume
do not perform directory traversal for files with dunder
fixed log aspect
Fixed a syntax error
Don t register SIM records twice
Improve the documentation of FixedSampleRateTraceFilter
Fixed compilation error
Changed default HttpClient to be the default one
Don t show flagged messages in the message list
Add support for item animations to UltimateRecyclerView
Added missing license header
Add getDispatcherType to ApplicationHttpRequest
Do not collect package providers for all users
remove redundant call
Remove unused code
Remove unnecessary precondition check
Fix the build
JTS driver does not implement free
set the ready status
don t use PsiFacade to create whitespace
do not allow config methods
Add throws declaration to MulticoreWrapper put method
Add listener for interest ops
Make BufferPool instance volatile
Added adaper to Ivy context manager
Add missing param documentation
Don t log ClassNotFoundExceptions
Fix javadoc for Maven publications
Add distance to current track summary
added appendToPathEnvVar method
BatchInvalidator pollInvalidations should not return an empty list
Fix typo in javadoc
Remove a TODO
android update WXViewUtils
Clarify SimpleItemAnimator javadoc
Fix NPE in ColumnFamilyRecordWriter
Don t remove movies watch list if trakt credentials are invalid
Fix typo in progress bar icon
fixed join if intention
Fix text in Simple Action Bar
Fix javadoc link
Replace RuntimeException with IllegalStateException
Add IPAD_PRO_WIFI to IOSDevice enum values
Add TRACE_TAG_REACT_VIEW constant
Removing unused code
do not log exception
Fixed a problem with shutdown of AbstractActiveService
Fix int overflow
Added missing parameter to getTanks
Fix NPE in CounterMutation
Don t show the lockscreen if the keyguard view is occluded
Fix comment in TransferManager
add error message for compiling
Close the dialog when adding a view
Fix typo in FileCollection
Add null check to postMessage
Handle unsupported framework zip type
Install the naming context if nessesary
Added colorFluid to FluidRenderer
do not log migration not applied twice
Use a ConcurrentMap instead of a ConcurrentHashMap
add breakpoint types
Fixed NPE in cloning
Recycle the bitmap before setting it to the original one
Allow zip files in input files
add Codec module to PercolatorBenchmarkTest
Don t allow unexpected loss of precision
Add javadoc for getCommitter
allow splitterSize to be specified as a parameter
Create method from usage with Kotlin built ins
Remove empty line
Fix HeaderCheckTest expected error message
add comment to SimpleConcreteVertex
Fix NPE when titleView is null
fixed CS error
Fix NPE in returnResourceObject
Fix gpx playback
Fixed unit test
Fix a bug in APR processing
Add a TODO
don t allow empty strings in New Property action
Remove Ignore from camel karaf
fixed background issue
Add missing Override annotation
Add a deprecated method to NullPointerTester
don t compact the header
Fix links to OrderingExplained
fixed failing test
Remove unnecessary ceil
Fix InputDevice SOURCE_CLASS_BUTTON javadoc
Deprecate Entity teleport
Remove unused variables
Remove unnecessary toString call
don t show chunk toolbar if no container available
removed unused import
Fixed bug in handling of embedded scripts
If we use the result of a function that doesn t return it get a warning
Remove unnecessary error log
Added missing RunWith and Category
Fix NPE in TomcatPlugin
Fix typo in IpFieldMapper
Added class to define the current version of JUnit
Remove unnecessary code
Remove unnecessary null check
add type parameter
Fix typo in Cache initialize javadoc
Tweaked ReaderAnim time
remove System out println
Log address of proxy advertisement
Remove unused variable
Don t remove unused class properties when inlining
SourceFormatter Fix content formatting
request focus to layout tree on cell editing
fixed path editor selection
Fix dex path check on Windows
Fix FolderMessageList sort order
Added start method to Session
Make executableType protected in RiverMongoDBTestAbstract
add getMasterToken to GithubApiUtil
Remove unused code
changed default image compression to jpeg
Fix bug in script intrinsic blur
Fixed bug in PutDesignTest
Add license to sample code
Fix NPE in AbstractMaster constructor
Remove empty line
Add support for memoryClass in ShadowActivityManager
removed unnecessary cast
Fix JarFile constructor
Remove unnecessary code
do not show include context for dumb mode
Fix shutdown logic
Remove minimum size for backend operations pool size
Turn off SPEW by default
Remove unnecessary final
Remove unused attribute
Remove unnecessary null check
added null check for foreign key columns
Add missing javadoc
Fix art icon not being updated on rotation
Fix NPE in TypeDescription
Fix NPE when no schema filters are specified
Fixed the CS error of camel gremlin
Removed outdated TODO
Don t swallow exception
Fix issue with negative duration in duration string
Increase the timeout of FingerprintService cancel
commented out unused code
Fix memory leak in InputMethodManagerService
Use the default encoding for the operations
Fixed the build error of camel jaxb
update xmpp conference after accepting crypto targets
Removed unused constant
Add XmlAttribute to setMessageHistory
removed unused imports
Remove unused onProvidersChanged method
do not highlight empty lines
Fix race condition in ec2RebalanceTest
Fix AppEngineServer startup policy
remove unused imports
Do not swallow InterruptedException
rename set to withDivider
Ignore pending kabir proxy controller test
remove debug output
Remove references to CoreMatchers
reformat groovy files
fixed typo in test name
Ignore transformers discard tests for EAP repositories
read messages when conversation is visible
Change parameter type for setBreakRequest boolean
fixed classloading bug
Synchronize the list of guard levels
Fix AppCompatSpinner documentation
Polyline add setVertices float
turn off optimization optimization
customize wizard step should return null if no footer
Add a NamingContextListener to the server
get latest definitions for aug assignments
Fixed a typo
Fixed a bug where CharEscaper allocates too much memory
Remove deprecated methods
Add touch events for Finger
Force the dismiss button to be the same as move task button
Don t retain fragment onCreate
Disable failing test due to test timing out
Improve documentation of startLockTask
Add Token getDescription to toString
Added private constructor for StringTools
Fixed comparison in PythonSpaceHandler
Add null check
preferred focused component
Added missing onLoad method
Remove unnecessary code
Fix line separators in built ins
Fix oneToOne encoder to send downstream
Fix a bug in ViewFlow scrolling
Set JiveLogHandler s level to ALL
Disable EncoderTest testAztecWriter
add final keyword
Fix quality flaws
don t run new activity
Fixed a crash when sliding tabs
Removed unused method
Fixed local chunk provider
Fix NPE in InFileObjectPersister
temporarily disable delete index test
Removed contains Entry
Fix BCryptPasswordAlgorithm matches
Fix regex for non printable response
Added some JavaDoc
Remove checkpoint files from main
Fix color dialogs
add window state handlers
Allow SIP messages in a reentrant non blocking mode
Fix sizeOf for BitmapLruCache
Add serial version UID
Remove unused imports in StressTestClient
Improve error message
Remove unnecessary await
Add logger to AbstractDiskHttpData
Fix ArrayIndex out of bounds exception
Add userPrincipal to GlassFishRequestUpgradeStrategy
Ignore failing test
Remove obsolete comment
Remove unnecessary private modifier
Removed final from getEnvironment
Fixed typo in MaterialContext
Save step address in step result
Adjust expected error margin
do not write new line
Fix a bug in PartitionManager ack Long
Discard and Join
Remove redundant initializer
add append option to writeToFile
Fix map marker coloring
Remove unnecessary comments
Make LoggingSystemBEAN_NAME public
Fix null check
Fix hillshade layer
Avoid NPE in Frame toString
Don t throw exception in removeSegment
Fix quality flaws
added a method to remove source info
Fixed DefaultJvmPlatform toString
Add params to run command
Fix wrong count check
remove data source
Fix logging in LZFCompressor
Remove unnecessary local variable
revalidate tree after update
Make IntersectionBuilder public
Make Builder final
Fix setSystemAudioVolume bug
Add missing license header
remove extraneous log line
fixed typo in top images list activity
Fix NPE in InputMethodManagerService
Fix condition on springIntegrationPublicMetrics bean
Make StartupFailureException public
Added a sendTypingNotification method
Fix loadMetadataSuceeded default value
Fix bug in nextSlide
reformat should use XmlUtil reformatTagStart
Removed unused field
Add missing imports
Add factory method to StringContainsInOrder
Fix checkstyle error in AuditOwnerCommand
Remove the minimized call button from the call panel
Make LastAnimationListener public
Add tests for PeepholeFoldConstants
Use instantShutdown to keep stream state if possible
Fix typo in DateUtils
remove icon for now
Fix broken Javadoc link
Fixed typo in exception message
Fix APR connection abort handling on Windows
Added missing breaks
enable ignore pos by default
Fix mesos task launch
Fix a couple of types in EpollChannelOption
Set reuseAddress true when creating a new socket
Fixed a bug where MissingPropertyException was not returning the correct exception
Add a method to MediaStore to detect the version
Added DB getStats
Add cause to PartitionAlreadyExistsException ctor
Flush the logs before shutting down the graph database
Add Service provider
Add missing constants to Constants
Fixed glm intercept calculation
Remove unnecessary volatile
Fix crash in model preview
Don t check permission on UsbAccessory
Add constructor with ruleParser
Add null check to BulkRequest
fixed potential NPE
Add support for setting exit number and turn angle
enforce the creation of a new client object for next uploads
Fixed height calculation for SelectBox
Replace Thread sleep with TimeUnitSECONDS sleep
deprecate query method
Fix line length
Remove Nullable from PreDexMerge getSecondaryDexDirectories
Fix a typo
Fix log message
Fix bug in TraitASTTransformation
Fix gestures gesture detection
do not instrument non compiled classes
Log error if the terminal is not visible after a reset
Fix test failures in ConstCheckTest
Added output slot to architect container
Fixed log message
Fix a bug in MethodCanBeStatic
Add check for skeleton before animData
Fix NPE in NewViolationsDecorator
Upgrade the Antennapod database
Improve JavaDoc of ShellStep getEnvironmentVariables
Clear accessibility focus on host after clearing accessibility state
Fix SQLite tests
Fixed potential ConcurrentModificationException
Fix typo in method name
Remove unnecessary close method
render usage view
Fix RevoluteJoint getLocalAnchorB
make PythonProcessHandler public
Fix StompSubProtocolHandler so it doesn t throw an exception
dump testRenameFileWithoutDir hprof
Add missing annotation for Column name
Fix timeline background
don t hide soft keyboard in fullscreen mode
JetNamedDeclaration extends JetDeclaration
Remove unused import
Add LatencyAwarePolicy to CassandraLoadBalancingPolicies
Remove extra newlines
Fix class name extraction for packageless classes
Add XML validation for batch subsystem
actions tree fix
Updated documentation for card emulation
Remove existing mapping from SimpleApplication when the esc key is pressed
fixed failing test
Fix a couple of boxing warnings
Fix regression in ResValueFactory
Ignore test on Windows
Call setupActionBarHandler in updateInfo
Fix a bug in CallResolutionContext
Fix test data
Fix a checkstyle error
Remove AliasFor from AnnotationAttributes javadoc
Added missing license header
Fix HttpClient setProvider
Remove unused imports
Fix tribe validation
Fix unit test
Add test for Props changeActiveProfiles
remove unused error messages
Add toString to Photo
Fixed the CS error of camel jaxb
Fixed issue with leaving room occupants that the user has left the room
Add selector name to the debug message
Fixed the test error of camel spring context
ignore manually validated fields in StructureFieldOrderTest
Fix Javadoc for DelayClientTransport2
Fix checkstyle issues
Add debug module to JSModules
Add toString to HystrixCollapserKey
Fixed the test on slower boxes
add code to javadoc
Move start to where it s used
Using DataBufferObservable to avoid deprecation warning
comment out the stream name
Added a small bit of time to clicker
fixed SceneExplorerProperty import
Suppress serial warning
Fixed failing test
Changed ItemFacade to control name of items
Fixed memory leak in AbstractJetPositionManagerTest tearDown
add target component to maven navbar
Changed main thread to scheduler io
Fixed the CS error of camel test
Added TODO for ordering by nation o_year desc
Add method to clear custom data fields
Remove unused ssid parameter
Removed unused import
Remove EclipseLink from test
Bump version number
Use getTypeWithCoercions instead of getType
ObjectStore throws NPE if tx is not active
Catch IllegalArgumentException when trying to unbind service
Fix mock connection manager
Reduced log level for MongoStorage putNext
Added some logging to remove orphan constraint indexes
git escape text filter
Ignore pinned test
don t init defaultLAF
Handle structural nodes in SiteMap
do not resolve conflicting method if there are more than one conflicts
Remove redundant null check
Remove a System out
Do not print client version if it s not available
perform click after pressing the button
corrected push condition in call action
disable scratch until it is ready
Don t print usage if the user has not specified a help
Fixed a bug where the add activity wouldn t always show up
Fix error message for mismatched column types
add toString to HyperLogType
Improved javadoc for MidiReceiver
Remove unused code
don t show usage view for non navigation nodes
add test for heap store
Added notification opened event
Added missing blocks to Block
MySQL export fix regex
create new file instead of mkdirs
Fix Android tests
Fix typo in javadoc
do not show external annotations dialog in tests
Remove unnecessary code
Change JavaSourceUDFFactory logging from info to debug
Fixed class cast exception
Disable broken tests
Remove unnecessary initialization
do not save empty files
Add getAliases to mod cluster container
Improved error message when MessageOutputFactory cannot be instantiated
comment out failing test
don t invalidate model chart on resize
Remove completed TODOs
Increased test wait time
Removed unnecessary equals check
Set the netd interface rule tolo
Skip tests that have not yet released this
Removing unused imports
remove unnecessary check
Add user agent to HLS manifest fetcher
Make PrimitiveTypes TYPES_DEFAULT_VALUES private
Add support for Oracle source type
Remove redundant parameter
Print more trace to console
Removed empty lines
remove unnecessary brackets
deprecate unused method
Fix potential deadlock in EndpointPublisherImpl
Fixed the CS error of camel core
Fix NPE in ObjectHelper useDelimiter
Add a note to WebSettings setAllowFileAccessFromFileURLs
Fixed TextField text offset
Add intent extra for checking if an app is a system app for apps that have one or more runtime permissions
Pass the correct length to the Util pump method
Build a new task stack when applying new theme
Fix NPE in LockScreenActivity
Fixed NPE in FileDisplayActivity
Add contact DEMOTED and UNDEMOTE
add null check for id in save
added NotNull annotation to PredPrediction
fixed the snitch datacenter
Quote attribute names
fixed backspace handling for quotes
added convenience methods for logging
Fix broken Javadoc links in StreamCharBuffer
Fix typo in javadoc
don t store selection in hint mode
Prevent the query and prompt being echoed back to terminal
Disable route points plugin
Add a test for pure voodoo
Fix SimpleConsoleEventBusListener NPE
Fix Danmakus comparator
Do nothing if a Filter was invalid due to not being scoped properly
Remove doc_src references
do not add task to worker if already disposed
added check for empty arrays
Fix merge conflict
Use common empty list to save memory
Don t start the dead window for real apps
Don t update USB notifications when charging is off
DefaultTypeConverter should only log WARN level for internal Camel convertions
use ConcurrentLinkedQueue instead of CopyOnWriteArraySet
do not compact bloom rows
Getopt now copies the argument array
Fix quality flaws in StorageBasedAuthorizationProvider
Fix buffer leak in TestTomcat
Copy backup agent name from ApplicationInfo
Increased wait time in ExchangeOperator
increased undo delay
Fix a couple of typos
Fix off by one error
add some comments
Make MemoryPoolInfo final
add at least a constructor with a ResourceBundle parameter
Release the request before handshaking
Added updateMediaLocalToRemoteId method
Add a flushOnNext flag
Added missing import
commented out equinox version
Avoid ArrayIndexOutOfBoundsException in searchLocalMessages
Fixes a bug where the display details was not updated properly
Fixed a typo inSortsTiming
Fixed problem with stubs in PyCallExpressionHelper
Add support for comment PN types
Replace ExtensionPoint TypeExtensionPoint with ExtensionPoint SelectedType
Removed a System out
Fixed bug on file mmap close
Add a constructor with coordinates
Add alarm handler to canal instance
throw exception if default ctor is not found
Fix audio notes active menu
Fix NPE in ActivityStack
AddDIV to magic numbers
Change the DICT_PATH to use IOUtils
fixed missing import
Removed unused imports
remove return true for now
Add setTexturePack String
Add missing Apache license header
Fix listview positioning logic
increase test timeout
Fixed a typo in FormSourceCodeGenerator
Disable login requirement for AllowAllAuthenticator
Fix typo in predict csv usage
fixed small bug in GL smooth
Added Player setListName and getListName
Reduce log level for task cache key generation
Fix Eclipse warnings
Fix NPE in UDP connector disconnect
Added error check to ConstrainedPropertyTests to verify that the error includes the correct error code
Don t allow backdrop frames to be passed to ViewRoot
Initialize the layoutAnimator with a default instance
Add WebSocketMessage echoPongMessage
Don t remove the failed route from the pool
Don t show a readme if the head is null
don t change account priority for dnd accounts
Align preferences frame group with the header
Added a comment
Add layout to BaseRoundCornerProgressBar
Fix checkstyle error
don t paint text border on graphite
Fixed bug in combined textures
AddComparisonResultType NON_EXISTENT
do not generate classpath index if already did something
Add missing import
do not optimize files for fast word search
Removed unnecessary clone
Fix PluginServlet to use the correct plugin name
Fixed unit test
Normalize caret offset
Add method to remove child views from FakeView
Fix checkstyle issues
Remove unused method
git Don t log error in unit test mode if the project is disposed
Fix AAR project dependency generation
don t add separator actions to toolbars
fixed NPE in validateTx
Fix TextView not working
add toJavaObject Class
Fix a typo in the jersey client name
remove redundant field
Addelytron XML validation test
log Don t send a refresh request if there are no roots to refresh
eliminate eclipse warnings
Remove unused field
Remove redundant modifiers
allow lenient policy for detecting non conforming subject change requests
fixed bug on updating record
Added max parallel replications to partition lost listener test
Remove unnecessary generics
disable save changes dialog on Mac
Fix typo in Javadoc
Fix an issue where UpdateLockService wouldn t start up immediately after boot
Fix some quality flaws
removed unnecessary logging
file status manager add NotNull
Fix generics warnings in Futures
push Don t show push active error if there are no commits
add setAirplaneMode and setWifiOn
Disable the test for now
remove debug print
remove configTopologyWorkers metric
Parse timestamp in ISODateTimeFormat
added closeSession method
Fix NPE in XIpPrefix
Improved error message if index name is missing
Remove unused code
added missing push in ConcatExprToken
Fixed test on slower boxes
Add a singleton for the missing file snapshot
Fixed issue on SELECT with multiple indexes
Remove use of System identityHashCode
call mergeInit before serial merge
skip invalid elements
update using setId on a template
disable popup menu for history invoker
Fix dialpad window size
Improve FieldMap documentation
Fix bitmap cache bug
remove legacy way of specifying TX manager
Fixed a test failure on some devices
remove debug output
Fix EIP selection
Add call to Lookup refreshDefault
Fixed unit test
Ignore ShadowDateUtilsTest for a moment
Add a javadoc
Fix NPE inRestrictedLockUtils
Removed Id keyword
Fix quality flaws
Fix small issue
Set parent bounds in ExploreByTouchHelper
Missing break in TwoDimTable
Add FALLBACK query parameter to FeedMedia
update parameter info only if it is visible
Remove commented out code
Fix NPE in OAuth2Authorization toString
Make AudioRecord native_release public
Add aliases to CommandExecuteAtSender
Set the live region of the accessibility info
Deprecated MongoInterruptedException constructors
Set log level to TRACE
Added support for processInstanceBusinessKeyLike parameter
fixed bug inConsistencyCheck
Remove dependency on ArrayUtils
Call stop instead of throw in MediaMuxer release
Harness Harness onPause should gain and close focus on pause
Added missing dot
Remove obsolete TODO
Fix memory leak in ViewfinderView
Remove a temporary hack to delete preds
Fix test after renaming
de add examples
Add an initWithoutPassword method
Fix ArrayIndexOutOfBoundsException in ViewDragHelper
Fix NPE in setMember
add final to EP_NAME
Use the encryptionOptions require_client_auth option
Removed unused variable
Add some docs to HeaderViewListener
Added saddle methods to Pig
Remove unnecessary code
fixed NPE in TryWithIdenticalCatchesInspection
Add ro z as container container target
Fix restricted check
Don t add a number to the input array if it s zero
Remove unused code
Changed the log level to severe in the test
Added serial version numbers to MpscLinkedQueue
Improve error reporting in IndexLookupTests
Add more documentation to DataFormat
Change checkbox text
Added tagScreen to the statistics service
Fix stop order in StreamResequencer
remove sleep from rebuildIndexRangesJob
removed unnecessary error message
Remove unused imports
remove listener from project open
Increase the default pool size
Fix line chart example
Improve AbstractModule javadoc
fixed possible NPE
added missing quote
Don t attachunctor for empty WebView classes
Don t allow the background task to be canceled
Fix typo in SSLConfig
Added method to check if a task is currently running
Improved test coverage
fixed bug in constantType
Fix NPE in access transformer
Add new TV shows
delete character from selection
improve disambiguation rules
Add NOW and WRITE to EventSubType
Fix flaky testBulkProcessorConcurrentRequests
Fix StatusCodes size calculation
Added missing license header
Remove unused import
Added service type to SSL
Fix NEDDownloader bug
Fix AbstractInstrumentedFilter setStatus
Fixed a bug where DefaultFuture isSuccess returns a wrong value
Removed unused imports
createMap should return a THashMap
Rename ro monkey to ro monkey
use ICS instead of HONEYCOMB
Add javadoc for matchesInterruptionFilter
made getStarter protected
changed axis formatter to IAxisValueFormatter
Improved handling of InterruptedException
Fix compilation error
added test for delete with response
Added support for JSONArray
disable dragNDrop by default
Add turnReluctance to RoutingRequest
Remove unused parameter
Hide a useful error which is produced when a line ends with a identifier without a semicolon
Changed log level to debug
Throw IAE in GeometryUtils polygonCentroid when there are no points
Make Printer public
Fix help page width
Improve error message
fix setAnimatedItem bug
Disassociate ImageView from any pending downloads
Fix OrcInputFormat isBlocking
Improve performance test timing
Fix ingame state binding not being cleared
Fix a couple of typos
Added check for system users in JdbcConfigService
Add missing javadoc
add more debug info
Removed unnecessary null check
Add go server dashboard starting on localhost
Fixed bug in SimpleExprGenerator
Fix AuthenticationITest to use new RequestSpecBuilder
Fixed JmePaletteSunLight to use normalizeLocal instead of normalize
Rename ingestNode to isIngestNode
Add report directory to log message
dispose NativeLibrary in test
Fix concurrency issue
Remove unnecessary ensureUDFContext
Add status code and reason to log when failed to login
Remove deprecated TextUtils join
Fixed a bug where the listview wasn t scrolling properly
Fixed local library dependency resolution
deprecate unused method
Fixed a typo
Removed testMustFail from TaskServiceTest
Remove setModal from CallDialog
Add Job getScheduledAt
Add missing source files to dependency bank
Rename restapi execution handler executor service
Added a method to create a keystore for a given keystoreParameters
disable gzip by default
Add a default for Ctags
Removed deprecated methods
Fix copy packaging
Fix broken stub
Fixed a bug where InProcessGradleExecuter does not stop the GradleLauncher
Remove sleep from ObtainHipsters
Only append the signature query parameter when using the standard identity
Make GradleBuild interface extend org gradle tooling model Model
Changed default display mode
Fix forkPerModule for JUnitTestDiscoveryConfiguration
Fix active thread count error type
Java Added documentation for BufferPositionReporter
Added comment to ReaderUpdateService
Remove unnecessary transient modifier
add error message if a node is not in the compilation unit
Turn off verbose logging in NodeEnvironment
Fixed issue on server startup
Fix int int
fixed typed handler
Avoid NPE when bound value is null
Added missing import
Added SpawnAction hasOutputs
Add documentation to Tag getTechnologyList
Remove references to AE_SERVICE
Ignore BadTokenException in showWindow
Fix a warning in AbstractUnsafe
ignored failing test
remove unused code
fixed isPackage check
don t cache it
Don t show update button for incompatible versions
add title to assertion
Fix servo metrics check
changed load level to DEBUG
Fix typo in new violations decorator
Don t activate the test window on exit
Fix bug in inspector
Fixed intent package
Temporarily ignore FileDownloadITest
Added a comment
Commented out failing test
EditTextUtils getText takes a TextView not a EditText
Fixed memory leak in VertexBufferObjectManager
Remove unnecessary try catch in PathTypeCoercer
Fixed typo in ItemID
don t stop executing methods for variables
Make JUnitAmbiguousTestClass a mATURE warning
Fix NPE in FileMonitorTest tearDown
remove debug log
Add a comment
Fixed a bug in switchToBubbleView
Add a constructor that takes a ClassLoader
Only reset InputStream if it is not supported
Fix typo in javadoc
Fix RajawaliRenderer to clone the projection matrix
Fix panel animator
add completion manager on paste
Fix type conversion test
Don t show toast if we can t read whitelist
Add getApplicationContext to MockContext2
ScrollPane without border set to empty border
Fixed a deadlock in SchedulerIsolatedTasksTest
Remove unused variable
Fix resource leak in test
don t load GroovyReflectorArray
do not apply effective conjuncts to non deterministic expressions
fix failing test
removed nth for Indexed
Fix checkstyle error
Remove static modifier from private fields
Fix MessageWebView background color
PostgreSQL does not support auto increment columns
Improve the documentation of AlarmManager
AbstractBeanDefinition has a default singleton value
Add getReference and getReferences from PsiReferenceService
add space between groups
Handle crash in ContentView onPageStarted
Incorporated PR comments
Remove unused methods
Keep screen on when launching videoplayer
don t resize ImagePreviewer if it was collapsed
Handle R identifier characters
Added missing return statement
Don t use Locale US as default locale
disable restart threshold by default
HideGuideOverlay now shows up the guide overlay
Fix BitmapDocIdIterator next
do not use deprecated methods
Fix bug in AsyncContextImpl setTimeout
Removed unused method
Implement isNamed and getActualName in JavaModule
Remove Google Talk from error message
add comment to clarify why we capture the base store directory
Fix NPE in GradientDrawable clone
Fix javadoc errors
Added test for expression lists02a
make test public
Fix typo in javadoc
Added Image position
Replace com sun xml internal ws util StringUtils StringUtils with org apache commons lang StringUtils
corrected usage message
Simplify the loop
Remove the QuantilesPage from the menu
Fixed bug in template rendering
Fixed type parsing for class like types
Renamed test class
Fix deobfuscating remapper
use errorType code not ordinal
Add a comment
Remove unused compiler options
Use buffered output stream in DiskBasedCache
Remove extra space in log message
Moving the PolymerPass enforced in the assertValidOrder method
Fix variable name in test
Allow dots in timestamp tags
make update all button slow down
Improve error message
Fix potential NPE
Cache invalidation message batch enabled
Remove unnecessary EnvironmentConfig creation
Remove unused method
remove unused variables
Fix double tap scale
Add missing javadoc
Fix typo in javadoc
Add test for getFqdnHost
Added missing Override annotation
Remove unused code
Fix NPE in NetworkCatalogTree
Fix javadoc formatting
do not show expert placeholder for template module builder
Fix NPE in BranchedUnfoldingUtils
Improve error message
synchronize makeThreadLocalLoopers and myLooper
Remove unnecessary parens
Improve Drawable newDrawable documentation
do not schedule rediff if already disposed
Fix SubscriptionName binding
add replaceResolveMode to CallCandidateResolutionContext
git Fix GitFetcher fetch
Fix typo in comment
create config table if not exists
Make date parsing functions public
Do not lower case category name
Fix NPE in ParseDataset2 close
Fix NPE in ViewRootImpl
Remove debug code
Remove unused code
Use Global instead of Secure
add isAcceptable to PsiFunctionalExpression
Added TypedProperties getStringInfo
Use US locale for DateCache
don t delete unused return statements
avoid NPE in MockVirtualFile
Add a comment
Catch IllegalArgumentException in HackerNewsClient getType
Fix broken link
Use BuildNumber fromString instead of BuildNumber fallback
Remove unnecessary code
removed unused imports
Added test for quantified predicates
Remove some debug code
Fix exec output streams for Windows
Use the getGlobalVisibleRect to get the intersection between the parents
Add missing comma
Don t throw an error if a node doesn t exist
Removed unused import
add getPropertyDescriptorForValue method
Remove unused tests
add TODO for check for label TTL
fixed bug on traverse
Fix FaceUnlockView measurement
Fixed a bug where Music stop was not called when prepared is false
fixed a typo
Fix bug in layout calculation
Fix NPE in LinearLayout allViewsAreGoneBefore
Remove stack trace from AnalystWorker
Fix UploadActivity to display home as up
Added HTTP_BASE_URI constant
Increase backup test timeout
deprecate test case
Fixed ClassCastException in OSQLMethodFormat
Remove outdated documentation
Fixed a bug where the notification was not expandable
update project structure on module state change
Remove unnecessary StringBuilder allocation
Add PlANNED vehicle to Update enum
Make constant final
don t set locale for Mac OS
overwrite DLModel with DLModel
Throw IAE instead of RuntimeException in any case
Fixing a failing test
prevent weights exploding too far in either direction
Remove unused messages from PreferenceActivity onDestroy
Rename registerConfigBeans to addConfigBeans
Removed unnecessary code
Added default constructor to Camera
en GB not en US
fixed checkstyle issues
Remove indeterminate progress bar
Remove unused code
Fix checkstyle issues
Fix XMPP server initialization
correctly determine image info based on file path
Remove OTP VMs
Removed unnecessary return value
Fix the codegen annotating visitor
Copy executor when cloning CallOptions
Fix JarFile leak in WAR packaging tests
fixed possible NPE
fix equals for java lang double
Disable test that is flaky
Valve should implement AccessLog
Dismiss the API around the availability change
Remove unnecessary increment of mEnqueueCount
Make accessibility info available to views
Remove unnecessary parentheses
delete traces directory if it doesn t exist
Ignore WebSocketOverSSLTest for now
Renamed destroyDirectByteBuffer to destroyByteBuffer
Fixed a bug where the server could get stuck in the wrong state
Fix TextRange assertProperRange
Fix unit test
Remove unnecessary code
Rename class loader service to TcclLookupPrecedence
Fix quality flaws
Add getSlices to EdgeDraftGetter
Only request the post if we have valid WP Com credentials
Added water blocks to canPassThrough
Add timestampProvider to consistent key locking
Fixed a bug where the title was not being set correctly
Fix pokemon powerup logic
Fix Javadoc warnings
Fix AnnotationUtils to work with all interfaces
MockTransportService now returns this
Throw an exception if a folder with the same name already exists
CurlMesh now uses alpha blending
Throw FileNotFoundException if no artwork image is set
Fix seed int to long conversion
Fix rotation four demo
Add some extra space to the video frame rate checkbox
Fixed a bug in BlockData incrementing the data
Fix description of TagVLiteralOrFilter
Remove unnecessary code
Fix a checkstyle issue
Fix binary expression parsing
Remove unnecessary parens
Remove unused imports
Don t show toast if empty string
Add option to enable turn restrictions
Don t show lights whenLedOnMs is off
Fix NPE in AbstractEndPoint toString
Remove unused parameter
don t dump open queries
Fixed javadoc param name
Allow subclasses to add their own granted authorities
remove unnecessary trim
Clarified javadoc for addTexture
Remove unnecessary code
Disable KotlinInternalModeToggleAction in tests
Deprecate unused method
Removed outdated comment
Added a flag to disable locals
Remove unused method
fixed NPE in XBooleanFilter
remove log spam
Updated SimpleThreadScope javadoc
Fix AnimControl setSpatial
Fix NPE in RoutingHelper
remove java awt
Analyze stack trace
Fix bitmap scaling in ASingleTexture
Fix a bug where the daemon hangs the client
update CompletionContributor javadoc
Ignore interface down for WiFi
Fix NPE in HalideLibrary requireBuildRule
Add sleep to LoadTestsAutoTest
Rename loadDeviceOwner to loadOwners
don t return null
Fix NPE in UpdateIndexFragment
increased network lock timeout
Added main method to SimpleTest
disable attach detector in tests and headless environment
Include fragment ID and tag in error message
Make the failure detector thread safe
Add unittest to PaymentParams
Make StringCleaning thread safe
Fix typo in audio context
Make constants private
Fix broken link in javadoc
git Fix GitCloneFunction to clone NetRCCredentialsProvider
commented out unused code
Remove obsolete TODO
Fix typo in targets command help
addBitmapToCache now accepts compressFormat
Handle back button in StatsDetailsActivity
Fixed a bug where the sort was always ascending
Fixed Pattern constructor
Removed unnecessary cast
Fixing the build
Added toBytes and fromBytes methods to ComplexMetricSerde
Don t exclude zip as well as application zip
Fix license header
Move Show Outline MenuItem to the bottom of the debug menu
Add missing imports
Improve CookieProcessor javadoc
Added locale to graph export
changed Pre Alpha to Alpha
Fix expiration time check
Remove unnecessary test timeouts
Fix test on Windows
Updated documentation for ApiOperation responseContainer
Exclude buck out files from blacklisting
remove unused code
Remove unused method
Stop the animation drawable when the image is resized
Added uvIndex to humidity
Fix equals for DefaultTextHttpMessageLocation
Fix quality flaw
added license header
Fix TestApproximateCountDoubleAggregation to return Double instead of BIGINT
fixed failing test
remove unused test method
Add missing Override annotation
improve error message
Pull current metrics from real Resources object
Remove empty constructor
Fixed exception message
Copy duration when cloning TransitionSet
remove obsolete comment
added test for complex boolean
Renamed parameter url to feedUrl
Remove trailing whitespace
Commit the partition on close
Prevent accessibility events from passed down to children
do not add gwt xml to core project
Fix BackupManager requestBackup NPE
Change CoreMessageLogger log level to DEBUG
Fix bug in RecentsTaskLoader
Add timeout for connection timeout
Add a list of frameworks to AppCompiler output
fixed bug in ASeq remove
Skip templates in the output
Fix checkstyle issue
Fix Animation setInterpolator
Fix javadoc for ValidationMetrics
android update WXEnvironment
Fix a bug in RenderTaskListGenerator
add test for getObjectInstance
added docs module
don t show manage button on mac
replaced System out println withLn d
Add a isEmpty method to BroadcastDispatch
hide move parameter action if it is not available
Make DarculaSpinnerUI LayoutManagerDelegate public
Remove unused methods from TextBodyBuilder
Fix NPE in ViewPropertyAnimator
Updated versions for release
Fixed World represent map
Removed a System out
Don t stop TestWithPeerGroup unless it s running
Fix android attribute remapping
enable geolocation by default
Fix ack timeout
AddCurved preset to default preset
Fix quality flaws
add stateful bolt support to FluxBuilder
Stop RecyclerView when no layout is available
help ids for codestyle
don t delete last parameter if there are no parameters
add missing license header
Fix typo in javadoc
add optDouble to JsonData
Fix repository name panel when project path is empty
don t trim the message body
Fix LocalType check cast
Remove unused code
Add autoTested to JavaCompile javadoc
Removed unused imports
Don t process module roots
Fix NPE in onLayoutRequested
remove timeout from notification background callback
Add missing setters to CharacterEncodingFilter
Add NullType to TypeRegistry
Removed redundant modifier
add html template to search profile
Make constructor public
commit all documents before saving file
fix failing test
add TimedTransferEdge to graph
Fix NPE in CommandLineMain
Remove unused variable
correctly escape one line at the end of file
Remove BoundingVolume from TempVars
reducing coercions for join expressions
Remove old NeuralNet tests
add utility method to disable structure update
Implement CacheRecordStore isEvictionRequired
rename redirect_to to redirect_url
get manager for delegate psi elements
remove unused imports
tweak the wording of the error message
Use isAssignableFrom to compare class types
Fixed sharing bug
Make TargetAndConfiguration an immutable class
Add missing Test annotation
Add some missing code
Add disambiguate properties test for prototype and instance2
Remove unused onIndexService method
Fix checkstyle issues
Don t show recent details for external activities
Fixed Timer resume
Fix battery stats history reading
Fix NPE in notifications list fragment
Fix variable name
Fix summary notification content intent
Remove debug output
FixUidReverseComparator to handle long messages
Updated javadoc comments
Fixed unit test
Fixed Informix support
Add missing resolve call
Add support for binary type in SQL dialect
remove unused constants
Fix typo in javadoc
Create a StandardServer if no server was defined
Fix SubjectDnX509PrincipalExtractor javadoc
Don t swallow exceptions in DistributedQueryRunner
Add test for Join_With
Rollback annotation should use annotation type constant
Fix a bug in RemoteBlockInStream
Moving the PolymerPass before the transpilation step
Improve toString for CoordinationPattern
Add a deprecated setVisibility method to ObjectMapper
add custom previewer for our special hooks
Fix a typo in NodeEnvironment
Fixed exception when aggregation collection is missing
sync engine handle deleted files correctly
create field from usage quickfix
Fixed unit test
add windowSize parameter to new window constructor
Added a log message when a request fails
Remove TODOs from coercion tests
Remove unnecessary code
Fixed NPE on plugin lookup
Fixed the CS error of CxfPayloadConverter
Don t install encryption unaware providers
Add dir method to DefaultFlatDirArtifactRepository
don t call noLoop on obj
Clarify sensor delay constants
improve toString performance
Add LoadController errorOccurred
Fix model repository to parse files correctly
TEST Fix SharedClusterSnapshotRestoreTests
Fix duration formating
Fix broken injection test
Fix infinite loop in FileInStream
do not swallow exceptions
Removed unused variable
Fixed javadoc warnings
Handle line feed events
Improve error message for table not found
Fix typo in MockMaker javadoc
Fix QueueConnectionFactory name
Fix creation of directory in TemporaryJobJsonReports
Removed unused method
Fix quality flaws
Remove unnecessary required parameter
Fix NPE in ResultSetViewer
Move assignment to where it s needed
fixed test case
use ExpressionUtils isNullLiteral
check canceled status
Fix a typo in the help text
Fix typo in transit graph builder
add license header
Fix link to wireless Bluetooth
Add null check to sendData
Remove empty line
Revert Revert Re enable TAP_OUTSIDE_STACK
Reduced log level for no registration found for serviceName
Added theme image size retrieval
Check the app id when checking if it is running under the system uid
Fix logging level
TEST Use correct logger name in BackgroundIndexer
Enum rule should make constants final
Fix logging statement
Add module conflict manager to module descriptor converter
Remove unnecessary Javadoc
add ConversionContext to GroovyConverterProvider
Log at info
Remove extra space
disable thread dump action if debugger session is not attached
Add a comment to the bootstrapper class
Reset resend count when a node dies
Remove unnecessary logging
Suppress unchecked warning in IsIterableContainingInAnyOrderTest
Remove stupidSync function
Added changeGroupAvatar to AndroidMessenger
Fix NPE in KeywordSubstitutionWrapper
Don t get the URI directly from the cache
Deprecated useBrowserHistory property
Fix IndexRemoveJob memory leak
Don t show home visibility for activities that are not visible
Fix signature of setPackagesForCallingUid String
Fixed javadoc formatting
Added error state to canDismiss
Fixed package info
Fix bug in PreloaderBundleGenerator
skip similarity and rename lines
Change default nthreads to Runtime availableProcessors
Fix NPE in BukkitEntity getState
Fixes a bug where show favorites wouldn t be saved
don t delete new lines
add tools jar to groovy classpath
Added CDDL header
Fix a typo in a DEAD message
Adds a property to enable auto join by default
Fixed limit in Vector3
Don t show the control panel in the Visual Panel if it s not present
Add onClickItem for route_id
Added debug logging for invalidating MongoSessions
Added getColumnType to PostgresDatabase
don t add extra whitespace before the sentence
Added OS name property to EnvironmentUtil
Close the msgLog in shutdown
missing quote in CheckoutWithRebaseAction
fix a bug in workerstorage
Disable system property transformer test for now
AddCACHED to PlanJSONDumpGenerator
remove useless comment
Added more logging
Add Retention to ContentRecommendation
Added getQuery and setQuery to GeoQuery
Fix CometProcessor issue
Check number of tokens
Fix test failure
Add tests for reserved word variables
Remove unused method
remove unused code
SMALLFIX Fix formatting
Added putAll to ShadowBundle
Remove unnecessary code
Revert MultiLayerNetwork setParameters
remove unused interface
Removed unnecessary code
add setMaxWalkDistance to RoutingRequest
Fix context names for relative paths
Fix post upload error notification text
Changed the name of the register to be the same as the server name
Fixed bug on loading of records with ORecordSchemaAware
Fix serialization id
Handle long press in BottomBar
Removedaste suffix
add elapsed time to console output
enable web debugging
Fixed typo in ReadTimeoutTimerTask
don t call PyCallExpressionHelper for modules
Don t overwrite NO_CONTENT_CUSTOM_DATE message
Make MIME class public
do not dispose built in server on shutdown
Fix error message for symbol conflict
Fix grid layout for vertical layout
Fixed a bug in OSProcessHandler
Fixed the CS errors
Don t destroy contextual mode if not the active one
Increased secret size
Add documentation to ServletContainerInitializer
fixed overScrollBy not getting the current mode
RSV editor fix
Expand bubble flow on up
Remove unnecessary code
Fix comment typo
remove unused method
Remove unnecessary cast
fixed checkstyle issues
avoid potential NPE
Fixed broken test
Clarify Geolocation API requirements
do not visit class with same name twice
Added setNames method to BatchProperties
Adds support for low limit record formats
add ContentPackLoaderPeriodical to Binder
remove feedback on cancel
Fix test failure
Improve startup timeout
added error message
Add HDMI rotation setting
Fix ignored files disjoint
get last committed stamp for windows
Fixed bug in ZKClient where committedTx was not set
removed unused import
Fixed Geometry clone
fix size of icon
Fix Bind constructor
Fix typo in WindowManager javadoc
Replace unsafe call with ExceptionUtils neverakyThrow
removed unnecessary local variable
Removed incorrect javadoc
Add startsWithIgnoredWord to SpellingCheckRule
DnsNameResolverContext operationComplete should check future isCancelled
Fix refresh icon
Simplify List partition predicate
added containsEnvKeySubstitution method
Ignore command with incorrect key
Add missing includeZero annotations
Exclude nobs from metrics
Added WPStats tracking for blog viewing admin
Improve error message in RowSeq canTimeDeltaFit
Added copyWithZone support to StatementGenerator
Return the result of dispatchGenericMotionEventInternal
Fix penalties calculation
Added two provider to the list of supported accounts
Removed unnecessary javadoc
Fixed memory leak in MemoryAwareThreadPoolExecutor
Make NamespaceContext public
Remove unnecessary lock
Fixed typo in comment
Delete the image file if decoding fails
Remove deprecated constructor
Fix NPE in HistoryGuru
fixed CS error
tweak server home page name
Add shards to the list of indexes if they are already in the list
make the icon text aligned
Fix test data
skip empty fields
Fix JsonFactory readResolve
Fix typo in javadoc
Fix ProgressBar animation
don t process builtin types
remove extra getDataProvider call
Changed editstack to editstackair
Make manageClosureDependencies a member of CompilerOptions
simplify the test
Fix SIP contact creation
change default color to Source Code Pro
Remove unused imports
Fixed NPE in ExpressionState
Close the database connection after reading the sqlite version
Fix javadoc for KEY_PROMPT_TEXT
clear initial prefix on backspace lookup
Add deprecated method setAsynchronousCallbacks
fixed CS error
Fix bug in recovery of execution graph
fixed NPE in ReferenceProvidersRegistry getProviderByType
Fixed start command to work with quotes
Do not set part background for disposed sash forms
Fixed issue on serialization of embedded collections
Move idGeneratorFactory switch to master
Fix NPE in Log flushStdout
don t allow SearchEverywhereAction to run in EDT
Remove deprecated code
log remove unused code
Commented out debug print
Fixed test cases
don t reindent empty lines
Remove unused import
Remove unnecessary parens
Do not hide toolSTRIPEs by default
Fix a broken build
fixed compilation problem
Add new analytics entry point for stats selection
Fix test condition
Remove unnecessary catch block
fixed folding outline visibility
Fix NPE in ClassGenerator
Add bike switch options to PlainStreetEdge
Fixed bug on iterator next
Remove unnecessary code
use Integer MAX_VALUE
Clear last locations when a provider is disabled
Add force build flag to target scope
Fix drawer state
add constructor that takes ClassFilter
Add a compound assignment test for PeepholeCollectPropertyAssignments
removed obsolete TODO
Fix TezJobMonitor to handle stderr output file
Fix crash in AppCompatDelegateImplV9
Don t truncate hosts collection
Remove unnecessary cast
Fixed LwjglRenderer initialization
Add a debug statement
Add missing javadoc
Add missing imports
Remove unused fields
Fix overflow in top N
Stop the StreamingLogInput JSSC when done
add container name to logging
Change the default dict path
Don t swallow the error when verifying various URLs
fixed DelegatingMetaClass to delegate the class not the class
Fix Javadoc for EmptyBlock
add TODO for registerRedirectingSocketStores
remove unnecessary field
Fix build path handling on Mac
Fix NPE in AsynchronousProcessor
Remove unnecessary if statement
Remove unnecessary argument
Fix exception message
Remove the content length header from the response
Fix HgFile buildRelativePath
Fix compiler warnings
Add import for titan core util
Added getter for mIsPlaying
Remove unused import
fixed TextField key handling on Mac
make code style scroll vertically
Make HeliosSoloIT s Solo rule a ClassRule
Add option to disable param p debug info
rename TransferTask to TransferThread
Make toSource public
emit partition messages using the right stream id
improve comments in ColumnFamilyStore
don t use background for tabs
Added OnCheckedChangeListener to FakeCompoundButton
Remove reference to WakeLock
Add another trust manager test
Added close to SpillingResettableIterator
Make class static
do not throw exception on non existent external storage
Added test for newCondition
preserve order of roots
Fix javadocs for ParameterParser
don t show empty color settings page
Improve error reporting
Fix javadoc for setLibrary
Remove unnecessary final keywords
Add toString to DefaultStructBindings
Print the host configuration in the output
Fixed bug on DNS reloading
Fix toString forJobExecutionContext
Improve InjectExtra javadoc
Fixed the CS error of camel spring
suppress unused warning
Fix NPE in ForwardingListener onDetachedFromWindow
Fix LayoutControllerImpl cancel
Fix friends progress bar
Remove extra newline
Fixed a bug in MappedRawLog
add warning message
fixed bug in shallow comparator
activate VCS before test run
add query params to collate query request
Remove Nullable from queryPluginMetadata
Don t compare plugins with their own version if they re the same
Fix typo in ExpectedExceptionTest
avoid creation of new mode if it does not exist
Removed unused field
Fix NPE in DecorView
Turn off debug for now
Trim object names
Fix NPE in CamelFunctionNameSanitizer
add macintosh shortcut
Ignore IllegalStateException in HttpSessionIntegrationFilter
Hide new posts bar when recycler view is scrolled
Fix since tags
Fix quality flaws
Fix typo in DefaultChannelPipeline
Add clone to Point
Remove unnecessary setChoreographerCallback
Tweak tURN distance
Hide unread items group if empty
rename compiler options txt to inputs txt
fixed live wallpaper resume
Log connection refused warning
Trim the description of the route
Throw correct exception when table does not exist
Fix a couple of typos
Fix SeekBar spacing calculation
Revert Don t perform execution phase for non nobuild builds
Added maven skipTests parameter
Fix NPE in IndexerCoordinatorNode
Fix NPE in tabs tag
Remove unused code
remove unnecessary method
disable JNA test in headless mode
Log MongoConnection connect errors
Ignore back slashes in UnicodeUtils
Fix RAMDataAccess capacity
Change getuserblogs endpoint from http to https
structural replace triggers usage
Fix LongMath ceilingPowerOfTwo
Add Locker acquireLock to WatchRunner
suppress JBColor warning
Don t perform list view perform click if adapter is disabled
Reset retry count on data connection creation
changed cascade to save update
Added isSameItem to InventoryUtils
Fix bug in ScoreBuildHistogram2
Fix NPE in ToolbarButton when left image is null
Add a new setting for using location information
Fixed generics warnings
Added ShadowCookieSyncManager reset
Added ENOTSOCK error code
Add note about FileDownloadQueueSet usage
fixed Maven dependency reference provider
Remove unnecessary check
Fix missing ASF license header
Logparsity of C4S chunks
Add toString to TypeCompatibilityModelProjectionSupport
Push remote task transitions to the correct state
Temporarily ignore SnapshotDataDaoTest
Add option to set default tick tuple interval
Fix NPE in ScrimController
use focusButton for existing project dir page
fixed possible NPE
Parse userId from token
Remove unnecessary code
Added support for SocketChannel
Renamed mustRetry to mayRetry
Fix a bug in theAstyanaxOrderedKeyColumnValueStore
Fix typo in disable2
Add PP to the list of allowed pcomp parents
Fixed the CS error of camel api
expire notification in EDT
fixed numbers negate
Fix selfi uri
Fixed cookie name parsing
Fix NPE in AndroidRuleClasses
Added shutdownTime and shutdownUnit to ScheduledExecutorServiceBuilder
remove outdated comment
only log flushed messages if debug is enabled
Fix misspelling of PexStep
Added test code
Fix NPE in FsTranslog
Add missing conditions to JNDI auto configuration
Add TargetApi annotation to onResume
Added some comments
Fix checkstyle issues
Java Removed unused code
Remove unused code
remove unused method
Fixed import of HintedHandoff
Add displayDistanceDirection to amenity menu
android update WXSDKInstance java
make ContainerState public
Remove unnecessary checks for NaN values
Changed signature of setStreamVersion from byte to int
Fix javadoc for TreeTraversalInSpiralOrder
fixed sample app
rename mediaCommandBuffer to processMediaCommandBuffer
Add ES6 arrow function test
Fix bug in MarkableInputStream
make applyTripUpdates atomically
Fixed equals for ReaderTag
android update WXSDKInstance java
Add VIEWERS to PeopleListFragment
Log number of connections in connect log
XML editor init should use empty string input
Improve error message for invalid tab separated lines
Fix typo in warning display
Fixed the javadoc error of GlobalOptionDefinition
Updated SRTM URL
Removed unused import
fixed course creator path
Make IQPEPHandler getPEPService public
removed debug output
Improved Jerry replaceWith Javadoc
Add change log to diff command line
don t destroy process recursively
Fix javadoc for ClusterException
Fix NPE in DomainNameMapping
Remove unused methods
Make ImmutableTableTest non emulated
added shape_dist_traveled to stop time
Remove extra blank line
Removing elevation from the blog settings action bar
Add a comment
groovy find usages for closures
correctly set background color
Remove unnecessary semi colon
Refactored BaseInteractionScreen getInteractionTarget
Remove unnecessary suppression
Added MapEventPublisher support
Fix NPE in ChannelImpl
Add HttpSessionIdListener to listener list
Only save single Data Driven Node in a context
Do not show selector for focused view
Added SuppressLint NewApi
Removed undoing of frames in Exec2
Fix NPE in BitmapFont toString
Fix maven tests
Suppress unchecked warning in DeleteHandler
Don t throw an exception if input plugin is not found
Remove unused code
Fix crossing street issue
Add luid to WifiConfiguration toString
don t set xmppNotificationService isForeground
remove System outs
massage try blocks in MethodAnalyzer
Fixed a bug where HttpStaticFileServerHandler sendListing was not passing the correct directory path
Fix log message
don t include runtime in maven dependencies
added click listener
fixed problem highlighting for unused variable
Fix wrong import
Added null checks to LiquidData constructor
Remove fprofile dir flag for clang
Fixed the CS error of camel core
Improved javadoc for ProviderManager
Removed the check for bean names in the lookup method
Add test for canEncodeUUIDString
Disable id generators by default
Fix typo in the max concurrent rebalance decider
Fixed bug in ObjectEncoder constructor
fixed checkstyle issue
Fix JavaDoc for announceForAccessibilityCompat
PsiFileSystemItem extends NavigatablePsiElement
Don t sort connected transactions in Wallet connect
Fixed bug on loading indexes
add new line after dependency check
Fix broken test
Fix javadoc typo
Add i to command line options
Remove unused constant
If blogOptions is empty default to empty JSON object
Remove unused methods
remove duplicate check
Fix TimerImpl isActive
Fix crash when no accounts are selected
Fixed syntax error
Fix a bug in NetworkLinker
Remove unnecessary parens
Add a test for IllegalArgumentException
Removed unused uploadInternalTaskName
Fix NPE in QSTileHost
Upgrade message boards
Remove useless code
Fix NPE in ContextConfig
changed uri to uri id
fixed checkstyle issues
Clarify javadoc for getInactiveDevices
fixed typo in log message
remove unnecessary String valueOf
Copy field types in RowType
make xRandomRectangle a little random rectangle by default
Fix NPE in VimKeyMapUtil
Fixed signature of JvmComponentPlugin
TcpIpJoiner ignores IPv6 addresses
Remove unnecessary cast
Remove System exit
Added previous and iterReverse to PooledLinkedList
Fix NPE in ActionBarWatson
Improved error handling for possible truncation attack
do not collect progress
do not cache externally defined classes
Use the new ZKMetadataProvider
Remove unnecessary code
Increase the timeout of Face Unlock on boot
Improved javadoc for TimedVcsCommit getTime
Added default value to Resource mappedName
Remove unnecessary code
Fix bug in GraphRelation
Add shin to LexiconEntry
Added a FIXME
Remove unused method
Added setFlipY method to TextureKey
add method to get previous exceptions used for testing
Fix NPE in UnderFileSystem get
Fixed bug on config loading
don t show empty package name if chooser doesn t show
Remove unused constructor
Removed a System out
SMALLFIX Removed redundant initializer
removed finish from closeApplication
Fix BloomFilter posting format
added check forSTORED
Hide algo name in Quantile
remove proxies that have no backup nor primaries
Fix issue where DeploymentStatus is not completed
Add a TODO
Make static field static
Fixed potential resource leak in IOConverter
Ignore failing test
Remove unnecessary synchronization
Fixed calculation of execution time in TimeFormat
Add parameter index to test failure message
don t use findChildByClass GrExpression
Add isSecretEstablished method to CordovaBridge
Fix hide tags in LocaleUtil
Improved error message when no busy connection is available
Make reconnect callback private
Fixed a bug where the default generator wouldn t be selected
cancel calc elements thread on cancel
remove outdated TODO
Fix H2ExceptionSuiteTest for batch update
Fixed unit test
Remove unnecessary debug flag
Throw a SemanticException if a distinct is applied to a comparable type
Remove unused field
encode help url
Don t create docBase if it doesn t exist
Added cancelHttpRequest method to HeadlessNet
Fix an NPE when externs zip is not found in the classpath
Reduced log level
handle src package
Add default constructor to Weighted
Fix formatting of started messages
Fix NPE in CallManager
Added a TODO
Fix checkstyle error
do not hide presentation
Create directories before writing to file
Fix Preference persistString
Fixed a bug where the floating toolbar wouldn t be visible
reduce scope weight
Fix crash when Bluetooth crashes
Added a method to clear comments for a blog
don t steal focus from fragment that s in front of the user
don t write multi upsert if there are no updates
reformat code sample
Improved error message when Hibernate database is not found
Add support for orientation tag values in MediaScanner
Don t trigger dev bootcomplete if we are not showing encryption progress
Set mCollected to true in onOptionsItemSelected
Fix typo in constant
Fix comment typo
Fix category button text
added check for between
Add a comment to the TvInputService setPlaybackRate method
Add AOSP check
Fixed HtmlEscaperTest on Windows
Remove a System out
Added parent parameter to createView
Fix typo in TransformsingView
Display up button on download screen
Remove unused imports
Disable allow permanent UDFs by default
Improved error message for test execution
Fix authentication failure URL
Fix file type detection
Fix radial restriction for Lucene version
Add test check for CalendarBasedTimeoutTestCase
Fix Selenium tests
removed debug output
Fixed the CS error of camel zookeeper
Fix bug in LocalStopFinder
Ignore java classes in superclass check
fixed suspend context
add setDonut_progress String
Add status code for gravatar error
Clarify encryption policy documentation
Remove unused imports
Added a constructor to LargeValueFormatter
Remove the soft keyboard hack
improve error message
Remove unnecessary code
ignore java util zipZipFile
Fix a bug in InputMethodManager getLastPendingEvent
Added responseMessages method
start the cluster in the main thread
ignore properties with transient fields
Add a TODO
shorten the URL
fixed small bug
Remove STOPSHIP logging
Fix HttpTunnelAddress compareTo
Add test for having column count
Added isValid and resetOctant methods to SelfLoop2dModel
Fixed method name
fixed XMLSerializer to handle n as well
Renamed sSyncAdapter to syncAdapter
Added flush to the DXF class
Fixed hasDirectoryTrees in DelegatingFileCollection
Log the exception
don t show history for directories
do not process exceptions in recursion
Removed IosAssetManager loadTexture
Handle SslErrors in FbDialog
Reduce menu drawer touch area width
Remove deprecated code
Fix source index benchmark
Fixed obj loading
Ignores IIOP tests
Fix boolean location check
Add legacy API for new stories widgets
Added moduleArtifact to DefaultResolvedDependency
Add a type test for new prototype method
Added error message for RESULT_TYPE_MISMATCH
Fixed issue on shutdown of distributed worker thread
don t send status message if there are no messages
Fixed WorldEdit mask parsing
Fix NUD_DELAY typo
added constructor to SimpleXMLConverter
Fixed a bug where rotating the screen would not return us to this directory
close existing completion popup when completion list is empty
increased pin check timeout
Removed unused code
AbstractPlugin should return a ImmutableSettings
Add constant for sleep duration
Rename addRequest extractAndAddRequests
Fixed bug on parsing of dates
handle empty stored string
Fix typo in javadoc
refactored groovy groovy extract class
fixed hostname and port display
Changed UnresolvedDependencyResult getFailure to return Throwable instead of Exception
Remove unused variables
TEST Add AwaitsFix annotation to TransportUpdateActionTest
don t suggest null smart completion
Remove unused variable
Fix a compilation error
Fix bug in SnapshotArray begin
Make _reactContext a singleton
Fix typo in TestServlet
Add missing log tag
rename test method
fixed NPE in StaticInvocationWriter
Fixed a bug in finding remote branches
Adds a check for CSeqHeader to createAck
fixed bug in hasJavaClass
don t insert closing tag if it s a HtmlTag
Added Server getPlayer
remove redundant check
Set the current value to empty after the test is started
Remove unnecessary SuppressWarnings
Don t start lists activity twice
Fixed bug on class index
added author tag
Fix Resources getDrawable javadoc
follow redirects on download url
Make doPost public
Fix NPE in PasswordAuthenticator
don t print empty starter conf
Fix NPE in CallbackProxy
Allow TypeModifiers to alter container types
remove unused methods
Fix merge conflict
renamed stop nodes in GWT stop context
Add a bad date format to the IMAP response parser
Add some Javadoc
add missing groups
Added getConfig method to Solo
Remove unnecessary comment
Added save method to Theme
Added getRemoteAddress to JglfwSocket
Do not show SDK library for non home module
Improve toString for ServletScopes
Catch IOException instead of UnknownHostException
set the OUT message with the same attachment instance of the IN message etc
Add ReadAfterWriteConsistent feature to AbstractTransferOperation
Remove unused imports
Remove StringUtils from javadoc
Rename getHelperTextAlwaysShown to isHelperTextAlwaysShown
Don t snapshot data by default
SMALLFIX Incorporated PR comment
Revert Ignore test that fails on server side echo
fixed jsonpath tests
Added compose function
Fixed the CS error of camel mina
Attach and Replace button should accept mouse events
Remove unused import
Add thread safety documentation to EventCollector
Fix live updates
Renamed displayMessage to displayMessageBody
Remove unnecessary format string
Fix a bug where FakeChannelSink does not set the Future
Add Android tests for multi file tests
Remove focus scroll strategy from BrowseRowView
Cancel timer on unload
Call notifyFocusChanged when process died
Removed unnecessary code
Revert Ignore failing test
make ChartAxis serializable
Fixed a bug in the XML output of the private query
Fix formatting in MediaSessionService
Fix unit test
Remove flower from isMature
Clarify javadoc for getTachyonURIs
Fix NPE in MethodAnalyzer
Fix DL reproducibility test
Set the loop to true when seeking
Fixed unit test
Fix CubeMapTexture addCompressedTexture
Fixed bug on SQL init
Fixed bug on scan cluster
Removed PrimaryKey annotation
Remove unused imports
fixed bug in PShape
Fix a flaky test
Removed empty schema fromMetaDataTable
add other path parser
Don t trigger preparation phase when package name changes
disable TreeSelectionListener in tests
Remove unnecessary DERTaggedObject call
add debug logging when hints are purged
Disable progress indicator for now
remove unused searchTerminationStrategy field
increased test timeout
Fixed error message
Add trace tag for webview
Fix NPE when error stream is null
Don t fire onConfigurationChanged if there are no changes
Remove unused imports
Removed unused import
Fix comment in AbstractInputBuffer
Ignore IOException in tearDown
Make StateMachine thread safe
fixed test case
always finish welcome activity when cacheword lock requested but already locked
Fix typo in docs
Make getEntity public
Fixed a bug in TestTraversal
remove invalid annotation
Use PathUtils concatPath
Added hasKey to FloatDict
Do not show normal permissions in the UI
Removed GL_FRAMEBUFFER from RajawaliScene
Include exception in exception
Add xpack to install plugin
Fix typo in Javadoc
Increased sleep time in HystrixCommandTimeoutConcurrencyTesting
Added int to ShadowBundle
replace String with CharArrayCharSequence
Removed unnecessary return statement
added JsonCreator annotation
add option to set rest over stream
Added getEqualsToken to JetValueArgument
Fix default allocator type for Android
Fix infinite loop in handleCachedHeader
Added a dispose method to HeadlessWorldRenderer
Commented out exceptionCaught
Don t flush the client stream when the server sends an unary request
Fix quality flaw
Log found activity instead of expected app
SimpleGraphServiceImpl extends GraphServiceBeanImpl
add getItemViewType to AnimationAdapter
removed author tag
Updated author tag
android modify registerComponent
Removed unneeded drop block
Fix error message in IndexLookupTests
Remove unused import
Java Remove AutoCloseable from PublicationImage
Fix testSimpleQueries to work inside a transaction
Add javadoc for StandardJarScanFilter
add assertion for empty shard stats
Reduce verbosity of SparkDl4jMultiLayer
Set DrawerLayout sDescendant focusability
Fix LoadInfo onRemove
Set the default value of the webappVersion to the empty string if null
Renamed DbTests to JtxTests
Removed setConfig which is no longer used
Invalidate View s parent when visibility changes
Remove unused setting of userLocale
Added Player getDisplayName and Player getDisplayName
Fix NPE in InputMethod getCurrentWindowIndex
Fixed the CS error of camel maven
Fix potential NPE
Java Fix CS error
Fix checkstyle error
Use sendEvent instead of trackEvent
add Nullable annotations
Fix NPE in ImageHelper
Enable object cache by default
Fix SClass equals
do not convert selection change event twice
Fixed bug in FixedBitSingleValueMultiColReader
Make numTokens final
Added test for labels on operator subrule
evaluate dialog should have a frame
Fix infinite loop in ChainShape
Don t use CoreAnnotationsTokensAnnotation
Set log4j defaultInitOverride to true in SecurityActions
Catch FileIntegrityViolationException in BuildSourceBuilder
Increased test stream keys size
Handle exceptions while determining content provider type
added NotNull annotation
Hiding IME if background user is requesting window
don t show project sharing dialog for non project projects
do not refresh project if it has already been resolved
don t deFocus on editor components
Fix trait fields index for java classes
Disable sensitive logging for HTTPD requests
add error message for var property override
Added default icon color
Fix sign in display
Remove unnecessary local variable
Fix session validation
Changed PluginEvent Type to PluginEnableEvent
Added abstract methods to BaseMonochromeBitmapSource
Make jacksonObjectMapper private
Fix plain text presentation bug
Fix crash in InputConnectionCompat when reading commit content flags
Hide the unshare button when there is no link to the image
Update the WebViewPool java
Add a minimal ID used for RS rendering
Add CORS support
disable the sanity check
Remove debug code
Fix crash when deleting an app widget
Added xts to categories
add search type to exception
Add pure button to HTML annotations
increased max heap size
fixed typo in javadoc
Fix CF creation in StorageService
Fixed imports in RestTest
Fix NPE in Gallery
Fixes a bug where people invite fragment wouldn t be replaced
Remove WearableExtender from GCM message service
Fix test on RedissonSet
Add missing final keyword
Log the time of the request
Updated to next release date
Remove outdated TODO
fixed sql parser for SEL
Make some methods private
Fixed a checkstyle error
Added a nopmd comment
Fixed issue on loading of documents
Ignore test that fails with the new HPACK encoder
Add a comment
Make MultiScheme serializable
Remove data connection over am from test suite
JavaSourceSet no longer extends LanguageSourceSet
Remove unnecessary code
Object value handler
Add argument check to AsyncQueue
add bracketsAndQuotesMap to InputHandler
Fix NetworkMonitor to not broadcast CaptivePortal
Remove unused code
Fix compilation error
Add a space to the height of the textfield
Fix typo in AccountManager
prevent default when searching for prefixes
FileSinkOperator log number of dynamic partitions per node
Improve error reporting in test
enable scroll in editor by default
Add factory method for DynamicPriorityTaskMaster
Don t use text wrap scale when using fixed viewport
Added R selectableItemBackground
Added factory method
do not start chat activity for private chats
Remove extra space in ModelTypeValidator
Remove redundant check
Fix quality flaws
Added SEQUENCE to UnexpectedLiquibaseException
Move application started events to the AnalyticsTracker
Fix typo in log message
Fix close group icon
remove unnecessary catch block
Fix link text positioning
Add missing type parameter
vcs log remove unused methods
Copy view before setting empty view
Reduced the log level of CxfProducer defaultOperationName
Throw error if template message does not exist
Clarify javadoc for getEffectiveManifest
Add a couple of missingProvide and missingRequire warnings to the DiagnosticGroups
Removed unnecessary code
Fix javadoc link
add description to action
Added getter and setter for rotation and move speed
Add missing Override annotation
do not set thumb bounds for scrollbar if alwaysShowTrack is true
Fix typo in forge network handler
Remove unnecessary code
added additional documentation
Remove unused imports
Changed log level of client exceptions to FINE
Fix exception deframing cancellation
Fixed remote test case
remove unused method
Add a method to check if sliding is enabled
added findRelativeAidlFileNames to generateAidl
Fix ReplayInputChannelContext getType
Remove unnecessary modifiers
Adds supported opsets to the list of supported opsets
Fix unit test
add getters to the drawer
Fix Pixmap fill
Don t draw labels if the chart is disabled
Add new APIs to SystemBarTintManager
Fixed seda consumer NPE
Fix quality flaws
Fix JSONValue usage in SSTableExportTest
don t show lookup hint for non focused lookup items
Fixed wrong import
Fix DetailsOverviewRowPresenter
Fixed typo in method name
Catch Throwable instead of SQLException
Fix regex for URI_ATTR
Fixed compilation error
Fix getCurrentInterruptionFilter method
allow overriding maxHeight in ScrollableToolbarPopupMenu
Fix the build
Fix checkstyle error
add test to AllTests
Add missing copyright header
add maven plugin to supported packagings
Fix NPE in Util
Remove redundant test
Make ShadowAudioManager getStreamVolume public
remove redundant checks
Added author tag
Handle error gracefully
Log metric type in GangliaWriter
Ignore ActivityManager mContext
Add setAdjustViewBounds method
don t update scope for file type if it can t be shown
Add Nullable annotation to AliasConfiguredTarget
Fix MultiMapService rollbackMigration
enable closures by default
Add rulePostamble to DefaultOutputModelFactory
do not highlight empty elements
Remove unused code
added daemon thread factory to cuda context
Removed unused import
don t send an interrupt if a popup is visible
Fix secondary dex filename
Fix response status in JaxrsUriBuilder
Make MessageException constructor final
Fixed the CS error of camel core
Call super finalize in RemotingModelControllerClient
Remove unnecessary assignment
Add a configuration for the AtmosphereHandler property
Improved javadoc for HttpChannelState
Fixed the CS errors
Fixed a snippet
Move DrawerNavTest to test package
Add convenience methods for creating ICE agent and stream
Updated license headers
Fixed NPE in PyImportOptimizer
do not show proxy credentials dialog if at least fixed proxy was used
Remove redundant hashCode method
Fix autofit width
Change the default link color toSecondary
Remove obsolete TODO
Render ctor visibility on visitConstructorDescriptor
Don t throw an exception if we can t get a public resource URL path
Add email ID to fetch list
Fix NPE in Learner
remove duplicate getConnectionFromSlot call
Removed unused class
Remove unused merge method
Fix crash in setAdapter
Bump prune time for unreferenced contents
Fix entity spawning
Add comments to Dictionaries
minify check box
Fix parsing of command line arguments
Improve error message
Throw exception if unable to build MapboxWebMapViewFragment
Changed RosterStore getEntries to return a list if there was an error while loading the store
Reduce neighbor update percentage
do not propagate exception
Add missing methods to PluginModule
fixed javadocs for HiddenCharacterRule
Fix search results
Fixed typo in RadioKafkaInput
Fixed JoglInput to work with new keys
Fix memory leak in navbar
Don t reset CompoundButton drawable state
Added some javadoc
Ignore test that is flaky
remove unused method
Improved error message when deserializing a record
Added support for primitive types in implementation import collector
fixed test url
Fix memory leak in event loop
Improve Layer removeEntity javadoc
remove string literal inspection
Remove unused code
suppress unused import warning
Close the constants cursor on destroy
Fix potential NPE in ExpectedResolveData
Remove unnecessary semi colon
Fix NPE in AndroidUtilities
SMALLFIX Fix typo in FileUtilsTest
add comment to ScorePhrasesLearnFeatWt
Set DEFINE OFF for Oracle
fixed failing test
Fix comments in SoBundleLauncher
Fix bug in FileDataModel
Added clamp to Vector
Register new TypeaheadRFModelKeyRequest and TypeaheadGLMModelKeyRequest
add GraphViewData to drawSeries javadoc
SSTableReader readRow should invalidate the streamed row if it is in the cache
Copy over the ABL whether we should fit system windows
Added method to get the db pools
Fix description for logoWidthPx
Read the UBJSON from the given stream
Fix typo in variable name
Fix NPE in equalsIgnoreWhitespaces
Changed the test port
Fix bug in ShardIterator
Fix buffer allocation in TextInputFormat
Added a FIXME
Fix failing test
Fix assertion in DeleteJob
Remove obsolete code
Remove node mode from node upgrade test
Changed log message
Add cause to exception
Don t throw exception in reset
make setVisible visible in UIServiceImpl
Fix broken FitCenterTest
removed empty line
Fix buffer leak in BinaryMemcacheEncoderTest
Add contact display details to the contact list
fixed bug in astop when chk is empty
Fixes a NPE when trying to add a contact without a protocol provider
set sandbox to allow same origin
Fix AndroidManifestFinder possibleLocations
Fix typo in PathCacheLoader javadoc
Added a javadoc
Added getRoom method to GroupChat
fixed NPE in DefaultGrailsCodecClass
Fix rename on windows
Add a TODO
make action panel public
Add javadoc for ExecutionHandler constructor
dispose module in EDT
add revision id to source control info
Fix bug in HashGenerationOptimizer
Add throwing to Callables
add brave playstore to list of browsers
Add a comment to explain why we remove the old annotator
provide default inspection profile name
Don t show avatar if jidToEdit is null
Add logging for shutdown of XMPPConnection
Added type parameter
Remove Beta from Escaper
Add deliberate test failure
added private constructor
Fix comment typo
Fixed bug on get user info
remove unnecessary code
git remove unused code
added edgesOnly calls
Fixed the test error of CxfEndpointBeanWithBusTest
Relax DLGradientCheck tolerance
Added finalName to AbstractAndroidMojo
do not replace string with empty string
write file in binary mode
Fix loadOnStartup default value
Fix maven tests
Added assertions for edge creation
Fix keyguard screen on early
don t set name of writable elements
Remove unused method
Add description to play run
ImagePresenter handle null model id or stream loader model id
Add test case for existing table lookup
Add Timber for debug builds
Fix AJP tests
Fix NPE in ChromeClient
Flush the response body
Make date formats public
Handle OOM in Binder
Don t negotiate TLS if client requested to use TLS
Remove redundant code
Remove unnecessary code
Add missing implementation for ShadowSocketTagger tag
Add isRotating method to ScreenRotationAnimation
Don t substitute generics for types with null values
Fixed a bug in BlockDebugUtil dumpBlockTree
Fix activity leak
Remove unused constant
Fix memory leak in ActivityManagerService
Fixed typo in comment
rename dependency factory to common dependency factory
Don t use OPAQUE for the status bar
Fix checkstyle error
Fix race condition in DaemonClientInputForwarder
Added support for editing font sizes
don t show cancelAllRequests if dialog is disposed
Rename setOnMeasureSpec to getOnMeasureSpec
Save GPX file after route steps
Remove obsolete TODO
Add missing mountMedia call
Don t set focus to home activity if it is not home activity
Fix test assertion
Fix potential NPE
Fixed return value of new String
add JavaDocTokenType DOC_COMMENT_DATA
Fix NPE in delete
Fix DiskCacheStrategy isDataCacheable
Remove redundant check
Adds a convenience method for firing RegistrationStateChangeEvent
don t check isWritable
Check mOperationDone before sending the final request
Don t create a folder twice
Clarified javadoc for AnnotationDrivenBeanDefinitionParser
dump stack on test failure
Added home button to HelpActivity
Fix server restart required handler
hide download javadocs checkbox when downloading sources javadocs
Fixed deprecation warning
Add documentation registry to the project
Fix ServletPathMatches redirect
Make BlockingObservable private
Add private constructor to ExpressionExtractor
Catch AmazonClientException
Remove unused method
Don t use deprecated ConnectionParameters
Fixed the CS errors
Fix merge conflict
Clarified javadoc for isRadioButtonChecked in Solo and RobotiumUtils
Add ECKey toStringWithPrivate
Add registry mutable
Fixed test framework to work with different plugins
Added missing closing bracket
Fix Capsule manifest validation
Disable full screen mode for Mac since it doesn t work anyway
Fix Integer overflow in TextView regionMatches
Add support for the ApplicationHttpRequest Security Enumerator class
Allow posters and episodes images
Remove unnecessary toLowerCase call
add lock to feed subscription service
Fix checkstyle error
Ignore test for now
Fix LogFileMvcEndpoint initAllowedLocations
Remove unused interrupt method
Simplify the query string decoding
Fix bootstrap cli parser test
Add default restriction if no restriction set
Add isReadyForWrite to registerWriteInterest
Fix typo in Spanned nextSpanTransition
Fixed the CS error of camel example
android update sdk version
Add missing layoutlib delegate
Clear identity before getting application info
catching focus exception
Fix JDBC driver service name
Fixed wrong exception handling in ClientExecutorServiceProxy
Fix a bug where Http11NioProtocol release returns null
Removed a System out
remove useless border
don t show delete x file if relative path is not set
removed unused imports
Fixed potential NPE
add hit count and totalHit count to DlvApi
Fix NPE in AppCompiler
Add test for LogInterceptor
Replace static modifier check with public modifier check
Add MapTransformer immutableMap
Don t close the devicePropDb in finalize
Register custom functions
Fix NPE in TextView bringPointIntoView
Fixed bug on cache remove
add description to copy
wait for smart mode in AndroidFacet
don t suggest non strict parent of directory
Reduce the buffer size in BackpressureTests
remove read access check
fixed wrong location
Don t use default notification
remove unused parameter
extract options from setup chunk
Fixed recycler view
Add skipCrunchPngs to AaptPackageResources
Added get setEdgeScale to EdgeSupervisor
Allow Provides to be invoked on non class methods
Fixed the CS errors
Move graphEnhancer first to preDexDeps
Remove deprecated method
Make fields final
added check on file existence
add check for null master node
Fix documentation link
add vehicle param to GraphHopperWeb
Remove error message
Remove unnecessary localLOGV check
Add a TODO
Fix JetRunConfigurationEditor module chooser
Change Watchdog s thread name to main thread blocked
Log Bdb Environment Stats
do not highlight empty dom elements
Fix TwoDimTable column name
Rename RemoteSdkCredentialsHolder to RemoteCredentialsHolder
restore lexer rule scope
removed printStackTrace from catch block
Added tests for JavaGenerator
make DomModelImpl implement DomModel
Remove unused settings
Removed static from hazelcast instance field
Fix race condition in VecStatsTest
Fix strict mode violation logging
Fixed a typo in the CoreProxy
Fixed typo in method name
Fixed bug on fetch with null parameters
Fix a bug where the panel could become closed
Remove unnecessary catch block
Make setupDictionary static
Folder properties dispose fix
Restore Spinner position when Spinner gets restored
disable action for non java parameters
Reset the matchedEOF flag in reset
Added a comment to Node getChild
Removed unnecessary increment
invalidate view on scale change
Remove VersionResponseFilterTest from parallel mode
don t show null status strings
remove comma feed from email
Reduced log level of info to debug
don t show file usage icon for non physical files
Fix NPE in ExplorerResource
Don t throw IAE in notifyVideoUnavailable
Fixed variable name
fixed failing test
Fix crash in ViewDragHelper
don t wrap RuntimeException
Improve documentation of PCF POISSON
Fix formatting in BlockManagerImpl
Fix typo in NamespacedExtractor
Improve error message
Avoid NPE when optimizations are empty
introduce some slack so that minimum transfer time does not cause missed trips
Add new metrics for confirmation confirmation dialog
Fix a bug in SpeeDRF
Fix ForwardingMultiset javadoc
Fix potential race condition in ClaimStrategy
Saving settings on dismiss
Changing grey_light to grey_extra_light
Only set the sub header text to Visible if we're GONE
Ignore test under windows
Don t throw an exception if the thread can t handle a call
reset NamedWriteableRegistry in AfterClass
tweak lCD background color
added tokenizer preprocess
Improve error reporting when resource not found
remove obsolete TODO
Add toString to TestHttpResponse
Fix overlayed properties creation
Fixed potential NPE in HystrixTimer
Remove unused constructor
Fixed NPE in IQMUCRegisterHandler
add editExternalAnnotation method
Fix HikariPool wait timeout
disable show dependencies action if project is not present
make ViewHolder public
Fix Javadoc for anyRequest
Make function parameter info dialog background a JBColor
added checkCanceled to PyResolveUtil
Fix race condition in HttpRemoteTask
Fix NPE in UrlImpl
Normalize tel URI to tel
Replaced PatternFilterable with SingleIncludePatternFileTree
tweak text size
Don t alert user for security off if the call is already ended
Remove unused testIteratorKnownOrderRemoveUnsupportedMethod
Updated ShaderProgram javadoc
replaced quotes with q
Remove deprecated code
Add a debug flag to the CLI
Fix typo in ApplicationConfig
fixed bug in ring model
add since tags
Changed the description of unbind tool to none
Fix potential NPE in ClientRequestExecutorPool
Remove unused field
do not encode file references
Fixed the CS error of camel http
Removed redundant check
Reduced log level for KafkaInput s timed out message
Fix JUnit38ClassRunner initialization
Don t call cancelWebCoreTouchEvent in WebView
don t generate mips
Fix ContainerKey equals
update tree table UI after scope changes
Add executeSynchronously to MainThread
reset coolant liquid when fuel amount is zero
don t remove non registered VCSs
Fix tests for symbol issues
Do not overwrite PREV_APPLICATION_MODE if already navigating
Fix NPE in Model get
do not change document if file is invalid
Remove unnecessary calls to super
Fix blockservice handler integration test
Remove unused dependency on BuildRuleResolver
Remove unnecessary rounding params
removed wildcard imports
Fix NPE in getSpeakerClusterId
SMALLFIX Remove unnecessary logs in MasterFaultToleranceIntegrationTest
Fix race condition in scheduleAsyncPersistence
Reduce the number of send requests
Remove unnecessary null check
fixed seed generator
Fix checkstyle error
Ignore extra files in legacy metadata
Add class object template
hide some hidden methods
Remove listener when broadcasting transactions
Remove unused variable
Fixed bug in checking for dirty transactions
Remove unnecessary assignment
Fix typo in BloomFilter create int
Fix help topics
Show toast when no camera found
Remove outdated TODO
improve error message
Fixes a NPE in the contact list tree model when there is no contact node
SourceFormatter add init ext
Added some logging to MasterClient
get data method by kind
Remove unnecessary volatile keyword
TEST Add AwaitsFix annotation to IndexWithShadowReplicasIT
Add getFrameworkPackageName to ModuleUtil
Fix a bug in GBM
Reduce the size of the rounded drawable
Remove unnecessary throws declaration
Fix ServerControllerService to use the container sub target
Fix double tap handling
Improve contract documentation
fixed a typo
removed unused import
Fix a typo in DaemonInfo
Fix formatting of test toHumanReadableConfig
show full stack trace for too many errors
Remove unused fields
Fixed javadoc warnings
Fix OEM directory detection
Don t send a redirect if the url is the same as the previous one
Add setPin method to TachyonFS
Make AMBIGUOUS_FUNCTION_DECL an error
Fix logic error in checkNonBlockingRead
Clarify documentation of East Asian Width calculation
Fix ModelPath child
Fix bug in shard indexing
Fix compiler warning
do not show project classpath for java projects
Improve test coverage
Make sure the tree has a top level unary rewrite
added a helper method to allow scripts to be run taking command line arguments
Make MISPLACED_TYPE_ANNOTATION a warning
Set default values for Glide transformation and downsampler
Improved exception message
Fix NPE in UserImpl getRoleIds
Fix default wallpaper
Remove unnecessary log message
fixed indentation in package info
Add extension to the list of notations
CamelContext getName should return the next name if not null
Fix LwjglCanvas on Linux
Increase the cloudsize for the test
Fix NPE in VCardComposer
Removed django string literal implementation
don t set next encryption to zero
Fix issue with SpillingQueueElement
Fix ApkDecoder so that it doesn t create a new directory
remove TabbedPaneWrapper from TabbedLanguageCodeStylePanel
Rename canFill to fillIfAbsent
added a slash to the prefix of the grails view
Add test for parsing errors
Fix error message for invalid operation
Fix NPE in JabberRegistrationListener
Do not write property declaration if there is no field
Added convenience constructors for FileHandles
don t leak completion manager
Remove redundant test
don t set platform prefix if it s already set
remove unused constructor
Fix function annotation generation for non function functions
Add subject to send intent text
Use the AndroidUtils downloadUrl
add smali link to the list of available sites
Change Iterators skip to advance
Don t open a folder if the remote folder is SEEN
Remove unused method
bind service service name and port
add pause method to streams api
fixed clip calculation
Removed unnecessary SuppressWarnings
Add a method to create a new endpoint instance
Catch ActivityNotFoundException when launching Recents
Remove unneeded query param
Fixed height calculation
suppress highlighting on mode change
Added SuppressLint NewApi
Log a warning when an empty body is received
Make Location Cloneable
Removed author tag
enable boolean expressions
Add isClosed method to AsyncHttpClientConfig
SMALLFIX Remove unused import
Fix off by one error in double histogram
Remove unused variable
Removed unnecessary array allocation
Fixed compiler warnings
Fix NPE in VPNServiceBinder
Don t call onStateChange twice
Fix setMasterMute boolean
add flow filter
Remove unnecessary cancelTask call
add method to get selected indices from top row count
Fix checkstyle error
debugger fix clear node children
RBucket extends RExpirable
Add tests for order by alias with same name as selected column
Fix cursor leak in DatabaseUtils
fixed checkstyle issue
Add getCoyoteRequest to TesterDigestAuthenticatorPerformance
Fix clip reveal animation position
don t init provider cursor
remove unused header offset
remove unused import
do not compare paths
Remove unused code
do not register tool window settings
Add missing line feed
Fix CertificateManager decodeServerIdentity
SMALLFIX Removed explicit type argument in NormalListDialog
Fix typo in ConnectivityService
Fix unchecked warning
Added a TODO
Fix NPE in AbstractSharedSessionContract
Remove unnecessary mod ops
made addedRelations volatile
Added a test for no body content type
remove debug code
Fix NPE in HdmiCecLocalDeviceTv
Add query parameters to redirect URI
Fix a bug in the CoyoteOutputStream
Don t swallow StreamException
Added test for subscriptions
Fix memory leak in tests
Added a better success message for Liquibase update
Fixing input type for email and web address
Fix SSL initialization
Remove unnecessary increment
Removed unused imports
Add error message for init before logout
Remove redundant else statement
Removed check for fixed shipping strategy in PactConnection
do not invoke isDisposeInProgress twice
make getTimeWritten public
Add StateMachine hasDeferredMessages and hasMessages
remove jabber pin fetch
Remove unnecessary code
Removed unnecessary if condition
redefine obsolete fix
pt add PortugueseCompoundRule to rule list
Improve documentation of SumReducer
Fixed bug in TimerHandler
Fix Integer valueOf Integer parseInt
Fix NPE in ShadowResources
disable update by timer on lookup creation
Added some comments
Fix poi edit
Rename parameter to be consistent
Use the pooled allocator
add first last of index method
catch all exceptions
Fix null check
tweak border for quick fix preview
Remove unnecessary call to reflection
Add support for VIDEO to MediaRouter
Fix typo in AsyncCompletionHandler
Fixed a bug in the scale transformer
restored API compatibility
add a warning when a shard is found on path with a different index UUID
Fix error message for inline property with backing field
Fixed the CS errors
Remove unnecessary nativeSetIsScrolling
Fix NPE in manifest extension list
Fix NPE in StatementResource updateCount
Remove obsolete comment
Add RecognizerResultsIntent EXTRA_VOICE_SEARCH_RESULT_HTTP_HEADERS
Don t resolve stubs in findTypeInVarSpec
Adds a contact dialog to the parent window
ImproveANTLRErrorStrategy javadoc
fixed multi select list preference
Add TableCommitNode to PruneUnreferencedOutputs
Removed unnecessary log message
Removed unnecessary cast
git init add root fix
Add javadoc for ConnectionException
Fix HttpClientTest testConnectThrowsUnknownHostException
add metaData method to RoutingAllocation
Added TaskArtifactStateCacheAccess createCache
Add domain check to DL validation
Removed unused method
Add trace logging for removal of container
Removed unnecessary local variables
Add headers for connection and upgrade
Handle Unicode Replacement Glyph
Added ProtectionDomain support to ClassGenerator
Fix crash in state main menu
avoid NPE in Language getRealLanguages
Don t throw an exception if compression is closed
Don t generate TerrainGen on non modWorld
Add since tags to DurationField subtract
Remove unused narrow method
Remove servlet context path from title
Avoid double onRemoval calls
do not serialize state if there are no expressions
Handle unknown namespace in SASLAuthentication
Add WITH NO DATA to AS clause
Add support for dynamic references in ESQueryBuilder
add help id for evaluate
Expose the state of the ExecHandle
publishIfFirst should check first notification type not only first
Added message to exception
Add a constructor that takes a PassiveScanParam
Fix unit test
Fix warning in ResultSet sum
Fixed MaterialListPreference Builder title
Fix webLayoutMinimumHeight calculation
Improved error message
Fix a crash in AccessibilityIterators
Don t cancel Auth if already in cache
Don t finish DummyActivity
fixed NPE in action
Improve documentation of SpellCheckerService onGetSuggestions
remove obsolete comment
Added clear method to ButtonGroup
Fix typo in custom http receiver
Fix imports in UtilsTest
Removed unnecessary check
Fixed typo in Javadoc
Delete all attachments by message_id not id
Deprecate unused HitTestResult
Add support for custom filter
Ignore test that fails for APR
Fix NPE in getPathTranslated
Add click listener to label
Fixed test case
Add constructor to TestThread
Set drag state before setting remove velocity
Remove unused import
Fix quality flaws
Make getKnownPrimaryTypesNames static
Turn off debug flag
Move logPosition into synchronized block
Remove unused parameter
Rename ipc ping interval to hdfs
Fix divide by zero in CompactionController
add problem highlighting
Add getter and setter for foreground background thread scheduler
Fixed a bug where the AuthenticatorActivity would not set the result
Remove unused import
Add error handler to TransformFuture
Renamed assertLowMemory to assertNotLowMemory
made SubVector public
remove timestamp from node stats
Fix exception message in IndexedSet
Fix NPE in make_model
Fix JavassistProxyFactory to use classloaderUtils
Remove unused code
Remove extra line
Fix indentation in FullRunningAverage
do not highlight null expressions
Fix NPE in processor
Removed unused imports
create ConfigService on startup
Fix ShellStepTest on Windows
Added no arg constructor to AudioNode
Fix Scene setTag to set tagInternal
add createPresentationCodeFragment method
Added reset method to RandomRule
Removed unused method
Make NettyChannelBuilder newClientTransport public
do not register inspections in tests
Removed Revision keyword from version tag
add missing mock
Fixed unit test
Added some documentation
add labels to vocab
Add support for org json JSONObject and org json JSONArray in JsonStreamerEntity
Fix char picker input
Added a getter for the base scale
Fix tab icon for first chat window
Fixed checkstyle issues
Fix the build
CleaningNeoServer now deletes the config file
Set secure and httpOnly when creating cookies
Fix error message in VectorDrawable
Remove unnecessary SuppressWarnings
add createView Closure
Add some missing accessors to AudioStream
Add minor params to TestNet3Params
Fix call hierarchy check
don t load HEAD refs
Add missing annotations to Metric
add method to get latest journal article by resourcePrimKey
Removed unused imports
Don t trigger bugreport if we are running stability tests via monkey
Turn off a flaky test
Bombed now has a month day
Include locale in error message
Remove updateTextSelectionFromMessage from WebView
remove unnecessary cast
Prevent NPE when output panel is null
Add missing license header
delete existing data if it exists
Fix NewJMXExtension writeContent
start server internals
Adding license header
Fix missing label in InfinispanConfiguration
Fixed test on jmx
Added ViewPort clearProcessors
don t send batchlog mutation twice
Fix merge conflict
Removed InternetExplorer check
Fix the test
Make hasXinstalled consistent with AdvancedSettings
fixed auth activity
Fix some typos
Added more validation to IgnoredWhenDetachedHandler
Revert close menu in the slide menu fragment
Catch Throwable instead of Exception
Fix delete logic
Fix test on windows
add final to EP_NAME
don t scroll gutter icons
Added test for GroupBy and MapProperly
Added setTextSize to FontAwesomeText
remove redundant code
Add a test for LexerInputPositionSensitivePredicates
Fix limit search
Remove TODO in CandidateEdge
Removed unnecessary javadoc tags
Use the default LZMA2Options PRESET_DEFAULT
Add async support for websockets
Clarify javadoc for FLAG_PARENT_CAN_ACCESS_MANAGED
Don t show unresponsive web pages if context is not activity
Remove redundant code
removed unused import
Fix javadoc link
Rename rule in ApplicationMasterTest
Remove commented out code
Don t reset transformed view if it is invisible
Remove redundant null check
shortened shortcut conflicts title
tweak retina gradient
Added helper method inInsertMode
Rename test methods
added a comment
Add missing resolvers to CompositeELResolver
Fix line length
Add test formatic broadcast filter
Add missing import
added license headers
Add node id to retry timout log
Simplify the logic
Fix javadoc formatting in CalendarContract
Fix a bug in the lexer that the dot character was not reading properly
Log the exception when a client fails to connect over ssl
Added setCapacity int capacity to liquid tank
Fixed link to maven and jetty documentation
Don t shutdown the scheduler if the Broadcaster is shared
Removed unused code
Remove unused code
added test for HookSteps
Added a thread to wait for the emulator process to exit
Added Server getAllowEnd
replaced equals with double comparison
Copy min max dates when restoring selection state
Don t return error code for daemons that don t support authentication
Events for unfavorite shows
Fixed NijiPermissionsResolver hasPermission
vcs log use SimpleColoredComponent getTextBaseLine
Fix NPE in NativeHelper
Fix bug in recalculation thread
Catch Throwable instead of IOException
Add a comment
removed extra newlines
Fix NPE in PopupWindow
Check surface insets when creating HardwareRenderer
Add null argument to convertJsStackTrace
Fix a race condition in SocketSslEchoTest
Added test for serialization of AlreadySerializable
Don t deploy the ServiceContextProvider if the context is a WebAppContext
Don t resize the window when setting the hardware renderer
preserve order of selected elements
commenting out failing test
Disable the timeout by default
Add space to illegal user key characters
Remove unused imports
Print out the command line instead of err
Fix expected response code
Remove unused logger
Catch IOException when flushing the writer
Fixed the CS error of camel jsgi
Removed unused import
Fixed a bug that caused the dividerHeight to be set to zero
Removing wildcard imports
enable handler for groovy files
Make ExifInfo public
Deprecated tx mode
Fixed a bug where roots wouldn t be selected
disable isReadOnly for PSI files
fixed unique constraint
Added Django module type description
Throw IAE if the dataset format is not recognized
Removed empty line
Fix incorrect warning message in LinuxPlatform
TEST Fix typo in restartRandomDataNode
Make EnableAutoConfiguration Inherited
dispose application on init
Fixed typo in javadoc
Fix NPE in ImageSettingsDialogFragment
Fix help text for output folder
Temporarily revert clearApplicationUserData to avoid some critical processes being killed during setup
Change group splitting warning to info
Add host header to websocket client
Fix a bug where the sensor could only be registered once
Fix flaky test
Fix small bug in PreviewGraphFactory
Recycle the EditText s drawable
Don t fail the build if the path doesn t exist
fixed whitespace issue
Fixed bug in PolygonRegion textureCoords
add badge to header drawer
Fix a bug in RaptorPageSink
fixed font generator loading for gen files
Fix possible NPE
Remove unused import
Fix NPE in ShinyAppsDeploy
fixed duplicates index
Allow enter in movie search
Improve Chain Javadoc
Fix authentication module
Remove deprecated code
Fix line numbers in TypeTransformationParser
Fix a typo
Fix NPE in BlurDialogFragment onResume
fixed bug with persistent contact data
Add constants for APIWEAVER
Fix typo in TestQueries
Fix Eclipse warnings
removed unused annotation
Fix system service
ConstantBeanHolder should create BeanInfo lazily
Fix bug in InternalNioOutputBuffer
added license header
Fixed wrong implementation name in SingleFilePageSwapperFactory
ignore failing test for now
Add a destroy method to EphemeralCheckpointForwarder
Fix NPE in handler
increased wait time to make test less flaky
Don t show lock screen when app is not shown
Fix NPE when writing to Parcel for Android SDK level 3
add env name to machine
Remove deprecated Base64 encode
Removed a System err println
fixed marshalling of SAML objects
Fixed a bug in the NeoAlgoTestCase
Fix TextViewCompatTest on Android
Fix test to use ReaderBasedJsonParser
Add a trailing to the default DynamoDB endpoint
Add a bit of documentation to RewritePolyfills
fixed pattern matching
Added null check
added space after comma
Make policy metered
Fix TextView getSelectedText
do not set full message for gelf
Add banner location property
Fixed a bug in the sync thread
add more time to recovery test
remove unnecessary null check
Fix NPE in TokenEndpoint
Reformat SvnVcs java
Fix html conversion
Fix possible NPE
Handle empty lines in ManualTagger
Changed the max command line number
Fixed URL encodeComponent usage
Add S3UnderFileSystem openAtPosition
Add test for append with wrong number of arguments
Fix NPE in ScramSha1
Fix handling of moved and ask responses
Fix AppBarLayout warnings
Fix typo in variable name
removed author tag
remove unused import
Fix NPE in ConfusionMatrix toASCII
Added missing Override annotations
Set cookies HttpOnly and secure cookies
fixed test case
Make MultiDimensionPoint serializable
Reduced log level of connection close
Don t show deleted messages
don t show negative number of failed tests
Make some methods static
Removed unused imports
Fix off by one error in OnBoardDepartService
Fix test bug in TestDatabaseShardManager
Increase the maximum event length
Remove unused imports
Fixed a bug where ScrollView would delay child pressed state
Remove unnecessary assertion
Added a method for adding an app to the list of apps
Fix AppWidget refresh issue
Added Intent FLAG_ACTIVITY_CLEAR_TOP and Intent FLAG_ACTIVITY_CLEAR_WHEN_TASK_RESET to startActivityForResult
fixed checkstyle issue
Adding license header
Fix NPE in TaskViewService when no plugin is configured
request focus in window instead of window focus
disable copyWebInf by default
Set user true when creating a new task
Fixed isWatched method
Fix call account chooser menu
Added getter and setter for timer auto reset
fixed a typo
Fix exception handling in NioEndpoint
Add TODO to BAD_SHIFT_AMOUNT_LONG
AudioClipHandler now skips empty buffers
Fix UrlImageViewHelper setUrlDrawable
Implement getProcessor in RawTableMaster
Fix typo in class list
Added some javadoc
Fix benchmark settings setting name
Fixed the CS error of camel consumer
Updated Player setHealthScaled and setHealthScale
reset server book hashes map on change
Remove obsolete comment
Fix quality flaws
Fix a problem with the manifest block in the backup manager service
do not edit cell output
Add missing import
Removed unused imports
Github API Handle empty response correctly
Remove unused import
Remove unnecessary check
Initialize m_rowKeyListeners to empty list
Fix entity equality check
Improved javadoc for AdditionalAnswers
Rename addHandler to withHandler
Removed unused code
Remove unused method
Reduced log level for no registration found
Added a method to move files in the same directory
Adjusted maxEXPOSURE value
Fix TachyonConf list to use delimiter
Fix C8Chunk inflate
make GroovyConsoleAction dumb aware
restored commented out code
Set default image to the first image in the media grid
bump version number
Fix incorrect class name
Remove debug code
Fixed a bug in the DebugRect constructor
don t use getClass SimpleName
Fix a typo in a comment
improve match all query performance
Added tests for URLs with no protocol
Replace IllegalStateException with IllegalArgumentException
Add Override annotation to CodeGenerator
Add some documentation to the performance test
don t evict state map twice
Fix State clone
Moved Fragment to app Fragment
Log a warning instead of a debug message
Re check connection after a brief delay
Allow input type to be extended
Remove outdated comment
Prevent NPE in case of closed channel
Fixed NPE in ItemStack getData
don t sort registry items by default
Don t show platform debug message for non android platforms
Add lock to EmojiconRecentsManager getInstance
Updated DatabasePopulator javadoc
Add error logging to ProxyResponseHandler
Fix a compilation error
Fix NPE in KeyguardBottomAreaView
add convenience constructor for interner
Added SpawnReasonBUILD_SNOWMAN
Remove unnecessary StringUtils replace
Add trace to WindowAnimator
Fix NPE in PeopleListFragment
do not resize grid caption if editor is not editable
show the create account view for extended accounts
do not resolve collections
Save inherited Principal in session when authenticating
Remove unused field
Renamed button text
Remove debug code
Improve exception message for View setMeasuredDimension
Fixed typo in JavaDoc
Fix Action Bar test
Added the header views count to the listView onItemClick
improve error message
Avoid equals implementation of ExecutableType
Remove unnecessary code
add setToken to ColibriIQ
Fix NPE in ARSCDecoder
Improve the message
Fix off by one error
reset empty text
Add media log to watchdog list
Fix context path
Fix bug in ExternalResourcesRootsProvider
Fixed silent flag in TagSettingsActivity
Throw IOException in AnnotatedClassCacheableService
fixed checkstyle issue
Remove deadlock check
Add a method to declare a variable in a scope
Ignore UI tests for now
Added missing documentation
Changed PlayerDropItemEvent to have its own Event Type
Remove unnecessary code
Resolved findbugs issue
Remove extra newlines
when a holder is reused clear the image view
Improve javadoc for SharedElementCallback
Fix a typo in the previous commit
Fixed some javadocs
Fixed a bug in SearchDataQuery
don t read lookup elements
remove long distance stop linker
Fixed typo in javadoc
Remove onBackPressed from sample
Add a remainingCapacity method to RingBuffer
Rename method in AccessibilityInteractionClient
ReadTimeoutException extends TimeoutException
Fix typo in NotFoundException message
Remove EOF edges from rule stop states
Add debug info to SecureChatTrustManagerFactory
Fix updateGeometry method
Restore compression on upgrade
added missing import
Remove unnecessary volatile
SourceFormatter remove unused tags
Added missing javadoc
Make configure Closure public
Fix log message
Remove unused variables from ActivationMeanIterationListener
Add the all group to the broadcast plugin
Add clusterStopsAsNeeded to index API
Fixed a couple of assertions in a test
don t auto grow paths
added todo comment
Allow execUTE_ASYNC_CONNECT to be a boolean
Fix NPE in FileHistoryCache
Add support for custom trustAllHosts
Add DEAD node stage
Fix NPE in ZWaveAssociationCommandClass
Added URL attribute to RecordingStatus
Added author tag
Remove EvictingQueue emulated flag
Do not override non visible functions
Remove unnecessary throws clause
use the correct variable
Do not search packages in tests
don t highlight javadocs in stubs
Fix NPE in VizGui
Fixed instance loading
Fix PartitionedLogicalPlanner to run on coordinator only
enable antialiasing for balloon
Fix typo in javadoc
set initial partitions when merging
Fix sorting of tasks by date
Fix NPE in Reflect valueOf
Add missing TargetApi annotation
Make resolveIndex return empty
Remove the root relative path lookup from the compiler output
throw exception if close fails
don t publish html content types
added Entity as an annotation
Fix WorldGenScreen to load all dependencies
Remove unnecessary synchronization in AbstractFramedChannel
Add IndexRange length
don t start activity on main intent
Remove unnecessary break statement
remove unused method
Fix method name
fixed failing test
Set project configuration for Python tests if it is empty
don t show top border for execution console
Add additional test for autosizing of double histogram
Add test for custom externs of NewTypeInference
avoid truly random
added missing license header
Fix app mode settings
Fix potential NPE
Fix bitmap loading
fixed wrong file existence check
Prevent NPE in ExtractorDimFilter
Add assertion to ScopeProvider
Set trakt notification priority to high
don t auto bundle unbundled notifications
Fix typo in SimpleHttpClient Javadoc
use processOutgoingPath instead of getJarFile
Allow abort in SslHandler error message
Sort the classes in the dex file
Hide usageToString for now
AbstractTreeStructureBase should use settings from project nodes
remove unused variable
Fix chat room search
Don t launch terminal emulator if already running
Added ListUtils swap
Don t create context for system apps
Fixed bug on soft closed cluster state
made some methods private
Add more constructors to KotlinReflectionNotSupportedError
add comment to SearchDialog
Add a setter for the response timeout
fix find in editor test
Activate backup service when device owner is reactivated
activate editor component on show
Remove unused method
fixed typo in javadoc
don t filter r as well as n
Fixed bug in MutableMethodImplementation
Reset mIsWpcomAccountWith2FA flag in onPreExecute
Compare contacts by display name and UID
Fixed log formatter
Fixed a bug in the output of theComplexCommand
Remove debug logging
don t lint documents when showing markers
Remove deprecated InputMethodSettings constructor
Improved javadoc for paging predicate
Fix parentTypes copy
Fix Gauge documentation
add version to xml output
Fixed PactRecord shift
Clarify the number of non constant predictor columns for checkpointed models
read only option settings
Always update kernel alarms as a backstop against missed wakeups
Update active input when report is powered on
Added Media Remote ID sample
Fix checkstyle issues
Fixed bug with options not being saved in data widget
Fix NPE in RealmProxyClassGenerator
Made MongoMappingCursor package private
don t generate transient fields
Add Nullable annotations to RecyclerView constructors
check file extension for state storage creation
remove debug print
Prevent pass code from being requested on rotations
Fix typo in Groovy shell output
Fixed bug on update of remote version
added test for Provider lookup
remove unnecessary call
Expose the reader and writer idle time in idle state handler
Fix NPE in RoutingResource setIntermediatePlaces
Fix NPE in RequestFutureTarget
Fix since tag
Fixed null intent
Fixed compilation error
Fix test bug
added integer support
Fix stupid test bug
do not remove changelist if it is read only
Removed unused field
Removed deprecated Alert reliability
Add sleep to DownloadIndexesThread
Remove unused code
Don t stop activity threads when sleeping
improved error message
Remove unused imports
reset processor on type change
Fixed wrong parent check
Add a convenience method to FileHandlerSpec
add javadocs to EntryProcessor
Fixed checkstyle issue
do not auto expand with scope
Fix off option in Indexer
Fix leak in PreferenceGroup
Rename onBeforeRediff to onSlowRediff
Add compression config to site main
remove unused method
Increased test timeout
Prevent NPE in PackageManagerService
Fixed logger name
Fixed bug in batch insertion
Don t set debug app for non persistent debug
Add sleep to FileSystemChangeWaiter
Fixed a bug where ValueAnimator wasn t resetting mPlayingBackwards
Added setName method to OClass
Fixed issue with sending empty messages
SCScrollPane setViewportView should set opaque false
Remove misleading comment
Removed unnecessary annotations
Add missing license header
do not log exceptions
Fixed NPE in DelimitedOutputFormat
fix upper bound detection in parametrized types
Fix missing newlines in display result
Log package change intent actions
added toString method to ToroDocument
Restore compatibility with demetra
check JavaSdkType not JavaSdk
Fix fold icon for IE
don t sort string types with more than one value per doc or more token per field
Remove unnecessary else clause
Mark new user as success
SFTP client should call close asynchronously
remove journal folder from scope
Don t set textDirection and textAlignment
Fixed a typo
Simplify the test
Add noResourceLeakDetection to heap dump
Fix quality flaws
avoid array copy if array size is equal
Turn off auto converting of Frame object in DKV
Remove obsolete javadoc
Make AvgRegistrator static
remove route recalculation workaround
Add throws annotations to SameKeyValueStoresTest
make find dialog dumb aware
remove redundant line
Remove unnecessary code
Fix wrong usage of AssetBundle load
Add a comment
Fixed a typo in LibsTitle
Add binding to Periodical
Remove redundant method
Fix exception handling in FilePermissionHandlerFactory
Fixed constructor of CheckBoxStyle
Updated documentation for active and dect
unregister user by id
Fix AirplaneMode E Call or whatever
Add a getDir method to ProviderTestCase2
removed unused code
Make FragmentActivity supportInvalidateOptionsMenu public
gutter highlighting fixed
send new issues even if there are no changes
Remove debug messages
Remove redundant finish calls
Add a couple of logging statements
Do not poll the transport
removed drop graph test
exclude static imports from completion
Added getEyeLocation to LivingEntity
add test for non null constraint column
Fix NPE in CustomRootLayout onKeyboardShowing
Remove unnecessary cast
Remove unused code
Fix a typo
fixed checkstyle test
Remove unused variables
Don t disable all tasks in dry run mode
Fix Javadoc error
Changed oil light opacity
Fix compilation error
Added Texture constructor that takes TextureAtlas
Fix typo in javadoc
add test for null and shorter identifiers
fixed CS error
Add a default constructor to ClientOptions
Remove unused random field from SlopPusherTest
Updated documentation of RealmMigration
don t commit transaction state loss on swapConversationFragment
Removed unnecessary generics
Fixed NPE in PyPackageManagerImpl
Add affiliation element name
Added isIdPRedirection check
Fix NPE when error message is null
Added some comments
Shutdown the scheduler in InstanceInfoReplicator stop
Reduced verbosity of ReceiverNotFoundEvent
Fix range validation for expressions
Fixed the CS error of camel blueprint
Remove redundant test case
Remove deprecated code
fixedCircularAdapter getCount
Only take external photo if av is enabled
change min max to TODO
refactoring fix unregistering actions
Add assert to AbstractCodecEmbedder handleEvent
Fix NPE in SqlQueryExecution
Handle empty strings in decodeArray
Fix NPE in getReadingLevelScale
Remove log message
Replace new String with empty string
Make some fields final
Add deprecated methods for testing
Disconnect the client after heartbeats
make buf and pos public
Fix NPE in ContextImpl
removed unused import
Added a reference to the JOGLAppletLauncher
removed unnecessary logging
remove redundant scale
Fix broken test
stripParentheses from throw statements
Reduce line stroke width
remove unused imports
Moved DB2DDLFormat to editors package
remove prostate csv from rebalance test
Fix LayoutLocalServiceUtil addLayout
Fixed bug in GifDecoder where the area of the frame was not restored to the background
Add missing documentation
add support for pre load beside nodes
Added some more logging to checkpoint tests
Fixed failing test
Remove a System out
removed unused imports
Switch Context TYPE to use Types token
Set the node ID when the cache service is shut down
Use SystemProperties instead of getString
Add Service Worker allowed header
Improve timeout message
Add missing parser call in CordovaWebViewTestActivity
Add support for file list
Fix test on windows
Remove unused method
Prevent rare disastrous classloading in first call to LockSupport
remove unused method
Remove unnecessary static modifier
Fix javadoc for setCompressionThreshold
Check the uid of the AudioManager before checking the policy
Fixed a bug where the escape character was not reset
Fixed test cases
Fixed member name check for interfaces
Fix LongDistancePathServiceTest to not use invalid state
Add ifModifiedSince helper
add null check to SafeEncoder encode
Fix main bug
Remove unused imports
Fix NPE in ServletContextHandler doStop
Fix TestExpressionCompiler equals
Fixed a typo
Search POI focus fix
Make static fields static
SMALLFIX Removed explicit argument type in PermissionCheckerTest
Fixed unit test
make NoYieldNoBackground abstract
use File pathSeparator
add multiple editors test
Added a backup method to BaseDatabaseDefinition
Added currentIndex method to BaseActions
ignored QueryIndexMigrationTest testIndexCleanupOnMigration
OBJ loader now catches a warning when normal meshes don t contain normals
Add backend to DL integration test
Add a TODO
Fix memory leak in AppCompiler
add unique id to filename format
added more logging to GraphSerializationLibrary
Removed unnecessary code
Remove unused imports
made some things final
fixed failing test
added support for EventSystemImpl
Remove debugging code
hide balloon on tap
Remove commented out code
Remove a System err println
Connection settings dialog fix
Fixed predicate pushdown to ignore null literals
Added ChineseConverter test
Make IconicsLayoutInflater package private
Added SuppressWarnings unused
Fix PeopleActivity activateOnItemClick
Add a new SafeTreeSet for tailSet
Removed unused method
Added getter for axis max and minimum values
Make createSend public
dispose the subject panel when the chat session is closed
Use the META INF_SERVICES constant
Remove unused Pattern instance
Trim username and password
Add missing annotation to opentsdb metrics export
Improved error message for missing description
resize editor on resize
Added borough CityType
Fixed wrong property names
Fixed the CS error of camel core
Suppress deprecation warnings in AbstractHistogram
do not process anonymous class declarations
fixed text color
Using Float compareTo instead of Float compareTo
Remove unnecessary code
Fix laser not initializing
Fix action type
Add a message toInitializationError
Use a SkyValueSupplier to prevent the progress receiver from being confidently accessible in memory
Added author tag
Fix callback id for plugins
Disable footer dividers for user list
Don t add childErrorInfos twice
Add missing Override annotations
Removed unnecessary ScaleInAnimationAdapter
fixed path generation
Fix typo in SonosBinding
removed assertion for read only elements
Don t animate screen brightness when pending screen is off
Fix color cutting
Add loggerName to CommonsLogger
added test for InsertWithKeys_Projected2
Fix NPE in HistoryGuru
Add EnumHand to TestItem
improve error message
Add check for null hostnameVerificationAlgorithm
Fix ListenableHideableDecorator to use SmartList
Make move exception check for UNKNOWN register types
Remove unnecessary static modifier
Fix comment formatting
Add null check
Fixed bug in timestamp handling
added license header
Removed unused method
Fix issue with deSERT biome
Added a little delay to make the presence priority test more reliable
Fix NPE in getPmString
Fix checkstyle error
Removed the default values for the debug color
Fixed a bug where notifications wouldn t be able to set their own
make addInspectionTool public
do not visit constant expressions
removed debug print
Added SuppressWarnings unchecked
Remove unnecessary argument order in CrshPropertiesTests
Fix error message
Fix vanilla handshake
fixed NPE in GrailsHibernateUtil
Fix R import
Removed System out println
Fix NativeString getBytes
Add toString to HttpTunnelAddress
Fix NPE in TelephonyManager constructor
Add missing copyright header
Fix off by one error in IndentingWriter
Renamed BufferUtils destroyByteBuffer
Fix NPE in BatchConfiguration
Fix typo in AprLifecycleListener
fixed test data
Removed unused code
improve error message when no groovy classpath is specified
Remove unnecessary mock
Fixed EntityAwareWorldProvider not cleaning up temporary entities
Added feed column to SEL_FI_EXTRA
Change default server to connectivitycheck android com
Fix SnackBar background color
Fix potential NPE
Set default theme for ActivityHelper
Don t show window status bar background for MATCH_PARENT windows
Added keyListener to MaterialDialog Builder
don t merge message if it s too long
Add comment to SocketIOClient
SMALLFIX Fix style
Remove the socket from the connections list after destroying it
Fixed a bug where Table constructor was not passing the context
Fixed the CS error of camel mock
Added loadstatus endpoint to master resource
Add a TODO for multiple unary transitions
Fix wifi copy constructor
don t load groovyc libraries from system dependent directories
Changed OneDayDecorator to use bold style instead of underline
Fixed bug in Cluster setTags
Removed unnecessary method
Fix quality flaws
Remove hard coded BitSet size
Fix Maven resource hashcode calculation
Add support for media screen and
Fixed a bug where NetworkModHolder checks the server side
Added resetValues method
Don t wrap MadvocException in MadvocException
avoid NPE in VClientImpl
add actionSystem MODULE_SETTINGS
Added support for onload attribute
add missing rename
Add transform to JsType toStringHelper
Added toString to RelationshipTypeHolder
Fixed a typo in MongoFind
removed unused import
don t change type signature for methods with only one element
add method to smooth close menu
Add HttpMethod condition to GzipFilterAutoConfiguration
Fix Qwerty Mode in ToolbarActionBar
Add new IcedHashMap types to TypeMap
Added fade methods
Remove unnecessary checks
Fix the build
Revert KMeansParameters _k default value
Remove unused variable
add minor notifications to GitVcs
Fix AnimatedRotateDrawable not inflating children
Remove unnecessary check
Fix typo in log message
preferred size for vert info panel
Allow preinitialized transformer chain in SpongeCoremod
Add a javadoc for the fairQueue option
Added test case for invalid configuration
get anything already at the console
Fix typo in error message
Only unlock accepting connections if it is needed
remove hardcoded openssl version
Handle unexpected error in legacy request thread manager
Don t throw an exception if we can t deserialize a message with a name
Remove unused field
Remove unused code
remove unused method
Implement equals and hashCode in BlockContainerIdGeneratorEntry
use collection instead of set
Make getAllExceptionTypes private
remove system exit
add deprecated param for chainPreflight
fixed the test case
Add FailedToCommitException to ElasticsearchException list
Catch exception when deserializing XML file
Remove completed TODOs
Changed LzmaDecompressor to accept a MemoryBuffer not a Buffer
Remove use of CollectionUtils
Allow the httpserver to be specified via command line parameter
added pin example
Change hash int to hash long
Add a simple string test for CompressedString
Fix DataNucleus templates
Make ContextName constants static
added thread name
Fix manifestFilename for plugins
Added adb path to android debug bridge
Improved message when a transaction is executed outside active transaction
tweak label for keep image aspect ratio
Fixed formatting of parentheses
Fix persist test
Add the externalizer id to the legacy global configuration
fixed NPE in ChaseCameraAppState
fixed NPE in example
Fix a comment in Fragment
Fixed bug in ProcessListUtil
Remove unused method
Add a timer to protobuf parser
Allow AbstractScheduledService to override the name of the thread
add extra space between editor component and toolbar
Deprecate unused code
Fix variable name
Disable strict mode for ConfigTO
Fix Bonded Device support
Added a scroll wheel to the vertical bar
Add the ImmutableSetTest to the suite
Improve exception message when source uri is not supported
Added a test to verify that a future navigation does not cause the driver to have problems
Fix widget deletion
do not initialize DirectoryIndex
Fixed NPE in DefaultManagementAgent
Fix del key bug
added Nullable annotation
Abort animation before pining scroll
Don t backPressed activity if activity is finishing
Remove mapred min split size from test store benchmark
Fixed concurrency problem
Remove unnecessary code
Make the constant private
Added sendToTarget to ShadowMessage
add add removeOnScrollListener methods
Fix RemoteTracebackFilter by using its own working directory
Add destroy and canReuseBitmap to BlurAlgorithm
Added test for MultiMap getNameAndType
don t set text color for hyperlinks
Remove unnecessary method
count acknowledged cluster state when nothing changed
Fixed cast exception in PinnedSectionListView
Use the default storage runtime for the runtime attribute
don t highlight identifiers under caret
removed unused prefix cache
add missing copyright header
fixed xml resolution for absolute xml files
Fix bug in path handling
don t highlight style and script cells
Make Response final
Updated MeshData javadoc
Make UnknownParameterException package private
Prevents updates on chunk generation
Fix Attribute html rendering
force lazy creation of default functions
Removed debug code
Remove unnecessary code
fixed stencil test
Don t snapshot libraries
Fix HttpConnection toString
Remove hide tags
Add javadoc for getDefaultBlockSize
Fixed some Javadoc errors
Make isSingleton true
Fixed formatting issue
Set isSurfaceCreated false on surface change
update the crate snapshot version
Fixed the CS error of camel spring
Clarify syntax errors inANTLRErrorListener
Change default model name
add a titled border to the log pane
update demo for setRecylerViewBackgroundColor
Fix a bug where the text field was not reset properly
Throw IllegalStateException if Broadcaster already existing
make linked list link weak
Fix RippleDrawable mask position
Fix missing import
Include exception in AssertionFailure
Improved documentation of IntegrationTest
Make fields private
Fix typo in StatsAggregator
Fix memory leak in WordCountTest
Increase the timeout for the client to start
set versionCode in manifest update mojo
always show plus for packages
Remove unnecessary private modifier
update expected test results
Added terminate method to SchedulerService
JetPackageDirective implements JetExpression
Set the thread pool size
Add missing javadoc
Fix SIP timeout
Remove unused field
add since tag
Fix SkyKey hashCode
add shiraji to copyright
Remove the old button from the call participant panel
Add getUserData to Joint
Fix SearchPOIActivity to use getOrientation
Add SourceHttpMessageConverter to RestTemplate
hide explain script sql property
Get the application data directory
Addxmlnsxmlns for XMLSchema instance
Don t fail the test if an exception is thrown
Fix race condition in idle state handler
Make cached stream output bytes and count limit public
Fix NPE in AutoCompleteTextView
Removed unnecessary assertion
Fix compilation error in IndexAliasesService
Adjusted wait time in MediaNames java
Add simple servlet for use by test cases
remove unnecessary try catch
remove invalid check
Fix compilation error
added more debug logging
Remove outdated comment
Removed call site check from property definition inspection
Fixed formatting of test methods
Add HTTP_POST_PROCESSOR to BeanIds
Fix a typo in the Stats class
Fix drawRect to account for extra space
SQLTransportExecutor should catch PSQLException
Fix typo in PartitionServiceSafetyCheckTest
Added support for custom selection drawable
Remove use of FluentIterable on
Ignores a test while I debug it
Added else clause to JsonHttpResponseHandler
fetch ext resource fix for absolute urls
Fix quality flaw
Removed unused code
Added a new IOConverter method
use the same term as the reader context
fixed compilation error
Fixed Group touchUp
add deprecated methods to RemoteSdkCredentialsProducer
Removed unnecessary logs
Fix road route service
Add Disruptor publishEvent EventTranslatorOneArg
correctly handle spaces in parameter names
commented the test code
Use the default catalog when previewing tables
Add missing methods to CacheEvictionConfig
Make getListView public
do not print stack trace for internal exceptions
Make PROFILES_PATH public
Remove extra space
Add TODO for commitBlock
Add DefaultJaxrsModule to config
Allow null or empty indexes to be passed as default name
Removed unused import
Fix weighted area calculation
Use constant for ALLOW_ALL_ACCESS_CONTROL
Fix NPE in MUCRoomImpl
use getContext instead of new ReplayPosition constructor
Add a convenience method to AnimationController
Add enum values for producer
Added a TODO
removed unused import
Remove unnecessary super calls
Added DataPointsMustBeStatic test
Move PojoRouteTest to component
Fix closed caption action id
Don t run Auth setup for AllowAllAuthenticator
Skip the cast for a boolean literal
Fix shadowOf method to return ShadowExpandableListView
Avoid NPE in unregisterAuthenticatedSession
Renamed select String query to select String cssQuery
Added a unit test for Jaxp
Added missing license header
Make tryToExtractPackageNameFromManifest static
Fix Javadoc error
Fix logic error in ContextConfig
Add missing property for default virtual server
do not preserve presentable name
Make MemoryPoolId final
receive pending wallets when we re connected
Fix ConcurrentModificationException in InitialDeploymentTracker
ValidatorParameterHelper anyType should return any type requirement
Fix missing translation
Don t add a script node to the synthetic block
Remove unnecessary code
Add a succeed method to TxFuturePair
Added a message to the rebase problem detector
Fix Android tests
Fix error message
Use threaded Trainer
Removed unused method
added a constructor that takes a classloader
Add logging for mod cluster node aliases
Fixed checkstyle issue
Make selector box transparent
Use the IQ s from field instead of the initiator field
Add delay to PacketWriter s heartbeat delay
Add missing license header
add title to info message
Fix quality flaw
vcs log add NotNull
Fix NPE in sherpafy
Fixing a few typos
if we found a regular plugin xml in the same jar root as a platform prefixed descriptor use the core loader
Remove unused method
Fix a bit mask typo
Fix background color
do not add annotation twice
Fix bad format string
Added javadoc forIndexedSet size
Fix NPE in PhoneWindowManager
Don t generate submodules by default
fixed html highlighting
Fix NPE in ShapeDrawable
Fix the build
Trim url before adding to it
Remove unused import
Fixing the build
Remove unused imports
fixed typo in test
remove unused startup bean
commented out debug output
remove unused field
Optimize AbstractBinaryMemcacheEncoder encodeMessage
Fixed bug in template rendering client error handler
Fix reset of RobolectricBase
add option to watch command
Fix unresolved issue per assignee path
Send statistics for shows
Added test for AnnotationUtils getClassAnnotation
Fix tinyPacketHandler default value
removed a System out
Fixed bug inOuterReferenceFixer visitClassInstanceCreation
Hide system ui flag when dismissing transient status bar
Expose markAsUnspent methods
replace TimeoutException with OverloadedException
Remove unused imports
Fix GeofenceHardwareImpl for FLP HAL
select chunk output widget
Added test for contains with inexistence
Fixed a bug where the text could overlap with other text
Removed unused continuousCurrentModel
Remove unused method
add constructor that takes a VcsConnectionProblem
Make instance volatile
Fix NPE in ConstructorUtils
Don t call refreshSharesForFolder if we are not sync full account
updated the javadoc
Added more trace logging to DefaultPackageScanClassResolver
Fixed JUnitRuleTest to use contains instead of startsWith
added more logging to the example
Fixed a typo
Added TileUpdate getAuxillaryInfoPacket
fixed navigatable navigation
Remove unnecessary private modifier
Added sand to OreDictionary
Suppress deprecation warning in RecyclerViewOnScrollEventDistributor
Add default values to Config java
Set default frame buffer name to display
Added tracking for self hosted WPCom
Don t show packrat init box if packrat is not available
Simplify the logic
Add a default ID property generator to the model spec
remove unused logger
Added missing import
reduced log level for starting dumb mode
Catch ISE exception in NewUserFragment
replace assertTrue with error
Added documentation to AuthenticationSuccessEvent
Fixed NullPointerException in ExtensionSpider
SourceFormatter Source formatting
fixed lfw path
Removed unnecessary toUpperCase call
Add no tls values for JNLP
do not show inherited JDK for java sdks
Remove unused imports
Fix model combiner
Log the base URL when ignoring URIs
Remove unused imports
fixed count check
remove unused method
Fixed typo in javadoc
Added varargs constructor to ClassPath
Fix NPE in JarHell
Fix settings dialog icon
Fix typo in SettingsModule javadoc
Move field to the right place
Remove obsoleteobf from inspector
Remove unnecessary code
Improved javadoc for setParentLoaderPriority
Fix path for EPISODE
Removed unused code
Use the CodeSigners field in VFSResourceLoader
do not use default constructor for inner classes
Don t warn about simple var assignments
Set the entity link from the tokens
Removed debug output
Avoid putting transient state views during a data change
Addisms to binder list
android add updateFinish method
Remove unnecessary assignment
Added index for location index
Omit field for updateAssociationsNode
Improved javadoc for SubscriptionMessageFlyweight
Remove unnecessary unboxing
Fix typo in Javadoc
Fix default jruby bundle path
add groovy scripting to list of supported features
Added a note about use of CamelContext
Set the default value for the replicateOnWrite option to false
Fix NPE in registerParentsIfMissing
Added a test for invalid bus elements
Fix TraversalRequirements canTraverse
Fix NPE in AccountSetupBasics
android update weex java
dispose parameters dialog
Fixed potential NPE
reduced log level
removed unused import
Don t use ConcurrentHashMap in TreeSnapshotter
Add missing license header
Catch exception in onNewAlbumCreated
removeINFRASTRUCTURE from DaoAuthenticationProvider
unbind item view
Added a note about blocking calls to RestPreProcessor
Fix parsing of adb device IP
Removed unused import
Fix HIST query property name
Remove double single quotes in output names
Deprecated getlasterror command
Fix typo in javadoc
Suppress serialization warning
Make ChannelUpgradeHandler addProtocol public
Improve error handling in Http11InputBuffer
enable javascript in theme preview
remove hard coded values
Fix maxWaitQueueSize calculation
HttpBindServlet sets content length
Add share button to custom tabs intent
add missing throws declaration
throw UnsupportedOperationException instead of break
do not activate tool before execution
Update the clip rect when a child is added to the layout
don t show accessors for synthetic methods
Fixed the route state command to use the route context to get the route status
Add Content Length header to LocalOnlyOkHttpDownloader
Ensure the broker is instantiated before starting as slave
Remove unused variable
Fix NPE in BasicFeatureFactory
Fix typo in TextUtils
fixed bug with overlay masking
Remove unused code
Add a test for PathBuilderValidator
Clear the pm metadata when we get back to us
Remove debug output
removed empty line
reset WelcomeFrame instance on dispose
update search view after adding search
remove extensions from parsing
Fix license regexp
Fix powered tile check
remove unused import
Renamed tv_name to nameTextView
Added missing fields to DB
add setReadOnly method
Fix NPE in NettyConfiguration
Remove dead code in IndexTask
Remove extraneous comma
Remove unnecessary code
add expire and clean interval to debug logging
AbstractMarshaller uses DocumentBuilder rather than createDocumentBuilder
TEST fix SSTableRewriterTest
Call dismiss from send
Updated the javadoc for Project convertToOutput
Fix memory size computation for NepheleMiniCluster
Add ProcessTerminatedListener to SMTestRunningState
Fix a bug in containsKey
try to select post when clicking on list
Added the egg field to the PlayerEggThrowEvent
Make HTTPLoggingDocIT extend ExclusiveServerTestBase
add method to get const value for attribute
remove bike bike test
Fixed the build
fixed failing unit test
Fix double multiplication of strings
Fix a bug in the CSSCompressor
Fix onMediaButtonEvent in MediaSessionCompat
Using Boolean parseBoolean to avoid boxing
Make TrustedCertificateStore look in the right place for CA certificates
Updated IPC exceptions
Added missing annotations to ClockPropertiesTest
Fix javadoc warning
Add ClusterScope annotation to CancelTests
Remove commented out code
Fix typo in Project javadoc
Fix dangerous restrictions
Make _parseName final
don t show recent project selection if there are only one selected index
Add default value to SeekBarPreference
Fixed bug on removing user
log Fix white border in DetailsPanel
Remove GNU shared library
don t show default profiles
Remove Revision tag from HybridSourceDataBinding
add offset and len to ProtobufConverter
Make Pool constructor public
allow drag to move tabs
do not perform dumb aware actions
Make MixinBiomeGenBaseForge abstract
Remove redundant annotation
fixed bug on create sequence
Add USER to Oracle table names
Remove unused error message
Fixed flaky test
Add convenience constructor with beanClassName
Fix checkstyle issues
Fix quality flaws
do not run side effect checker for simple getters
Fix crop image bounds calculation
Add cluster name to JmxService store name
Remove DL subframe for now
git set PlatformLangXml property
Rename field to setField
Add support for KeyV3
Log an error when a passive scan rule does not have a plugin ID
Don t swallow NoSuchTargetException
fixed toggle enable status
Add initShaders to TestAppRS
Removed unused field
Add scrollX Y values to setMoveEvent
Fix HasParentQueryBuilder javadoc
Allow comments in mssql
Fix BackupManagerService crash due to null token
Fix code style
Fixed typo in exception message
Add missing import
Add the ability to dispatch the managed AtmosphereResource
Add a couple of missing default values for the NullVM runtime
Fix typo in JavaDoc
Updated RealmList javadoc
Remove unnecessary code
remove unused code
Ignore flaky test
Added some comments to InjectorImpl
register inspection fix
Add missing documentation
Remove unused method
remove unused constructors
Fix type binding name generation
Remove an errant println
Fix bug in VizGui
Remove unnecessary close
Remove unused code
SourceFormatter Source formatting
Remove unnecessary code
set minimum size
Avoid circular dependency
Fix kotlinc lib alt for tests
Remove unnecessary disableInputCopy
Fix bug in ClassNodeLoader
Fix default navigation menu icon color
Add RunWith annotation to AutoWindowSizingOnTest
made getHandler public for Kotlin
Fix typo in TransactionBroadcast logging
simplified the test
make QualifiedMode serializable
Fix typo in mathutils
Fix broken build
Fix formatting of Flac
Fix broken link to package summary html
Fix FileNormaliser for Windows
don t request focus in single inspection view
Fixed a bug where the last search wouldn t work
Remove the hashcode method from ProtocolProviderService
TEST Add LoggingListener to ElasticsearchLuceneTestCase
Add missing license header
added reset method to MoveToAction
add ChineseWordRepeatBeginningRule toRelevantRules
LinkedHashMapTest uses correct Assert class
Fixed cloning of Letters
Added getImageURL to NetworkImageView
create child directories for libraries
Fixed a bug in the calculation of refX Y
Fixed a bug where the notification panel could be too small
Fix bug in KernelTransactionImplementation
add node messages to default click function
enable find usages for variables
don t wrap documentation providers if there are too many
Fix a bug in findDrawableByLayerId
Remove extra line
Remove redundant conditional
Updated upgrade message
Improved message on error
Remove unused constants
Removing hardcoded magic link
Changed default min prefs per user
Fixed test on Windows
Added isConnected method to ApiClientImplementation
Fix a bug where RippleDrawable maskBuffer was not recycled
remove dependency on VfsUtil
add margin before the back button
fixed test cases
Fix a bug where SqlTaskExecution can not finish task output
Don t create context for unpackWARs
Fix quality flaws
remove debug code
eliminate eclipse warnings
Added byte byte to Kryo
Fixed wrong type parameter
deprecate getParentByTree and getParentByTree
Added GeometryUtils fromBarycoord
Fix registry get
Set StatsConfig Fast to false
Remove unused method
Fix line length
Add a property to disable installing security manager
added bound check
Clarify PrimaryKey javadoc
Remove unused catch block
Fix quality flaws
Removed finalizer from DBPort
Fix interface add criteria
Fix import of MoreObjects
Add explanation for absolute
Use the correct command to check if a process is running
add resetBranchesCache method
Fixed a bug with clicking the first item in the adapter view
remove lombok annotations
Remove unused Request getAvailable
Skip HUN check for filtered out notifications
Remove unnecessary sleep
Add setCertificateRaw native method
Fix line separators in assert message
Remove period from log message
Allow null NBT names
Fixed issue with release of change log lock
Handle empty password correctly
Add a reference to AnnotatedElement annotations
Fix NPE in BdbStorageEngine
Handle alternative content type in pdu
Fix config slurper content provider
Fixes a bug in the SIP media handler
Fix index out of bounds exception
Disable analytics test for now
disable binary inspections
don t compile REPLAC statements
don t show up in desktop mode
Display search in actionDisplaySearch
use new distance comparator
added PathString and PathComparable
Compute term offset based on termLength not termLengthMask
removed a System out
Changed Kryo to use the same buffer as Kryo does
Cancel long press on TOUCH
Added a toString method to Rectangle
turn off debug logging
Fixed a bug where the drag would not close to left
Fix quality flaw
Fix nextval template
Fix line length
Don t start activity if no uid is set
don t exit application on Mac
don t add null buttons to the panel
Don t flush the error message if the foreground is off
Don t display password in toString
Fix crash in DownloadsUtil
Handle negative sync frequency
de add main method
add comments to json serializable interface
Make value default to empty string
remove unused imports
Refresh the database once all the library type handlers are registered
Don t show ringer mode for lower volume
fix icon evaluation
Fix JMX test
Add worker IDDefiner class
Include the exception in the exception
Remove unnecessary check
added OR operator to binary operator map
Add test for Capsule move
Add constant for file modification resolution
add Vec toTwoDimTable
don t use xIncludeAware and namespaceAware
Add CLOB and BLOB to the list of column types
Log authentication errors
Do not respond if we get a header position by accident
Do not load indicator in edit mode
try to create directory
Adding thread safety annotations to TachyonConf
Refined liquid colors
Improved error message
Add NoBatooJPA annotation
Revert Disallow nick changes on join
Unwrap JavaFileObject in inferBinaryName
remove debug output
Fix parser creation
Fix Python SDK tests
Remove zipalign from sign apk progress message
Don t create custom typeface in CondensedTextView in edit mode
Remove unnecessary code
Fix PAN connect error
don t add zero flush counter
Fix ClassCastException in Bundle setTaskDependsOn
Fix BuildCompat isAtLeastN
Turn off load balancing for small datasets
Add SVG support to FolderTypeRelationship
make field protected
add missing import
get content from gzipped entity
Fix bug in FilteredBlock getTransactionCount
Add JavaDoc to MediaCodecTrackRenderer
Fixed a couple of compiler warnings
Remove outdated javadoc
add validation check to roundtrip routing template
remove unused code
Add VIEWERS_TABLE to deletePeopleExceptForFirstPage
don t throw MissingMethodException
Flush the output stream
Fix potential NPE
Add Intent to show Daydream settings
Don t print a stack trace for client lost connection
Removed unused variable
Remove unused elasticSearchProperties from AirpalConfiguration
Remove unused JsConsole thePrototypeInstance
Set the checked state on the AccessibilityNodeInfo
Remove unnecessary variables
Fix StrictMathTest on ARM
Switch JulDebugger to JulDebugger
add test for recursive mapping
Add null check to isDateToday
Don t reset the accessory mode request time
don t wait for axolotl message to be sent
Add more icons test
Allow the body of TypeScript namespace to be a statement parent
Suppress deprecation warning
Fixed driver instance count in remote source plan
don t show password prompt for disabled users
Fix server pick
Fix TextIconGenerator to use correct style
Don t call onContextItemSelected when view pager is selected
Fix typo in Configuration getEntityStateListeners
Add AnimControl clearChannels
Remove NotNull annotation
remove unnecessary cast
Fix location option for TitanHadoop
Fix account list
Improved the example
Added TabListener to FeatureToggles
git remove outdated comment
remove obsolete code
Add check for battery report length
remove toString from DetailAST
library Remove unused variable
add comment to hasLayoutManagerPermission
Fixed bug on export and import database
Close the processor registry in ingest service
Fix PDF export
Add management element to the stream
Fix TestStandardContextResources so that context won t call init
Use a couple of seconds instead of a maximum
Fix test on windows
added UNDEFINED constant
Do not parse EL expressions for libraries that are not ELIgnored
update dex method counts version
Fix zip inspector test
Remove unused comments
Remove unused KeyguardManager reference
Sort Pager items by position
Fix javadoc warning
Fixed since tags
Improved error message
Remove unused code
Annotate shutdown with PreDestroy
Fixed the test error of camel management
Don t allow Tomcat running as a traditional container
removed unnecessary log message
Fix checkstyle errors
pt fix checkstyle
Add createRenderScript to RSSurfaceView
Fix NPE in InputManagerService
made getStream public
Move type check before each test
Add missing imports
Use a Pattern to split strings
Fix bitmap caching in VectorDrawable
Add documentation to XContentParser
Don t log empty messages
Don t count downloaded files for hillshade activities
Remove unused method
Fixed a bug where the list wouldn t be in the choice mode
Remove unnecessary subscription in test
remove unused code
Using Calendar instead of DateTime
Set default activity resize mode to RESIZE_MODE_RESIZEABLE
Fixed Config equals check
Make custom factories final
Fix wallet balance display
Remove unused method
Remove Shell UID from list of process UIDs
Fix typo in EXTRA_USER
Remove redundant type parameter
Changed MockWebServer to use localhost
Fix ActionNamesBackwardsCompatibilityTest after merging
Add documentation for managed intents
Fix permission check
Fixed a broken test
Added requestLayout and didRequestLayout to ShadowView
Changed log level to debug
Fix SctpInboundByteStreamHandler acceptInboundMessage
Don t show progress footer when activity is removed
Fix Recent Catalog list
Require credential for keyguard
Fix opentripplanner webapp path
Fix a bug where the invalidation flag was not being applied
Fixed a bug where the folder list wouldn t refresh the title
Adds a copy of the subgroups to the Jabber implementation
android update WXComponentRegistry java
Fix memory leak in AsyncHttpClient
Improved documentation of TachyonException
Fix output path for generated classes
use realLesson getName instead of hardcoded lessonId
Fix issue with dynamic graph statistics
accept digits in the name
add TODO for tesellator code
Fixed a bug where TripTimeSubsetCache was not created
Remove unnecessary code
Close the imageview when it s destroyed
Fix NPE in FakeFile
Fix crash when calling onChildrenLoaded with no options
Move mSystem to where it is used
Added getter for IdGraph s IdFactory
Added toString method to MongoClientOptions
Use the tree argument version of forName to initialize a class
add missing copyright header
Fix FileStorageService createParents
remove actual score from scored
disable change type signature action for non psi types
Fix race condition in LogbackHelper
Add fail to assume
Add fileKEEP_IN_SYNC to content provider table
Don t animate fragments that are hidden
Add debug flag to disassembleDexFile
Temporarily ignore failing test
Fix race condition in ZenModeController
Removing unused method
Catch NPE in BouncyCastle
Beeline should not force the console output
OSS Under FileSystem should return empty group owner
Replace HashSet with Lists newArrayList
Don t create the logger if it doesn t exist
dispose tests in a separate thread
Added MediaPlayer setOnCompletionListener
do not generate custom button groups
Fix tank not filling liquid containers
Remove unused imports
Add null check for video url
Added blocking mode for server sockets
Fix typo in documentation
Added setFlip boolean
Adding missing constants to SiteSettingsInterface
interrupt the stopper thread if it s too long
Fix bug in DataBufferUtils onNext
Remove unused import
remove debug log
Fix flaky test
Fix multipart support
Add a newline to the collapsed dependencies output
Check the correct uid for start stopLockTaskMode
add subscription id to log message
Add a findAddress method that takes a case insensitive search
TEST Enable trace logging for testIndexWithFewDocuments
Fix PageModelExtractor initialization
Remove unnecessary return tag
Fixed a bug where HEAD response body would not be included in a entity header
Fixing the build
DO NOT MERGE Fix RecyclerView touch handling
removed toString from AbstractSqlStatement
Removed dead code
Remove unnecessary call
Removed unused constructor
destroy the process
Added test for verifying that truststore jks is loaded
Fix setting of batched scan settings
Added a System out
Fixed a problem with camel management
Fix crash in Keyguard
Remove unused parameter
Remove TODO from SpeakerInfo
Added Tree getOverObject
Add comma to param value regex
fixed possible NPE
Reduce number of partitions in ParallelTransitiveClosureTest
Remove extraneous logging
do not include builder name in message
Cancel auto focus when picture is taken
don t resolve reference expressions
Add a hashCode method to PeerAddress
Rename SslConnector to NonSslConnector
Fix base style lookup
Fix SlidingPaneLayout right position calculation
Added missing methods to IntentBuilder
add test for NearCacheConfig setEvictionConfig
made validateNode protected
Fixed a bug in the test
Added a TODO
Fix OilPopulate to allow deposit biome
set content type to application json
fixed generic signature
Fixed typos in GetLineageInfoListOptionsTest
Fix NPE in FileDirContext
Remove unnecessary methods from BaseCheckTestSupport
add missing commit
added support for nested commands
don t register constraints for void return types
Fix crash in ProfileActivity onUserInfoUpdatedEvent
Add isSupporterChannel helper
improved error message
Fix quality flaws
Remove sleep from Wi Fi tethering test
Added a comment about JmeCloneable
Fixed issue with due date display
Print out the battery stats
cancel deploy button on connect
Updated default time zone
TableWithProgress calculateBounds should use container get visibleRect
Add missing license header
remove unused imports
Fix a typo in TfsShell
don t get the notification twice
Remove unnecessary null check
Do not set cross context
Do not highlight super and this as a keyword
remove obsolete comment
Updated upgrade message
Fix NPE in TermSession finish
AddJAX History Type to search results
set font size when preSENTation mode is enabled
remove debug code
Fix typo in QueryState
Fix checkstyle issue
ImmutableListConverter convert should return an empty list instead of null
Removed unused method
added extensions jar to classpath
Added AbstractOneDReader decodeRow int BitArray row Hashtable
add the footerView before the setAdapter
Don t render POJO code if we can t figure out the answer too big
Added javadocs to TransactionalTask
ignore non physical variables
Removed a System out
Replaced Hashtable with HashMap
Renamed test method
Add usage message when classes is empty
handle new cluster state from a node that is not part of the cluster
increased interval in file moco watcher
Fix AndroidNeverlinkAspect builder
Add compatibility methods to ShareUtils
use aggregate instead of fold
Add notNull check to IntentAssert
Add a method to set the page cache size
corrected missing parenthesis
Fix rollup time reporting
Hide new posts bar when refreshing posts list
help id for new project wizard
OpenSSL deviates from the DER spec
Add top SLEEPING to ensureForegroundActivity
Add FunctionInfo accessor
Improved message on low memory threshold notification
Set notNull default to false
Remove unused class
Fix console scroll position on typing without this
Add toString to IntRange
Add missing license header
Reuse cached graphs from old analyst worker
Added additional error message to serial module
added operation headers to parsed interface query
Don t include GELF syntax underscore in message field key
Change the documentation link to rules md
Don t include rowid properties in CREATE TABLE statement
Fix log message
Fix constant branch pruner
Flushes the output buffer before executing the command
remove extra space
Added getName to HumanEntity
Improved chat room visibility check
Suppress warnings in SQLEvalVisitorUtils
Java Remove unused method
make constant public
removed unnecessary Component annotation
Updated versions for release
fixed NPE in introduceParameter
library Remove unnecessary enum
Remove unused SkylarkCallable
remove zero rarely print
Flush existing data in the transaction log to reduce file size of the database
Bump touch priority for RecentsView
reverted the pullUpdates change
Add missing org testng annotations
Remove static modifier from field
Don t destroy dim layer state on user removal
Fix synchronization in ThreadPools
Remove unused method
Remove outdated comment
Fix quality flaws
FilterAdapter setSelection doesn t always return null
Fix logger creation for proxy servlets
Improve notification text
don t clear found component
Fix high level filter response
Log the buffer that we failed to parse
Remove unused imports
Move the heartbeat check to the right place
Add Fluent to WebSocketBase
validate config before creating panes
Use the correct attribute name
Remove unnecessary cast
Removed unnecessary call of callback
Add a setter for the constructorExpression
Remove unused UID field from DragState
Fix a bug in NetworkUtils
Make inner classes static
Skip test that does not work
Fixed Object3D cloneChildren
Added setToolbarClickListener method
Added a log message when a single update request is cancelled
Make YearInfo cache transient
create output directory before unpacking
Removed unused getJSONType method
Added a method to check if a string has brackets and quotes
Fix roster creation
Adding JsonIgnore annotations to Node methods
fixed MySqlValidConnectionChecker to use pingInternal method
add default folder to local terminal
Add sleep to battery drain
do not append coverage argument for debugging runner data
sort compactions by generation when there s a tie
Added ability to set script parameters
simplify NAK handling
Fixed a javadoc
Add thread safety annotations to CompilerDaemonClient
Rename FilterAndProjectOperator to ScanFilterAndProjectOperator
Added toString to MyModifierSet
add inspection color settings page
Stop preview when video recording is supported
Fix bug in DataInputStream read
Changed return type of siblingIndex to int
Make onDestroy public
RedisConnection should return failed Future if Redisson is shutdown
fixed failing test
Fix assertSearchHits in GroovyScriptTests
Throw exception if the file does not exist
Use isMicrophoneMuted instead of isStreamActive
fixed SimpleReplaceRuleTest testWrongWords
Changed magic link button to WPTextView
Add ShadowActivity clickMenuItem int
Add documentation to IfExistsNode
Removed unnecessary waitForIdleSync
Get the invoked business interface from the ComponentViewInstance
Added serverIsAtLeastVersion method to TestBase
Fix leak in TimePickerDelegate
Temporarily disable cross profile caller id check
Updated version number
Fixed bug in TableParam where it was not creating the table
Add test for HystrixRequestCache without HystrixRequestContext
Fix message class initialization
allow one way only one is relevant
Fix column spacing for sticky headers grid
Fix a typo in the TSV sentence iterator test
Clamp gradient rect to square if it is too small
Add setSecurityHandler method
Add helper method to get RestClientUtils
Improve the error message when a daemon request is sent to the daemon
Add AtomView test to PetClinicSuiteTests
Fixed bug in unshare operation
Removed unused methods
Call super stop
Pass the config to EmbeddedGraphDatabase
Set locale for command line
Fix MockMvcRequestBuilders asyncDispatch
Added workaround for incorrect TO attribute in IQAuthHandler
DataSources should have data source capabilities
do not show Django modules in module type description
Remove unused import
Fix LogEvent eventsArePair
restore tip text
Added PyFileEvaluator getVisitedFiles
Catch NameNotFoundException support info
Fix test failure
Fix Customer toString
Fix CFBundleIdentifier NPE
Add a function to select a word at the last click point
Added width and height to images that aren t wider than the display
switching account resource if it has a conflict
Removed unused code
Added more tests
Fixed compilation error
Reduce log level for acquire lock
do not assert write access
Resolve the toast gravity using the Locale
Remove unnecessary processDoNotAskOnCancel
Removed debug output
Removed author tag
Fixed author tag
Removed unused variable
Add a comment about using inRange
don t clone HTML_CODE attribute
disable fractional metrics on mac
Remove use of singleton list
Add test server info to TestingConnectorFactoryContext
don t delete elements that are not the end of the document
Fixed bug on OCFile getParentRemotePath
Send broadcasts before boot
add getXmlElement to AbstractConvertContext
Fix offset calculation
Removed unused method
Updated javadocs for RealmQuery
Added ChannelPipelineCoverageONE to MemcachedDecoder
Fixed bug in JobGraph
Show toast on ui thread
Fix voice search
don t wrap around checkbox
don t use Status fromStatusCode
do not analyze files in test sources
Fix NPE in GrizzlyResponse getResponseBodyAsBytes
Add filter to query phase
remove stale TODOs
Remove unused param in MasterBase
Do not show volume bar when Hdmi CEC is on
Fixed bug where wasPlaying was always true
reset key state on second key press
Added hint for findbugs
fixed NPE in PsiUtil
do not resolve external resources if catalog is disabled
Fixed a bug where notifications could overlap over the content
Added missing dot
Set user agent to wp android native
add trace logging
do not log PCE
do not show global inspections for non project files
add way names to osm
Remove unused ClusterService field
Fixed the CS error of camel component
do not print cluster status if no arguments
Fix unit test
Improved javadoc for SmackDebugger
Added documentation for Visitor pattern
don t save local changes if there are no updaters
remove unused EnvironmentExecutionTestListener
Add setDefaultLocale and getDefaultLocale to Layoutlib
Fix TURN_ON_CONTINUE state
Make ResultTransport a static inner class
Fix osmand location
disable failing test
Throw IAE on bad task icon filename
Fix a bug in BlockMasterSync
Fixed a typo in Node invalidateUpdateList
Added check for null data file block
Fix NPE in CustomScanDialog
Fix content overlay gravity
removed unused code
Remove unused code
Renamed updateDrawerList to updateNavigationDrawer
added call to clearJob
Make configForm and generalScrollPane protected
Removed unnecessary code
Disable ImageIO cache for now
Relaxed performance test
add new JBInsetsUIResource
don t show file presentation for files
Remove unused import
removed unused import
fixed checkstyle issue
Make ClassBodyWriter package private
clear pending class change events in finally block
Fixed a bug in MockBlock
Added missing javadoc
Remove FrameUtils MissingInserter after use
Add some canonical names for PowerManager and WakeLock
Changed ListAdapterWithProgress getData to return an ArrayList
Fixed a bug where the list of notes could not be loaded
Remove an empty line
Make the server environment variable public
Fix disable winp
don t report errors in TypeUtils makeUnsubstitutedType
Enabled the ChangeDetector by default
Add TODO for attachBinariesToAssembleLifecycle
Remove a System out
Removed unnecessary private modifier
PlainStreetEdge has length
Added generic type
Restlet headers should not include org restlet
Fix the max value of the counter
Add SearchView to HeaderGridView
Add a couple of TODOs
Add remove Object
Fix a typo
Fixed a checkstyle error
Fixed a bug where discovery registry was fetched successfully
Fixed logging statement
Allow AJP APR connectors to support SSL
Ignore interfaces in classpath jaxb scanning
remove unused variable
Do not load bitmaps if cache is recycled
Added missing c tag
Fixed table names
Added check for ios moe backend location
remove debug log
Exhibit keyguard behavior for status bar
Commented out failing test
Removed unnecessary use of reusedSameRecord true
remove file from mru list on server error
Fix NPE in PhotoPickerFragment onDetach
Remove unused code
Fix DexFile creation
Handle onSyncCanceled in FileSyncAdapter
Adds INACTIVE media direction
Send an error event to the client
Fix comment in AbstractHistogram
Don t log a warning if the size of a View is greater than the maximum drawing cache size
fixed MultiValuesMap remove
Fix assertion in TransportReplicationAction
Fix memory leak in DeepWater
Propagate InputMismatchException to the recognizer
Fixed bug on version attribute
don t add divider if there are no custom expressions
Fixed token handler test
Fix integration tests for plugins
Add a couple of null checks to ThrowableLogEvent
Fix quality flaws
Added protected method to SelectorManager
use com liferay util dao hibernate CustomSQLUtil Hypersonic
Fix typo in documentation
Deprecate ConfigurationProperties locations
Added some logging to client socket stats
Fix unit test
Set action bar title
Removed unnecessary calls to onUpdateTextureCoordinates
Removed AJAR from enum
Fix typo in javadoc
fixed method name
added TaalTik and Taaltiknl to Dutch
Added missing license header
ditch extended type detection
removed toString method from AuditEvent
Added some javadocs
fixed checkstyle issue
Improve the copyright notice for ImmutableText
remove assertion in AbstractSuppressByNoInspectionCommentFix
don t wrap empty lines
do not use PsiFileImpl derefStub
don t inject html scripts in injected languages
Set up the communication channel after the scheduled job and the sync manager
Make static fields private
add comment to abstract class
fix router list
Remove unused constant
Fix wrong import
Fix quality flaws
Removed unused field
Added exception handling to BaseBitmapTextureAtlasSourceDecorator
Don t cover GETELEMs with a global root node
Fix a bug in TCPNetworkConnection
rename startChat to openChat
Fixed typo in comment
Added a way to get a date from the server
make makeLongMap return type BTreeMap
Fix UITextWrap bug
Added some documentation
Add electionTimeout and heartbeatInterval to RaftInstanceBuilder
do not move empty lines
do not load commits from history
Fix test cases
Added setProperties method
added test for description
Fix NPE in NetworkImageView
Fix session creation
Fix MixinEnchantment canApplyAtEnchantingTable
Added field to signature
Fix unit test
Fix Windows test failure on commitlog on segment deletion
Remove unnecessary isCancelled check
Added a label to the comment status enum
added additional information to TypeConverterValidator
Removed unused method
Fix unit test
Add missing Override annotations
Fixed a bug in the PermissionsEx resolver
Commented out failing test
Updated benchmarks threads
Added support for multiple employee ids
Make getMediaCodecInfo public
Added error logging to NativeAuthProvider
changedCollabEditSavedEvent getWriteTime to return double instead of String
Fixed LightningStrikeEvent getLightning
provide help id for refactoring
add nowebref to glRotatef
Clarify AbstractBoottimeAddStepHandler documentation
Made createRegistry protected
Fixed a bug where the notification could overlap
Rename GoReference to GoIdentifier
Use Uri toString instead of Uri getPath
Fixed broken dependency resolution
do not report malformed message for invalid pattern
Ignore available presence packets of closed sessions
fixing logger initialization
Fixed focus animation in MaterialEditText
Add a method to wrap an exception generated by the async operation
remove unused import
do not return empty array
Rename AbstractRanking to DynamicRanking
replace Arrays asList with Collections singletonList
add warn when getContext returns null
Use startsWith to compare emergency numbers
Add DataSourceType to DoubleGauge config
update repository after merge
Fix javadoc formatting
Fixed a bug in ColorTypeHandler
Fix ClassCastException in FilterModelImpl
fixed parameter name
Add support for SSL_TLS_OPTIONAL and STARTTLS_REQUIRED
add public constructor for CollectionListModel
Fix race condition in BoundedLocalCache
Use a random UUID if not set
remove unnecessary static modifier
Add support for SUPER to Es6ToEs3Converter
remove unused code
Fix copyright date
Ignore free and delete tests
Fix NPE in PackageManagerService
Fix failing test
Added getter for BatchNode geometry
Added Server getSpawnRadius and setSpawnRadius
Allow unknown options in cleanup command
Fix a bug where CCdmaDataConnectionTracker does not clean up properly
implement supported VCS methods
removed unnecessary code
add more logging to WorkerTaskMonitor
Changed setUpClass to Before
Add Preconditions checkArgument String String int
Add call to disconnect
Fix wrong SqlUpdate annotation
Fixing failing test
Removed unused parameter
Fix NPE in TextToSpeech
make CodeBrowserEditingTargetWidget resizeable
Remove unused boneId field
Commented out failing test
remove unused methods
Fix typo in Cglib filter id
don t export empty tables
Added check for thermostat in updateEcobee
Made UserService afterLogin package private
Remove references to constraints from package info
Remove unused import
Add default value to HongbaoSignature contentDescription
remove duplicate license header
Fixed a bug where the bottom of the grid wouldn t work
Fix typo in test name
Fix back button behavior
Remove unnecessary test code
Remove unused field
Use a concurrent map for security Realm lookup
Fix ordering of reported events
Fix OOM tests
Add a TODO
Fix quality flaws
don t show default value for required attributes
Corrected description of DigestReportStep
Fixed bug on remote graph creation
Add metrics system prefix to ExpirationStoreCache
always open project dialog on success
Fix typo in ViewGroup
Make metric repository primary
Fixed BaseTypeReference equals to compare strings
Fix typo in test
Ignore null Subject
add getSocialNetwork int
Fixed a typo
ignore design documents
Fix MUC permissions check
don t move the form layout if the cell is out of range
Connect the underlying socket to the APNS
Improved error reporting
Fix SchemaDao for Oracle
handle writeExternalException in RunConfigurationExtensionsManager
commit document before inserting import
Set mInLayout after layout
Add missing return statement
Fixed failing test
Set lastWATCHedID to the correct default value
Don t return dependencies if Injector is not initialized
Fixed the CS error of camel cxf
Updated version number
Add a test for loadVersion
Remove outdated TODO
add getIcon to renderer wrapper
Add missing imports for Eclipse color schemes
removed extraneous logging
MediaType includes isCompatibleWith isCompatibleWith
Fix TestPublicBus group names
add UsageTrigger for Append action
fixed potential resource leak
Add check for timestamp in IndexIO
Remove UnsupportedOperationException from ConcurrentArrayBlockingQueue
Fix test failure
Fix NPE in incoming tcp connection
Added missing documentation
en en missing authentication credentials
Use configured timeout for packet reply timeout
don t show file size
Add support for networkUrl in image method
Add FunctionalInterface to JerryFunction
Improve documentation of Configuration
Fix bug in loading index fields
Remove redundant implementation
Don t handle folders in AccountSetupNames
Fix NPE in SizeAdaptiveLayout
Remove Closeable from TachyonBlockStore
Remove a System out
Remove unnecessary code
Fix log message
improved test case
Add a Groovy chain overload to the given Closure
Improved error message
Fixed NPE in CommentActions
fixed coding style
Don t create classes dir if it doesn t exist
Fix a bug in RemoteViews where setAlpha and setLevel were not called
Make Version serializable
Check isCancelled when checking for local shows
Fix class name
do not show frameworks for frameworks
Add OnClose to SnakeAnnotation
remove unnecessary break
remove rmarkdown and rsconnect dependencies
Add javadocs to BodyParserEngineManager
Fix CoverView to work with Canvas clipping in Android N
Removed Comparable from AbstractChannel
Fix typo in comment
Updated versions for release
Fix NPE in FadeableViewPager
TEST Fix SearchScrollWithFailingNodesTests testScanScrollWithShardExceptions
Compare timestamp using compareTo instead of equals
Fix indicator height calculation
Rename property properties
Make CUBRIDTemplates builder non final
Fix off by one error in TitlePageIndicator
Fixed a bug where the reader fragment wouldn t be added to the list
Change default refresh token value to true
Add sbrowser to whitelist
assert message added
Catch IllegalStateException in injectWorldIDMap
Fix bug in RobolectricTestRunner
Use Bulk Mail folder for Spam
Add a link to SearchSourceBuilder parseXContent
Add node name to nodes list
rename files to files
remove unused method
SourceFormatter check for incorrect line break
Added more logging to CompareTxStreams
Don t retry requests with cancelled requests
renamed OpenClosedType toHSBType
revert changes in test mode
Fix restricted state
Fix file history restore test
Add ByteString asByteBuffer
don t generate stubs for empty files
Fix checkstyle error
do not commit MODULE ROOT MODELS
Fix NPE when deleting a local session
Add a new setting for device provisioning mobile data
fixed the test
Fix Javadoc errors
Add toString to LegacyTableLayoutHandle
Fix memory leak in NfcActivityManager
Fix memory leak in CompilerEnvironment
Copy encryptionAware flag when parsing activity alias
fixed code style
next variable action should use caret
Don t rematch network if it was uncreated
Add support for proxying HTTP proxy socket
Fix system decoration for hidden windows
Fix AbstractProject getGroup
Remove unnecessary check
Fix TextView clickLongable
remove table ref factory from benchmark
Improve the log message
Add a helper method to get include paths
Added msdn links to empty methods
Fix broken build
fixed bug in DBQuery preprocessSql
rename Select Locale to Configure Locale
Added forceDisable parameter to disable method
Fix broken test
Fix System getSystemVersion for iOS
Fixed concurrency issue in DefaultChannelPipeline
Fix memory leak in background task queue
Don t show the global settings for local drafts
add listener to inner class name field
Add missing return statement in HttpReadListener
Make ControlFlowGraph public
Remove unnecessary runOnUiThread
Added getMyMachineId to Broker interface
Reduce log level for trace and counter logging
Fix NearCacheStatsImpl getRatio
Don t set mLastNotDraggingSlideState when it s not DRAGGING
Remove postgresql alias
Fix checkstyle issues
Add some docs to ApiProperty
use ReflectUtil instead of Reflection
Fixed NPE in RedirectDynamicMethod
Add a TODO
Fixed the CS error of camel cxf
use TransactionGuard submitTransaction instead of runnable
restore focus watcher on tab insertion
Fix soft block detection
Fixed a bug where the text display lists were not dirty
Changed shuffle to return null instead of true
Remove HISTOGRAM histogram from UKV
Fix broken link
fixed update example
Always show the sliding menu
Fixing the build
removed some system outs
added writeBundleFile method
add setters to OtpsRoutingRequest
Make startJMXServer protected
Fixed a bug in the kinematic ragdoll control
Added isToggleButtonChecked String int to Solo
Disable SSL test for IBM jdks
disable show usages action if presentation is disabled
bump version number
Fix Swift discoverInfo isValid
Fix NPE in showControllerInFullScreen
Remove focusable view from action bar overlay
RefreshBlogContentTask does not return results
Fix SystemUiVisibility bug
do not clear subscriber cache if already disposed
Fix formatting of TransformUncorrelatedInPredicateSubqueryToSemiJoin
remove validate method
Fix race condition in RoutingHelper
Make enter transition transparent
Add missing braces
Pass project to Messages showDialog
Fixed bug in handoff routing
Added a method to ensure that posts that are no longer followed don t have their status
Added a TODO
Fix license header
Fix FingerprintView display id
Remove commented out code
add title to VizGui
Fix race condition in ActivityManagerService
don t switch projects on quit session
Ignore intrusives in speedbump placement
Catch exception when getting line number
Increase the timeout of DefaultStepRunnerTest
Remove a StringBuffer from RegistrationState toString
Fixed LinearGradientFillTextureSourceDecorator s default values
pass correct plugin properties to processNotification
Remove unused variable
Fix Realm table lookup
Fix poi filters
added factory id for hot restart cluster
remove a debug log
Use the correct classloader for FixedCertificates
Removed outdated TODO
Fix a compilation error in LogBufferDescriptor
remove obsolete todo
Remove unnecessary promise tryFailure call
Fix possible NPE
update PlatformTestCase java
Fixed a bug in the foreign store id test
Added tests for quoting
Fix exception message
Fix system table index deletion
do not drop resolve caches on error
add color to manage packages dialog
Fix HttpTest testConnectInvalidHost
improve error message
add another link to the PCA
Fix crash in HardwareRenderer
Fixed test case
Fix NPE in DnsUtilActivator
update UltimateRecyclerView java
Fix BsonTypeClassMap constructor
Remove redundant call to removeBadge
Fix LLFUEvictionPolicyEvaluator selectEvictableAsPolicy
Move removeUnusedClassProperties to the end of the list
add some debugging code
remove empty line
fixed gwt sound manager
avoid NPE in setVertex
Create history file if it doesn t exist
Remove unused method
log remove unused field
get syntax highlighter from injected file
Sites group type fix
activate the default rules
Add hint for placeholder body
Made GetMoreMessage class package private
Removed exceptions from add methods
add null check to isMongos
handle the back press :D close the drawer first and if the drawer is closed close the activity
Added a FIXME
Fixed issue on graph creation
do not add compilation status listener if there are no entries
Remove unused field
Fix comment typo
Remove PngChunkPHYS from sampleGrid
Fix NPE in DefaultAuthenticationEventExecutionPlan
Fix bug in FingerprintService hasEnrolledFingerprints
Use the correct value for user home
Add setBorderColorResource to CircleImageView
Fix NPE in ResolvableType hashCode
remove stack trace
Get default color from ColorStateList
removed unused imports
Print compiler version when building C++ runtime
Added support for EditorAction
Print secret expiry in CLI
fixed return statement in function signature generation
Add restart method to MockTaskManager
Make resolveNow package private
Fix a bug in the GiantComponentBuilder
Add toString to ScannerSupplierImpl
Added a short test for issue614
Add toString to Result
SMALLFIX Removed explicit type argument in ViewHolderDeferredProcess
avoid unnecessary field creation
Added getWidgets to Dashboard
Clarified javadoc for clicker sleeper
Fixed a bug where the username password field was not getting set correctly
Removed unused import
Added author tag
Improved UptimeClientHandler example
Add min and max tests
add getProject to DomModelImpl
Fixed a bug where notifications could overlap
Fix test error message
add support for sql without query param
Added missing javadoc
Add a TODO
Don t throw exception on non RPC daemon
Allow source distribution to be set to COORDINATOR_ONLY
Don t add stdout to the writer if no GUI present
replaced unboxing with Util compare
Switch Linker to use ArrayDeque
Remove unnecessary code
remove unused code
remove dead code
Add comment to SpeeDRFTest2
add null check for DRPC servers
Fix log typo
Add fxcop to list of required plugins
fix broken test
provide consoles from run tool window by default
do not update inspection profile if current scheme is null
Fix RecurringInvoiceItem description
add detected sdk to project sdk combo
Don t load comments if activity is finishing
Changed the default logger to verbose
don t change task text pane background on Darcula
do not add special cells for non commited cells
sort copied files by path
fixed test case
don t show lookup icon if calculating is disabled
Remove unused code
Fix NPE in UndertowDeploymentInfoService
Fix bug in TransactionalBoltExecutor
Added a stop method to close the tx log
Remove unnecessary String format call
Fix quality flaws
fix failing test
ResultTransformerOutputWriter stop throws LifecycleException
Remove deprecated method
Fixed a bug where BuildCraftCore is precompiled
Fix Maven tests
Fix NPE in updateMobileActivityIconId
Fix setImageBitmap bitmap
use new shortcut set
Add a trace tag for AUC
add progress bar
remove assertion that is not needed
Remove unnecessary selector
Allow StrictMode to be enabled on file URIs
Add javadoc for DeleteIndexRequest constructors
Add missing commands to Unity processor
Remove unnecessary variable
don t compare private messages
do not remove minimized views
Don t show settings page for Python settings
Commented out failing test
Fix NPE in AbstractExcerpt
Remove unnecessary else block
Fix NPE in CompositeDocumentationProvider
Remove redundant test
Remove Beta from MediaTypeTest
Fix javadoc typo
Added missing classes to Record
TapTargetView didn t correctly draw path
addFulup Jakez as a maintainer
Remove excessive message length check
Remove log message
Fix helios solo test to avoid extraneous environment variables
Added javadoc for Skin constructor
Make fields final
Handle null GELFMessageChunk
Added support for Chosen Provider
added support for skipping and limit
remove empty line
Fixed bug in LocalFileListFragment
Removed unused imports
set topic prefix
Reduce number of threads in SmokeTestClient
provide thread indicator
Remove debug code
don t fire VcsDirtyScopeManagerImpl beforeFileDeletion for non local files
Fixed test case
Log uncaught exceptions
Removed unused method
Add javadoc for AlluxioURI
parse intermediate throw event
Throw UnsupportedOperationException instead of log an error
Don t update the timer thread if it s already alive
skip empty lines
Fix small map menu button
make IdGatheringRecursiveVisitor walking
Write the first sync message to PREF_MAX_SYNCED_DATE
Only calculate output hash and size for built locally builds
remove selected injection from settings
Ignore CoarseSessionPassivationTestCase for now
Remove unnecessary call to setMaxConnectionsPerNode
Add trace logging to ClusterEventPeriodical
Remove unnecessary static import
Don t collect instrumented files when there s no instrumented files provider
remove bookmark link from location hash
Add fail to test
Removed unused variable
Fixed potential NPE
Fix broken Javadoc links in PropertyValuesHolder
add WeakReference import
Fix bug in FileSystemMaster
Starting memory monitor at the end of the node
Fixed the CS error of Decal
improved error message
Fix a bug in the commit log archive command replacement on Windows
Added getContext to PGraphicsOpenGL
ProxyResponseHandler should not remove Content Length header
Removed empty lines
Remove unused import
remove cleanup after check on Windows
SMALLFIX Removed explicit argument type in ClientHandler
Clear pending flag on new packages after install
hide more info menu item when no account is selected
Fixed typo in DefaultGroupProvider
Fixed compilation problem
Fix broken test
removed unused code
do not report type arguments as not callable
Avoid NPE in SuperTypeVisitor
add constructor to set the header divider
allow https urls in redirect
Fix MultiMapContainer null handling
Set colHeaderForRowHeaders to empty string
Fixed a problem with logging
Log failure to find Unsafe due to
Added DbTester select String DbSession ConnectionSupplier
Fix test on windows
Fix tests on Windows
Make DefaultTestMethod serializable
don t use ArrayList in order to avoid ConcurrentModificationException
Explicit outputs should have an environment NONE
Fix StreamId hashCode
do not use project structure context in headless environment
Fix RemoteUrlPropertyExtractorTests on windows
removed debug output
Fix ssh command in console
add cancelTaskWindowTransition to window manager
Fix NPE in AbstractCachesTest
Fixed a typo
Add seed creation time to DDeterministicKeyChain
Fix header table size setting
Add missing withQueryGranularity method
Remove unused import
Fixed template name for leave
Improved exception message
remove unused isMainFrame variable
If we don t have a hostname for whatever reason set a sane default
Remove Nullable from JsonFileMappingsSaver
Fixed NPE in TagViewActivity
Add some documentation to InstrumentationTestRunner
Added debugging information
Changed default value of strictCompare to true
Add missing isOpen method
Add Scope isFunctionScope
Don t sort filters by size
Don t store accounts from the config service
Added Vector constants
refactored debugger support utility
Fix lint warning inRollingSampleBuffer
add getRoot method
Fixed a failing test
Remove unused code
added tryCount to Invocation toString
Fix GBM scoring
Remove unnecessary double boxing
SourceFormatter Remove unused field
Fixed logic error in session useInventory
don t update selected page if it s already there
Fix the build
Fixed isRenaming on data context
fixed sql error
Fix example post url
do not show external annotations line marker in scrolling
Fix resetViewBeforeLoading bug
Removed unused code
Add an assert to GBM
Add showAndGet method to DialogWrapper
Log the path of the plugin
Add missing Override annotation
Reduce log spam in ProcessState
Fix typo in error message
Remove log statement
Add language to email subject
Removed SideOnly annotation
remove unused import
Added missing break
Fix crash in CropImageView onRestoreInstanceState
Added boolean tests
Fix connection pool leak
Clarify javadoc for MenuItem actionEnum
remove unnecessary null check
commit document after rename
comment out jdf newlines
Added support for foreign column name in DatabaseTableConfigUtil
Clarified javadoc for getCurrentToggleButtons in Solo and ViewFetcher
Add a checkin message to trakt task
fixed bug in CSVReader
Fix bug in handling of portable filters
Don t broadcast SearchManager intent changes twice
Replace Throwables propagate with RuntimeException
check user name before push
Increase the buffer size
Corrected bean documentation
Fixed a bug where the context menu wouldn t be shown
Throw exception if we can t generate outer expression for a class without outer declaration
Add null check for viewName
Always get writable database
Removed problematic marker
Remove debug output
Improved error message when a servlet is not registered
Add Server base URL property to CorePlugin
Removed stone from dictionary
Add Validate notNull to HardcodedContracts
SchemaMapper now returns a JType
Remove unused field
Fixed NPE when liquid dictionary is not found
Fixed typo in javadoc
Fix quality flaws
Add check for empty array in TaskStackBuilder getPendingIntent
Removed author tag
added more tests
Add ThreadNameDeterminer PROPOSED CURRENT
add new line
corrected name of recent files
Fix CountryNamesFragment example
Fixed the test
Renamed DropUserOperation to removeUser
made method calls unmodifiable
throw exception when trying to recur from catch finally
Don t use Lists transform
Fix custom view inflation in MaterialDialog
Add settings for finding print nfc services to install
Java Remove unnecessary code
Increased the number of iterations in the test
Removed null check
Fixes a case where the rename operation fails
do not show customizable configurations in options dialog
Add a small hack to make sure the string null doesn t end up in a StringBuffers
Remove unnecessary code
Fixed compile error
Fixed a bug where the settings menu wouldn t show up
Fixed NPE on closing of pooled database
Remove Component from PeriodicGraphUpdater
Make fields in Message transient
Add getNative to PGraphicsFX2D
update modification count
Add ApplicationSpec to DefaultPlayPlatformAwareComponentSpec
Fix touch slop for header views
add warning to test if only one language is found
Fix broken Javadoc link
Fix downsample size calculation
Fixed a bug where NIO packets were being sent to the server
Fix XMLRPCUtils sanitizeSiteUrl test
Suppress deprecation warning
removed test code
add navigation item
Log the cause of the exception
Fix NPE in DiscoveryService
Fix build break
do not fire disabled property change twice
Log a warning when no BroadcasterCache configured
restored commented out code
Add the error path to the request mapping
Fixed log message
Use a factory method to create the default repository configuration
Add a bit more coverage to the test
Don t log invalid chunks in debug mode
Fix typo in MetaDataCreateIndex
Remove deprecated warnings
Add comments to CoinChangingMinimumCoin
JSONObject setJSONObject String JSONObject setJSONArray String JSONArray
Fix compilation error in FileOutStreamIntegrationTest
Don t play volume in media session when external priority is high
Add support for UDP_RECEIVE_PACKET_SIZE
Add version test for SQLite
Fix compilation error in LocalMiniDFSCluster
Remove plan99 net from main net params
Add support for id and log in shell messages
Added unit test for sending with different encoding
Cancel notification manager on initial scan
Fix javadoc for getVisibleTitleHeight
Added HashSet to ServerTypeUtils
rewind the CharBuffer before returning it
Add a log message for reattempting failed deployment
Don t use opaque surface if format has alpha
Improve error message for unknown endpoints
Remove unused variable
Removed unused import
add constructor for SessionCountChangedEvent count
CachingHiveMetastore invalidates table
Fixing memory leak in tests
do not add file to the list if it doesn t exist
Add some javadocs
don t pack content if size and location are zero
Added a property to get the max number of occupants
MakeScatterGatherBuilder getRequestInfo public
Fix TextureView on Lollipop
Remove unnecessary protected modifier
Fix NPE in acker when ack_fail message arrives before ack_init
Fix upload filename
Fix bug in comparator
Fix failing test
Fix AntiCsrfForm URL generation
Increase the timeout of BluetoothTestUtils
Fix NPE in AbstractTFS
don t start the engine twice
Catch InvalidPathException in ProjectWorkspace linkTo
Make Reflection non final
Remove unused method
Fix NPE in NioServer when ConnectionHandler returns null
Add toString to FragmentCollection
Fix Files toString charset
enable local inspections
remove unused method
Fixed NPE in DnsUtilActivator
removed unused import
added a getRootLoader method
Fix version check
Remove unused imports
Hide the progress bar when the adapter is empty
Fix typo in Configuration javadoc
Fixed typo in Javadoc
write only with 50 meters accuracy
Fix test failure
Use a different cache key for buildSrc
Add getter for genJar
added Nullable annotation
disable coverage for IDEACoverageRunner
Remove deprecated screenOrientation constant
Rename parseStatusResponse to parseResponseText
Added hint for findbugs
Changed GraphJung to implement DirectedGraph
Use bigtextview forroad selection
Only set the JSP servlet if it s non null
refresh ui after end selection
Improved documentation of KeyValueFormat
when merging contacts then don t move the whole meta
Fixed Cache put String
Make account info combobox non opaque
Added unit test for XMLTokenizeWrapLanguageTest
remove unused method
Fixed bug in updateToOpenfire
Set provider authority as string
Removed unnecessary javadoc
Fix broken test
reset tokenizer on retokenize document
Add verbose mode to executor task
Added setters for x y in ShadowView
Added getCurrentImageViews View parent
Don t show progress bar when loading more posts
Remove version info from banner
Fix wrong method name
fixed problem with context parsing for jsp files
Added notes to UpOperation
Catch IllegalArgumentException while scanning for cookies
Fixed typo in smart keys checkbox text
do not assert project disposed
Addressing review comments
Fixed BlockUtil isFullFluidBlock
Fix javadoc for AbstractMockMvcBuilder
remove unused code
Move the second view position to the right
Fix test on Darwin
add time method to MultiKeyPipelineBase
Fixed summary2 for binned quantile
remove dead code
Add documentation to Plugin onOverrideUrlLoading
Fix Iron pipe logic
Fix log message
Remove unnecessary code
Fix quality flaws
Add a missing lock in isBusy
do not log query timeout exceptions
Add a display setting for shows exact date
Fix TimSort memory leak
Fixed bug in proxy injector
Add asString method
Use constant instead of hardcoded
Fixed NPE in traverse process
Add a test for BuildRuleResolver
add gwt dependency forashley
fixed possible NPE
Removed unused variable
Fix abort notification
fixed Validation of locked refs
add support for Xcode path
Remove reference to buildscript dependencies classpath
remove tabs from sample
Fixed serializer class names
Fix update frustum
Fix POI filter
Fix a memory leak in SimpleFuture
removed unnecessary try catch
do not show suppress mac CORS on MacOSYosemite
Include original path when validating invalid path
Fix a typo
Parse the value of enableAccessControl
simplify session lookup
Don t draw spaces in Bitmap4x8FontRenderer
Adds a method to find a SSRC parameter by name
Fix NPE in AnActionEvent getData
Fix NPE in ErrorCheckerService
Add isICSOrHigher to utils
Fixed NPE in VCardManager
Fix line length
removed duplicated test
Add hashCode and equals to Context
OracleAQ does not support JMSType header
Fix GsonSerializer bug
Fix NPE in incrementConflictCount
don t allow history popup to jump to the top
add IS_32BIT and IS_64BIT
Added missing javadoc
Fix NPE in OpenSprinklerBinding
Use the right classloader
Added throws Exception to CollectionProducer
Better error message
Remove unused code
Fix broken Javadoc
Added a comment
Fix snapshot restore test
added support for Literals
ignore index creation but shards not instantiated yet
Fix ScriptIntrinsicBlur documentation
Generate a unique job prefix for each test
Move field to local variable
Revert Remove STOPSHIP from testMountSdNormalInternal
add debug logging for cluster state action
Don t show window placement when sketch is disabled
Remove unnecessary logging
Add an assert
Remove unnecessary comment
fixed scala tests
removed System out println
Don t scan WEB INF classes
Fixed a bug where the SnackBar wouldn t start animation when the container is not visible
Add toString to GrokPatternSummary
Make inMemoryPercentage private
fixed javadoc for child and sibling
Close the database which will close all pending statements to be finalized also
replace IllegalArgumentException with UnsupportedOperationException
Remove unnecessary null check
Add space to output directory
Add rule key to PythonInPlaceBinary
fixed bug in equals method
Fix links in BinaryJedis
Fix wrong method name
Improve error message
Added a leftShift overload to the DefaultGroovyMethods
Removed unused variable
log server doesn t have a client Queryable
Remove unused code
Fix a bug in RestClientUtils
added error message if list attributes are of type ListExpression
Don t generate signing keys for use without user authentication
Unbind SuggestionServiceConnectionManager on destroy
run actions in read action
Use HTTP_NOT_MODIFIED instead of HTTP_ 304 for plugin list loading
Fixes a couple of javadoc errors
Fix ZooKeeperMasterModel isRolloutTimedOut to use statusDeploymentGroupTasks
Removed redundant code
Honor automatic instance settling period
Fixed SetViewportSizeOf not updating the defaultInstance frameBuffersManager
Add documentation for getLine1Number
Fix compiler warning
Revert unintentional change
Added new EntityTargetEvent
do not create PsiElement with invalid PsiElement
Fixing compilation error
set mContext in OTRChatManager
Add a TODO
remove unused code
Removed unused field
Removed Override on interface methods
fixed checkstyle issue
Fix transport stop display
Add getUrl to CordovaWebView
Add Cache Header Prefix
Rename ProjectGenerationStarted to ProjectGenerationFinished
Fix option name
Don t pivot camera widgets
Improved error message
Fix accessibility state changes
Removed unnecessary copyright header
Remove unused code
Fix TextureView setLayerType
Added IOUtil delete File
do not throw exception
Fix typo in SimUtilsTest
Log the execution of CDDL commands and directories
don t leak info bar
improved logging when a subscription frame handler is not found
Fix a bug in hasNoDragOffset
Fix regression in AppSecurityPermissions
Fix the build
Handle primitive messages
do not trim trailing
Remove unused code
Don t show insertion point cursor when there is no text
Fix infinite loop in sendAndKeep
sort packages by library to preserve grouping
Don t restore empty file
fixed emmet generation for template tags
remove unnecessary UniqueQueue
Fix test on Windows
Added a comment
Fix quick settings location refresh
fixed error condition
Add vold decrypt property to UsbDeviceManager
Initialize hdfsfs cache on startup
Introduce introduce onTextChanged in TextView
Remove isRecentlyUsed from SitePickerAdapter title
Fix crash in ClipboardModule
Fix a memory leak in PowerManagerService
Add setImageResource to ImageViewTouchBase
Fix transport stops drawing
Fix text selection
Updated FileRegion javadoc
Removed empty lines
fixed typo in javadoc
Fix NPE in DevicePolicyManagerService
Remove misleading javadoc
do not search project content for injected files
fixed FPSAnimator constructor
Fix CommandStreamTest timeout
Add cookies to the response
Remove unused code
Fixed typo in javadoc
Don t filter out user keys
remove unused throws declaration
remove extraneous logging
Added MSDN reference
Fix access point parsing
add missing and order to the aggregation
Add setDisplayedChild int to ShadowViewAnimator
Using ActivityLauncher to show SitePickerActivity
Fix NPE in NavigationService
Fix SysOperationsTest testQueryNameFromSysOperations
Add no arg constructor to Histogram constructor
fixed cs issues
Only call verifyInstrumentedTarget when needed
Added default constructor to Chapter
Add missing method to AbstractHgTestCase
Add and to sandbox stage title
Renamed isRunning to isStarted
Fix SIP listener removal
Fixed a bug in the last commit
set platform prefix
Don t throw an exception if the annotated holder class is invalid
set docstring format in test
validate the input stream and output streams
TEST remove ExtendedStats
TransactionImpl should wait longer on exception
Fix video player completion
add border to filter panel
made HttpArgs constructor private
Add support for System LOCKSCREEN_DISABLED
Rename SpringBootConfiguration to TestConfiguration
Fix a stupid load leak
augmenting inactive time for cycle sessions
removed extra quotes
Fix broken Javadoc link
Don t throw an exception if we can t read the content of a file
don t use PsiModifierList in TestOnlyInspection
changed route color
Added comment action
throw query interrupted exception
Add a hacky fix for js second completion tests
Remove unnecessary parens
Fix a bug in ControlFlowUtils
Increase repo gc interval
fix brace matching
Remove unused parameter
Added missing socket close
add onNavigationClick listener to ComplexHeaderDrawerActivity
Fix NaN mean computation
Fix NPE when passing a null argument
Reduce visibility of swallowAbortedUploads variable
Fix over not working
Fixed compilation problem
Fix ActivityLifecycleCallbacksWrapper equals hashCode
don t stop peers if it isn t running
do not use cached cursor
Remove unnecessary final modifier
Add class tag to javadoc
Truncate SQL query tooltip
Fix checkstyle violation
vcs log do not paint ui on progress change
Throw IAE on invalid timestamp
Throw exception if master password is required to store passwords in the database
Fixed the CS errors
Fix NPE in Transition cancel
remove duplicated usages
add utility method to get tree foreground color
Fix NPE in MockTraceContext
Added support for ShadowActivity getMenuInflater
don t append blah to module description
Turn off debug flag
Fix bug in updateUniform
Remove outdated TODO
Added a TODO
Use US locale for auto save id
handle JsonParseException in BlobStoreRepository readSnapshot
remove deprecated method
Fix typo in ScopeUtils
Fixing the build
Removed a TODO
Fix IndexRangeComparator to use ComparisonChain
don t generate unique URLs
use ArrayList instead of HashSet for ProjectGroup getProjects
Trim search query before showing it in LocalSearch
Fixes a bug where group contacts were not being removed
Fix broken test
Added invokeLater to find file handler
Fix NPE in ClassDefinition visitDirectMethod
Add support for bitCOIN_URI_PATTERN
register handler for xterm resize handler
Remove deprecated constructor
Fix NPE in LocalWebSocketConnection
only set top level breakpoint for shiny files
Remove unnecessary code
Added missing import
remove unused variable
do not show empty options editor
Fix a bug in AbsListView where we were tracking the wrong value
Fixed javadoc typos
Don t add transitive inputs to input headers
Adjust the size of the upload file
Remove GoogleAnalytics unused imports
Add rsbrowser and rbrowser to list of browsers
add a TODO
Fixed the failing test
Use the preferred focused component for the authentication dialog
Add missing license header
Fixed Terasology getTimeInMs
Remove unused imports
fixed license header
Fixed bug where noop is not marked as not logged in
do not detect existing library type if it is not editable
Don t create remote trash folders
read xml rules as UTF
Added the camelribbon to the list of EXCLUDE_DOC_FILES
Fix Bluetooth status bar
Added getCurrentActivity to getViews
do not process empty values
Ignore bad test as the newRequest method throws IAE
Dismiss SearchDialog if we re embedded in an application
disable focus traversal keys
remove debug process
Improve error message for setName
Fix quality flaws
AddURE to DeploymentPhases
Fix exception in OAuth20ProfileController
added check for header svg tag
Fix exception class check
Replace com google common base
fixed bug in RequestManagerImpl
don t write empty buffers
add number of concurrent streams to recovery log
increase max height for Internet Explorer
Removed unused import
Fixed checkstyle errors
Don t support DDL in transaction
Fix PeopleTable delete query
Fix a buffer overflow bug in ReaderWriter
Fixed copyright notice
Fix merge conflict
remove writeBeyond2GB test
Add missing dot in javadoc
Remove TargetApi annotation from onCreateView
Fix app transition animation positioning
update tree model on rebuild
Fix dependency parse annotator requirementsSatisfied
Fix unit test
just put Downloads folder for now
Fixed bug on parsing of command context
fixed xml eq handling for file context
Add failure message to assertFailure
HexDumpProxy should use LoggingHandler
do not resolve file names from gobject
implement nearest address methodology once ranging bug is fixed
Fix Gram constructor
Allow t in line numbers
Fix transaction commit
Fixed checkstyle issues
Reorder the calls to listLocalMessages
Add StringUtils isFullJID
Add missing Broadcaster lookup methods
Add a property to disable lockscreen rotation
Fix test failure
Reduce the number of hidden processes
Add total to precipitation
Don t register ExecutionListener twice
IdempotentRepository contains String should lock the repo
Renamed test method
Add option to force proxy bypass to sip provider
Fix NPE in K2JVMCompiler when alt headers path is null
Add a test utility method to test tiered block store
Disable comment at first column by default
Force dialog auth in debug builds
added bind unbindStickers
Add a workaround to set the daemon idle timeout to be max X min
Remove unused imports
add missing space in logging
Fix CM assignment in DGLM
Fixed potential NPE
Fix NPE when keyguard is disabled
Fix command timeout in test
remove unnecessary error logging
Remove unused code
Add extended interfaces to type info check
Add missing preRequest calls
Improved Adapter Javadoc
Fix JavaDoc for loadingDialog
don t show live templates in completion
Use apply instead of commit in LocalNotification
update index file name
update route bearing too
Fix NPE in APTModelHelper
Fix padding in sticky headers view
remove unused method
add script usage to the output of openhab
add update command line option
Don t notify client of frame changes in case of stack bound animations
Fixed bug in ParticleEmitter
Fix exception message
Fix bad import
Fix NPE in TagLibraryInfoImpl
Added support for volumeedown and volumedown key
Use the file schema rather than the metadata
Prevent NPE if indent printer is null
fix failing test
restore state in display level map
Log an error if we cancelling the updateDataLayer request
Prevent exceptions if user types !foo
Handle the case where Clearspace received a transcript update but was not able to process it
Fix javadoc warnings
remove scanner reference from hash login service
Fix linkify row index
add name to debug filter
Fix typo in analytics event name
Fix update to upsert
don t set def on non public methods
Don t use HttpHeaders entrySet
Fix javadoc warning
Make fields final
Fix crash in DateFormatter
Fixed potential NPE
Fixed a TODO
Make TypeWriter getModifiers for instrumented types public
Remove unnecessary assignment
Changed the AuthenticationMechanismOutcome NOT_AUTHENTICATED to NotATTEMPTED
Update WebKit if visible title bar height changed
do not activate project view in focus request
Add carrier metered apn types string
Fix crash in SurfaceFlinger
Don t print stack trace when parsing airtime
Fix namespace generation to prevent synthetic classes from having Test or other parseable suffix
Fixed DateTimeType for older MySQL versions
remove unused parameter
Remove unnecessary SuppressWarnings
Fixed typo in wording of wording
Add some badges until they are explicitly removed
Added support for HSQL
Fixed typo in comments
don t add classes if there are more than one invocation
Remove Ignore from test
Fix NPE in CollectNode
Removed scrollToPosition call from Adapter initDisplayOptions
Fix pagesMemorySize computation
Add missing TargetApi annotation
Fix typo in javadoc
Don t send downstream if the channel is not connected
Added shufflePoolItems to GenericPool
calculate ignored file children
Remove vframe from test
Added quotes around column names in the database type
Deprecate RequiredMeasures interface
add missing since annotation
Don t show welcome pages for DefaultServlet
correctly detect PSI for file based files
do not preload java lang classes
Fixed a bug where the delete intent wouldn t delete the user
Add a comment
Fixed compilation error
restore original file after copy
Fixed a bug where the default app preference wouldn t finish
Renamed parameter value to regexp
Added Corridor to chest
Fix typo in unique constraint query
Fixed bug on tx optimistic proxy
Fix NPE in resetTellstickListener
Commented out PostprocessingMachine
Pass ctx to LambdaExpression invoke
show error when language is not found
Fix pullIfAbsent method
Allow a managed model creation rule to be created with a value type
Added PropertyFactory from String
Don t wrap the filter with a new one
Added registration class to serializer
do not update documents that aren t started yet
Don t reset display state when restoring
reset the output buffer
Fix UserManager isUserAGoat
Clarify the type of Guice
Don t update the mLatestProgress when the progress is changed
Added support for shared groups
Add a trace tag for vIDEO
Don t force local FS
Add a default completion feature to tag completion feature
Fix Javadoc errors
Add PSK to the list of allowed ciphers
Catch alluxio TException
do not close global inspection view on rerun
Fix NPE in JetShortNamesCache
Fix recents animation in RecentsActivity
single selection mode for existing templates
Add some assertions to LabelTest
Add missing since tag
Pass correct result to connectResolvedInetSocketAddress
Renamed set enable enableShouldRequestCommentFromNote to enable
Added double and object arrays
Add a comment to clarify the SSID behavior
Fix lost blocks cache size
Fix typo in TransactionalEventListener
Add a hidden query parameter for vcard without photo
Fixed potential NPE
annotate toggle action should report exception
fixed test case
Do not throw XMLRPCUtilsException to verify URLs
remove invalid name check
add cluster state version to debug log
Add support for FileItem
Do not resolve annotation arguments for non static methods
Fixed issue where IabHelper was not getting loaded
Make max size for attribute values
Don t show the header footer when pulling up to refresh
add new icons
don t autodetect platform prefix
Fix typo in Notification getType
Fix ImageCache exists
Initialize user repo prefix in GitBlit
Remove BINARY verb from the DROPPABLE_VERBS list
avoid NPE when no commit node is set
Fix MortarScope findService
do not cancel popup if already disposed
Changed the fighting rood material
added support for scheduled jobs
Allow symlinks in the project path
Disable caching in dev tools
improve highlighting of no file error
change log level from warn to debug
Add constructor with clock to TimerMetric
make canRename public
made parseKeywordStatement protected
remove unused import
remove trailing comma
Don t keep SSE connection open for cold streams
Fix JdbcDataSourceStat iterator
initialize calendar and groups
Fix NPE in RestAdapter
Make isAnimationRunning private
Fixed a unit test
make size return static JBDimension
Remove unnecessary code
Use the SslImplementation instead of the JSSEFactory
Fix rest id check to use URL separator
SourceFormatter ignore trailing spaces
Redirect to the owning page when deleting a repository
remove wildcard imports
Prevent ViewGroup from dispatching the pressed state
Add column names to PDP
Use short class names when package is null
remove unnecessary code
fixed test case
removed unused imports check
Fix NPE in SpringLiquibase list
Add assertions for boolean arrays
remove unused code
Replace Inject with Singleton
Add additional information to assertion
added comment to ImageAlt
Fixed NPE in Create Virtual Environment dialog
Add precondition message for INODE_TREE_UNINITIALIZED_IS_ROOT_ID
Fixed NPE in NullLiteralExpression
Remove unnecessary warn log
Fixed compilation problem
Remove test xml file from the DumpRenderTree skipped list
truncated checksum log
Added support for JRubyHookDefinition execute
Fixed Sponge forge event factory
Temporarily ignore FileSystemAclIntegrationTest
Add partitionId to Operation toString
don t create RefManagerImpl references for directories
Fix ValidatorHelper elementHasAnnotationSafe
add final to writeJSON_impl
Added new enabled preference to status line screen
Remove unnecessary synchronization
do not add targets to the top level of imported projects
TwitterComponent should extend UriEndpointComponent
Improved exception handling for buffer overflow
Remove file extension from
Fix type definition regex
Added support for HTTP connections
Fix SQLMergeClause isEmpty
Add test for BeanViolations check
Fixed radians calculation
Don t expand frames on Advice
Fix HorizontalBarChart to extend BarChart
DO NOT MERGE Remove unnecessary code
Change List to Collection
Fixed a bug where the ringer mode wouldn t reset the correct state
Add a TODO
Fix resource index
allow dots in file extensions
Fix DASH chunk processing
Fix class cast exception
Removed unnecessary warning
Remove unused import
Escape single quotes in stubs
Remove the flamegraphs option
Revert Ignore failing tests
deprecated old class
Reset visible limit on account reset
Removed outdated comment
Make some fields in RajawaliScene final
Added missing Override annotation
Add CheckReturnValue as an alt name for ResultOfMethodCallIgnored
Add MockPageCacheRecycler reset
Fix bug in KeySearcher
Remove unused code
Fix user switcher
removed the setting of groovy grape report downloads
avoid NPE in IdTableBuilding
Fix a bug in EnumSet
Fixed SmoothCamera update method
Remove untested TODO
Reset the current offset on start of the SmoothProgressDrawable
Added getQueueSize to QueuedThreadPool
Remove duplicate log line
Added a comment
Fix return type of withCancellationToken
Improved performance db URL
remove debug output
Fix merge error
don t list packages in java code reference elements
Disable jabber until ready
Add VarImp to DLModel
remove unused import
Ignore failing test
Execute check in task on thread pool
enable new welcome screen for JetBrains products
Add a knownStates method toShiftReduceParser
remove unused methods
Fix NioWorker setInterestOps
Add LWJGL P2D and P3D to the list of supported renderer names
log Fix NPE in log configuration dialog
Removed generics from HazelcastClientListTest
Fixed compilation error
Make Path Builder public
don t infer contracts for non public methods
Fixdesktop sharing warning
Renamed isNull to wasNull
AbstractStore should use Adler32
Fix NPE in EnforcedPlainTextFileTypeManager
Adding thread safety annotations for mesos framework
Add getNotification method
Added offset by usage to VertexAttributes
Fix race condition in BackupManagerService
Clarify Futures dereference documentation
Added a comment
Fix NPE in AbstractClient disconnect
Don t show undo order menu if there are no markers
Fix CommandExecutorTest on windows
Make AbstractClientAuthenticationHandler supports final
Fix NPE in SignerUtils toString
Remove Override annotations
Remove unnecessary code
fixed test cases
don t show empty title
Fix test after renaming
remove unused method
Updated preferences java
Fix NPE in TextureView setSurfaceTexture
Fix a compilation error
Do nothing if the adapter is null just bail
Move test to util buf package
Fix head tag in WebViewTest
fix live template shortcut
move ClassReader to asm package
Fixed javadoc typo
activate toolwindow on mouse click
disable type migration for void types
Removed a TODO
Don t switch user if already in use
Fix JmxMonitorRegistry javadoc
Fix Nullable annotation
add ensureGreen to test
Remove unnecessary code
Encode and decode username
Fixed cast exception
fixed bug in serializer
Fix NPE in ToggleButton
fixed typo in method name
Remove unnecessary future isDone check
Add a constant for calling package
Remove unused imports
remove unused field
Fixed the local player rotation of the mouse
Add a comment
Fix NPE in network monitor
Fixed error message for invalid task listener
Fix NPE in HiveServer2 when thrift CLI is not available
Fix osmo intent handler
remove broadcast direction from DumbModeListener
Added test for LONG
Fix SmileXContent class
Fix MenuBuilder removeGroup
Remove unused methods
fix bug in transit index builder
Improve memory resizing
Add missing import
Remove empty line
Fix NameResolver javadoc
Check isFinishing in onPostExecute
ignore all exceptions in remote testng starter
fixed testAppletPage to work with windows
Don t insert directory entries in the media provider
restore border for tabs
Ignore examples that are running when validating documentation
Add lookup key to caller info
Fix wrong class reference
Do not show disabled extensions
fix recent projects removal
Remove CommandCallable getValueFlags
Fix cast exception in UnicodeUtils
Removed unused field
added null check for custom header view
add getText and getTextLength to ClsFileImpl
Add getSignalDbm to Device
Add missing parameter
Removed debug output
Remove unused NoopSearchRequestBuilder setNoStoredFields
Fix notification service id conflict
remove unused parameter
suppress unused warning
Changed ComboPopup show to show if the combo box is visible
remove redundant exception declaration
Remove unnecessary override
Added Skin getRegions String
add custom custom executor to TerrainLodControl
Add dimension to split order when skipMaterializationForDimensions is false
ignore java lang java util
deprecate getStringToReplace String FindModel
Fix fake extractor input exception
Fix maven tests
Fix NPE in ChainedExecutionQueryRunner
add default target to console
Fix NPE in InputMethodManager
Throw IAE on invalid mapColorForTab
added missing line break
Remove dead code
remove unused hit method
add createDatastore method
Fixed a bug where the comment row wouldn t show up in the dot com blog
Improved documentation of HostedConnection getSession
Remove outdated comment
Fix NPE in ContentProvider
Make PgpSignatoryFactory extend GroovyObjectSupport
Fix Range closed test
Added missing isDebugEnabled
Fix TestUpdateOps testUpdate
register a custom authentication filter with a jsonp filter
Fix ImageResizer to handle null config
Removed exit action
Removed a System out
Fix copy paste error
Fix NPE in CleanXmlAnnotator
Close soft keyboard in test
Increased delay in ScanStreamFileTest
Implemented AdapterDataObserver methods
Don t mark the contract as unconnected
Move reportUnrecognizedRequires to the end of the loop
Fix session id comparison
Fixed an issue with the download index activity
android remove unused layout params
Removed a TODO
add validation to Rules
Added CompilerConfiguration configure Properties
Added a FIXME
remove obsolete TODO
Handle an error if the session was closed and was not closed normally
Removed unused variable
Fixed javadoc warning
Changed the endpoints of theFlickr API to https
Fix caching module version artifact caching
Create empty children cache
Added SuppressWarnings deprecation
Fixed a bug where HttpReadListener does not suspend reads on the same thread
Disables the NETWORK and STORAGE TIMEOUTS
Change default stack xml to orghibernate cache jgroups stacks xml
Don t allow ListWidgetService to move cursor to position
Changed the log level of AIDL generation to info
Fix NN test
Fixed setDummyDataWithHeader method
ServletComponent sets the rest http binding
Added getAuthoritiesPoulator to LdapAuthenticationProvider
Made LRUCache serializable
make cutoffMinutes default to 90
Fix a bug in HdfsFileInputStream
Print the class name of the new anon ID generated
remove debug logging
Removed manual Wire Protocol from QueryFlag
fixed cs issues
Make PreconditionsExpensiveString an EXPERIMENTAL category
Remove final from GeneratorBase methods
Remove redundant modifiers
Add default values to ClientEndpoint
added a TODO
don t register custom annotators for languages that are already registered
Added missing import
added setter for current
Add a method to draw the HTML page into the specified canvas
Add a handler to show the camera preview
Change the permission of the temporary file to full
preserve order of methods in cache
Add wake up interval to sleep mode dialog
Check payload size in Http2UpgradeHandler
Renamed comments to people
Remove unnecessary try catch
Add a seed to the ControlledRandom
Don t show hidden tasks in gtasks filter
Make some methods final
Set the default value of ciphersuites disabled to empty string
Fix log message in ShardingService
make getDefaultLaf public
Added isDbLockedByCurrentThread to ShadowSQLiteDatabase
Fix expected output type for load nodes
Fix NPE in PercentFrameLayout
Added a comment about the algorithm used here
Fix NPE when there is no network name
Make JsonMessageFactory abstract
Fix jingle reason parsing
remove unnecessary method
Fix a bug in Files appendPath
Remove unneeded null check
Make POSDictionaryBuilder constructors public
Added a TODO
Fix RmStep pathRelativizer
Make ErrorOutputStream logs with a warning level
Fix NPE in FileChooserActivity
added support for setProperty for MetaClass
Fix SystemGesturesPointerEventListener crash
Ignore com apple developer associated domains in provisioning profile store
Fix NPE in BuildCheckpoints
Remove unnecessary generics
add workaround for MathJax sizing on Windows
Remove unnecessary logging
Ignores test which can t be executed as automated
Fix default scale value
Removed unused method
Improve javadoc for CodeFlow createSignatureDescriptor
Fix Broadcaster classloading
Removed obsolete TODO
Set column names must use DKV instead of UKV
don t print null objects
Fixed thread name
Set unstable flag on DL output
Don t check visibility of interfaces
Add a comment
Fix error message for Ordering leastOf
Fixed a bug in Address toString
Changed the header name to Cache Control
Fix bug where activity was paused twice
Pass lastUsed instead of date to updateToken
Remove unnecessary debug statement
Fixed NPE in OffsetDateTimeField constructor
Improved JavaDoc of MetaClass getMethods
Add CREATOR to SearchView
Rename getUserAudio to getUserMedia
added TODOs to decoder reader tests
Use system resource stream for built ins
Fix listmembers example
Only validate top activities when the task is the current task
add application and project components to plugin tags
Remove unused import
en add throws IOException
Revert RunCommand pre prend args
Fixed a bug where the fragment could be added twice
Fix NPE in ConnectivityService
Add monster CreatureType to list of registered creatures
Throw AssertionError in ViewObservable
handle empty string
Fix FileSystemMediumTest on Windows
remove unused method
Remove unnecessary code
Clarify documentation of ParseException
Removed some System outs
Throw IAE if inputGraph inputInputStream is null
removed unused code
Reduce log level for rollingUpdateStep
Fixed typo in javadoc
add instanceof check for VoldemortException
Added missing license header
Fix scaling thread pool test
remove unnecessary code
TEST Don t use transport client nodes in RecoveryPercolatorTests
Fix tag name
fixed bug in runner where idea updater log file was not created
print myDisposed in debug mode
fixed bug in Assert assertNull
don t start welcome screen
improve error message
Fix broken link in documentation
CollectionUtils join should check notNullOrEmpty
added test for sql parser
Don t add views to ignored views list
Remove flush from log output
Don t create new metadata links for tasks with no description date
Make HeliosClient a AutoCloseable
Remove unnecessary check for null
Removed commented out code
Added a null check to the levelOrder method
Remove unnecessary precondition check
