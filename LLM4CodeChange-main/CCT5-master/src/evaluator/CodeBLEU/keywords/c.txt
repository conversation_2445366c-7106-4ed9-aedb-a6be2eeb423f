auto
break
case
char
const
continue
default
do
double
else
enum
extern
if
elif
else
endif
ifdef
ifndef
define
undef
include
line
error
pragma
defined
__has_c_attribute
float
for
goto
if
inline
int
long
register
restrict
return
short
signed
sizeof
static
struct
switch
typedef
union
unsigned
void
volatile
while
_Alignas
_Alignof
_Atomic
_Bool
_Complex
_Decimal128
_Decimal32
_Decimal64
_Generic
_Imaginary
_Noreturn
_Static_assert
_Thread_local