__halt_compiler
abstract
and
array
as
break
callable
case
catch
class
clone
const
continue
declare
default
die
do
echo
else
elseif
empty
enddeclare
endfor
endforeach
endif
endswitch
endwhile
eval
exit
extends
final
finally
fn
for
foreach
function
global
goto
if
implements
include
include_once
instanceof
insteadof
interface
isset
list
match
namespace
new
or
print
private
protected
public
readonly
require
require_once
return
static
switch
throw
trait
try
unset
use
var
while
xor
yield