{"diff": "@@ -1,5 +1,4 @@\n\n-@ApiOperation(value = \"Save cluster basic configuration.\")\n-    @PutMapping(path = \"/clusters/basic\", consumes = APPLICATION_JSON_VALUE)\n+@PutMapping(path = \"/clusters/basic\", consumes = APPLICATION_JSON_VALUE)\n     public ResponseEntity<Void> saveBasicCluster(\n         @AuthenticationPrincipal Account acc,\n         @RequestHeader(value = \"demoMode\", defaultValue = \"false\") boolean demo,", "nl": "Save cluster basic configuration.", "old_nl": "Save basic clusters.", "peft": "1\tSave basic cluster.\n", "icl": "1\tSave basic clusters.\n", "slm": "Save cluster basic configuration.", "label": ["Feat"]}
{"diff": "@@ -1,14 +1,14 @@\n\n-private SSLConfig configureLegacyServerSSL(final DistributionConfig distributionConfig,\n-      final SSLConfig sslConfig) {\n-    sslConfig.setCiphers(distributionConfig.getServerSSLCiphers());\n-    sslConfig.setEnabled(distributionConfig.getServerSSLEnabled());\n-    sslConfig.setKeystore(distributionConfig.getServerSSLKeyStore());\n-    sslConfig.setKeystorePassword(distributionConfig.getServerSSLKeyStorePassword());\n-    sslConfig.setKeystoreType(distributionConfig.getServerSSLKeyStoreType());\n-    sslConfig.setTruststore(distributionConfig.getServerSSLTrustStore());\n-    sslConfig.setTruststorePassword(distributionConfig.getServerSSLTrustStorePassword());\n-    sslConfig.setTruststoreType(distributionConfig.getServerSSLKeyStoreType());\n-    sslConfig.setProtocols(distributionConfig.getServerSSLProtocols());\n-    sslConfig.setRequireAuth(distributionConfig.getServerSSLRequireAuthentication());\n-    return sslConfig;\n+private SSLConfig.Builder configureLegacyServerSSL(final DistributionConfig distributionConfig,\n+      final SSLConfig.Builder sslConfigBuilder) {\n+    sslConfigBuilder.setCiphers(distributionConfig.getServerSSLCiphers());\n+    sslConfigBuilder.setEnabled(distributionConfig.getServerSSLEnabled());\n+    sslConfigBuilder.setKeystore(distributionConfig.getServerSSLKeyStore());\n+    sslConfigBuilder.setKeystorePassword(distributionConfig.getServerSSLKeyStorePassword());\n+    sslConfigBuilder.setKeystoreType(distributionConfig.getServerSSLKeyStoreType());\n+    sslConfigBuilder.setTruststore(distributionConfig.getServerSSLTrustStore());\n+    sslConfigBuilder.setTruststorePassword(distributionConfig.getServerSSLTrustStorePassword());\n+    sslConfigBuilder.setTruststoreType(distributionConfig.getServerSSLKeyStoreType());\n+    sslConfigBuilder.setProtocols(distributionConfig.getServerSSLProtocols());\n+    sslConfigBuilder.setRequireAuth(distributionConfig.getServerSSLRequireAuthentication());\n+    return sslConfigBuilder;\n   }", "nl": "Configure a SSLConfig.Builder for the server using the legacy configuration", "old_nl": "Configure a sslConfig for the server using the legacy configuration", "peft": "2\tConfigure a SSLConfig.Builder for the server using the legacy configuration\n", "icl": "2\tConfigure a sslConfig for the server using the legacy configuration\n", "slm": "Configure a sslConfig builder for the server using the legacy configuration.", "label": ["Ref"]}
{"diff": "@@ -1,7 +1,7 @@\n\n @Test\n-    public void testNpara() {\n-        System.out.println(\"npara\");\n+    public void testLength() {\n+        System.out.println(\"length\");\n         GeometricDistribution instance = new GeometricDistribution(0.3);\n         instance.rand();\n-        assertEquals(1, instance.npara());\n+        assertEquals(1, instance.length());\n     }", "nl": "Test of length method, of class GeometricDistribution.", "old_nl": "Test of npara method, of class GeometricDistribution.", "peft": "4\tTest of length method, of class GeometricDistribution.\n", "icl": "4\tTest of length method, of class GeometricDistribution.\n", "slm": "Test of length method, of class Geometric distribution.", "label": ["Ref"]}
{"diff": "@@ -1,7 +1,7 @@\n\n public static int[] drawSparseSample(int n, int N, Random r) throws IllegalArgumentException {\n \n     if ((n > N) || (n < 0) || (N < 0)) {\n-      throw new IllegalArgumentException(\"drawSparseSampl: cannot sample\" + n + \" points from \" + N + \" points.\");\n+      throw new IllegalArgumentException(\"drawSparseSample: cannot sample\" + n + \" points from \" + N + \" points.\");\n     }\n \n     final int[] vals = new int[n]; // This will hold the n selected indices\n@@ -13,7 +13,7 @@\n\n       final Integer index_rand = r.nextInt(i);\n       final Integer iObj = i - 1;\n       final Integer stored_at_index_from_end = map.remove(iObj);\n-      if (index_rand == iObj) { // Swapping the value with itself\n+      if (index_rand.equals(iObj)) { // Last element selected? (Making sure we use correct comparison!)\n         vals[selected++] = (stored_at_index_from_end != null) ? stored_at_index_from_end : iObj;\n       } else {\n         final Integer stored_at_index_rand = map.put(index_rand,", "nl": "Bui (2015) \"CacheDiff: Fast Random Sampling\" URL", "old_nl": "Bui (2015) \"Cachediff: Fast Random Sampling\" URL", "peft": "5\tBui (2015) \"Cachediff: Fast Random Sampling\" URL\n", "icl": "5\tBui (2015) \"Cachediff: Fast Random Sampling\" URL\n", "slm": "Bui (2015) \"Cachediff: Fast Random Sampling\" URL", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public @NonNull ListenableFuture<BrowserResult> getSearchResult(final @NonNull String query,\n+public @NonNull ListenableFuture<LibraryResult> getSearchResult(final @NonNull String query,\n             @IntRange(from = 0) int page, @IntRange(from = 1) int pageSize,\n             final @Nullable LibraryParams params) {\n         if (TextUtils.isEmpty(query)) {", "nl": "If it's successfully completed, {@link LibraryResult#getMediaItems()} will return the search result.", "old_nl": "If it's successfully completed, {@link BrowserResult#getMediaItems()} will return the search result.", "peft": "6\tIf it's successfully completed, {@link LibraryResult#getMediaItems()} will return the search result.\n", "icl": "6\tIf it's successfully completed, {@link LibraryResult#getMediaItems()} will return the search result.\n", "slm": "If it's successfully completed, {@link LibraryResult#getMediaItems()} will return the search result.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,4 @@\n\n-public void setOptions(final List<DialogNodeOutputOptionsElement> options) {\n-    this.options = options;\n-  }\n+public Builder options(List<DialogNodeOutputOptionsElement> options) {\n+      this.options = options;\n+      return this;\n+    }", "nl": "Set the options.", "old_nl": "Sets the options.", "peft": "7\tAdds the options.\n", "icl": "7\tSets the options.\n", "slm": "Add the options.", "label": ["Ref"]}
{"diff": "@@ -1,25 +1,45 @@\n\n-private Properties getInstallationProperties() {\n+private Properties getInstallationScript() {\n \t\tProperties prop = new Properties();\n-\t\tString fileName = System.getProperty(\"installation.script\", null);\n+\t\t\n+\t\tString fileName = System.getProperty(\"installation.script\");\n+\t\tif (fileName == null) {\n+\t\t\treturn prop;\n+\t\t}\n+\t\t\n \t\tFile file = new File(fileName);\n-\t\tif (!file.exists()) {\n+\t\tif (file.exists()) {\n+\t\t\tInputStream input = null;\n \t\t\ttry {\n-\t\t\t\tprop.load(getClass().getClassLoader().getResourceAsStream(fileName));\n-\t\t\t\tlog.info(prop);\n+\t\t\t\tinput = new FileInputStream(fileName);\n+\t\t\t\tprop.load(input);\n+\t\t\t\tlog.info(\"Using installation script from absolute path: \" + file.getAbsolutePath());\n+\t\t\t\t\n+\t\t\t\tinput.close();\n \t\t\t}\n \t\t\tcatch (IOException ex) {\n-\t\t\t\tlog.error(\"Failed to load installation properties: \" + fileName, ex);\n+\t\t\t\tlog.error(\"Failed to load installation script from absolute path: \" + file.getAbsolutePath(), ex);\n+\t\t\t\tthrow new RuntimeException(ex);\n+\t\t\t}\n+\t\t\tfinally {\n+\t\t\t\tIOUtils.closeQuietly(input);\n \t\t\t}\n \t\t} else {\n+\t\t\tInputStream input = null;\n \t\t\ttry {\n-\t\t\t\tFileInputStream propertyStream = new FileInputStream(fileName);\n-\t\t\t\tprop.load(propertyStream);\n-\t\t\t\tlog.info(prop);\n-\t\t\t\tIOUtils.closeQuietly(propertyStream);\n+\t\t\t\tinput = getClass().getClassLoader().getResourceAsStream(fileName);\n+\t\t\t\tprop.load(input);\n+\t\t\t\tlog.info(\"Using installation script from classpath: \" + fileName);\n+\t\t\t\t\n+\t\t\t\tinput.close();\n \t\t\t}\n \t\t\tcatch (IOException ex) {\n-\t\t\t\tlog.error(\"Failed to load installation properties: \" + fileName, ex);\n+\t\t\t\tlog.error(\"Failed to load installation script from classpath: \" + fileName, ex);\n+\t\t\t\tthrow new RuntimeException(ex);\n+\t\t\t}\n+\t\t\tfinally {\n+\t\t\t\tIOUtils.closeQuietly(input);\n \t\t\t}\n \t\t}\n+\t\t\n \t\treturn prop;\n \t}", "nl": "Convenience method to get custom installation script", "old_nl": "Convenience method to get custom install file", "peft": "8\tConvenience method to get custom install script\n", "icl": "8\tIt reads the installation script file and returns the properties.\n", "slm": "Convenience method to get custom install script", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,4 @@\n\n-public void setConfidence(final Double confidence) {\n-    this.confidence = confidence;\n-  }\n+public Builder confidence(Double confidence) {\n+      this.confidence = confidence;\n+      return this;\n+    }", "nl": "Set the confidence.", "old_nl": "Sets the confidence.", "peft": "9\tAdd the confidence.\n", "icl": "9\tSets the confidence.\n", "slm": "Add the confidence.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n public synchronized int requestUpdate() {\n         this.needUpdate = true;\n-        return this.version;\n+        return this.updateVersion;\n     }", "nl": "Request an update of the current cluster metadata info, return the current updateVersion before the update", "old_nl": "Request an update of the current cluster metadata info, return the current version before the update", "peft": "10\tRequest an update of the current cluster metadata info, return the current update version before the update\n", "icl": "10\tRequest an update of the current cluster metadata info, return the current version before the update\n", "slm": "Request an update of the current cluster metadata info, return the current update version before the update", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public static String getFullPath(final String filename) {\n-        return doGetFullPath(filename, true);\n+public static String getFullPath(final String fileName) {\n+        return doGetFullPath(fileName, true);\n     }", "nl": "Gets the full path from a full fileName, which is the prefix + path.", "old_nl": "Gets the full path from a full filename, which is the prefix + path.", "peft": "11\tGets the full path from a full file name, which is the prefix + path.\n", "icl": "11\tGet the full path from a full filename, which is the prefix + path.\n", "slm": "Gets the full path from a full file name, which is the prefix + path.", "label": ["Ref"]}
{"diff": "@@ -1,12 +1,12 @@\n\n-void unlink(Node<E> p, Node<E> trail) {\n+void unlink(Node<E> p, Node<E> pred) {\n         // assert putLock.isHeldByCurrentThread();\n         // assert takeLock.isHeldByCurrentThread();\n         // p.next is not changed, to allow iterators that are\n         // traversing p to maintain their weak-consistency guarantee.\n         p.item = null;\n-        trail.next = p.next;\n+        pred.next = p.next;\n         if (last == p)\n-            last = trail;\n+            last = pred;\n         if (count.getAndDecrement() == capacity)\n             notFull.signal();\n     }", "nl": "Unlinks interior Node p with predecessor pred.", "old_nl": "Unlinks interior Node p with predecessor trail.", "peft": "12\tUnlinks interior Node p with predecessor pred.\n", "icl": "12\tUnlinks interior Node p with predecessor pred.\n", "slm": "Unlinks interior Node p with predecessor pred.", "label": ["Ref"]}
{"diff": "@@ -1,35 +1,3 @@\n\n-public static MongoClient create(final ConnectionString connectionString, final List<CommandListener> commandListeners) {\n-        ClusterSettings clusterSettings = ClusterSettings.builder()\n-                .applyConnectionString(connectionString)\n-                .build();\n-        ConnectionPoolSettings connectionPoolSettings = ConnectionPoolSettings.builder()\n-                .applyConnectionString(connectionString)\n-                .build();\n-        ServerSettings serverSettings = ServerSettings.builder()\n-                .applyConnectionString(connectionString)\n-                .build();\n-        SslSettings sslSettings = SslSettings.builder()\n-                .applyConnectionString(connectionString)\n-                .build();\n-        SocketSettings socketSettings = SocketSettings.builder()\n-                .applyConnectionString(connectionString)\n-                .build();\n-        SocketSettings heartbeatSocketSettings = SocketSettings.builder()\n-                .connectTimeout(20, TimeUnit.SECONDS)\n-                .readTimeout(20, TimeUnit.SECONDS)\n-                .build();\n-\n-        StreamFactory streamFactory = new SocketStreamFactory(socketSettings, sslSettings);\n-\n-        StreamFactory heartbeatStreamFactory = new SocketStreamFactory(heartbeatSocketSettings, sslSettings);\n-\n-        List<MongoCredential> credentialList = getCredentialList(connectionString);\n-        Cluster cluster = new DefaultClusterFactory().createCluster(clusterSettings,\n-                serverSettings, connectionPoolSettings, streamFactory, heartbeatStreamFactory,\n-                credentialList,\n-                getCommandListener(commandListeners), connectionString.getApplicationName(),\n-                null, connectionString.getCompressorList());\n-\n-        return new MongoClientImpl(cluster, credentialList, getReadPreference(connectionString), getWriteConcern(connectionString),\n-                connectionString.getRetryWrites(), getReadConcern(connectionString));\n+public static MongoClient create(final MongoClientSettings settings) {\n+        return create(settings, null);\n     }", "nl": "Create a new client with the given client settings.", "old_nl": "Create a new client with the given connection string.", "peft": "13\tCreate a new client with the given settings.\n", "icl": "13\tCreate a new client with the given connection string.\n", "slm": "Create a new client with the given MongoClientSettings.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public BlobDescriptor toBlobDescriptor() {\n+public void computeDigest() {\n     try {\n       byte[] hashedBytes = digest.digest();\n \n@@ -9,8 +9,9 @@\n\n       }\n       String hash = stringBuilder.toString();\n \n-      DescriptorDigest digest = DescriptorDigest.fromHash(hash);\n-      return new BlobDescriptor(totalBytes, digest);\n+      bytesHashed = bytesSoFar;\n+      descriptorDigest = DescriptorDigest.fromHash(hash);\n+      bytesSoFar = 0;\n \n     } catch (DigestException ex) {\n       throw new RuntimeException(\"SHA-256 algorithm produced invalid hash: \" + ex.getMessage(), ex);", "nl": "The buffer resets after this method is called, so this method should only be called once per computation.", "old_nl": "The buffer resets after this method is called, so this method should only be called once per BlobDescriptor.", "peft": "14\tThe buffer resets after this method is called, so this method should only be called once per Blob.\n", "icl": "14\tIt resets the buffer and computes the digest of the bytes that have been written to the buffer.\n", "slm": "The buffer resets after this method is called, so this method should only be called once per DescriptorDigest.", "label": ["Feat"]}
{"diff": "@@ -39,7 +39,7 @@\n\n         ImmutableList.of(),\n         Linker.LinkableDepType.STATIC,\n         CxxLinkOptions.of(),\n-        FluentIterable.from(params.getBuildDeps()).filter(NativeLinkable.class),\n+        FluentIterable.from(params.getBuildDeps()).filter(NativeLinkableGroup.class),\n         /* cxxRuntimeType */ Optional.empty(),\n         /* bundleLoader */ Optional.empty(),\n         ImmutableSet.of(),", "nl": "Creates a {@link NativeLinkableGroup} using sources compiled by the D compiler.", "old_nl": "Creates a {@link NativeLinkable} using sources compiled by the D compiler.", "peft": "16\tCreates a {@link NativeLinkableGroup} using sources compiled by the D compiler.\n", "icl": "16\tCreates a {@link NativeLinkable} using sources compiled by the D compiler.\n", "slm": "Creates a {@link NativeLinkableGroup} using sources compiled by the D compiler.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-public Builder setIsBusy(boolean isBusy) {\n-            this.isBusy = isBusy;\n+public Builder setBusySince(int busySince) {\n+            this.busySince = busySince;\n \n             return this;\n         }", "nl": "Sets the CPU status (free or busy since some ms).", "old_nl": "Sets the CPU status (free or busy).", "peft": "17\tSets the time when the CPU became busy.\n", "icl": "17\tSets the CPU status (free or busy).\n", "slm": "Sets the CPU status (free or busy)Since.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,5 @@\n\n public static void maximum(Planar<GrayF64> input, GrayF64 output, int startBand, int lastBand) {\n \t\tcheckInput(input, startBand, lastBand);\n-\t\tImplImageBandMath.maximum(input,output,startBand,lastBand);\n+\t\toutput.reshape(input.width,input.height);\n+\t\tImplImageBandMath.maximum(input, output, startBand, lastBand);\n \t}", "nl": "Computes the maximum for each pixel across selected bands in the {@link Planar} image.", "old_nl": "Computes the maximum for each pixel across all bands in the {@link Planar} image.", "peft": "18\tComputes the maximum for each pixel across the specified bands in the {@link Planar} image.\n", "icl": "18\tReshape the output image.\n", "slm": "Computes the maximum for each pixel across all band in the {@link PlanarImage} image.", "label": ["Feat"]}
{"diff": "@@ -5,7 +5,7 @@\n\n         if (getDeviceKey() != null)\n             sb.append(\"DeviceKey: \").append(getDeviceKey()).append(\",\");\n         if (getAccessToken() != null)\n-            sb.append(\"AccessToken: \").append(getAccessToken());\n+            sb.append(\"AccessToken: \").append(\"***Sensitive Data Redacted***\");\n         sb.append(\"}\");\n         return sb.toString();\n     }", "nl": "Returns a string representation of this object.", "old_nl": "Returns a string representation of this object; useful for testing and debugging.", "peft": "19\tReturns a string representation of this object; useful for testing and debugging.\n", "icl": "19\tReturns a string representation of this object; useful for testing and debugging.\n", "slm": "This object is useful for testing and testing.", "label": ["Ref"]}
{"diff": "@@ -1,9 +1,9 @@\n\n-public static byte[] toAsciiBytes(byte[] raw) {\n-        if (raw == null || raw.length == 0) {\n+public static byte[] toAsciiBytes(final byte[] raw) {\n+        if (isEmpty(raw)) {\n             return EMPTY_BYTE_ARRAY;\n         }\n         // get 8 times the bytes with 3 bit shifts to the left of the length\n-        byte[] l_ascii = new byte[raw.length << 3];\n+        final byte[] l_ascii = new byte[raw.length << 3];\n         /*\n          * We decr index jj by 8 as we go along to not recompute indices using multiplication every time inside the\n          * loop.", "nl": "Converts an array of raw binary data into an array of ASCII 0 and 1 character bytes - each byte is a truncated char.", "old_nl": "Converts an array of raw binary data into an array of ascii 0 and 1 character bytes - each byte is a truncated char.", "peft": "22\tConverts an array of raw binary data into an array of ascii 0 and 1 character bytes - each byte is a truncated char.\n", "icl": "22\tConverts an array of raw binary data into an array of ascii 0 and 1 character bytes - each byte is a truncated char.\n", "slm": "Converts an array of raw binary data into an array of ascii 0 and 1 character bytes - each byte is a truncated char.", "label": ["Feat"]}
{"diff": "@@ -1,6 +1,10 @@\n\n public void pauseDownloads() {\n+    if (downloadsPaused) {\n+      return;\n+    }\n+    downloadsPaused = true;\n     pendingMessages++;\n     internalHandler\n-        .obtainMessage(MSG_SET_DOWNLOADS_RESUMED, /* downloadsResumed */ 0, /* unused */ 0)\n+        .obtainMessage(MSG_SET_DOWNLOADS_PAUSED, /* downloadsPaused */ 1, /* unused */ 0)\n         .sendToTarget();\n   }", "nl": "Pauses downloads.", "old_nl": "Pauses all downloads.", "peft": "23\tPauses all downloads and prevents new downloads from starting.\n", "icl": "23\tPauses all downloads.\n", "slm": "Pauses all downloads.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,4 @@\n\n-public Class<?> loadClassWithApplicationClassLoader(String name) throws ClassNotFoundException {\n-    return applicationClassLoader.getClassLoader().loadClass(name);\n+public Class<?> loadClassWithATestRunnerClassLoader(String name) throws ClassNotFoundException {\n+    ArtifactClassLoader classLoader = getTestRunnerPluginClassLoader();\n+    return classLoader.getClassLoader().loadClass(name);\n   }", "nl": "Loads the {@link Class} using the test runner {@link ArtifactClassLoader}.", "old_nl": "Loads the {@link Class} using the application {@link ArtifactClassLoader}.", "peft": "24\tLoads the {@link Class} using the {@link ArtifactClassLoader} of the {@link TestRunnerPlugin}.\n", "icl": "24\tLoads the Class using the application ArtifactClassLoader.\n", "slm": "Loads the {@link Class} using the test runner plugin {@link ArtifactClassLoader}.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public static Schema buildBeamSqlRowType(Object... args) {\n+public static Schema buildBeamSqlSchema(Object... args) {\n     return Stream.iterate(0, i -> i + 2)\n         .limit(args.length / 2)\n         .map(i -> toRecordField(args, i))", "nl": "Convenient way to build a {@link Schema}.", "old_nl": "Convenient way to build a {@code BeamSqlRowType}.", "peft": "25\tConvenient way to build a {@code BeamSqlSchema}.\n", "icl": "25\tConvenient way to build a {@code BeamSqlSchema}.\n", "slm": "Convenient way to build a {@code BeamSqlSchema}.", "label": ["Ref"]}
{"diff": "@@ -9,7 +9,7 @@\n\n         Rectangle transformedIntersection = null;\n \n         if (intersectionRect != null) {\n-            transformedIntersection = shearCoordinatesAndInverseY(imageRect.getLeft(), imageRect.getTop(), intersectionRect);\n+            transformedIntersection = transformIntersection(renderInfo.getImageCTM(), intersectionRect);\n         }\n \n         return new PdfCleanUpCoveredArea(transformedIntersection, imageRect.equals(intersectionRect));", "nl": "Calculates intersection of the image and the render filter region in the coordinate system relative to the image.", "old_nl": "Calculates intersection of the image and the render filter region in the coordinate system relative to image.", "peft": "26\tCalculates intersection of the image and the render filter region in the coordinate system relative to image and inverts y coordinate.\n", "icl": "26\tCalculates the intersection of the image and the render filter region in the coordinate system relative to the image.\n", "slm": "Calculates intersection of the image and the render filter region in the coordinate system relative to the image.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public String[] getKeys() {\n-        return keys == null ? new String[0] : CoreUtils.clone(keys);\n+public String getKeyFilter() {\n+        return keyFilter;\n     }", "nl": "If {@code keyFilter = \"abc1234\"}, settings with a key equal to \"abc1234\" are returned.", "old_nl": "If keys = \"abc1234\", settings with a key equal to \"abc1234\" are returned.", "peft": "27\tIf keyFilter = \"abc1234\", settings with a key equal to \"abc1234\" are returned.\n", "icl": "27\tIf keys = \"abc1234\", settings with a key equal to \"abc1234\" are returned.\n", "slm": "If keyFilter = \"abc1234\", settings with a key equal to \"abc1234\" are returned.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,4 @@\n\n protected boolean isTimeOnly(final String isoDate) {\n-        return isoDate.matches(\"^\\\\d{2}:\\\\d{2}:\\\\d{2}$\");\n+        return isoDate.matches(\"^\\\\d{2}:\\\\d{2}:\\\\d{2}$\")\n+                || NowAndTodayUtil.isNowOrTodayFormat(isoDate);\n     }", "nl": "CAUTION: Does NOT recognize values with a timezone information (...[+-Z]...)", "old_nl": "18:43:59 CAUTION: Does NOT recognize values with a timezone information (...[+-Z]...)", "peft": "28\t18:43:59 CAUTION: Does NOT recognize values with a timezone information (...[+-Z]...) or \"now\" or \"today\"\n", "icl": "28\tDoes NOT recognize values with a timezone information (...[+-Z]...)\n", "slm": "18:43:59 CAUTION: Does NOT recognize values with a timezone information (...[+-Z]) or null", "label": ["Feat"]}
{"diff": "@@ -1 +1,4 @@\n\n-public void tell( String messageId, Object ... args ) { }\n+@CallerSideMethod\n+    public void tell(String messageId, Object ... args ) {\n+        getScheduler().enqueueCall(Actor.sender.get(),getActor(),messageId,args,false);\n+    }", "nl": "generic method for untyped remoting.", "old_nl": "generic method for untyped messages.", "peft": "29\tgeneric method for untyped messages.\n", "icl": "29\tA generic method for untyped messages.\n", "slm": "generic method for executing messages.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,4 @@\n\n-public static com.liferay.calendar.model.CalendarResource findByC_C(\n-\t\tlong classNameId, long classPK)\n+public static CalendarResource findByC_C(long classNameId, long classPK)\n \t\tthrows com.liferay.calendar.NoSuchResourceException {\n \t\treturn getPersistence().findByC_C(classNameId, classPK);\n \t}", "nl": "or throws a {@link NoSuchResourceException} if it could not be found.", "old_nl": "or throws a {@link com.liferay.calendar.NoSuchResourceException} if it could not be found.", "peft": "31\tor throws a {@link NoSuchResourceException} if it could not be found.\n", "icl": "31\tor throws a {@link NoSuchResourceException} if it could not be found.\n", "slm": "or throws a {@link org.liferay.calendar.NoSuchResourceException} if it could not be found.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public RolePermissions read(boolean read) {\n-        this.read = read;\n+public RolePermissions setReadPermission(boolean hasReadPermission) {\n+        this.readPermission = hasReadPermission;\n         return this;\n     }", "nl": "Sets the read permission status.", "old_nl": "Grants the role read permissions on the resource.", "peft": "33\tSets the read permission status for the role.\n", "icl": "33\tGrants the role read permissions on the resource.\n", "slm": "Grants the role read permissions on the resource.", "label": ["Ref"]}
{"diff": "@@ -1,7 +1,11 @@\n\n-public void waitValueEqualsTo(WebElement webElement, String expectedValue, int timeout) {\n+public void waitValueEqualsTo(WebElement element, String expected, int timeout) {\n+    String[] actual = new String[1];\n     webDriverWaitFactory\n-        .get(timeout)\n+        .get(timeout, () -> format(\"\\nexpected:\\n'%s'\\nbut was:\\n'%s'\\n\", expected, actual[0]))\n         .until(\n             (ExpectedCondition<Boolean>)\n-                driver -> waitVisibilityAndGetValue(webElement, timeout).equals(expectedValue));\n+                driver -> {\n+                  actual[0] = waitVisibilityAndGetValue(element, timeout);\n+                  return actual[0].equals(expected);\n+                });\n   }", "nl": "Waits during {@code timeout} until value extracted from specified {@code element} by {@link", "old_nl": "Waits during {@code timeout} until text extracted from specified {@code webElement} by {@link", "peft": "34\tWaits during {@code timeout} until text extracted from specified {@code element} by {@link #getText(WebElement)} equals to {@code expected} value.\n", "icl": "34\tWaits during {@code timeout} until text extracted from specified {@code element} by {@link\n", "slm": "Waits during {@code timeout} until text extracted from specified {@code element} by {@link", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-protected int argminNegative(double[] best) {\n-      double min = 0.;\n+protected int argmin(double[] best) {\n+      double min = Double.POSITIVE_INFINITY;\n       int ret = -1;\n       for(int i = 0; i < best.length; i++) {\n         final double v = best[i];", "nl": "Find the smallest (most negative) value.", "old_nl": "Check if there is any negative cost (= improvement).", "peft": "36\tCheck if there is any positive cost (= improvement).\n", "icl": "36\tCheck if there is any negative cost (= improvement).\n", "slm": "Check if there is a positive cost (= improvement).", "label": ["Feat"]}
{"diff": "@@ -15,7 +15,12 @@\n\n \t\t// Calling inspect led to an java.lang.ArrayIndexOutOfBoundsException in\n \t\t// 4.0.2\n \t\tSTViz viz = st.inspect();\n-\t\twaitUntilAnyWindowIsVisible(4000);\n-\t\tviz.viewFrame.dispose();\n-\t\twaitUntilAllWindowsAreClosed();\n+\t\tif (interactive) {\n+\t\t\tviz.waitForClose();\n+\t\t}\n+\t\telse {\n+\t\t\twaitUntilAnyWindowIsVisible(4000);\n+\t\t\tviz.viewFrame.dispose();\n+\t\t\twaitUntilAllWindowsAreClosed();\n+\t\t}\n \t}", "nl": "see URL", "old_nl": "see URL html", "peft": "37\tsee URL interactive\n", "icl": "37\tsee URL html\n", "slm": "see URL html", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-static Builder builder(BaseEvent event) {\n+static Builder builder(CoreEvent event) {\n     return new DefaultEventBuilder((InternalEvent) event);\n   }", "nl": "Create new {@link Builder} based on an existing {@link CoreEvent} instance.", "old_nl": "Create new {@link Builder} based on an existing {@link BaseEvent} instance.", "peft": "38\tCreate new {@link Builder} based on an existing {@link CoreEvent} instance.\n", "icl": "38\tCreate new {@link Builder} based on an existing {@link CoreEvent} instance.\n", "slm": "Create new {@link Builder} based on an existing {@link CoreEvent} instance.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-@ApiModelProperty(example = \"false\", required = true, value = \"User / Group may update meta data of nodes: rename files and folders, change classification, etc.\")\n-  public Boolean getChange() {\n+@ApiModelProperty(required = true, value = \"User / Group may update metadata of nodes: rename files and folders, change classification, etc.\")\n+   public Boolean isChange() {\n     return change;\n   }", "nl": "User / Group may update metadata of nodes: rename files and folders, change classification, etc.", "old_nl": "User / Group may update meta data of nodes: rename files and folders, change classification, etc.", "peft": "39\tUser / Group may update metadata of nodes: rename files and folders, change classification, etc.\n", "icl": "39\t### Instruction:\n", "slm": "User / Group may update metadata of nodes: rename files and folders, change classification, etc.", "label": ["Feat"]}
{"diff": "@@ -5,7 +5,7 @@\n\n         if (getName() != null)\n             sb.append(\"Name: \").append(getName()).append(\",\");\n         if (getValue() != null)\n-            sb.append(\"Value: \").append(getValue());\n+            sb.append(\"Value: \").append(\"***Sensitive Data Redacted***\");\n         sb.append(\"}\");\n         return sb.toString();\n     }", "nl": "Returns a string representation of this object.", "old_nl": "Returns a string representation of this object; useful for testing and debugging.", "peft": "40\tReturns a string representation of this object; useful for testing and debugging.\n", "icl": "40\tReturns a string representation of this object; useful for testing and debugging.\n", "slm": "This object is useful for testing and debugging.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,11 @@\n\n-public void setFacility(String facility) {\n-    this.facilityStr = facility;\n+public void setFacility(final String facility) {\n+    if (facility != null) {\n+        syslogFacility = getFacility(facility);\n+        if (syslogFacility == -1) {\n+          System.err.println(\"[\"+facility +\n+                      \"] is an unknown syslog facility. Defaulting to [USER].\");\n+          syslogFacility = LOG_USER;\n+        }\n+        facilityStr = getFacilityString(syslogFacility);\n+    }\n   }", "nl": "See RFC 3164 for more information about the", "old_nl": "See {@link SyslogConstants} and RFC 3164 for more information about the", "peft": "41\tSee {@link SyslogConstants} and RFC 3164 for more information about the facility.\n", "icl": "41\tSee {@link SyslogConstants} and RFC 3164 for more information about the\n", "slm": "See {@link SyslogConstants} and RFC 3164 for more information about the User Management.", "label": ["Feat"]}
{"diff": "@@ -1,25 +1,33 @@\n\n-public DBParameterGroup createDBParameterGroup(CreateDBParameterGroupRequest createDBParameterGroupRequest) {\n+@Override\n+    public DBParameterGroup createDBParameterGroup(\n+            CreateDBParameterGroupRequest createDBParameterGroupRequest) {\n         ExecutionContext executionContext = createExecutionContext(createDBParameterGroupRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<CreateDBParameterGroupRequest> request = null;\n         Response<DBParameterGroup> response = null;\n-        \n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new CreateDBParameterGroupRequestMarshaller().marshall(super.beforeMarshalling(createDBParameterGroupRequest));\n+                request = new CreateDBParameterGroupRequestMarshaller()\n+                        .marshall(super\n+                                .beforeMarshalling(createDBParameterGroupRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            response = invoke(request, new DBParameterGroupStaxUnmarshaller(), executionContext);\n+            StaxResponseHandler<DBParameterGroup> responseHandler = new StaxResponseHandler<DBParameterGroup>(\n+                    new DBParameterGroupStaxUnmarshaller());\n+            response = invoke(request, responseHandler, executionContext);\n+\n             return response.getAwsResponse();\n \n         } finally {\n-            \n+\n             endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "Once you've created a DB parameter group, you need to associate it with your DB instance using", "old_nl": "Once you've created a DB parameter group, you need to associate it with your DB instance using ModifyDBInstance .", "peft": "42\tOnce you've created a DB parameter group, you need to associate it with your DB instance using ModifyDBInstance.\n", "icl": "42\tOnce you've created a DB parameter group, you need to associate it with your DB instance using ModifyDBInstance.\n", "slm": "Once you've created a DB parameter group, you need to associate it with your DB instance using", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,3 @@\n\n-@Deprecated\n-    default ChangeApplied removeAxioms(OWLOntology ont, Collection<? extends OWLAxiom> axioms) {\n-        return removeAxioms(ont, axioms.stream());\n+default ChangeReport removeAxioms(OWLAxiom... axioms) {\n+        return removeAxioms(Arrays.stream(axioms));\n     }", "nl": "The appropriate RemoveAxiom change objects are automatically generated.", "old_nl": "RemoveAxiom change objects are automatically generated.", "peft": "44\tRemoveAxiom change objects are not automatically generated.\n", "icl": "44\tRemoveAxiom change objects are automatically generated.\n", "slm": "RemoveAxiom change objects are automatically generated.", "label": ["Feat"]}
{"diff": "@@ -1,21 +1,11 @@\n\n-public PGPSignature generate() throws PGPException {\n-        MPInteger[] sigValues;\n-\n+public byte[] getSignature() throws PGPException {\n         if (contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.RSA_SIGN\n-                || contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.RSA_GENERAL) {\n-            sigValues = new MPInteger[1];\n-            sigValues[0] = new MPInteger(new BigInteger(1, contentSigner.getSignature()));\n-        } else if (contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.EDDSA) {\n-            byte[] sig = contentSigner.getSignature();\n-\n-            sigValues = new MPInteger[2];\n-\n-            sigValues[0] = new MPInteger(BigIntegers.fromUnsignedByteArray(sig, 0, 32));\n-            sigValues[1] = new MPInteger(BigIntegers.fromUnsignedByteArray(sig, 32, 32));\n+                || contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.RSA_GENERAL\n+                || contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.EDDSA\n+                || contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.ECDSA\n+                || contentSigner.getKeyAlgorithm() == PublicKeyAlgorithmTags.DSA) {\n+            return contentSigner.getSignature();\n         } else {\n-            sigValues = PGPUtil.dsaSigToMpi(contentSigner.getSignature());\n+           throw new UnsupportedOperationException(\"Unsupported algorithm\");\n         }\n-\n-        return new PGPSignature(new SignaturePacket(sigType, contentSigner.getKeyID(), contentSigner.getKeyAlgorithm(),\n-                contentSigner.getHashAlgorithm(), null, null, null, sigValues));\n     }", "nl": "Return the signature.", "old_nl": "Return a signature object containing the current signature state.", "peft": "45\tReturn the signature bytes.\n", "icl": "45\tReturn a signature object containing the current signature state.\n", "slm": "Return a signature object containing the current signature state for this signature.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public SettingSelector setLabels(String... labels) {\n-        this.labels = labels;\n+public SettingSelector setLabelFilter(String labelFilter) {\n+        this.labelFilter = labelFilter;\n         return this;\n     }", "nl": "If {@code labelFilter = \"*abc*\"}, settings with a label containing \"abc\" are returned.", "old_nl": "If {@code labels = \"*abc*\"}, settings with a label containing \"abc\" are returned.", "peft": "46\tIf {@code labelFilter = \"*abc*\"}, settings with a label containing \"abc\" are returned.\n", "icl": "46\tIf {@code labelFilter = \"*abc*\"}, settings with a label containing \"abc\" are returned.\n", "slm": "If {@code labelFilter = \"*abc*\"}, settings with a label containing \"abc\" are returned.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,4 @@\n\n public void add(final URI uri) {\n-        this.uris.add(uri);\n+        this.unique.add(uri);\n+        this.all.add(uri);\n     }", "nl": "Adds a new URI to the collection.", "old_nl": "Adds a new URI to the list of redirects.", "peft": "47\tAdds a new URI to the list of unique redirects.\n", "icl": "47\tAdds a new URI to the list of redirects.\n", "slm": "Adds a new URI to the list of redirects.", "label": ["Feat"]}
{"diff": "@@ -1,6 +1,6 @@\n\n-public double stdDevBiased(Dataset ds, PointSet region) {\n+public double sampleKurtosisExcess(Dataset ds, PointSet region) {\n \t\tFunction<long[],DoubleType> imgFunc = imgFunc(ds);\n \t\tFunction<PointSet,DoubleType> func =\n-\t\t\t\tnew RealStdDevBiasedFunction<DoubleType>(imgFunc);\n+\t\t\t\tnew RealSampleKurtosisExcessFunction<DoubleType>(imgFunc);\n \t\treturn measure(func, region);\n \t}", "nl": "Returns the (unbiased) kurtosis excess of all the values within a {@link PointSet} region of a {@link Dataset}", "old_nl": "Returns the (biased) estimate of the sample standard deviation of the values within a {@link PointSet} region of a {@link Dataset}.", "peft": "48\tReturns the (biased) estimate of the sample excess kurtosis of the values within a {@link PointSet} region of a {@link Dataset}.\n", "icl": "48\tReturns the (biased) estimate of the sample standard deviation of the values within a {@link PointSet} region of a {@link Dataset}.\n", "slm": "Returns the (biased) estimate of the sample variance of the values within a {@link PointSet} region of a {@link Dataset}.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-default MessageReceiver getMessageReceiver() {\n+default MessageChannel getMessageChannel() {\n         return getCause().getContext()\n-                .get(EventContextKeys.MESSAGE_TARGET)\n-                .orElseGet(() -> getCause().first(MessageReceiver.class).orElseGet(Sponge::getSystemSubject));\n+                .get(EventContextKeys.MESSAGE_CHANNEL)\n+                .orElseGet(() -> MessageChannel.to(getCause().first(MessageReceiver.class).orElseGet(Sponge::getSystemSubject)));\n     }", "nl": "The {@link EventContextKeys#MESSAGE_CHANNEL}, if any", "old_nl": "The {@link EventContextKeys#MESSAGE_TARGET}, if any", "peft": "50\tThe {@link EventContextKeys#MESSAGE_CHANNEL}, if any\n", "icl": "50\tThe {@link EventContextKeys#MESSAGE_CHANNEL}, if any\n", "slm": "The {@link EventContextKeys#MESSAGE_CHANNEL}, if any", "label": ["Feat"]}
{"diff": "@@ -1,6 +1,5 @@\n\n @CheckReturnValue\n-  public <U> ObjectArrayAssert<U> extracting(Extractor<? super ELEMENT, U> extractor) {\n-    U[] extracted = FieldsOrPropertiesExtractor.extract(actual, extractor);\n-\n-    return new ObjectArrayAssert<>(extracted);\n+  public <U> AbstractListAssert<?, List<? extends U>, U, ObjectAssert<U>> extracting(Extractor<? super ELEMENT, U> extractor) {\n+    List<U> values = FieldsOrPropertiesExtractor.extract(Arrays.asList(actual), extractor);\n+    return newListAssertInstance(values);\n   }", "nl": "Extract the values from the array's elements by applying an extracting function on them, the resulting list becomes the new object under test.", "old_nl": "Extract the values from the array's elements by applying an extracting function on them.", "peft": "52\tExtract the values from the array's elements by applying an extracting function on them and returns them as a new list.\n", "icl": "52\tExtract the values from the array's elements by applying an extracting function on them.\n", "slm": "Extract the values from the array's elements by applying an extracting function on them.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public void setCipher(Cipher cipher) {\n-        this.cipher = cipher;\n+public void setCipherService(CipherService cipherService) {\n+        this.cipherService = cipherService;\n     }", "nl": "Sets the {@code CipherService} to use for encrypting and decrypting serialized identity data to prevent easy inspection of Subject identity data.", "old_nl": "Sets the {@code Cipher} to use for encrypting and decrypting serialized identity data to prevent easy inspection of Subject identity data.", "peft": "53\tSets the {@code CipherService} to use for encrypting and decrypting serialized identity data to prevent easy inspection of Subject identity data.\n", "icl": "53\tSets the {@code CipherService} to use for encrypting and decrypting serialized identity data to prevent easy inspection of Subject identity data.\n", "slm": "Sets the {@codeCipherService} to use for encrypting and decrypting serialized identity data to prevent easy inspection of Subject identity data.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,3 @@\n\n public static WithAdjuster next(DayOfWeek dayOfWeek) {\n-        Objects.requireNonNull(dayOfWeek, \"dayOfWeek\");\n         return new RelativeDayOfWeek(2, dayOfWeek);\n     }", "nl": "Returns the next day-of-week adjuster, which adjusts the date to the first occurrence of the specified day-of-week after the date being adjusted.", "old_nl": "Returns the next day-of-week adjuster, which adjusts the date to be the next of the specified day-of-week after the specified date.", "peft": "56\tReturns the next day-of-week adjuster, which adjusts the date to be the next of the specified day-of-week after the date being adjusted.\n", "icl": "56\tReturns the next day-of-week adjuster, which adjusts the date to be the next of the specified day-of-week after the specified date.\n", "slm": "Returns the next day-of-week adjuster, which adjusts the date to be the next of the specified day of week after the specified date.", "label": ["Feat"]}
{"diff": "@@ -1,7 +1,7 @@\n\n @ServiceMethod(returns = ReturnType.SINGLE)\n-    public Mono<Response<ConfigurationSetting>> addSettingWithResponse(ConfigurationSetting setting) {\n+    public Mono<Response<ConfigurationSetting>> addConfigurationSettingWithResponse(ConfigurationSetting setting) {\n         try {\n-            return withContext(context -> addSetting(setting, context));\n+            return withContext(context -> addConfigurationSetting(setting, context));\n         } catch (RuntimeException ex) {\n             return monoError(logger, ex);\n         }", "nl": "{@codesnippet com.azure.data.appconfiguration.configurationasyncclient.addConfigurationSettingWithResponse#ConfigurationSetting}", "old_nl": "{@codesnippet com.azure.data.appconfiguration.configurationasyncclient.addSettingWithResponse#ConfigurationSetting}", "peft": "57\t{@codesnippet com.azure.data.appconfiguration.configurationasyncclient.addConfigurationSettingWithResponse#ConfigurationSetting}\n", "icl": "57\t{@codesnippet com.azure.data.appconfiguration.configurationasyncclient.addConfigurationSettingWithResponse#ConfigurationSetting}\n", "slm": "{@codesnippet com.azure.data.appconfiguration.configurationasyncclient.addConfigurationSettingWithResponse#ConfigurationSetting}", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,5 @@\n\n-public void setC_ElementValue_ID (int C_ElementValue_ID)\n+@Override\n+\tpublic void setC_ElementValue_ID (int C_ElementValue_ID)\n \t{\n \t\tif (C_ElementValue_ID < 1) \n \t\t\tset_Value (COLUMNNAME_C_ElementValue_ID, null);", "nl": "Set Kontenart.", "old_nl": "Set Account Element.", "peft": "58\tSet Elementwert.\n", "icl": "58\tSet Account Element.\n", "slm": "Set Account Element.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n default OrderType getOrderType() {\n-        return getAsString(ORDER_TYPE)\n+        return getAsString(HasOrderTypeUtil.ORDER_TYPE)\n             .map(OrderType::valueOf)\n             .orElse(OrderType.ASC);\n     }", "nl": "The {@code orderType} is an enum value located under the {@link HasOrderTypeUtil#ORDER_TYPE} key.", "old_nl": "The {@code orderType} is an enum value located under the {@link #ORDER_TYPE} key.", "peft": "60\tThe {@code orderType} is an enum value located under the {@link HasOrderTypeUtil#ORDER_TYPE} key.\n", "icl": "60\tThe {@code orderType} is an enum value located under the {@link HasOrderTypeUtil#ORDER_TYPE} key.\n", "slm": "The {@code orderType} is an enum value located under the {@linkHasOrderTypeUtil#ORDER_TYPE} key.", "label": ["Ref"]}
{"diff": "@@ -1,10 +1,10 @@\n\n-private Long readOptionalLong(JSONObject json, String key, Long defaultValue) {\n+private Date readOptionalDate(JSONObject json, String key) {\n \n         try {\n             String str = json.getString(key);\n-            return Long.valueOf(str);\n+            return new Date(Long.parseLong(str));\n         } catch (NumberFormatException | JSONException e) {\n             LOG.debug(\"Reading optional JSON Long failed. Default to provided default value.\", e);\n         }\n-        return defaultValue;\n+        return null;\n     }", "nl": "Read an optional Date value (stored as string) form a JSON Object.", "old_nl": "Read an optional Long value (stored as string) form a JSON Object.", "peft": "61\tRead an optional Date value (stored as string) form a JSON Object.\n", "icl": "61\tRead an optional Date value (stored as string) form a JSON Object.\n", "slm": "Read an optional Date value (stored as string) form a JSON Object.", "label": ["Feat"]}
{"diff": "@@ -1,13 +1,13 @@\n\n public void swapSheet ()\n     {\n         try {\n-            if (isModified()) {\n+            if (isModified() || isUpgraded()) {\n                 logger.info(\"{} storing\", this);\n                 storeSheet();\n             }\n \n             if (sheet != null) {\n-                logger.info(\"{} disposed\", sheet);\n+                logger.info(\"Disposed sheet{}\", sheet.getStub().getNum());\n                 sheet = null;\n                 Memory.gc(); // Trigger a garbage collection...\n             }", "nl": "If modified or upgraded, sheet material will be stored before being disposed of.", "old_nl": "If modified and not discarded, sheet material will be stored before being disposed of.", "peft": "63\tIf modified or upgraded, sheet material will be stored before being disposed of.\n", "icl": "63\tIf modified and not discarded, sheet material will be stored before being disposed of.\n", "slm": "If modified and not upgraded, sheet material will be stored before being disposed of.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,4 @@\n\n-public boolean isAccessible() {\n+@Deprecated(since=\"9\")\n+    public boolean isAccessible() {\n         return override;\n     }", "nl": "Get the value of the {@code accessible} flag for this reflected object.", "old_nl": "Get the value of the {@code accessible} flag for this object.", "peft": "64\tGet the value of the {@code accessible} flag for this object.  This flag is set by {@link #setAccessible(boolean) setAccessible} and cleared by {@link #setAccessible(boolean) setAccessible(false)}.\n", "icl": "64\tGet the value of the {@code accessible} flag for this object.\n", "slm": "Gets the value of the {@code accessible} flag for this object.", "label": ["Feat"]}
{"diff": "@@ -1,16 +1,11 @@\n\n-static void getClientAppImg(StringBuilder buf, String name) {\n-        if (_binary.containsKey(name)) {\n-            buf.append(\"<img src=\\\"/Plugins/pluginicon?plugin=\").append(name).append(\"\\\" height=\\\"16\\\" width=\\\"16\\\" alt=\\\"\\\">\");\n-        } else {\n-            String iconpath = _icons.get(name);\n+private static void getClientAppImg(StringBuilder buf, String name, String iconpath) {\n             if (iconpath != null) {\n                 buf.append(\"<img src=\\\"\").append(iconpath).append(\"\\\" height=\\\"16\\\" width=\\\"16\\\" alt=\\\"\\\">\");\n-            } else if (name.equals(\"Orchid\")) {\n+            } else if (name.equals(\"orchid\")) {\n                 buf.append(\"<img src=\\\"/themes/console/light/images/flower.png\\\" alt=\\\"\\\">\");\n             } else if (name.equals(\"i2pbote\")) {\n                 buf.append(\"<img src=\\\"/themes/console/light/images/mail_black.png\\\" alt=\\\"\\\">\");\n             } else {\n                 buf.append(\"<img src=\\\"/themes/console/images/plugin.png\\\" height=\\\"16\\\" width=\\\"16\\\" alt=\\\"\\\">\");\n             }\n-        }\n     }", "nl": "Get 16x16 icon img and append to buf", "old_nl": "Get icon img and append to buf", "peft": "65\tGet icon img and append to buf, if iconpath is null, use default icon\n", "icl": "65\tGet icon img and append to buf\n", "slm": "Get icon img and append to buf", "label": ["Feat"]}
{"diff": "@@ -1,22 +1,17 @@\n\n @Test\n   public void testTimesTranspose() {\n- \n-    final double[] v1 = {1.21, 3 , 7 ,-2};\n-    final double[] v2 = {3.5 , -1, 4 ,-7};\n-    \n-    final double[][] res = {\n-        {v1[0]*v2[0], v1[0]*v2[1], v1[0]*v2[2], v1[0]*v2[3]}, //\n-        {v1[1]*v2[0], v1[1]*v2[1], v1[1]*v2[2], v1[1]*v2[3]}, //\n-        {v1[2]*v2[0], v1[2]*v2[1], v1[2]*v2[2], v1[2]*v2[3]}, //\n-        {v1[3]*v2[0], v1[3]*v2[1], v1[3]*v2[2], v1[3]*v2[3]}  //\n-    };\n-    \n-    assertThat(timesTranspose(v1, v2), is(equalTo(res)));\n-    \n+    final double[] v1 = { 1.21, 3, 7, -2 }, v2 = { 3.5, -1, 4, -7 };\n+    final double[][] res = { //\n+        { v1[0] * v2[0], v1[0] * v2[1], v1[0] * v2[2], v1[0] * v2[3] }, //\n+        { v1[1] * v2[0], v1[1] * v2[1], v1[1] * v2[2], v1[1] * v2[3] }, //\n+        { v1[2] * v2[0], v1[2] * v2[1], v1[2] * v2[2], v1[2] * v2[3] }, //\n+        { v1[3] * v2[0], v1[3] * v2[1], v1[3] * v2[2], v1[3] * v2[3] } };\n+    assertTrue(VMath.equals(timesTranspose(v1, v2), res));\n+\n     // testing timesTranspose(vector, vector) via times(matrix, matrix)\n-    // because times(vector) returns a matrix. This is at the same time a test for transpose if timesTranspose is correct.\n+    // because times(vector) returns a matrix. This is at the same time a test\n+    // for transpose if timesTranspose is correct.\n     final double[][] m1 = transpose(transpose(TESTVEC));\n     final double[][] m2 = transpose(TESTVEC);\n-    \n-    assertThat(timesTranspose(TESTVEC, TESTVEC), is(equalTo(times(m1, m2))));\n+    assertTrue(VMath.equals(timesTranspose(TESTVEC, TESTVEC), times(m1, m2)));\n   }", "nl": "Testing the timesTranspose(vector, vector) method of {@link VMath} class.", "old_nl": "Testing the timesTranspose(vector, vector) method of VMath class.", "peft": "66\tTesting the timesTranspose(vector, vector) method.\n", "icl": "66\tTesting the timesTranspose(vector, vector) method of VMath class.\n", "slm": "Testing the timesTranspose(Vector, vector) method of VMath class.", "label": ["Code&Doc"]}
{"diff": "@@ -5,7 +5,7 @@\n\n         if (getKeyId() != null)\n             sb.append(\"KeyId: \").append(getKeyId()).append(\",\");\n         if (getPlaintext() != null)\n-            sb.append(\"Plaintext: \").append(getPlaintext()).append(\",\");\n+            sb.append(\"Plaintext: \").append(\"***Sensitive Data Redacted***\").append(\",\");\n         if (getEncryptionContext() != null)\n             sb.append(\"EncryptionContext: \").append(getEncryptionContext()).append(\",\");\n         if (getGrantTokens() != null)", "nl": "Returns a string representation of this object.", "old_nl": "Returns a string representation of this object; useful for testing and debugging.", "peft": "67\tReturns a string representation of this object; useful for testing and debugging.\n", "icl": "67\tReturns a string representation of this object; useful for testing and debugging.\n", "slm": "This object is useful for testing and debugging.", "label": ["Feat"]}
{"diff": "@@ -12,9 +12,9 @@\n\n                 return false;\n             }\n             for (Map.Entry<String, Set<OffsetEquation>> entry : superTypeLTL.map.entrySet()) {\n-                String array = entry.getKey();\n+                String sequence = entry.getKey();\n                 Set<OffsetEquation> superOffsets = entry.getValue();\n-                Set<OffsetEquation> subOffsets = map.get(array);\n+                Set<OffsetEquation> subOffsets = map.get(sequence);\n \n                 if (!isSubtypeOffset(subOffsets, superOffsets)) {\n                     return false;", "nl": "Otherwise, this qualifier must contain all the sequences in superType.", "old_nl": "Otherwise, this qualifier must contain all the arrays in superType.", "peft": "68\tOtherwise, this qualifier must contain all the sequences in superType.\n", "icl": "68\tOtherwise, this qualifier must contain all the sequences in superType.\n", "slm": "Otherwise, this qualifier must contain all the offsets in superType.", "label": ["Ref"]}
{"diff": "@@ -1,6 +1,8 @@\n\n-protected Rectangle getSplitButtonBounds ( final Rectangle b, final C c )\n+protected Rectangle getMenuButtonBounds ( final Rectangle b, final C c )\n     {\n         final Insets i = c.getInsets ();\n-        final int x = b.x + ( ltr ? b.width - i.right + contentGap + 1 + splitIconGap : i.left - contentGap - 1 - splitIconGap );\n-        return new Rectangle ( x, b.y + i.top, component.getSplitIcon ().getIconWidth (), b.height - i.top - i.bottom );\n+        final int iconWidth = getMenuIcon ().getIconWidth ();\n+        final int x = b.x + ( ltr ? b.width - i.right + contentGap + 1 + menuIconGap :\n+                i.left - contentGap - 1 - menuIconGap - iconWidth );\n+        return new Rectangle ( x, b.y + i.top, iconWidth, b.height - i.top - i.bottom );\n     }", "nl": "Returns bounds of the menu button part.", "old_nl": "Returns bounds of the split button part.", "peft": "69\tReturns bounds of the menu button part.\n", "icl": "69\tReturns bounds of the menu button part.\n", "slm": "Returns bounds of the menu button part.", "label": ["Feat"]}
{"diff": "@@ -1,21 +1,15 @@\n\n private static boolean isConstructorEmpty(final JConstructor constructor) {\n-    List<JStatement> statements = FluentIterable\n-        .from(constructor.getBody().getStatements())\n-        .filter(new Predicate<JStatement>() {\n-          @Override\n-          public boolean apply(JStatement statement) {\n-            JClassType type = constructor.getEnclosingType();\n-            if (isImplicitSuperCall(statement, type.getSuperClass())) {\n-              return false;\n-            }\n-            if (isEmptyInitCall(statement, type)) {\n-              return false;\n-            }\n-            if (statement instanceof JDeclarationStatement) {\n-              return ((JDeclarationStatement) statement).getInitializer() != null;\n-            }\n-            return true;\n-          }\n-        }).toList();\n-    return statements.isEmpty();\n+    return Iterables.all(constructor.getBody().getStatements(), new Predicate<JStatement>() {\n+      @Override\n+      public boolean apply(JStatement statement) {\n+        JClassType type = constructor.getEnclosingType();\n+        if (isImplicitSuperCall(statement, type.getSuperClass())) {\n+          return true;\n+        }\n+        if (isInitCall(statement, type)) {\n+          return true;\n+        }\n+        return false;\n+      }\n+    });\n   }", "nl": "Returns true if the constructor method is locally empty (allows calls to init and super constructor).", "old_nl": "Returns true if the constructor method is locally empty (allows calls to empty init and super constructor).", "peft": "71\tReturns true if the constructor method is locally empty (allows calls to empty init and super constructor).\n", "icl": "71\tReturns true if the constructor method is locally empty (allows calls to empty init and super constructor).\n", "slm": "Returns true if the constructor method is locally empty (allows calls to empty init and super constructor).", "label": ["Feat"]}
{"diff": "@@ -1,9 +1,9 @@\n\n-public static <T extends ImageBase<T>> void log(T input , T output ) {\n+public static <T extends ImageBase<T>> void log(T input , double val, T output ) {\n \t\tif( input instanceof ImageGray ) {\n \t\t\tif (GrayF32.class == input.getClass()) {\n-\t\t\t\tPixelMath.log((GrayF32) input, (GrayF32) output);\n+\t\t\t\tPixelMath.log((GrayF32) input, (float)val, (GrayF32) output);\n \t\t\t} else if (GrayF64.class == input.getClass()) {\n-\t\t\t\tPixelMath.log((GrayF64) input, (GrayF64) output);\n+\t\t\t\tPixelMath.log((GrayF64) input, val, (GrayF64) output);\n \t\t\t} else {\n \t\t\t\tthrow new IllegalArgumentException(\"Unknown image Type: \" + input.getClass().getSimpleName());\n \t\t\t}\n@@ -12,7 +12,7 @@\n\n \t\t\tPlanar out = (Planar)output;\n \n \t\t\tfor (int i = 0; i < in.getNumBands(); i++) {\n-\t\t\t\tlog( in.getBand(i), out.getBand(i));\n+\t\t\t\tlog( in.getBand(i), val, out.getBand(i));\n \t\t\t}\n \t\t}\n \t}", "nl": "Sets each pixel in the output image to log( val + input(x,y)) of the input image.", "old_nl": "Sets each pixel in the output image to log( 1 + input(x,y)) of the input image.", "peft": "72\tSets each pixel in the output image to log( 1 + input(x,y)) of the input image.\n", "icl": "72\tSet each pixel in the output image to log( 1 + input(x,y)) of the input image.\n", "slm": "Sets each pixel in the output image to log( 1 + input(x,y)) of the input image.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-private static SkylarkInfo makeSchemalessInfoWithF1F2Values(\n+private static SkylarkInfo makeInfoWithF1F2Values(\n       SkylarkProvider provider, @Nullable Object v1, @Nullable Object v2) {\n     ImmutableMap.Builder<String, Object> values = ImmutableMap.builder();\n     if (v1 != null) {\n@@ -7,5 +7,5 @@\n\n     if (v2 != null) {\n       values.put(\"f2\", v2);\n     }\n-    return SkylarkInfo.createSchemaless(provider, values.build(), Location.BUILTIN);\n+    return SkylarkInfo.create(provider, values.build(), Location.BUILTIN);\n   }", "nl": "Creates an instance of a provider with the given values for fields f1 and f2.", "old_nl": "Creates a schemaless instance of a provider with the given values for fields f1 and f2.", "peft": "73\tCreates an instance of a provider with the given values for fields f1 and f2.\n", "icl": "73\tCreates an instance of a provider with the given values for fields f1 and f2.\n", "slm": "Creates a schemaless instance of a provider with the given values for fields f1 and f2.", "label": ["Ref"]}
{"diff": "@@ -9,37 +9,31 @@\n\n         if (p2==null || p2.length()==0)\n             return p1;\n         \n-        int split=p1.indexOf(';');\n-        if (split<0)\n-            split=p1.indexOf('?');\n-        if (split==0)\n-            return p2+p1;\n-        if (split<0)\n-            split=p1.length();\n-\n+        boolean p1EndsWithSlash = p1.endsWith(SLASH);\n+        boolean p2StartsWithSlash = p2.startsWith(SLASH);\n+        \n+        if (p1EndsWithSlash && p2StartsWithSlash)\n+        {\n+            if (p2.length()==1)\n+                return p1;\n+            if (p1.length()==1)\n+                return p2;\n+        }\n+        \n         StringBuilder buf = new StringBuilder(p1.length()+p2.length()+2);\n         buf.append(p1);\n         \n-        if (buf.charAt(split-1)=='/')\n+        if (p1.endsWith(SLASH))\n         {\n-            if (p2.startsWith(URIUtil.SLASH))\n-            {\n-                buf.deleteCharAt(split-1);\n-                buf.insert(split-1,p2);\n-            }\n-            else\n-                buf.insert(split,p2);\n+            if (p2.startsWith(SLASH))\n+                buf.setLength(buf.length()-1);\n         }\n         else\n         {\n-            if (p2.startsWith(URIUtil.SLASH))\n-                buf.insert(split,p2);\n-            else\n-            {\n-                buf.insert(split,'/');\n-                buf.insert(split+1,p2);\n-            }\n+            if (!p2.startsWith(SLASH))\n+                buf.append(SLASH);\n         }\n+        buf.append(p2);\n \n         return buf.toString();\n     }", "nl": "Add two Decoded URI path segments.", "old_nl": "Add two URI path segments.", "peft": "74\tAdd two path segments.\n", "icl": "74\tAdd two URI path segments.\n", "slm": "Add two URI path segments.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-public void in(MetaInAlarm meta, Indicator indicator) {\n-        if (!meta.getIndicatorName().equals(indicatorName)) {\n+public void in(MetaInAlarm meta, Metrics metrics) {\n+        if (!meta.getMetricsName().equals(metricsName)) {\n             //Don't match rule, exit.\n             return;\n         }\n@@ -11,15 +11,15 @@\n\n         }\n \n         if (valueType == null) {\n-            if (indicator instanceof LongValueHolder) {\n-                valueType = IndicatorValueType.LONG;\n-                threshold.setType(IndicatorValueType.LONG);\n-            } else if (indicator instanceof IntValueHolder) {\n-                valueType = IndicatorValueType.INT;\n-                threshold.setType(IndicatorValueType.INT);\n-            } else if (indicator instanceof DoubleValueHolder) {\n-                valueType = IndicatorValueType.DOUBLE;\n-                threshold.setType(IndicatorValueType.DOUBLE);\n+            if (metrics instanceof LongValueHolder) {\n+                valueType = MetricsValueType.LONG;\n+                threshold.setType(MetricsValueType.LONG);\n+            } else if (metrics instanceof IntValueHolder) {\n+                valueType = MetricsValueType.INT;\n+                threshold.setType(MetricsValueType.INT);\n+            } else if (metrics instanceof DoubleValueHolder) {\n+                valueType = MetricsValueType.DOUBLE;\n+                threshold.setType(MetricsValueType.DOUBLE);\n             } else {\n                 return;\n             }\n@@ -30,11 +30,11 @@\n\n             Window window = windows.get(meta);\n             if (window == null) {\n                 window = new Window(period);\n-                LocalDateTime timebucket = TIME_BUCKET_FORMATTER.parseLocalDateTime(indicator.getTimeBucket() + \"\");\n+                LocalDateTime timebucket = TIME_BUCKET_FORMATTER.parseLocalDateTime(metrics.getTimeBucket() + \"\");\n                 window.moveTo(timebucket);\n                 windows.put(meta, window);\n             }\n \n-            window.add(indicator);\n+            window.add(metrics);\n         }\n     }", "nl": "In alarm, only minute dimensionality metrics are expected to process.", "old_nl": "In alarm, only minute dimensionality indicators are expected to process.", "peft": "75\tIn alarm, only minute dimensionality metrics are expected to process.\n", "icl": "75\tIn alarm, only minute dimensionality indicators are expected to process.\n", "slm": "In alarm, only minute dimensionality metrics are expected to process.", "label": ["Ref"]}
{"diff": "@@ -1,6 +1,3 @@\n\n-@Deprecated\n-  public static <T> StarlarkList<T> copyOf(\n-      @Nullable StarlarkThread thread, Iterable<? extends T> elems) {\n-    Mutability mu = thread == null ? null : thread.mutability();\n-    return copyOf(mu, elems);\n+public static <T> StarlarkList<T> immutableCopyOf(Iterable<? extends T> elems) {\n+    return copyOf(null, elems);\n   }", "nl": "Equivalent to {@code copyOf(null, elems)}.", "old_nl": "If {@code thread} is null, the list is immutable.", "peft": "76\tIf {@code thread} is null, the list is frozen (immutable).\n", "icl": "76\tIf {@code thread} is null, the list is immutable.\n", "slm": "If {@code thread} is null, the list is immutable.", "label": ["Feat"]}
{"diff": "@@ -1,9 +1,3 @@\n\n-private Callable<String[]> getDoubleWhoAmICallable(final ReAuthnType type, final String username, final String password) {\n-        return () -> {\n-            final Entry bean = SeccontextUtil.lookup(\n-                    SeccontextUtil.getRemoteEjbName(SERVER1, \"EntryBean\", Entry.class.getName(), isEntryStateful()),\n-                    server1.getApplicationRemotingUrl());\n-            final String server2Url = server2.getApplicationRemotingUrl();\n-            return bean.doubleWhoAmI(username, password, type, server2Url, isWhoAmIStateful());\n-        };\n+protected Callable<String[]> getDoubleWhoAmICallable(final ReAuthnType type, final String username, final String password) {\n+        return getDoubleWhoAmICallable(type, username, password, null);\n     }", "nl": "Creates a callable for executing {@link Entry#doubleWhoAmI(String, String, ReAuthnType, String)} as given user.", "old_nl": "Creates callable for executing {@link Entry#doubleWhoAmI(String, String, ReAuthnType, String)} as given user.", "peft": "77\tCreates callable for executing {@link Entry#doubleWhoAmI(String, String, ReAuthnType, String, boolean)} as given user.\n", "icl": "77\tCreates callable for executing {@link Entry#doubleWhoAmI(String, String, ReAuthnType, String)} as given user.\n", "slm": "Creates callable for executing {@link doubleWhoAmI(String, String, ReAuthnType, String)} as given user.", "label": ["Feat"]}
{"diff": "@@ -2,7 +2,7 @@\n\n   public void getStatus() throws Exception {\n     AlluxioURI file = new AlluxioURI(\"/file\");\n     URIStatus status = new URIStatus(new FileInfo());\n-    GetStatusOptions getStatusOptions = GetStatusOptions.defaults();\n+    GetStatusPOptions getStatusOptions = GetStatusPOptions.getDefaultInstance();\n     when(mFileSystemMasterClient.getStatus(file, getStatusOptions)).thenReturn(status);\n     assertSame(status, mFileSystem.getStatus(file, getStatusOptions));\n     verify(mFileSystemMasterClient).getStatus(file, getStatusOptions);", "nl": "Tests for the {@link BaseFileSystem#getStatus(AlluxioURI, GetStatusPOptions)} method.", "old_nl": "Tests for the {@link BaseFileSystem#getStatus(AlluxioURI, GetStatusOptions)} method.", "peft": "79\tTests for the {@link BaseFileSystem#getStatus(AlluxioURI, GetStatusPOptions)} method.\n", "icl": "79\tTests for the {@link BaseFileSystem#getStatus(AlluxioURI, GetStatusPOptions)} method.\n", "slm": "Tests for the {@link BaseFileSystem#getStatus(AlluxioURI, GetStatusPOptions)} method.", "label": ["Ref"]}
{"diff": "@@ -1,34 +1,4 @@\n\n public void requestPasswordReset(ResetPasswordRequest body) throws ApiException {\n-    Object localVarPostBody = body;\n-    \n-    // verify the required parameter 'body' is set\n-    if (body == null) {\n-      throw new ApiException(400, \"Missing the required parameter 'body' when calling requestPasswordReset\");\n+\n+        requestPasswordResetWithHttpInfo(body);\n     }\n-    \n-    // create path and map variables\n-    String localVarPath = \"/v4/auth/reset_password\";\n-\n-    // query params\n-    List<Pair> localVarQueryParams = new ArrayList<Pair>();\n-    Map<String, String> localVarHeaderParams = new HashMap<String, String>();\n-    Map<String, Object> localVarFormParams = new HashMap<String, Object>();\n-\n-\n-    \n-    \n-    final String[] localVarAccepts = {\n-      \"application/json;charset=UTF-8\"\n-    };\n-    final String localVarAccept = apiClient.selectHeaderAccept(localVarAccepts);\n-\n-    final String[] localVarContentTypes = {\n-      \"application/json;charset=UTF-8\"\n-    };\n-    final String localVarContentType = apiClient.selectHeaderContentType(localVarContentTypes);\n-\n-    String[] localVarAuthNames = new String[] {  };\n-\n-\n-    apiClient.invokeAPI(localVarPath, \"POST\", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAccept, localVarContentType, localVarAuthNames, null);\n-  }", "nl": "### Effects: Provided user receives email with password reset token.", "old_nl": "### Effects: Provided user receives email with reset token.", "peft": "80\t### Errors:\n", "icl": "80\tProvided user receives email with reset token.\n", "slm": "Verify that Provided user receives email with reset token.", "label": ["Code&Doc"]}
{"diff": "@@ -1,13 +1,13 @@\n\n @Nullable\n     public ColumnIdent shiftRight() {\n-        if (path.isEmpty()) {\n-            return null;\n+        switch (path.size()) {\n+            case 0:\n+                return null;\n+\n+            case 1:\n+                return new ColumnIdent(path.get(0));\n+\n+            default:\n+                return new ColumnIdent(path.get(0), path.subList(1, path.size()));\n         }\n-        ColumnIdent newCi;\n-        if (path.size() > 1) {\n-            newCi = new ColumnIdent(path.get(0), path.subList(1, path.size()));\n-        } else {\n-            newCi = new ColumnIdent(path.get(0));\n-        }\n-        return newCi;\n     }", "nl": "If the columnName doesn't have a path the return value is null", "old_nl": "If the columnIdent doesn't have a path the return value is null", "peft": "82\tIf the columnIdent doesn't have a path the return value is null.\n", "icl": "82\tIf the columnIdent doesn't have a path the return value is null\n", "slm": "If the columnIdent doesn't have a path the return value is null", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,5 @@\n\n-private boolean isMatchingByLineAndColumnAndNotIdent(DetailAST ast) {\n+private boolean isMatchingByLineAndColumnAndTokenType(DetailAST ast) {\n         return ast.getLineNo() == lineNumber\n-                && expandedTabColumn(ast) == columnNumber;\n+                && expandedTabColumn(ast) == columnNumber\n+                && (tokenType == 0 || tokenType == ast.getType());\n     }", "nl": "Checks if the given {@code DetailAST} node is matching line number, column number and token type.", "old_nl": "Checks if the given {@code DetailAST} node is matching line and column number.", "peft": "83\tChecks if the given {@code DetailAST} node is matching line and column number and token type.\n", "icl": "83\tChecks if the given  node is matching line and column number.\n", "slm": "Checks if the given {@code DetailAST} node is matching line and column number and tokenizer type.", "label": ["Feat"]}
{"diff": "@@ -3,8 +3,8 @@\n\n     RetryStrategy retryStrategy = RetryStrategies.timeLimit(5L, TimeUnit.SECONDS,\n                                                             RetryStrategies.exponentialDelay(100L, 1000L,\n                                                                                              TimeUnit.MILLISECONDS));\n-    Retries.runWithRetries(() -> Transactionals.execute(transactional, context -> {\n-      RemoteRuntimeDataset dataset = RemoteRuntimeDataset.create(context, datasetFramework);\n+    Retries.runWithRetries(() -> TransactionRunners.run(transactionRunner, context -> {\n+      RemoteRuntimeTable dataset = RemoteRuntimeTable.create(context);\n       dataset.write(programRunId, programOptions);\n     }, RetryableException.class), retryStrategy);\n   }", "nl": "Persists running state to the {@link RemoteRuntimeTable}.", "old_nl": "Persists running state to the {@link RemoteRuntimeDataset}.", "peft": "84\tPersists running state to the {@link RemoteRuntimeTable}.\n", "icl": "84\tPersists running state to the {@link RemoteRuntimeTable}.\n", "slm": "Persists running state to the {@link RemoteRuntimeTable}.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public List<CreateDialogNode> dialogNodes() {\n+public List<DialogNode> dialogNodes() {\n     return dialogNodes;\n   }", "nl": "An array of objects describing the dialog nodes in the workspace.", "old_nl": "An array of objects defining the nodes in the dialog.", "peft": "85\tAn array of objects describing the dialog nodes.\n", "icl": "85\tThe nodes in the dialog.\n", "slm": "An array of objects defining the nodes in the dialog.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-default SoroushBotEndpointProducerBuilder retryLinearIncrement(\n-                Long retryLinearIncrement) {\n-            setProperty(\"retryLinearIncrement\", retryLinearIncrement);\n+default SoroushBotEndpointProducerBuilder maxRetryWaitingTime(\n+                long maxRetryWaitingTime) {\n+            setProperty(\"maxRetryWaitingTime\", maxRetryWaitingTime);\n             return this;\n         }", "nl": "Maximum amount of time (in millisecond) a thread wait before retrying failed request.", "old_nl": "The amount of time (in millisecond) which adds to waiting time when using Linear back off strategy.", "peft": "86\tThe maximum amount of time (in millisecond) which adds to waiting time when using Linear back off strategy.\n", "icl": "86\tThe amount of time (in millisecond) which adds to waiting time when using Linear back off strategy.\n", "slm": "The amount of time (in millisecond) which adds to waiting time when using S3 back off strategy.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,5 @@\n\n-public void setLocationCharsets(Map<Resource,Charset> locationCharsets) {\n-\t\tthis.locationCharsets.clear();\n-\t\tthis.locationCharsets.putAll(locationCharsets);\n+public void setLocationValues(List<String> locationValues) {\n+\t\tAssert.notNull(locationValues, \"Location values list must not be null\");\n+\t\tthis.locationValues.clear();\n+\t\tthis.locationValues.addAll(locationValues);\n \t}", "nl": "An alternative to {@link #setLocations(List)} that accepts a list of", "old_nl": "Specify charsets associated with the configured {@link #setLocations(List) locations}.", "peft": "87\tSpecify values associated with the configured {@link #setLocations(List) locations}.\n", "icl": "87\tSpecify values associated with the configured locations.\n", "slm": "Specify values associated with the configured {@link #setLocations(List) locations}.", "label": ["Feat"]}
{"diff": "@@ -7,15 +7,13 @@\n\n             if (correlationType.getId() == CorrelationAttributeInstance.FILES_TYPE_ID) {\n                 currentCase.getSleuthkitCase().getCaseDbAccessManager().select(getFileQuery(mimeTypesToFilterOn), instancetableCallback);\n             } else {\n-                dbManager.processInstanceTableWhere(correlationType, String.format(interCaseWhereClause, caseId,\n+                dbManager.processInstanceTableWhere(correlationType, String.format(INTER_CASE_WHERE_CLAUSE, caseId,\n                         TskData.FileKnown.KNOWN.getFileKnownValue()),\n                         instancetableCallback);\n             }\n             return instancetableCallback.getInstanceCollatedCommonFiles();\n-        } catch (EamDbException ex) {\n+        } catch (EamDbException | TskCoreException ex) {\n             LOGGER.log(Level.SEVERE, \"Error accessing EamDb processing CaseInstancesTable.\", ex);\n-        } catch (TskCoreException ex) {\n-            Exceptions.printStackTrace(ex);\n         }\n         return new HashMap<>();\n     }", "nl": "Builds maps of obj id to value and case.", "old_nl": "Builds maps of obj id to md5 and case.", "peft": "88\tBuilds maps of obj id to md5 and case for the given correlation type.\n", "icl": "88\tBuilds maps of obj id to md5 and case.\n", "slm": "Builds maps of obj id to MD5 and case.", "label": ["Feat"]}
{"diff": "@@ -2,9 +2,7 @@\n\n \t\t\t@Nullable List<? extends HandlerMethodArgumentResolver> argumentResolvers) {\n \n \t\tif (argumentResolvers != null) {\n-\t\t\tfor (HandlerMethodArgumentResolver resolver : argumentResolvers) {\n-\t\t\t\tthis.argumentResolvers.add(resolver);\n-\t\t\t}\n+\t\t\tthis.argumentResolvers.addAll(argumentResolvers);\n \t\t}\n \t\treturn this;\n \t}", "nl": "Add the given {@link HandlerMethodArgumentResolver HandlerMethodArgumentResolvers}.", "old_nl": "Add the given {@link HandlerMethodArgumentResolver}s.", "peft": "90\tAdd the given {@link HandlerMethodArgumentResolver HandlerMethodArgumentResolvers}.\n", "icl": "90\tAdd the given {@link HandlerMethodArgumentResolver}s.\n", "slm": "Add all {@link HandlerMethodArgumentResolvers}s.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-private void bsonJackson(String json) throws IOException {\n+private void bsonThenJackson(String json) throws IOException {\n     ObjectNode toWrite = maybeWrap(mapper.readTree(json));\n \n     BasicOutputBuffer buffer = new BasicOutputBuffer();", "nl": "inverse of {@link #jacksonThenBson(String)}", "old_nl": "inverse of {@link #jacksonBson(String)}", "peft": "93\tinverse of {@link #jacksonBsonThen(String)}\n", "icl": "93\tinverse of {@link #bsonThenJackson(String)}\n", "slm": "inverse of {@link #jacksonThenJackson(String)}", "label": ["Ref"]}
{"diff": "@@ -2,37 +2,53 @@\n\n \t\tif (text == null) {\n \t\t\treturn \"A null is not a legal XML value\";\n \t\t}\n-\n-\t\t// Do check\n-\t\tfor (int i = 0, len = text.length(); i<len; i++) {\n-\n-\t\t\tint ch = text.charAt(i);\n-\n-\t\t\t// Check if high part of a surrogate pair\n-\t\t\tif (isHighSurrogate((char) ch)) {\n-\t\t\t\t// Check if next char is the low-surrogate\n-\t\t\t\ti++;\n-\t\t\t\tif (i < len) {\n-\t\t\t\t\tfinal char low = text.charAt(i);\n-\t\t\t\t\tif (!isLowSurrogate(low)) {\n-\t\t\t\t\t\treturn \"Illegal Surrogate Pair\";\n+\t\t\n+\t\t// lowx indicates we expect a low surrogate next.\n+\t\tboolean lowx = false;\n+\t\tfinal int len = text.length();\n+\t\tfor (int i = 0; i < len; i++) {\n+\t\t\t// we are expecting a normal char, but may be a surrogate.\n+\t\t\tif (isXMLCharacter(text.charAt(i))) {\n+\t\t\t\tif (lowx) {\n+\t\t\t\t\t// we got a normal character, but we wanted a low surrogate\n+\t\t\t\t\treturn String.format(\"Illegal Surrogate Pair 0x%04x%04x\",\n+\t\t\t\t\t\t\t(int)text.charAt(i - 1), (int)text.charAt(i));\n+\t\t\t\t}\n+\t\t\t} else {\n+\t\t\t\t// the character is not a normal character.\n+\t\t\t\t// we need to sort out what it is. Neither high nor low\n+\t\t\t\t// surrogate pairs are valid characters, so they will get here.\n+\t\t\t\t\n+\t\t\t\tif (!lowx && isHighSurrogate(text.charAt(i))) {\n+\t\t\t\t\t// we have the valid high char of a pair.\n+\t\t\t\t\t// we will expect the low char on the next loop through,\n+\t\t\t\t\t// so mark the high char, and move on.\n+\t\t\t\t\tlowx = true;\n+\t\t\t\t} else if (lowx && isLowSurrogate(text.charAt(i))) {\n+\t\t\t\t\t// we now have the low char of a pair, decode and validate\n+\t\t\t\t\tint chi = decodeSurrogatePair(\n+\t\t\t\t\t\t\ttext.charAt(i - 1), text.charAt(i));\n+\t\t\t\t\tif (!isXMLCharacter(chi)) {\n+\t\t\t\t\t\t// Likely this character can't be easily displayed\n+\t\t\t\t\t\t// because it's a control so we use it'd hexadecimal \n+\t\t\t\t\t\t// representation in the reason.\n+\t\t\t\t\t\treturn String.format(\"0x%06x is not a legal XML character\",\n+\t\t\t\t\t\t\t\tchi);\n \t\t\t\t\t}\n-\t\t\t\t\t// It's a good pair, calculate the true value of\n-\t\t\t\t\t// the character to then fall thru to isXMLCharacter\n-\t\t\t\t\tch = decodeSurrogatePair((char) ch, low);\n-\t\t\t\t}\n-\t\t\t\telse {\n-\t\t\t\t\treturn \"Surrogate Pair Truncated\";\n+\t\t\t\t\tlowx = false;\n+\t\t\t\t} else {\n+\t\t\t\t\t// Likely this character can't be easily displayed\n+\t\t\t\t\t// because it's a control so we use it's hexadecimal \n+\t\t\t\t\t// representation in the reason.\n+\t\t\t\t\treturn String.format(\"0x%04x is not a legal XML character\",\n+\t\t\t\t\t\t\t(int)text.charAt(i));\n \t\t\t\t}\n \t\t\t}\n-\n-\t\t\tif (!isXMLCharacter(ch)) {\n-\t\t\t\t// Likely this character can't be easily displayed\n-\t\t\t\t// because it's a control so we use it'd hexadecimal \n-\t\t\t\t// representation in the reason.\n-\t\t\t\treturn (\"0x\" + Integer.toHexString(ch) +\n-\t\t\t\t\t\t\" is not a legal XML character\");\n-\t\t\t}\n+\t\t}\n+\t\t\n+\t\tif (lowx) {\n+\t\t\treturn String.format(\"Truncated Surrogate Pair 0x%04x????\",\n+\t\t\t\t\t(int)text.charAt(text.length() - 1));\n \t\t}\n \n \t\t// If we got here, everything is OK", "nl": "are specifically excluded except for carriage return, line-feed, and the horizontal tab.", "old_nl": "are specifically excluded except for carriage return, linefeed, and the horizontal tab.", "peft": "94\tare specifically excluded except for carriage return, linefeed, and the horizontal tab.\n", "icl": "94\tCheck if the character is a control character.\n", "slm": "are specifically excluded except for carriage return, line feed, and the horizontal tab, and the surround.", "label": ["Code&Doc"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-String computeHash(String algorithm) {\n-        String result = cachedHash;\n+byte[] computeHash(String algorithm) {\n+        byte[] result = cachedHash;\n         if (result != null)\n             return result;\n         if (hasher == null)", "nl": "Computes the hash of this module.", "old_nl": "Computes the hash of this module, returning it as a hex string.", "peft": "95\tComputes the hash of this module, returning it as a byte array.\n", "icl": "95\tComputes the hash of this module, returning it as a byte array.\n", "slm": "Computes the hash of this module, returning it as a hex string.", "label": ["Feat"]}
{"diff": "@@ -1,10 +1,5 @@\n\n @CheckReturnValue\n     @SchedulerSupport(SchedulerSupport.NONE)\n-    public final <R> R to(Function<? super Single<T>, R> convert) {\n-        try {\n-            return ObjectHelper.requireNonNull(convert, \"convert is null\").apply(this);\n-        } catch (Throwable ex) {\n-            Exceptions.throwIfFatal(ex);\n-            throw ExceptionHelper.wrapOrThrow(ex);\n-        }\n+    public final <R> R to(@NonNull SingleConverter<T, ? extends R> converter) {\n+        return ObjectHelper.requireNonNull(converter, \"converter is null\").apply(this);\n     }", "nl": "Calls the specified converter function during assembly time and returns its resulting value.", "old_nl": "Calls the specified converter function with the current Single instance during assembly time and returns its result.", "peft": "96\tCalls the specified converter function with the current Single instance during assembly time and returns its result.\n", "icl": "96\tCalls the specified converter function with the current Single instance during assembly time and returns its result.\n", "slm": "Calls the specified converter with the current Single instance during assembly time and returns its result.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,4 @@\n\n-public static com.liferay.knowledgebase.model.KBArticle findByR_V(\n-\t\tlong resourcePrimKey, int version)\n+public static KBArticle findByR_V(long resourcePrimKey, int version)\n \t\tthrows com.liferay.knowledgebase.NoSuchArticleException {\n \t\treturn getPersistence().findByR_V(resourcePrimKey, version);\n \t}", "nl": "or throws a {@link NoSuchArticleException} if it could not be found.", "old_nl": "or throws a {@link com.liferay.knowledgebase.NoSuchArticleException} if it could not be found.", "peft": "97\tor throws a {@link NoSuchArticleException} if it could not be found.\n", "icl": "97\tor throws a {@link NoSuchArticleException} if it could not be found.\n", "slm": "or throws a {@link KBArticleNotFoundException} if it could not be found.", "label": ["Feat"]}
{"diff": "@@ -3,8 +3,8 @@\n\n         if (o == null) return false;\n         if (!(o instanceof Comment)) return false;\n         Comment c = (Comment) o;\n-        return rating == c.rating &&\n-               eq(text, c.text) &&\n-               eq(name, c.name) &&\n-               hashCode() == c.hashCode();\n+        int tdiff = time - c.time;\n+        if (tdiff > MAX_SKEW || tdiff < 0 - MAX_SKEW)\n+            return false;\n+        return equalsIgnoreTimestamp(c);\n     }", "nl": "Comments within 10 minutes (not necessarily in same bucket)  and otherwise equal are considered equal.", "old_nl": "Comments in the same 10-minute bucket and otherwise equal  are considered equal.", "peft": "98\tComments in the same 10-minute bucket and otherwise equal, ignoring the timestamp, are considered equal.\n", "icl": "98\tCompares the given object with this  for equality. Returns {@code true} if and only if the given object is also a  and both  have the same rating, text, name, and timestamp.\n", "slm": "Comments in the same 10-minute bucket and so that they are considered equal.", "label": ["Feat"]}
{"diff": "@@ -1,13 +1,13 @@\n\n @Nullable\n     public ColumnIdent shiftRight() {\n-        if (path.isEmpty()) {\n-            return null;\n+        switch (path.size()) {\n+            case 0:\n+                return null;\n+\n+            case 1:\n+                return new ColumnIdent(path.get(0));\n+\n+            default:\n+                return new ColumnIdent(path.get(0), path.subList(1, path.size()));\n         }\n-        ColumnIdent newCi;\n-        if (path.size() > 1) {\n-            newCi = new ColumnIdent(path.get(0), path.subList(1, path.size()));\n-        } else {\n-            newCi = new ColumnIdent(path.get(0));\n-        }\n-        return newCi;\n     }", "nl": "creates a new columnName which just consists of the path of the given columnName e.g.", "old_nl": "creates a new columnIdent which just consists of the path of the given columnIdent e.g.", "peft": "99\tcreates a new columnIdent which just consists of the path of the given columnIdent e.g.\n", "icl": "99\tcreates a new columnIdent which just consists of the path of the given columnIdent e.g.\n", "slm": "Creates a new columnIdent which just consists of the path of the given columnIdent e.g.", "label": ["Ref"]}
{"diff": "@@ -1 +1 @@\n\n-public Optional<FlavorSpec> flavorSpec() { return flavor; }\n+public Optional<NodeResources> nodeResources() { return nodeResources; }", "nl": "Returns the resources requested for each node, or empty to leave this decision to provisioning", "old_nl": "Returns the capacity specified for each node, or empty to leave this decision to provisioning", "peft": "100\tReturns the resources specified for each node, or empty to leave this decision to provisioning\n", "icl": "100\tReturns the node resources specified for each node, or empty to leave this decision to provisioning\n", "slm": "Returns the capacity specified for each node, or empty to leave this decision to provisioning by default", "label": ["Ref"]}
{"diff": "@@ -1,12 +1,12 @@\n\n public Map<String, Collection<String>> headers() {\n+    Map<String, Collection<String>> headerMap = new LinkedHashMap<>();\n+    this.headers.forEach((key, headerTemplate) -> {\n+      List<String> values = new ArrayList<>(headerTemplate.getValues());\n \n-    return Collections.unmodifiableMap(\n-        headers.entrySet().stream().filter(h -> h.getValue() != null && !h.getValue().isEmpty())\n-            .collect(toMap(\n-                Entry::getKey,\n-                Entry::getValue,\n-                (e1, e2) -> {\n-                  throw new IllegalStateException(\"headers should not have duplicated keys\");\n-                },\n-                LinkedHashMap::new)));\n+      /* add the expanded collection, but only if it has values */\n+      if (!values.isEmpty()) {\n+        headerMap.put(key, Collections.unmodifiableList(values));\n+      }\n+    });\n+    return Collections.unmodifiableMap(headerMap);\n   }", "nl": "Returns an immutable copy of the Headers for this request.", "old_nl": "Returns an immutable copy of the current headers.", "peft": "101\tReturns an immutable copy of the current headers, with expanded values.\n", "icl": "101\tReturns an immutable copy of the current headers.\n", "slm": "Returns an unmodifiable copy of the current headers.", "label": ["Code&Doc"]}
{"diff": "@@ -20,4 +20,9 @@\n\n         if (!toRemove.isEmpty()) {\n             parts.removeAll(toRemove);\n         }\n+\n+        if (parts.size() > params.maxPartCount) {\n+            Collections.sort(parts, Glyphs.byReverseWeight);\n+            parts.retainAll(parts.subList(0, params.maxPartCount));\n+        }\n     }", "nl": "Purge the population of parts candidates as much as possible, since the cost of their later combinations is exponential.", "old_nl": "Purge the population of parts candidates as much as possible, since the cost of their later combinations is worse than exponential.", "peft": "102\tPurge the population of parts candidates as much as possible, since the cost of their later combinations is worse than exponential.  Also, limit the number of parts to the maximum allowed.\n", "icl": "102\tPurge the population of parts candidates as much as possible, since the cost of their later combinations is worse than exponential.\n", "slm": "Purge the population of parts candidates as much as possible, since the cost of their later combinations is worse than exponential.", "label": ["Feat"]}
{"diff": "@@ -1,6 +1,6 @@\n\n @Override\n-\tpublic SyncDLObject[] findByParentFolderId_PrevAndNext(\n-\t\tlong syncDLObjectId, long parentFolderId,\n+\tpublic SyncDLObject[] findByM_R_PrevAndNext(long syncDLObjectId,\n+\t\tlong modifiedTime, long repositoryId,\n \t\tOrderByComparator orderByComparator)\n \t\tthrows NoSuchDLObjectException, SystemException {\n \t\tSyncDLObject syncDLObject = findByPrimaryKey(syncDLObjectId);\n@@ -12,13 +12,13 @@\n\n \n \t\t\tSyncDLObject[] array = new SyncDLObjectImpl[3];\n \n-\t\t\tarray[0] = getByParentFolderId_PrevAndNext(session, syncDLObject,\n-\t\t\t\t\tparentFolderId, orderByComparator, true);\n+\t\t\tarray[0] = getByM_R_PrevAndNext(session, syncDLObject,\n+\t\t\t\t\tmodifiedTime, repositoryId, orderByComparator, true);\n \n \t\t\tarray[1] = syncDLObject;\n \n-\t\t\tarray[2] = getByParentFolderId_PrevAndNext(session, syncDLObject,\n-\t\t\t\t\tparentFolderId, orderByComparator, false);\n+\t\t\tarray[2] = getByM_R_PrevAndNext(session, syncDLObject,\n+\t\t\t\t\tmodifiedTime, repositoryId, orderByComparator, false);\n \n \t\t\treturn array;\n \t\t}", "nl": "Returns the sync d l objects before and after the current sync d l object in the ordered set where modifiedTime > ?", "old_nl": "Returns the sync d l objects before and after the current sync d l object in the ordered set where parentFolderId = ?.", "peft": "103\tReturns the sync d l objects before and after the current sync d l object in the ordered set where m_r =?.\n", "icl": "103\tReturns the sync d l objects before and after the current sync d l object in the ordered set where parentFolderId =?.\n", "slm": "Returns the sync d l objects before and after the current sync d l object in the ordered set where repositoryId = ?.", "label": ["Feat"]}
{"diff": "@@ -1,18 +1,4 @@\n\n private void calculate(Trade trade) {\n-        final int entryIndex = trade.getEntry().getIndex();\n-        int begin = entryIndex + 1;\n-        if (begin > values.size()) {\n-            Num lastValue = values.get(values.size() - 1);\n-            values.addAll(Collections.nCopies(begin - values.size(), lastValue));\n-        }\n-        int end = trade.getExit().getIndex();\n-        for (int i = Math.max(begin, 1); i <= end; i++) {\n-            Num ratio;\n-            if (trade.getEntry().isBuy()) {\n-                ratio = getRatio(entryIndex, i);\n-            } else {\n-                ratio = getRatio(i, entryIndex);\n-            }\n-            values.add(values.get(entryIndex).multipliedBy(ratio));\n-        }\n+        if (trade.isOpened()) { throw new IllegalArgumentException(\"Trade is not closed. Final index of observation needs to be provided.\"); }\n+        calculate(trade, trade.getExit().getIndex());\n     }", "nl": "Calculates the cash flow for a single closed trade.", "old_nl": "Calculates the cash flow for a single trade.", "peft": "104\tCalculates the cash flow for a single trade.\n", "icl": "104\tCalculates the cash flow for a single trade.\n", "slm": "Calculates the cash flow for a single trade and returns the result.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,8 @@\n\n public static void toNative(RBaseObject obj) {\n-        ToNativeNodeGen.getUncached().execute(obj);\n+        NativeMirror mirror = obj.getNativeMirror();\n+        if (mirror == null) {\n+            createNativeMirror(obj);\n+            mirror = obj.getNativeMirror();\n+        }\n+        ToNativeNodeGen.getUncached().execute(mirror);\n     }", "nl": "Assigns a native mirror object ID to the given RBaseObject object.", "old_nl": "Assigns a native mirror object to the given RBaseObject object.", "peft": "105\tAssigns a native mirror object to the given R object.\n", "icl": "105\tAssigns a native mirror object to the given RBaseObject object.\n", "slm": "Assigns a native mirror object to the given RBaseObject object.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public static final ServiceAlreadyExistsException serviceAlreadyExistsException(String serviceName) {\n-        return new ServiceAlreadyExistsException(Messages.i18n.format(\"ServiceAlreadyExists\", serviceName)); //$NON-NLS-1$\n+public static final ApiAlreadyExistsException apiAlreadyExistsException(String apiName) {\n+        return new ApiAlreadyExistsException(Messages.i18n.format(\"ApiAlreadyExists\", apiName)); //$NON-NLS-1$\n     }", "nl": "Creates an exception from an API name.", "old_nl": "Creates an exception from an service name.", "peft": "106\tCreates an exception from an api name.\n", "icl": "106\tCreates an exception from an api name.\n", "slm": "Creates an exception from an api name.", "label": ["Ref"]}
{"diff": "@@ -1,28 +1,6 @@\n\n-public static void appendString(final StringBuffer buf, final String s) {\n-        buf.append('\\\"');\n-        for (int i = 0; i < s.length(); ++i) {\n-            char c = s.charAt(i);\n-            if (c == '\\n') {\n-                buf.append(\"\\\\n\");\n-            } else if (c == '\\r') {\n-                buf.append(\"\\\\r\");\n-            } else if (c == '\\\\') {\n-                buf.append(\"\\\\\\\\\");\n-            } else if (c == '\"') {\n-                buf.append(\"\\\\\\\"\");\n-            } else if (c < 0x20 || c > 0x7f) {\n-                buf.append(\"\\\\u\");\n-                if (c < 0x10) {\n-                    buf.append(\"000\");\n-                } else if (c < 0x100) {\n-                    buf.append(\"00\");\n-                } else if (c < 0x1000) {\n-                    buf.append('0');\n-                }\n-                buf.append(Integer.toString(c, 16));\n-            } else {\n-                buf.append(c);\n-            }\n-        }\n-        buf.append('\\\"');\n+@Deprecated\n+    public static void appendString(final StringBuffer stringBuffer, final String string) {\n+        StringBuilder stringBuilder = new StringBuilder();\n+        appendString(stringBuilder, string);\n+        stringBuffer.append(stringBuilder.toString());\n     }", "nl": "Appends a quoted string to the given string buffer.", "old_nl": "Appends a quoted string to a given buffer.", "peft": "107\tAppends a quoted string to a given string buffer.\n", "icl": "107\tAppends a quoted string to a given buffer.\n", "slm": "Appends a quoted string to a given string buffer.", "label": ["Feat"]}
{"diff": "@@ -8,5 +8,4 @@\n\n         } catch (Exception e) {\n             throw new RuntimeException(\"Failed to create webview. \", e);\n         }\n-        return ret;\n     }", "nl": "Construct the CordovaWebView object.", "old_nl": "Construct the default web view object.", "peft": "109\tConstruct the default web view.\n", "icl": "109\tConstruct the default web view object.\n", "slm": "Construct the web view object.", "label": ["Feat"]}
{"diff": "@@ -1,11 +1,11 @@\n\n-public static void log( GrayF32 input , GrayF32 output ) {\n+public static void log( GrayF32 input , final float val , GrayF32 output ) {\n \n \t\toutput.reshape(input.width,input.height);\n \n \t\tint N = input.width*input.height;\n \t\tif( BoofConcurrency.USE_CONCURRENT && N > SMALL_IMAGE) {\n-\t\t\tImplPixelMath_MT.log(input,output);\n+\t\t\tImplPixelMath_MT.log(input,val,output);\n \t\t} else {\n-\t\t\tImplPixelMath.log(input,output);\n+\t\t\tImplPixelMath.log(input,val,output);\n \t\t}\n \t}", "nl": "Sets each pixel in the output image to log( val + input(x,y)) of the input image.", "old_nl": "Sets each pixel in the output image to log( 1 + input(x,y)) of the input image.", "peft": "110\tSets each pixel in the output image to log( 1 + input(x,y)) of the input image and the specified value.\n", "icl": "110\tSets each pixel in the output image to log( 1 + input(x,y)) of the input image.\n", "slm": "Sets each pixel in the output image to log( 1 + input(x,y)) of the input image.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n public static boolean isStampingEnabled(RuleContext ruleContext, BuildConfiguration config) {\n-    if (config.isHostConfiguration()\n+    if (config.isToolConfiguration()\n         || !ruleContext.attributes().has(\"stamp\", BuildType.TRISTATE)) {\n       return false;\n     }", "nl": "This returns false for unstampable rule classes and for rules used to build tools.", "old_nl": "This returns false for unstampable rule classes and for rules in the host configuration.", "peft": "111\tThis returns false for unstampable rule classes and for rules in the tool configuration.\n", "icl": "111\tThis returns false for unstampable rule classes and for rules in the tool configuration.\n", "slm": "This returns false for unstampable rule classes and for rules in the tool configuration.", "label": ["Ref"]}
{"diff": "@@ -9,8 +9,7 @@\n\n \n         assertTrue(write(500));\n         verifyNeverWrite(STREAM_A);\n-        assertEquals(200, captureWrites(STREAM_B));\n-        assertEquals(300, captureWrites(STREAM_C) + captureWrites(STREAM_D));\n+        assertEquals(500, captureWrites(STREAM_B) + captureWrites(STREAM_C) + captureWrites(STREAM_D));\n \n         assertFalse(write(1300));\n         verifyNeverWrite(STREAM_A);", "nl": "In this test, we close an internal stream in the priority tree.", "old_nl": "In this test, we close an internal stream in the priority tree but tree should not change.", "peft": "113\tIn this test, we close an internal stream in the priority tree but the tree should not change.\n", "icl": "113\tIn this test, we close an internal stream in the priority tree but tree should not change.\n", "slm": "In this test, we close an internal stream in the priority tree but tree should have a focus.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-void addNodeToPartition(IGroupMemberNode node) {\n+void addNonOptionalNonMinusNodeToPartition(IGroupMemberNode node) {\n       nonOptionalNonMinusNodes.add(node);\n       definitelyProduced.addAll(bindingInfo.get(node).definitelyProduced);\n    }", "nl": "Adds a (non-optional non-minus) node to a join group partition and updates the set of definitely produced variables accordingly.", "old_nl": "Adds a (non-optional non-minus) node to a join group partition.", "peft": "114\tAdds a (non-optional non-minus) node to a join group partition, and updates the definitely produced set.\n", "icl": "114\tAdds a (non-optional non-minus) node to a join group partition.\n", "slm": "Adds a non-optional non-minus node to the join group partition.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-static public void assertNotSame(Object expected, Object actual) {\n+public static void assertNotSame(Object expected, Object actual) {\n     assertNotSame(null, expected, actual);\n   }", "nl": "AssertionFailedError is thrown.", "old_nl": "If they are not the same an AssertionFailedError is thrown.", "peft": "115\tIf they are not the same an {@link AssertionError} is thrown.\n", "icl": "115\tIf they are not the same an AssertionFailedError is thrown.\n", "slm": "If they are not the same an AssertionErrorFailedError is thrown.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n public org.kaaproject.kaa.server.appenders.hbase.config.gen.Type getValueType() {\n-    return ValueType;\n+    return valueType;\n   }", "nl": "Gets the value of the 'valueType' field.", "old_nl": "Gets the value of the 'ValueType' field.", "peft": "116\tGets the value of the 'valueType' field.\n", "icl": "116\tGets the value of the 'valueType' field.\n", "slm": "Gets the value of the 'ValueType' field.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public final void addAttributes(RemoteFileAttributes remoteFileAttributes) {\n-\t\tattributes.add(remoteFileAttributes);\n+public final <T extends RemoteFileAttributes> void setAttributes(T remoteFileAttributes) {\n+\t\tattributes.put(remoteFileAttributes.getClass(), remoteFileAttributes);\n \t}", "nl": "Sets remote file attributes to this remote file class.", "old_nl": "Adds remote file attributes to this remote file class.", "peft": "117\tSets remote file attributes to this remote file class.\n", "icl": "117\tSet remote file attributes to this remote file class.\n", "slm": "Adds remote file attributes to this remote file class.", "label": ["Feat"]}
{"diff": "@@ -1,16 +1,16 @@\n\n-public <T extends Options> T parse(final Item item, final T options, final QueryError error)\n+public <T extends Options> T assign(final Item item, final T options, final QueryError error)\n       throws QueryException {\n \n     if(item != null) {\n       final TokenBuilder tb = new TokenBuilder();\n       try {\n         if(item instanceof Map) {\n-          options.parse((Map) item, !acceptUnknown, info);\n+          options.assign((Map) item, !acceptUnknown, info);\n         } else {\n           if(test == null) throw MAP_X_X.get(info, item.type, item);\n           if(!test.eq(item)) throw ELMMAP_X_X_X.get(info, root.prefixId(XML), item.type, item);\n           final String opts = optString((ANode) item, error);\n-          options.parse(tb.add(opts).toString());\n+          options.assign(tb.add(opts).toString());\n         }\n       } catch(final BaseXException ex) {\n         throw error.get(info, ex);", "nl": "Assigns options to the specified options.", "old_nl": "Extracts options from the specified item.", "peft": "119\tAssigns options from the specified item.\n", "icl": "119\tExtracts options from the specified item.\n", "slm": "Extracts options from the specified item.", "label": ["Ref"]}
{"diff": "@@ -1,15 +1,22 @@\n\n public void checkExpiryBeforeMutation() {\n-    if (entryExpiredListeners() != null && entry == NON_FRESH_DUMMY) {\n+    if (entryExpiredListeners() == null) {\n+      noExpiryListenersPresent();\n+      return;\n+    }\n+    if (entry == NON_FRESH_DUMMY) {\n       wantData();\n       return;\n     }\n-    if (entryExpiredListeners() != null && entry.getNextRefreshTime() < 0) {\n+    long nrt = entry.getNextRefreshTime();\n+    if (nrt < 0 && millis() >= -nrt) {\n       boolean justExpired = false;\n       synchronized (entry) {\n-        if (entry.getNextRefreshTime() < 0) {\n+        nrt = entry.getNextRefreshTime();\n+        if (nrt < 0 && millis() >= -nrt) {\n           justExpired = true;\n           entry.setNextRefreshTime(Entry.EXPIRED);\n           timing().stopStartTimer(0, entry);\n+          heapDataValid = false;\n         }\n       }\n       if (justExpired) {", "nl": "Check whether we are executed on an expired entry before the timer event for the expiry was received.", "old_nl": "Check whether we are executed on an expired entry before the timer event for expiry was received.", "peft": "120\tCheck whether we are executed on an expired entry before the timer event for expiry was received, and if so, expire the entry.\n", "icl": "120\tChecks whether the entry is expired.\n", "slm": "Check whether we are executed on an expired entry before the timer event for expiry was received.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,4 @@\n\n @FromString\n-  public static KnockType of(String uniqueName) {\n-    ArgChecker.notNull(uniqueName, \"uniqueName\");\n-    return valueOf(CaseFormat.UPPER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, uniqueName));\n+  public static KnockType of(String name) {\n+    return NAMES.parse(name);\n   }", "nl": "Obtains an instance from the specified name.", "old_nl": "Obtains an instance from the specified unique name.", "peft": "121\tObtains an instance from the specified name.\n", "icl": "121\tObtains an instance from the specified name.\n", "slm": "Obtains an instance from the specified name.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public static String requireBinaryName(String what, String name) {\n+private static String requireTypeName(String what, String name) {\n         if (name == null)\n             throw new IllegalArgumentException(\"Null \" + what);\n         int next;", "nl": "Checks if the given name is a legal type name.", "old_nl": "Checks if the given name is a legal binary name.", "peft": "122\tChecks if the given name is a legal type name.\n", "icl": "122\tChecks if the given name is a legal type name.\n", "slm": "Checks if the given name is a legalTypeName.", "label": ["Ref"]}
{"diff": "@@ -1,42 +1,43 @@\n\n public static void main(String[] args) throws Exception {\n-        while (true) {\n-            boolean restart = false;\n-            System.setProperty(\"karaf.restart\", \"false\");\n-            if (Boolean.getBoolean(\"karaf.restart.clean\")) {\n-                File karafHome = Utils.getKarafHome();\n-                File karafBase = Utils.getKarafDirectory(Main.PROP_KARAF_BASE, Main.ENV_KARAF_BASE, karafHome, false, true);\n-                File karafData = Utils.getKarafDirectory(Main.PROP_KARAF_DATA, Main.ENV_KARAF_DATA, new File(karafBase, \"data\"), true, true);\n-                Utils.deleteDirectory(karafData);\n-            }\n-            final Main main = new Main(args);\n-            try {\n-                main.launch();\n-            } catch (Throwable ex) {\n-                main.destroy();\n-                main.setExitCode(-1);\n-                System.err.println(\"Could not create framework: \" + ex);\n-                ex.printStackTrace();\n-            }\n-            try {\n-                main.awaitShutdown();\n-                boolean stopped = main.destroy();\n-                restart = Boolean.getBoolean(\"karaf.restart\");\n-                if (!stopped) {\n-                    if (restart) {\n-                        System.err.println(\"Timeout waiting for framework to stop.  Restarting now.\");\n-                    } else {\n-                        System.err.println(\"Timeout waiting for framework to stop.  Exiting VM.\");\n-                        main.setExitCode(-3);\n-                    }\n-                }\n-            } catch (Throwable ex) {\n-                main.setExitCode(-2);\n-                System.err.println(\"Error occured shutting down framework: \" + ex);\n-                ex.printStackTrace();\n-            } finally {\n-                if (!restart) {\n-                    System.exit(main.getExitCode());\n-                }\n-            }\n-        }\n-    }\n+\t\twhile (true) {\n+\t\t\tboolean restart = false;\n+\t\t\tSystem.setProperty(\"karaf.restart\", \"false\");\n+\t\t\tfinal Main main = new Main(args);\n+\t\t\tLifecycleManager manager = null;\n+\t\t\tint exitCode = 0;\n+\t\t\ttry {\n+\t\t\t\tmanager = main.launch();\n+\t\t\t} catch (Throwable ex) {\n+\t\t\t\tmain.lifecycleManager.destroyKaraf();\n+\t\t\t\texitCode = -1;\n+\t\t\t\tSystem.err.println(\"Could not create framework: \" + ex);\n+\t\t\t\tex.printStackTrace();\n+\t\t\t}\n+\t\t\ttry {\n+\t\t\t\tif (manager != null) {\n+\t\t\t\t\tmanager.awaitShutdown();\n+\t\t\t\t\tboolean stopped = main.lifecycleManager.destroyKaraf();\n+\t\t\t\t\trestart = Boolean.getBoolean(\"karaf.restart\");\n+\t\t\t\t\tif (!stopped) {\n+\t\t\t\t\t\tif (restart) {\n+\t\t\t\t\t\t\tSystem.err\n+\t\t\t\t\t\t\t\t\t.println(\"Timeout waiting for framework to stop.  Restarting now.\");\n+\t\t\t\t\t\t} else {\n+\t\t\t\t\t\t\tSystem.err\n+\t\t\t\t\t\t\t\t\t.println(\"Timeout waiting for framework to stop.  Exiting VM.\");\n+\t\t\t\t\t\t\texitCode = -3;\n+\t\t\t\t\t\t}\n+\t\t\t\t\t}\n+\t\t\t\t}\n+\t\t\t} catch (Throwable ex) {\n+\t\t\t\texitCode = -2;\n+\t\t\t\tSystem.err.println(\"Error occured shutting down framework: \"\n+\t\t\t\t\t\t+ ex);\n+\t\t\t\tex.printStackTrace();\n+\t\t\t} finally {\n+\t\t\t\tif (!restart) {\n+\t\t\t\t\tSystem.exit(exitCode);\n+\t\t\t\t}\n+\t\t\t}\n+\t\t}\n+\t}", "nl": "A case insensitive StringMap is created for the configuration property file and is passed into the framework.", "old_nl": "StringMap       is created for the configuration property file and is passed       into the framework.", "peft": "123\tStringMap       is created for the configuration property file and is passed into the framework.\n", "icl": "123\tIt creates a StringMap for the configuration property file and passes it into the framework.\n", "slm": "Configuration property file and is passed       into the framework.", "label": ["Feat"]}
{"diff": "@@ -1,9 +1,3 @@\n\n-boolean hasUnreferencedCommits() throws IOException {\n-        final IndexCommit lastCommit = this.lastCommit;\n-        if (safeCommit != lastCommit) { // Race condition can happen but harmless\n-            final long maxSeqNoFromLastCommit = Long.parseLong(lastCommit.getUserData().get(SequenceNumbers.MAX_SEQ_NO));\n-            // We can clean up the current safe commit if the last commit is safe\n-            return globalCheckpointSupplier.getAsLong() >= maxSeqNoFromLastCommit;\n-        }\n-        return false;\n+boolean hasUnreferencedCommits() {\n+        return maxSeqNoOfNextSafeCommit <= globalCheckpointSupplier.getAsLong();\n     }", "nl": "Checks if the deletion policy can delete some index commits with the latest global checkpoint.", "old_nl": "Checks if the deletion policy can release some index commits with the latest global checkpoint.", "peft": "124\tChecks if the deletion policy can release some index commits with the latest global checkpoint.\n", "icl": "124\tChecks if the deletion policy can release some index commits with the latest global checkpoint.\n", "slm": "Checks if the deletion policy can release some index commits with the latest global checkpoint.", "label": ["Code&Doc"]}
{"diff": "@@ -2,7 +2,7 @@\n\n   public void rename() throws Exception {\n     AlluxioURI src = new AlluxioURI(\"/file\");\n     AlluxioURI dst = new AlluxioURI(\"/file2\");\n-    RenameOptions renameOptions = RenameOptions.defaults();\n+    RenamePOptions renameOptions = RenamePOptions.getDefaultInstance();\n     doNothing().when(mFileSystemMasterClient).rename(src, dst, renameOptions);\n     mFileSystem.rename(src, dst, renameOptions);\n     verify(mFileSystemMasterClient).rename(src, dst, renameOptions);", "nl": "Tests for the {@link BaseFileSystem#rename(AlluxioURI, AlluxioURI, RenamePOptions)} method.", "old_nl": "Tests for the {@link BaseFileSystem#rename(AlluxioURI, AlluxioURI, RenameOptions)} method.", "peft": "125\tTests for the {@link BaseFileSystem#rename(AlluxioURI, AlluxioURI, RenamePOptions)} method.\n", "icl": "125\tTests for the {@link BaseFileSystem#rename(AlluxioURI, AlluxioURI, RenamePOptions)} method.\n", "slm": "Tests for the {@link BaseFileSystem#rename(AlluxioURI, AlluxioURI, RenamePOptions)} method.", "label": ["Ref"]}
{"diff": "@@ -1,7 +1,7 @@\n\n-public static Configuration resolveRequiresAndUses(ModuleFinder before,\n-                                                       List<Configuration> parents,\n-                                                       ModuleFinder after,\n-                                                       Collection<String> roots)\n+public static Configuration resolve(ModuleFinder before,\n+                                        List<Configuration> parents,\n+                                        ModuleFinder after,\n+                                        Collection<String> roots)\n     {\n         Objects.requireNonNull(before);\n         Objects.requireNonNull(after);\n@@ -12,7 +12,7 @@\n\n             throw new IllegalArgumentException(\"'parents' is empty\");\n \n         Resolver resolver = new Resolver(before, parentList, after, null);\n-        resolver.resolveRequires(roots).resolveUses();\n+        resolver.resolve(roots);\n \n         return new Configuration(parentList, resolver, true);\n     }", "nl": "Resolves a collection of root modules to create a configuration.", "old_nl": "Resolves a collection of root modules, with service binding, to create configuration.", "peft": "126\tResolves a collection of root modules, with service binding, to create a configuration.\n", "icl": "126\tResolves a collection of root modules, with service binding, to create configuration.\n", "slm": "Resolves a collection of root modules, with service binding, to create configuration.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,4 @@\n\n public static Type getMethodType(final String methodDescriptor) {\n-        return getType(methodDescriptor.toCharArray(), 0);\n-    }\n+    final char[] valueBuffer = methodDescriptor.toCharArray();\n+    return new Type(METHOD, valueBuffer, 0, valueBuffer.length);\n+  }", "nl": "Returns the {@link Type} corresponding to the given method descriptor.", "old_nl": "Returns the Java type corresponding to the given method descriptor.", "peft": "127\tReturns the Java type corresponding to the given method descriptor.\n", "icl": "127\tReturns the Java type corresponding to the given method descriptor.\n", "slm": "Returns the Java type corresponding to the given method descriptor, including the opening parenthesis.", "label": ["Feat"]}
{"diff": "@@ -1,36 +1,36 @@\n\n-public UploadMultipartPartResult uploadMultipartPart(UploadMultipartPartRequest uploadMultipartPartRequest) {\n+@Override\n+    public UploadMultipartPartResult uploadMultipartPart(\n+            UploadMultipartPartRequest uploadMultipartPartRequest) {\n         ExecutionContext executionContext = createExecutionContext(uploadMultipartPartRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<UploadMultipartPartRequest> request = null;\n         Response<UploadMultipartPartResult> response = null;\n-        \n-        final InputStream isOrig = uploadMultipartPartRequest.getBody();\n-        \n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new UploadMultipartPartRequestMarshaller().marshall(super.beforeMarshalling(uploadMultipartPartRequest));\n+                request = new UploadMultipartPartRequestMarshaller(\n+                        protocolFactory).marshall(super\n+                        .beforeMarshalling(uploadMultipartPartRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            Unmarshaller<UploadMultipartPartResult, JsonUnmarshallerContext> unmarshaller =\n-                new UploadMultipartPartResultJsonUnmarshaller();\n-            JsonResponseHandler<UploadMultipartPartResult> responseHandler =\n-                new JsonResponseHandler<UploadMultipartPartResult>(unmarshaller);\n-\n+            HttpResponseHandler<AmazonWebServiceResponse<UploadMultipartPartResult>> responseHandler = protocolFactory\n+                    .createResponseHandler(new JsonOperationMetadata()\n+                            .withPayloadJson(true)\n+                            .withHasStreamingSuccessResponse(false),\n+                            new UploadMultipartPartResultJsonUnmarshaller());\n             response = invoke(request, responseHandler, executionContext);\n \n             return response.getAwsResponse();\n+\n         } finally {\n-            \n-            // Restore the original input stream, so that the caller\n-            // (who is responsible) can close it\n-            uploadMultipartPartRequest.setBody(isOrig);\n-            \n-            endClientExecution(awsRequestMetrics, request, response, LOGGING_AWS_REQUEST_METRIC);\n+\n+            endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "SHA256 tree hash does not matchTo ensure that part data is not corrupted in transmission, you compute a SHA256 tree hash of the part and include it in your request.", "old_nl": "SHA256 tree hash does not match To ensure that part data is not corrupted in transmission, you compute a SHA256 tree hash of the part and include it in your request.", "peft": "128\tSHA256 tree hash does not match To ensure that part data is not corrupted in transmission, you compute a SHA256 tree hash of the part and include it in your request.\n", "icl": "128\tSHA256 tree hash does not match To ensure that part data is not corrupted in transmission, you compute a SHA256 tree hash of the part and include it in your request.\n", "slm": "SHA256 tree hash does not match To ensure that part data is not corrupted in transmission, you compute a SHA256 tree hash of the part and include it in your request.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public final BasicNodeIter precedingSibling() {\n+public final BasicNodeIter precedingSiblingIter() {\n     return new BasicNodeIter() {\n       /** Child nodes. */\n       private BasicNodeIter iter;\n@@ -13,7 +13,7 @@\n\n           if(r == null) return null;\n \n           final ANodeList list = new ANodeList();\n-          for(final ANode n : r.children()) {\n+          for(final ANode n : r.childIter()) {\n             if(n.is(ANode.this)) break;\n             list.add(n.finish());\n           }", "nl": "Before nodes are added to the result, they must be finalized via {@link ANode#finish()}.", "old_nl": "If nodes returned are to be further used, they must be finalized via {@link ANode#finish()}.", "peft": "130\tIf nodes returned are to be further used, they must be finalized via {@link ANode#finish(QueryContext)}.\n", "icl": "130\tUse {@link ANode#childIter()} instead of {@link ANode#children()}.\n", "slm": "If nodes returned are to be further used, they must be finalized via {@link ANode#finish()}.", "label": ["Ref"]}
{"diff": "@@ -1,6 +1,8 @@\n\n-public static boolean startsWithAny(String self, String... prefixes) {\n-        for (String prefix : prefixes) {\n-            if (self.startsWith(prefix)) {\n+public static boolean startsWithAny(CharSequence self, CharSequence... prefixes) {\n+        String str = self.toString();\n+\n+        for (CharSequence prefix : prefixes) {\n+            if (str.startsWith(prefix.toString())) {\n                 return true;\n             }\n         }", "nl": "Tests if this CharSequence starts with any specified prefixes.", "old_nl": "Tests if this string starts with any specified prefixes.", "peft": "131\tTests if this character sequence starts with any specified prefixes.\n", "icl": "131\tCheck if a string starts with any specified prefixes.\n", "slm": "Tests if this string starts with any specified CharSequence prefixes.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,4 @@\n\n-public static com.liferay.privatemessaging.model.UserThread findByPrimaryKey(\n-\t\tlong userThreadId)\n+public static UserThread findByPrimaryKey(long userThreadId)\n \t\tthrows com.liferay.privatemessaging.NoSuchUserThreadException {\n \t\treturn getPersistence().findByPrimaryKey(userThreadId);\n \t}", "nl": "Returns the user thread with the primary key or throws a {@link NoSuchUserThreadException} if it could not be found.", "old_nl": "Returns the user thread with the primary key or throws a {@link com.liferay.privatemessaging.NoSuchUserThreadException} if it could not be found.", "peft": "132\tReturns the user thread with the primary key or throws a {@link NoSuchUserThreadException} if it could not be found.\n", "icl": "132\tReturns the user thread with the primary key or throws a {@link NoSuchUserThreadException} if it could not be found.\n", "slm": "Returns the user thread with the primary key or throws a {@link UserThreadNotFoundException} if it could not be found.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n @Override\n-    public void willDeclareSetter(TypeSpec.Builder builder, PropertyGenerator propertyGenerator,\n+    public void beforeDeclareSetter(TypeSpec.Builder builder, PropertyGenerator propertyGenerator,\n             MethodSpec.Builder setterParams) {\n     }", "nl": "Stub of {@link Plugin#beforeDeclareSetter(TypeSpec.Builder, PropertyGenerator, MethodSpec.Builder)} for subclasses to override", "old_nl": "Stub of {@link Plugin#willDeclareSetter(TypeSpec.Builder, PropertyGenerator, MethodSpec.Builder)} for subclasses to override", "peft": "133\tStub of {@link Plugin#beforeDeclareSetter(TypeSpec.Builder, PropertyGenerator, MethodSpec.Builder)} for subclasses to override\n", "icl": "133\tStub of {@link Plugin#beforeDeclareSetter(TypeSpec.Builder, PropertyGenerator, MethodSpec.Builder)} for subclasses to override\n", "slm": "Stub of {@link Plugin#beforeDeclareSetter(TypeSpec.Builder, PropertyGenerator, MethodSpec.Builder)} for subclasses to override", "label": ["Ref"]}
{"diff": "@@ -2,9 +2,8 @@\n\n         if (!observers.isMetadataRequired()) {\n             metadata = null;\n         }\n-        // Notify sync observers first\n-        notifySyncObservers(observers.getImmediateSyncObservers(), event, metadata);\n-        notifyTransactionObservers(observers.getTransactionObservers(), event, metadata);\n-\n-        return notifyAsyncObservers(observers.getAsyncObservers(), event, metadata, executor);\n+        final ObserverExceptionHandler handler = new CollectingExceptionHandler();\n+        notifyTransactionObservers(observers.getTransactionObservers(), event, metadata, handler);\n+        notifySyncObservers(observers.getImmediateSyncObservers(), event, metadata, handler);\n+        return notifyAsyncObservers(observers.getAsyncObservers(), event, metadata, executor, handler);\n     }", "nl": "Transactional observer methods are scheduled to be executed in the corresponding transaction phase.", "old_nl": "Observer methods with other transaction phase are scheduled for the corresponding transaction phase.", "peft": "134\tObserver methods with other transaction phase are scheduled for the corresponding transaction phase.\n", "icl": "134\tObserver methods with other transaction phase are scheduled for the corresponding transaction phase.\n", "slm": "Observer methods with other transaction phase are scheduled for the corresponding transaction phase.", "label": ["Code&Doc"]}
{"diff": "@@ -4,7 +4,13 @@\n\n \n         //because there is no way to remove a datasource we only add to this map.\n         for (Long id : eventManager.getDataSourceIDs()) {\n-            datasourcesMap.putIfAbsent(id, skCase.getContentById(id).getDataSource().getName());\n+            try {\n+                if (datasourcesMap.containsKey(id) == false) {\n+                    datasourcesMap.put(id, skCase.getDataSource(id));\n+                }\n+            } catch (TskDataException ex) {\n+                throw new TskCoreException(\"Error looking up datasource for id \" + id, ex);\n+            }\n         }\n \n         //should this only be tags applied to files or event bearing artifacts?", "nl": "Update the data used to determine the available filters.", "old_nl": "Use the given SleuthkitCase to update the data used to determine the available filters.", "peft": "135\tUse the given SleuthkitCase to update the data used to determine the available filters and datasources.\n", "icl": "135\tUse the given SleuthkitCase to update the data used to determine the available filters.\n", "slm": "Use the given {@link SleuthkitCase} to update the data used to determine the available filters.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-protected void appendBuffer(StringBuffer b, String s) {\n+protected void appendBuilder(StringBuilder b, String s) {\n         if (b.length() > 0)\n-            b.append(\":\");\n+            b.append(':');\n         b.append(s);\n     }", "nl": "Adds a  to a , with each  being separated by a \":\".", "old_nl": "Adds a  to a buffer, with each  being separated by a \":\".", "peft": "137\tAdds a  to a builder, with each  being separated by a \":\".\n", "icl": "137\tAdds a  to a builder, with each  being separated by a \":\".\n", "slm": "Adds a  to a StringBuilder, with each  being separated by a \":\".", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n protected void buildEnumConstantComments(Content enumConstantsTree) {\n-        if (!configuration.nocomment) {\n+        if (!options.noComment) {\n             writer.addComments(currentElement, enumConstantsTree);\n         }\n     }", "nl": "Do nothing if {@link BaseOptions#noComment} is set to true.", "old_nl": "Do nothing if {@link BaseConfiguration#nocomment} is set to true.", "peft": "138\tDo nothing if {@link Options#noComment} is set to true.\n", "icl": "138\tDo nothing if {@link BaseConfiguration#noComment} is set to true.\n", "slm": "Do nothing if {@link BaseConfiguration#noComment} is set to true.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public static ServiceLocator createLocator(final String name, final ServiceLocator parent, final Binder... binders) {\n-        return _createLocator(name, parent, binders);\n+public static InstanceManager createInstanceManager(InstanceManager parent, String defaultClassAnalyzer, Binder... binders) {\n+        return _instanceManager(null, parent, defaultClassAnalyzer, binders);\n     }", "nl": "Create a {@link InstanceManager}.", "old_nl": "Create a {@link ServiceLocator}.", "peft": "140\tCreate an {@link InstanceManager}.\n", "icl": "140\tCreate a InstanceManager.\n", "slm": "Create a {@link InstanceManager}.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n ASTExpression forceExprContext() {\n         // by the time this is called, this node is on top of the stack,\n         // meaning, it has no parent\n-        return shrinkOneSegment(ASTVariableReference::new, ASTFieldAccess::new);\n+        return shrinkOneSegment(ASTVariableAccess::new, ASTFieldAccess::new);\n     }", "nl": "If the name is a single identifier, then this can be       reclassified as an {@link ASTVariableAccess}", "old_nl": "If the name is a single identifier, then this can be       reclassified as an {@link ASTVariableReference}", "peft": "142\tIf the name is a single identifier, then this can be       reclassified as an {@link ASTVariableAccess}\n", "icl": "142\tIf the name is a single identifier, then this can be reclassified as an ASTVariableAccess\n", "slm": "If the name is a single identifier, then this can be       reclassified as an {@link ASTVariableAccess}", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,5 @@\n\n-public void setAccountType (String AccountType)\n+@Override\n+\tpublic void setAccountType (java.lang.String AccountType)\n \t{\n \n \t\tset_Value (COLUMNNAME_AccountType, AccountType);", "nl": "Set Kontenart.", "old_nl": "Set Account Type.", "peft": "143\tSet Kontoart.\n", "icl": "143\tSet the account type.\n", "slm": "Set Buchungsart.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public UnaryCallSettings<LabelAudioRequest, Operation> labelAudioSettings() {\n-    return ((DataLabelingServiceStubSettings) getStubSettings()).labelAudioSettings();\n+public UnaryCallSettings<GetEvaluationRequest, Evaluation> getEvaluationSettings() {\n+    return ((DataLabelingServiceStubSettings) getStubSettings()).getEvaluationSettings();\n   }", "nl": "Returns the object with the settings used for calls to getEvaluation.", "old_nl": "Returns the object with the settings used for calls to labelAudio.", "peft": "144\tReturns the object with the settings used for calls to getEvaluation.\n", "icl": "144\tReturns the object with the settings used for calls to getEvaluation.\n", "slm": "Returns the object with the settings used for calls to getEvaluation().", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,4 @@\n\n-public ElectPreferredLeadersResult electPreferredLeaders(Collection<TopicPartition> partitions) {\n+@Deprecated\n+    public ElectPreferredLeadersResult electPreferredLeaders(Collection<TopicPartition> partitions) {\n         return electPreferredLeaders(partitions, new ElectPreferredLeadersOptions());\n     }", "nl": "This is a convenience method for {@link #electLeaders(ElectionType, Set, ElectLeadersOptions)} with preferred election type and default options.", "old_nl": "This is a convenience method for {@link #electPreferredLeaders(Collection, ElectPreferredLeadersOptions)} with default options.", "peft": "145\tThis is a convenience method for {@link #electPreferredLeaders(Collection, ElectPreferredLeadersOptions)} with default options.\n", "icl": "145\tThis is a convenience method for {@link #electPreferredLeaders(Collection, ElectPreferredLeadersOptions)} with default options.\n", "slm": "This is a convenience method for {@link #electPreferredLeaders(Collection, ElectPreferredLeadersOptions)} with default options.", "label": ["Feat"]}
{"diff": "@@ -1,9 +1,27 @@\n\n-@SuppressWarnings(\"unchecked\")\n-\tpublic <V> V get(ParamInfo<V> info) {\n-\t\tV value = (V) paramMap.getOrDefault(info.getName(), info.getDefaultValue());\n-\t\tif (value == null && !info.isOptional() && !info.hasDefaultValue()) {\n-\t\t\tthrow new RuntimeException(info.getName() +\n-\t\t\t\t\" not exist which is not optional and don't have a default value\");\n+public <V> V get(ParamInfo<V> info) {\n+\t\tString value = null;\n+\t\tString usedParamName = null;\n+\t\tfor (String nameOrAlias : getParamNameAndAlias(info)) {\n+\t\t\tif (params.containsKey(nameOrAlias)) {\n+\t\t\t\tif (usedParamName != null) {\n+\t\t\t\t\tthrow new IllegalArgumentException(String.format(\"Duplicate parameters of %s and %s\",\n+\t\t\t\t\t\tusedParamName, nameOrAlias));\n+\t\t\t\t}\n+\t\t\t\tusedParamName = nameOrAlias;\n+\t\t\t\tvalue = params.get(nameOrAlias);\n+\t\t\t}\n \t\t}\n-\t\treturn value;\n+\n+\t\tif (usedParamName != null) {\n+\t\t\t// The param value was set by the user.\n+\t\t\treturn valueFromJson(value, info.getValueClass());\n+\t\t} else {\n+\t\t\t// The param value was not set by the user.\n+\t\t\tif (!info.isOptional()) {\n+\t\t\t\tthrow new IllegalArgumentException(\"Missing non-optional parameter \" + info.getName());\n+\t\t\t} else if (!info.hasDefaultValue()) {\n+\t\t\t\tthrow new IllegalArgumentException(\"Cannot find default value for optional parameter \" + info.getName());\n+\t\t\t}\n+\t\t\treturn info.getDefaultValue();\n+\t\t}\n \t}", "nl": "Returns the value of the specific parameter, or default value defined in the {@code info} if this Params doesn't have a value set for the parameter.", "old_nl": "Returns the value of the specific parameter, or default value defined in the {@code info} if this Params doesn't contain the param.", "peft": "146\tReturns the value of the specific parameter, or default value defined in the {@code info} if this Params doesn't contain the param or the param is not set by the user.\n", "icl": "146\tThe method will return the value of the specific parameter, or default value defined in the {@code info} if this Params doesn't contain the param.\n", "slm": "Returns the value of the specific parameter, or default value defined in the {@code info} if this Params doesn't contain the param.", "label": ["Code&Doc"]}
{"diff": "@@ -1,4 +1,4 @@\n\n @XmlElementDecl(namespace = \"http://schemas.openxmlformats.org/wordprocessingml/2006/main\", name = \"moveFromRangeEnd\", scope = Hdr.class)\n-    public JAXBElement<CTMarkupRange> createHdrMoveFromRangeEnd(CTMarkupRange value) {\n-        return new JAXBElement<CTMarkupRange>(_CTCustomXmlCellMoveFromRangeEnd_QNAME, CTMarkupRange.class, Hdr.class, value);\n+    public JAXBElement<CTMoveFromRangeEnd> createHdrMoveFromRangeEnd(CTMoveFromRangeEnd value) {\n+        return new JAXBElement<CTMoveFromRangeEnd>(_CTSdtContentRowMoveFromRangeEnd_QNAME, CTMoveFromRangeEnd.class, Hdr.class, value);\n     }", "nl": "Create an instance of {@link JAXBElement }{@code <}{@link CTMoveFromRangeEnd }{@code >}}", "old_nl": "Create an instance of {@link JAXBElement }{@code <}{@link CTMarkupRange }{@code >}}", "peft": "147\tCreate an instance of {@link JAXBElement }{@code \n", "icl": "147\tCreate an instance of {@link JAXBElement }{@code \n", "slm": "Create an instance of {@link JAXBElement }{@code <}{@link CTMoveFromRangeEnd }{@code >}}", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,7 @@\n\n public void clearPersistedFiles(List<Long> persistedFiles) {\n     synchronized (mLock) {\n-      mPersistedFiles.removeAll(persistedFiles);\n+      for (long persistedId : persistedFiles) {\n+        mPersistedFilesInfo.remove(persistedId);\n+      }\n     }\n   }", "nl": "Clears the given persisted files stored in {@link #mPersistedFilesInfo}.", "old_nl": "Clears the given persisted files stored in {@link #mPersistedFiles}.", "peft": "149\tClears the given persisted files stored in {@link #mPersistedFilesInfo}.\n", "icl": "149\tClears the given persisted files stored in {@link #mPersistedFilesInfo}.\n", "slm": "Clears the given persisted files stored in {@link #mPersistedFilesInfo}.", "label": ["Feat"]}
{"diff": "@@ -1,8 +1,7 @@\n\n-@Override\n-    public int compareTo(ChronoLocalDateTime<ISOChrono> other) {\n-        int cmp = date.compareTo(other.getDate());\n-        if (cmp == 0) {\n-            cmp = time.compareTo(other.getTime());\n+@Override  // override for Javadoc and performance\n+    public int compareTo(ChronoLocalDateTime<?> other) {\n+        if (other instanceof LocalDateTime) {\n+            return compareTo0((LocalDateTime) other);\n         }\n-        return cmp;\n+        return super.compareTo(other);\n     }", "nl": "Compares this date-time to another date-time.", "old_nl": "Compares this {@code LocalDateTime} to another date-time.", "peft": "150\tCompares this date-time to another date-time.\n", "icl": "150\tCompares this {@code LocalDateTime} to another date-time.\n", "slm": "Compares this date-time to another date-time.", "label": ["Code&Doc"]}
{"diff": "@@ -7,5 +7,8 @@\n\n       AdjustablePayment upFrontFee,\n       ReferenceData refData) {\n \n-    return convention.toTrade(legalEntityId, tradeDate, tenor, buySell, notional, fixedRate, upFrontFee, refData);\n+    return accrualStart.equals(AccrualStart.IMM_DATE)\n+        ? convention.toTrade(legalEntityId, tradeDate, tenor, buySell, notional, fixedRate, upFrontFee, refData)\n+        : convention.toTrade(\n+            legalEntityId, tradeDate, tradeDate.plusDays(1), tenor, buySell, notional, fixedRate, upFrontFee, refData);\n   }", "nl": "This returns a trade based on the specified trade date and upfront fee.", "old_nl": "This returns a trade based on the specified trade date.", "peft": "151\tThis returns a trade based on the specified trade date, with the accrual period starting on the next day.\n", "icl": "151\tThis returns a trade based on the specified trade date.\n", "slm": "This returns a trade based on the specified trade date, including the imm date.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public <U, E extends Exception> Task<U> thenApply(String name, Scheduler scheduler, ExceptionalFunction<T, U, E> fn) {\n-        return new UniApply<>(name, scheduler, fn);\n+public <U, E extends Exception> Task<U> thenApply(String name, Executor executor, ExceptionalFunction<T, U, E> fn) {\n+        return new UniApply<>(fn).setExecutor(executor).setName(name);\n     }", "nl": "Returns a new Task that, when this task completes normally, is executed using the supplied Executor, with this task's result as the argument to the supplied function.", "old_nl": "Returns a new Task that, when this task completes normally, is executed using the supplied Scheduler, with this task's result as the argument to the supplied function.", "peft": "152\tReturns a new Task that, when this task completes normally, is executed using the supplied Executor, with this task's result as the argument to the supplied function.\n", "icl": "152\tReturns a new Task that, when this task completes normally, is executed using the supplied Executor, with this task's result as the argument to the supplied function.\n", "slm": "Returns a new Task that, when this task completes normally, is executed using the supplied Executor, with this task's result as the argument to the supplied function.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n public static <E> List<E> of(E e1, E e2, E e3, E e4, E e5) {\n-        return new ImmutableCollections.ListN<E>(e1, e2, e3, e4, e5);\n+        return new Unmodifiable.ListN<E>(e1, e2, e3, e4, e5);\n     }", "nl": "See Unmodifiable Lists for details.", "old_nl": "See Immutable List Static Factory Methods for details.", "peft": "155\tSee Unmodifiable List Static Factory Methods for details.\n", "icl": "155\tSee Immutable List Static Factory Methods for details.\n", "slm": "See Unmodifiable List Static Factory Methods for details.", "label": ["Feat"]}
{"diff": "@@ -1,40 +1,41 @@\n\n-public void saveComponent() {\n-    int                           result;\n-    JComponentWriter              writer;\n-    File                          file;\n-    JComponentWriterFileFilter    filter;\n-    \n+@Override\n+  public void saveComponent() {\n+    int result;\n+    JComponentWriter writer;\n+    File file;\n+    JComponentWriterFileFilter filter;\n+\n     // display save dialog\n     m_FileChooserPanel.setDialogTitle(getSaveDialogTitle());\n     do {\n       result = m_FileChooserPanel.showSaveDialog(getComponent());\n-      if (result != JFileChooser.APPROVE_OPTION)\n+      if (result != JFileChooser.APPROVE_OPTION) {\n         return;\n-    }\n-    while (m_FileChooserPanel.getSelectedFile() == null);\n-    \n+      }\n+    } while (m_FileChooserPanel.getSelectedFile() == null);\n+\n     // save the file\n     try {\n       filter = (JComponentWriterFileFilter) m_FileChooserPanel.getFileFilter();\n-      file   = m_FileChooserPanel.getSelectedFile();\n+      file = m_FileChooserPanel.getSelectedFile();\n       writer = filter.getWriter();\n-      if (!file.getAbsolutePath().toLowerCase().endsWith(writer.getExtension().toLowerCase()))\n-        file = new File(file.getAbsolutePath() + writer.getExtension()); \n+      if (!file.getAbsolutePath().toLowerCase()\n+        .endsWith(writer.getExtension().toLowerCase())) {\n+        file = new File(file.getAbsolutePath() + writer.getExtension());\n+      }\n       writer.setComponent(getComponent());\n       writer.setFile(file);\n       writer.setScale(getXScale(), getYScale());\n       writer.setUseCustomDimensions(m_CustomDimensionsCheckBox.isSelected());\n       if (m_CustomDimensionsCheckBox.isSelected()) {\n-\twriter.setCustomWidth(Integer.parseInt(m_CustomWidthText.getText()));\n-\twriter.setCustomHeight(Integer.parseInt(m_CustomHeightText.getText()));\n-      }\n-      else {\n-\twriter.setCustomWidth(-1);\n-\twriter.setCustomHeight(-1);\n+        writer.setCustomWidth(Integer.parseInt(m_CustomWidthText.getText()));\n+        writer.setCustomHeight(Integer.parseInt(m_CustomHeightText.getText()));\n+      } else {\n+        writer.setCustomWidth(-1);\n+        writer.setCustomHeight(-1);\n       }\n       writer.toOutput();\n-    }\n-    catch (Exception e) {\n+    } catch (Exception e) {\n       e.printStackTrace();\n     }\n   }", "nl": "Swing JFileChooser: if you entered a new filename in the save dialog and press Enter the  method returns  instead of the filename.", "old_nl": "Fixes a bug with the Swing JFileChooser: if you entered a new filename in the save dialog and press Enter the  method returns  instead of the filename.", "peft": "157\tFixes a bug with the Swing JFileChooser: if you entered a new filename in the save dialog and press Enter the method returns instead of the filename.\n", "icl": "157\tFixes a bug with the Swing JFileChooser: if you entered a new filename in the save dialog and press Enter the  method returns  instead of the filename.\n", "slm": "JFileChooser: if you entered a new filename in the save dialog and press Enter the  method returns  instead of the filename.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-private static String getOperationEnumValue(DescriptorProtos.MethodDescriptorProto methodDescriptor) {\n-    return getOperationName(methodDescriptor).toUpperCase().replace('-', '_');\n+private static String getOperationConstant(DescriptorProtos.MethodDescriptorProto methodDescriptor) {\n+    return CaseFormat.UPPER_CAMEL.to(CaseFormat.UPPER_UNDERSCORE, methodDescriptor.getName());\n   }", "nl": "Returns the operation constant name.", "old_nl": "Returns the operation enum value.", "peft": "158\tReturns the operation constant.\n", "icl": "158\tReturns the operation constant.\n", "slm": "Returns the operation constant value.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-static InstanceManagerStore createHk2LocatorManager() {\n-        final InstanceManagerStore manager = lookupService(InstanceManagerStore.class);\n-        return manager != null ? manager : new SingleInstanceManagerStore();\n+static InjectionManagerStore createHk2InjectionManagerStore() {\n+        final InjectionManagerStore manager = lookupService(InjectionManagerStore.class);\n+        return manager != null ? manager : new SingleInjectionManagerStore();\n     }", "nl": "Create new instance of {@link InjectionManagerStore}.", "old_nl": "Create new instance of {@link InstanceManagerStore}.", "peft": "161\tCreate new instance of {@link InjectionManagerStore}.\n", "icl": "161\tCreate new instance of {@link InjectionManagerStore}.\n", "slm": "Create new instance of {@link InjectionManagerStore}.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public void setDependencyOptions(DependencyOptions options) {\n-    this.dependencyOptions = options;\n+public void setDependencyOptions(DependencyOptions dependencyOptions) {\n+    this.dependencyOptions = dependencyOptions;\n   }", "nl": "Sets the dependency management options.", "old_nl": "Sets dependency options.", "peft": "162\tSets the dependency options.\n", "icl": "162\tSets dependency options.\n", "slm": "Sets dependency options.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,3 @@\n\n-public void setCommentFormat(String commentFormat) {\n-        this.commentFormat = commentFormat;\n-        commentRegexp = CommonUtil.createPattern(commentFormat);\n+public void setCommentFormat(Pattern commentPattern) {\n+        commentFormat = commentPattern;\n     }", "nl": "Setter for comment pattern.", "old_nl": "Setter for comment format.", "peft": "163\tSetter for comment pattern.\n", "icl": "163\tSetter for comment format.\n", "slm": "Setter for comment pattern.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n public void setEntity(String entity)\n         {\n-            user.setAttribute(ENTITY_ATTR_NAME, entity);\n+            userElement.setAttribute(ENTITY_ATTR_NAME, entity);\n         }", "nl": "Sets the entity attribute of this User's element to entity", "old_nl": "Sets the entity attribute of this User to entity", "peft": "164\tSets the entity attribute of this User to entity.\n", "icl": "164\tSet the entity attribute of this User to entity\n", "slm": "Sets the entity attribute of this User to entity", "label": ["Ref"]}
{"diff": "@@ -1,7 +1,7 @@\n\n-public static Configuration resolveRequiresAndUses(ModuleFinder before,\n-                                                       List<Configuration> parents,\n-                                                       ModuleFinder after,\n-                                                       Collection<String> roots)\n+public static Configuration resolveAndBind(ModuleFinder before,\n+                                               List<Configuration> parents,\n+                                               ModuleFinder after,\n+                                               Collection<String> roots)\n     {\n         Objects.requireNonNull(before);\n         Objects.requireNonNull(after);\n@@ -12,7 +12,7 @@\n\n             throw new IllegalArgumentException(\"'parents' is empty\");\n \n         Resolver resolver = new Resolver(before, parentList, after, null);\n-        resolver.resolveRequires(roots).resolveUses();\n+        resolver.resolve(roots).bind();\n \n         return new Configuration(parentList, resolver, true);\n     }", "nl": "More specifically, the root modules are resolved as if by calling {@code resolve}.", "old_nl": "More specifically, the root modules are resolved as if by calling {@code resolveRequires}.", "peft": "165\tMore specifically, the root modules are resolved as if by calling {@code resolve}.\n", "icl": "165\tMore specifically, the root modules are resolved as if by calling {@code resolveRequires}.\n", "slm": "More specifically, the root modules are resolved as if by calling {@code resolveAndBind}.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n-public static <T> Set<T> getCustomProviders(InstanceManager instanceManager, Class<T> contract) {\n+public static <T> Set<T> getCustomProviders(InjectionManager injectionManager, Class<T> contract) {\n         Collection<ServiceHolder<T>> hk2Providers =\n-                getServiceHolders(instanceManager, contract, CustomAnnotationLiteral.INSTANCE);\n+                getServiceHolders(injectionManager, contract, CustomAnnotationLiteral.INSTANCE);\n         return getProviderClasses(hk2Providers);\n     }", "nl": "Get the set of all custom providers registered for the given service provider contract in the underlying {@link InjectionManager injection manager} container.", "old_nl": "Get the set of all custom providers registered for the given service provider contract in the underlying {@link InstanceManager instance manager} container.", "peft": "166\tGet the set of all custom providers registered for the given service provider contract in the underlying {@link InjectionManager injection manager} container.\n", "icl": "166\tGet the set of all custom providers registered for the given service provider contract in the underlying {@link InjectionManager injection manager} container.\n", "slm": "Get the set of all custom providers registered for the given service provider contract in the underlying {@link InjectionManager injection manager} container.", "label": ["Ref"]}
{"diff": "@@ -1,16 +1,9 @@\n\n-private FormElement generateForm(CmsUUID structureId, CmsContextMenuEntryBean menuEntryBean) {\n+private Map<String, String> getDialogParameters(CmsUUID structureId, CmsContextMenuEntryBean menuEntryBean) {\n \n-        String fileName = menuEntryBean.getParams().get(CmsMenuCommandParameters.PARAM_DIALOG_URI);\n-        // create a form to submit a post request to the editor JSP\n-        Map<String, String> formValues = new HashMap<String, String>();\n+        HashMap<String, String> parameters = new HashMap<String, String>();\n         if (structureId != null) {\n-            formValues.put(PARAM_CONTENT_STRUCTURE_ID, structureId.toString());\n+            parameters.put(PARAM_CONTENT_STRUCTURE_ID, structureId.toString());\n         }\n-        formValues.putAll(menuEntryBean.getParams());\n-        FormElement formElement = CmsDomUtil.generateHiddenForm(\n-            CmsCoreProvider.get().link(fileName),\n-            Method.post,\n-            m_iFrameName,\n-            formValues);\n-        return formElement;\n+        parameters.putAll(menuEntryBean.getParams());\n+        return parameters;\n     }", "nl": "Generates the dialog parameters.", "old_nl": "Generates the form to post to the dialog frame.", "peft": "167\tGenerates the parameters to post to the dialog frame.\n", "icl": "167\tGenerates the form to post to the dialog frame.\n", "slm": "Creates the dialog frame parameters to the editor JSP.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,6 @@\n\n protected boolean shouldCreateWorkunit(long createTime, long updateTime, LongWatermark lowWatermark) {\n+    if (new DateTime(updateTime).isBefore(this.maxLookBackTime)) {\n+      return false;\n+    }\n     return new DateTime(updateTime).isAfter(lowWatermark.getValue());\n   }", "nl": "Returns  If the  is greater than the  and   is not used.", "old_nl": "Returns  If the  is greater than the .", "peft": "168\tReturns  If the  is greater than the  and greater than the.\n", "icl": "168\tReturns true if the updateTime is greater than the lowWatermark.\n", "slm": "Returns  If the  is greater than the .", "label": ["Feat"]}
{"diff": "@@ -1,9 +1,5 @@\n\n-protected void moveTouchPoint(double x, double y, Bounds bounds) {\n+protected void moveTouchPoint(double x, double y) {\n         labelText.set(createLabelText(x, y, touchId));\n-        line.setStartX(x);\n-        line.setStartY(y);\n-        line.setEndX(bounds.getMinX());\n-        line.setEndY(bounds.getMinY());\n         Bounds cb = circle.getBoundsInParent();\n         this.relocate(x - cb.getMinX() - cb.getWidth() / 2, y - cb.getMinY() - cb.getHeight() / 2);\n     }", "nl": "Move this TouchDisplay to a new x-y position.", "old_nl": "Move this TouchDisplay to a new x-y position and bounds.", "peft": "170\tMove this TouchDisplay to a new x-y position.\n", "icl": "170\tMove this TouchDisplay to a new x-y position and bounds.\n", "slm": "Move this TouchDisplay to a new x-y position.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public static TriangulateTwoViewsCalibrated triangulateTwoDLT() {\n-\t\treturn new WrapTwoViewsTriangulateDLT();\n+public static TriangulateTwoViews triangulateTwoDLT() {\n+\t\treturn new WrapTwoViewsTriangulateUncalibratedDLT();\n \t}", "nl": "Triangulate two view using the Discrete Linear Transform (DLT) with an uncalibrated camera.", "old_nl": "Triangulate two view using the Discrete Linear Transform (DLT)", "peft": "171\tTriangulate two view using the Discrete Linear Transform (DLT) and uncalibrated camera parameters\n", "icl": "171\tTriangulate two view using the Discrete Linear Transform (DLT)\n", "slm": "Triangulate two view using the Discrete Linear", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public static String getFullPathNoEndSeparator(final String filename) {\n-        return doGetFullPath(filename, false);\n+public static String getFullPathNoEndSeparator(final String fileName) {\n+        return doGetFullPath(fileName, false);\n     }", "nl": "Gets the full path from a full fileName, which is the prefix + path, and also excluding the final directory separator.", "old_nl": "Gets the full path from a full filename, which is the prefix + path, and also excluding the final directory separator.", "peft": "173\tGets the full path from a full filename, which is the prefix + path, and also excluding the final directory separator.\n", "icl": "173\tGet the full path from a full filename, which is the prefix + path, and also excluding the final directory separator.\n", "slm": "Gets the full path from a full filename, which is the prefix + path, and also excluding the final directory separator.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,5 @@\n\n-void calculateLocked() {\n+@Override\n+    void calculateLocked() {\n         if (currentCart == null) {\n             waited = 0;\n         }", "nl": "As the name implies, {@code trainLeaving} indicates whether the train or cart is in the process of leaving the track.", "old_nl": "As the name implies,  indicates whether the train or cart is in the process of leaving the track.", "peft": "174\tAs the name implies,  indicates whether the train or cart is in the process of leaving the track.\n", "icl": "174\tThe train is in the process of leaving the track.\n", "slm": "As the name implies,  indicates whether the train or cart is in the process of leaving the track.", "label": ["Feat"]}
{"diff": "@@ -9,9 +9,9 @@\n\n         if (getVersionId() != null)\n             sb.append(\"VersionId: \").append(getVersionId()).append(\",\");\n         if (getSecretBinary() != null)\n-            sb.append(\"SecretBinary: \").append(getSecretBinary()).append(\",\");\n+            sb.append(\"SecretBinary: \").append(\"***Sensitive Data Redacted***\").append(\",\");\n         if (getSecretString() != null)\n-            sb.append(\"SecretString: \").append(getSecretString()).append(\",\");\n+            sb.append(\"SecretString: \").append(\"***Sensitive Data Redacted***\").append(\",\");\n         if (getVersionStages() != null)\n             sb.append(\"VersionStages: \").append(getVersionStages()).append(\",\");\n         if (getCreatedDate() != null)", "nl": "Returns a string representation of this object.", "old_nl": "Returns a string representation of this object; useful for testing and debugging.", "peft": "175\tReturns a string representation of this object; useful for testing and debugging.\n", "icl": "175\tReturns a string representation of this object; useful for testing and debugging.\n", "slm": "This object is useful for testing and debugging.", "label": ["Feat"]}
{"diff": "@@ -1,13 +1,15 @@\n\n-public LockResource acquireReadLockResource() throws IOException {\n+public Optional<CountResource> acquireResourceToBlockReinit() throws IOException {\n     Optional<IOException> exception = mExecutor.getException();\n     if (exception.isPresent()) {\n       throw exception.get();\n     }\n-    mLock.readLock().lock();\n+    if (mCount.updateAndGet(c -> c >= 0 ? c + 1 : c) <= 0) {\n+      return Optional.empty();\n+    }\n     exception = mExecutor.getException();\n     if (exception.isPresent()) {\n-      mLock.readLock().unlock();\n+      mCount.decrementAndGet();\n       throw exception.get();\n     }\n-    return new LockResource(mLock.readLock(), false);\n+    return Optional.of(new CountResource(mCount, false));\n   }", "nl": "When the context is being reinitialized, this call returns an empty optional.", "old_nl": "When the context is being reinitialized, this call blocks until the reinitialization succeeds or fails.", "peft": "176\tWhen the context is being reinitialized, this call blocks until the reinitialization succeeds or fails, or the context is closed.\n", "icl": "176\tWhen the context is being reinitialized, this call blocks until the reinitialization succeeds or fails.\n", "slm": "When the context is being reinitialized, the block until the reinitialization succeeds or fails.", "label": ["Feat"]}
{"diff": "@@ -1,8 +1,10 @@\n\n-private static int initializeTimeout() {\n-        Integer tmp = java.security.AccessController.doPrivileged(\n-                new GetIntegerAction(\"com.sun.security.crl.timeout\"));\n+private static int initializeTimeout(String prop, int def) {\n+        Integer tmp = GetIntegerAction.privilegedGetProperty(prop);\n         if (tmp == null || tmp < 0) {\n-            return DEFAULT_CRL_CONNECT_TIMEOUT;\n+            return def;\n+        }\n+        if (debug != null) {\n+            debug.println(prop + \" set to \" + tmp + \" seconds\");\n         }\n         // Convert to milliseconds, as the system property will be\n         // specified in seconds", "nl": "If the property has not been set, or if its value is negative, set the timeout length to the specified default.", "old_nl": "If the property has not been set, or if its value is negative, set the timeout length to the default.", "peft": "177\tIf the property has not been set, or if its value is negative, set the timeout length to the default value.\n", "icl": "177\tIf the property has not been set, or if its value is negative, set the timeout length to the default.\n", "slm": "If the property has not been set, or if its value is negative, set the timeout length to the default.", "label": ["Feat"]}
{"diff": "@@ -1,24 +1,36 @@\n\n-public void deleteVaultNotifications(DeleteVaultNotificationsRequest deleteVaultNotificationsRequest) {\n+@Override\n+    public DeleteVaultNotificationsResult deleteVaultNotifications(\n+            DeleteVaultNotificationsRequest deleteVaultNotificationsRequest) {\n         ExecutionContext executionContext = createExecutionContext(deleteVaultNotificationsRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<DeleteVaultNotificationsRequest> request = null;\n-        \n+        Response<DeleteVaultNotificationsResult> response = null;\n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new DeleteVaultNotificationsRequestMarshaller().marshall(super.beforeMarshalling(deleteVaultNotificationsRequest));\n+                request = new DeleteVaultNotificationsRequestMarshaller(\n+                        protocolFactory).marshall(super\n+                        .beforeMarshalling(deleteVaultNotificationsRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            JsonResponseHandler<Void> responseHandler = new JsonResponseHandler<Void>(null);\n-            invoke(request, responseHandler, executionContext);\n+            HttpResponseHandler<AmazonWebServiceResponse<DeleteVaultNotificationsResult>> responseHandler = protocolFactory\n+                    .createResponseHandler(\n+                            new JsonOperationMetadata().withPayloadJson(true)\n+                                    .withHasStreamingSuccessResponse(false),\n+                            new DeleteVaultNotificationsResultJsonUnmarshaller());\n+            response = invoke(request, responseHandler, executionContext);\n+\n+            return response.getAwsResponse();\n \n         } finally {\n-            \n-            endClientExecution(awsRequestMetrics, request, null, LOGGING_AWS_REQUEST_METRIC);\n+\n+            endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "For more information, see Access Control Using AWS Identity and Access Management (IAM).", "old_nl": "Access Control Using AWS Identity and Access Management (IAM)  .", "peft": "178\tAccess Control Using AWS Identity and Access Management (IAM) For more information about IAM, see AWS Identity and Access Management (IAM) in the IAM User Guide.\n", "icl": "178\tAccess Control Using AWS Identity and Access Management (IAM) .\n", "slm": "Access Control Using AWS Identity and Access Management (IAM)  .", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,5 @@\n\n static private void assertNamedSubqueryForEachInclude(\n+    \t\tfinal AST2BOpContext context,\n             final NamedSubqueriesNode namedSubqueries,\n             final NamedSubqueryInclude[] allIncludes) {\n \n@@ -21,9 +22,16 @@\n\n \n             }\n \n-            if (!found)\n-                throw new RuntimeException(\n-                        \"No subquery produces that solution set: \" + namedSet);\n+            if (!found) {\n+            \ttry {\n+            \t    context.getSolutionSetStats(namedSet);\n+            \t    // There is a named solution set so we are OK.\n+            \t}\n+            \tcatch (RuntimeException e) {\n+\t                throw new RuntimeException(\n+\t                        \"No subquery produces the solution set: \" + namedSet, e);\n+            \t}\n+            }\n \n         }\n ", "nl": "Verify that a named subquery of solution set exists for each INCLUDE.", "old_nl": "Verify that a named subquery exists for each INCLUDE.", "peft": "179\tVerify that a named subquery exists for each INCLUDE or SOLUTION_SET.\n", "icl": "179\tIt verifies that a named subquery exists for each INCLUDE.\n", "slm": "Verify that a named subquery exists for each filter.", "label": ["Feat"]}
{"diff": "@@ -1,17 +1,7 @@\n\n-public static byte[] copyOfRange(byte[] data, int from, int to)\n+public static byte[] copyOfRange(byte[] original, int from, int to)\n     {\n         int newLength = getLength(from, to);\n-\n-        byte[] tmp = new byte[newLength];\n-\n-        if (data.length - from < newLength)\n-        {\n-            System.arraycopy(data, from, tmp, 0, data.length - from);\n-        }\n-        else\n-        {\n-            System.arraycopy(data, from, tmp, 0, newLength);\n-        }\n-\n-        return tmp;\n+        byte[] copy = new byte[newLength];\n+        System.arraycopy(original, from, copy, 0, Math.min(original.length - from, newLength));\n+        return copy;\n     }", "nl": "Make a copy of a range of bytes from the passed in array.", "old_nl": "Make a copy of a range of bytes from the passed in data array.", "peft": "180\tMake a copy of a range of bytes from the passed in original array.\n", "icl": "180\tMake a copy of a range of bytes from the passed in data array.\n", "slm": "Make a copy of a range of bytes from the passed in original array.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n boolean isPresent() {\n-        Thread t = Thread.currentThread();\n-        ThreadLocalMap map = getMap(t);\n+        Strand s = Strand.currentStrand();\n+        ThreadLocalMap map = getMap(s);\n         return map != null && map.getEntry(this) != null;\n     }", "nl": "Returns {@code true} if there is a value in the current strand's copy of this thread-local variable, even if that values is {@code null}.", "old_nl": "Returns {@code true} if there is a value in the current thread's copy of this thread-local variable, even if that values is {@code null}.", "peft": "181\tReturns {@code true} if there is a value in the current strand's copy of this thread-local variable, even if that values is {@code null}.\n", "icl": "181\tReturns {@code true} if there is a value in the current thread's copy of this thread-local variable, even if that values is {@code null}.\n", "slm": "Returns {@code true} if there is a value in the current strand's copy of this thread-local variable, even if that values is {@code null}.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n @Override\n-    public Temporal doWithAdjustment(Temporal temporal) {\n+    public Temporal adjustInto(Temporal temporal) {\n         if (Chrono.from(temporal).equals(ISOChrono.INSTANCE) == false) {\n             throw new DateTimeException(\"Adjustment only supported on ISO date-time\");\n         }", "nl": "Applications should use the {@code with(TemporalAdjuster)} method on the date-time object to make the adjustment passing this as the argument.", "old_nl": "Applications should use the {@code with(WithAdjuster)} method on the date-time object to make the adjustment passing this as the argument.", "peft": "182\tApplications should use the {@code with(WithAdjuster)} method on the date-time object to make the adjustment passing this as the argument.\n", "icl": "182\tApplications should use the {@code adjustInto(Temporal)} method on the date-time object to make the adjustment passing this as the argument.\n", "slm": "Applications should use the {@code withAdjuster} method on the date-time object to make the adjustment passing this as the argument.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n @Override\n-\tpublic void setQtyToInvoiceInPriceUOM_Eff (java.math.BigDecimal QtyToInvoiceInPriceUOM_Eff)\n+\tpublic void setQtyToInvoiceInPriceUOM (java.math.BigDecimal QtyToInvoiceInPriceUOM)\n \t{\n-\t\tset_Value (COLUMNNAME_QtyToInvoiceInPriceUOM_Eff, QtyToInvoiceInPriceUOM_Eff);\n+\t\tset_Value (COLUMNNAME_QtyToInvoiceInPriceUOM, QtyToInvoiceInPriceUOM);\n \t}", "nl": "Set Zu berechn.", "old_nl": "Set Zu ber.", "peft": "184\tSet Zu berechnen.\n", "icl": "184\tSet QtyToInvoiceInPriceUOM.\n", "slm": "Set UM ber.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n public int getTryCatchBlockIndex() {\n-        return (value & 0x00FFFF00) >> 8;\n+        return (targetTypeAndInfo & 0x00FFFF00) >> 8;\n     }", "nl": "This method must only be used for type references whose sort is {@link #EXCEPTION_PARAMETER} .", "old_nl": "This method must only be used for type references whose sort is {@link #EXCEPTION_PARAMETER EXCEPTION_PARAMETER} .", "peft": "185\tThis method must only be used for type references whose sort is {@link #EXCEPTION_PARAMETER EXCEPTION_PARAMETER} or {@link #INSTANCEOF INSTANCEOF}.\n", "icl": "185\tThis method must only be used for type references whose sort is {@link #EXCEPTION_PARAMETER EXCEPTION_PARAMETER}.\n", "slm": "This method must only be used for type references whose sort is {@link #EXCEPTION_PARAMETER EXCEPTION_PARAMETER} .", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public ConfigurationClientBuilder retryPolicy(RetryPolicy retryPolicy) {\n+public ConfigurationClientBuilder retryPolicy(HttpPipelinePolicy retryPolicy) {\n         this.retryPolicy = retryPolicy;\n         return this;\n     }", "nl": "Sets the {@link HttpPipelinePolicy} that is used when each request is sent.", "old_nl": "Sets the {@link RetryPolicy} that is used when each request is sent.", "peft": "188\tSets the {@link HttpPipelinePolicy} that is used when each request is sent.\n", "icl": "188\tSets the {@link HttpPipelinePolicy} that is used when each request is sent.\n", "slm": "Sets the {@link HttpPipelinePolicy} that is used when each request is sent.", "label": ["Ref"]}
{"diff": "@@ -1,9 +1,9 @@\n\n-protected String dockerFileLocation() {\n+protected String devfileFileLocation() {\n     return new StringJoiner(\"/\")\n         .add(\"https://raw.githubusercontent.com\")\n         .add(username)\n         .add(repository)\n         .add(branch)\n-        .add(dockerfileFilename)\n+        .add(devfileFilename)\n         .toString();\n   }", "nl": "Provides the location to devfile yaml file", "old_nl": "Provides the location to dockerfile", "peft": "189\tProvides the location to devfile\n", "icl": "189\tProvides the location to devfile\n", "slm": "Provides the location to devfile", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-PackageBinding addPackage(PackageBinding element, ModuleBinding module, boolean checkForSplitSiblings) {\n+PackageBinding addPackage(PackageBinding element, ModuleBinding module) {\n \tif ((element.tagBits & TagBits.HasMissingType) == 0) clearMissingTagBit();\n \tthis.knownPackages.put(element.compoundName[element.compoundName.length - 1], element);\n \treturn element;", "nl": "For a split parent package this will include potentially enriching with siblings, in which case the enriched (split) binding will be returned.", "old_nl": "For a split parent package this will include enriching with siblings, if checkForSplitSiblings is true in which case the enriched (split) binding will be returned.", "peft": "190\tFor a split parent package this will include enriching with siblings.\n", "icl": "190\tFor a split parent package this will include enriching with siblings, if true in which case the enriched (split) binding will be returned.\n", "slm": "For a split parent package this will include enriching with siblings in which case the enriched (split) binding will be returned.", "label": ["Feat"]}
{"diff": "@@ -1,30 +1,34 @@\n\n-public ListJobsResult listJobs(ListJobsRequest listJobsRequest) {\n+@Override\n+    public ListJobsResult listJobs(ListJobsRequest listJobsRequest) {\n         ExecutionContext executionContext = createExecutionContext(listJobsRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<ListJobsRequest> request = null;\n         Response<ListJobsResult> response = null;\n-        \n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new ListJobsRequestMarshaller().marshall(super.beforeMarshalling(listJobsRequest));\n+                request = new ListJobsRequestMarshaller(protocolFactory)\n+                        .marshall(super.beforeMarshalling(listJobsRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            Unmarshaller<ListJobsResult, JsonUnmarshallerContext> unmarshaller =\n-                new ListJobsResultJsonUnmarshaller();\n-            JsonResponseHandler<ListJobsResult> responseHandler =\n-                new JsonResponseHandler<ListJobsResult>(unmarshaller);\n-\n+            HttpResponseHandler<AmazonWebServiceResponse<ListJobsResult>> responseHandler = protocolFactory\n+                    .createResponseHandler(new JsonOperationMetadata()\n+                            .withPayloadJson(true)\n+                            .withHasStreamingSuccessResponse(false),\n+                            new ListJobsResultJsonUnmarshaller());\n             response = invoke(request, responseHandler, executionContext);\n \n             return response.getAwsResponse();\n+\n         } finally {\n-            \n-            endClientExecution(awsRequestMetrics, request, response, LOGGING_AWS_REQUEST_METRIC);\n+\n+            endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "To retrieve an archive or retrieve a vault inventory from Amazon Glacier, you first initiate a job, and after the job completes, you download the data.", "old_nl": "Glacier, you first initiate a job, and after the job completes, you download the data.", "peft": "192\tGlacier, you first initiate a job, and after the job completes, you download the job output.\n", "icl": "192\tGlacier, you first initiate a job, and after the job completes, you download the data.\n", "slm": "You first initiate a job, and after the job completes, you download the data.", "label": ["Feat"]}
{"diff": "@@ -1,6 +1,6 @@\n\n-private static String deriveKey(@NonNull byte[] password, int version) throws UnsupportedEncodingException, NoSuchAlgorithmException {\n+private static String deriveKey(@NonNull byte[] fileKey, int version) throws UnsupportedEncodingException, NoSuchAlgorithmException {\n         PKCS5S2ParametersGenerator gen = new PKCS5S2ParametersGenerator(new SHA256Digest());\n-        gen.init(password, salt, ITERATION_COUNT);\n+        gen.init(fileKey, salt, ITERATION_COUNT);\n         byte[] keyBytes = ((KeyParameter) gen.generateDerivedMacParameters(version == 2 ? KEY_LENGTH * 8 : KEY_LENGTH_SHORT * 8)).getKey();\n         return toHex(keyBytes);\n     }", "nl": "Derive secret key by PBKDF2 algorithm (1000 iterations of SHA256)", "old_nl": "derive secret key by PBKDF2 algorithm (1000 iterations of SHA256)", "peft": "193\tderive secret key by PBKDF2 algorithm (1000 iterations of SHA256) from file key\n", "icl": "193\tderive secret key by PBKDF2 algorithm (1000 iterations of SHA256)\n", "slm": "derive secret key by PBKDF2 algorithm (1000 iterations of SHA1)", "label": ["Ref"]}
{"diff": "@@ -1,21 +1,7 @@\n\n @SuppressWarnings(\"unchecked\")\n-    public static JSONObject toJson(Statistics stats) {\n-        JSONObject output = new JSONObject();\n-        if (stats.getRequests() == 0) {\n-            return output;\n-        }\n-        output.put(STATISTIC_REQUEST_CATEGORIES, new JSONObject(stats.getRequestCategories()));\n-        output.put(STATISTIC_TIMING, new JSONObject(stats.getTiming()));\n-        output.put(STATISTIC_TIMING_MIN, new JSONObject(stats.getTimingMin()));\n-        output.put(STATISTIC_TIMING_MAX, new JSONObject(stats.getTimingMax()));\n-        output.put(STATISTIC_TIMING_AVG, new JSONObject(stats.getTimingAvg()));\n-        output.put(STATISTIC_MINUTES, stats.getMinutes());\n-        output.put(STATISTIC_REQUESTS, stats.getRequests());\n-        output.put(STATISTIC_REQUESTS_PER_MINUTE, stats.getRequestsPerMinute());\n-        output.put(STATISTIC_REQUESTS_PER_MINUTE_MIN, stats.getRequestsPerMinuteMin());\n-        output.put(STATISTIC_REQUESTS_PER_MINUTE_MAX, stats.getRequestsPerMinuteMax());\n-        output.put(STATISTIC_REQUESTS_PER_MINUTE_AVG, stats.getRequestsPerMinuteAvg());\n-        output.put(STATISTIC_DAY_HISTOGRAM, convertArrayToJSONArray(stats.getDayHistogram()));\n-        output.put(STATISTIC_MONTH_HISTOGRAM, convertArrayToJSONArray(stats.getMonthHistogram()));\n-        return output;\n+    public static String toJson(Statistics stats) throws JsonProcessingException {\n+        ObjectMapper mapper = new ObjectMapper();\n+        String jsonStr = mapper.writeValueAsString(stats);\n+\n+        return jsonStr;\n     }", "nl": "Convert statistics object into JSON.", "old_nl": "Convert statistics object into JSONObject.", "peft": "194\tConvert statistics object into JSON string.\n", "icl": "194\tConvert statistics object into JSONObject.\n", "slm": "Convert statistics object into String representation.", "label": ["Feat"]}
{"diff": "@@ -1,24 +1,32 @@\n\n-public void deprecateWorkflowType(DeprecateWorkflowTypeRequest deprecateWorkflowTypeRequest) {\n+@Override\n+    public void deprecateWorkflowType(\n+            DeprecateWorkflowTypeRequest deprecateWorkflowTypeRequest) {\n         ExecutionContext executionContext = createExecutionContext(deprecateWorkflowTypeRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<DeprecateWorkflowTypeRequest> request = null;\n-        \n+        Response<Void> response = null;\n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new DeprecateWorkflowTypeRequestMarshaller().marshall(super.beforeMarshalling(deprecateWorkflowTypeRequest));\n+                request = new DeprecateWorkflowTypeRequestMarshaller()\n+                        .marshall(super\n+                                .beforeMarshalling(deprecateWorkflowTypeRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            JsonResponseHandler<Void> responseHandler = new JsonResponseHandler<Void>(null);\n+            JsonResponseHandler<Void> responseHandler = SdkJsonProtocolFactory\n+                    .createResponseHandler(null, false);\n+            responseHandler.setIsPayloadJson(true);\n             invoke(request, responseHandler, executionContext);\n \n         } finally {\n-            \n-            endClientExecution(awsRequestMetrics, request, null, LOGGING_AWS_REQUEST_METRIC);\n+\n+            endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "This operation is eventually consistent.", "old_nl": "NOTE:This operation is eventually consistent.", "peft": "195\tThis operation is eventually consistent.\n", "icl": "195\tThis operation is eventually consistent.\n", "slm": "This operation is eventually consistent.", "label": ["Feat"]}
{"diff": "@@ -6,10 +6,10 @@\n\n \n         vector.setY(-Math.sin(Math.toRadians(rotY)));\n \n-        double h = Math.cos(Math.toRadians(rotY));\n+        double xz = Math.cos(Math.toRadians(rotY));\n \n-        vector.setX(-h * Math.sin(Math.toRadians(rotX)));\n-        vector.setZ(h * Math.cos(Math.toRadians(rotX)));\n+        vector.setX(-xz * Math.sin(Math.toRadians(rotX)));\n+        vector.setZ(xz * Math.cos(Math.toRadians(rotX)));\n \n         return vector;\n     }", "nl": "Gets a unit-vector pointing in the direction that this Location is facing.", "old_nl": "Gets a Vector pointing in the direction that this Location is facing", "peft": "196\tGets a Vector pointing in the direction that this Location is looking.\n", "icl": "196\tGets a Vector pointing in the direction that this Location is facing\n", "slm": "Gets a Vector pointing in the direction that this Location is facing.", "label": ["Ref"]}
{"diff": "@@ -1,6 +1,5 @@\n\n-@Override\n-    @Nullable\n-    public ASTPrimaryExpression getLhsExpression() {\n-        ASTPrimaryExpression lhs = ASTQualifiableExpression.super.getLhsExpression();\n-        return lhs instanceof ASTAmbiguousName ? null : lhs;\n+@NonNull\n+    @Override\n+    public ASTPrimaryExpression getLhs() {\n+        return (ASTPrimaryExpression) jjtGetChild(0);\n     }", "nl": "Returns the node to the left of the \"::\".", "old_nl": "Returns the expression to the left of the \"::\" if it exists and is unambiguous.", "peft": "268\tReturns the expression to the left of the \"::\" if it exists.\n", "icl": "268\tReturns the expression to the left of the \"::\" if it exists and is unambiguous.\n", "slm": "Returns the expression to the left of the \"::\" if it exists.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public static long initializeNextSession(long id) {\n+public static long initializeNextSessionId(long id) {\n         long nextSid;\n         nextSid = (Time.currentElapsedTime() << 24) >>> 8;\n         nextSid = nextSid | (id << 56);", "nl": "High order 1 byte is serverId, next 5 bytes are from timestamp, and low order 2 bytes are 0s.", "old_nl": "High order byte is serverId, next 5 5 bytes are from timestamp, and low order 2 bytes are 0s.", "peft": "342\tHigh order byte is serverId, next 5 5 bytes are from timestamp, and low order 3 bytes are 0s.\n", "icl": "342\tReturns a session id that is guaranteed to be unique for the current device.\n", "slm": "High order byte is serverId, next 5 5 bytes are from timestamp, and low order 2 bytes are 0s.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-@ApiModelProperty(value = \"Phone Number\")\n+@ApiModelProperty(value = \"Phone number\")\n   public String getPhone() {\n     return phone;\n   }", "nl": "Phone number", "old_nl": "Phone Number", "peft": "407\tPhone number\n", "icl": "407\tPhone number\n", "slm": "Phone number", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-protected double predict(SparseVector x) throws LibrecException {\n+protected double predict(int userId, int itemId, SparseVector x) throws LibrecException {\n         double res = 0;\n         // global bias\n         res += w0;", "nl": "Predict the rating given a sparse appender vector.", "old_nl": "predict * predict the rating given a sparse appender vector", "peft": "409\tPredict * predict the rating given a sparse appender vector.\n", "icl": "409\tpredict the rating given a sparse appender vector\n", "slm": "predict * predict the rating given a sparse appender vector and store the result in sparse", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n public Builder setNoun(String noun) {\n-\t\t\t\tthis.name = noun;\n+\t\t\t\tthis.noun = noun;\n \t\t\t\treturn this;\n \t\t\t}", "nl": "Set the noun.", "old_nl": "Set name of the noun.", "peft": "458\tSet noun.\n", "icl": "458\tSet name of the noun.\n", "slm": "Set noun.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public Builder withSerializer(Serializer serializer) {\n-      this.serializer = checkNotNull(serializer, \"serializer cannot be null\");\n+public Builder withNamespace(Namespace namespace) {\n+      this.namespace = checkNotNull(namespace, \"namespace cannot be null\");\n       return this;\n     }", "nl": "Sets the journal namespace, returning the builder for method chaining.", "old_nl": "Sets the journal serializer, returning the builder for method chaining.", "peft": "461\tSets the namespace, returning the builder for method chaining.\n", "icl": "461\tSets the journal namespace, returning the builder for method chaining.\n", "slm": "Sets the journal namespace, returning the builder for method chaining.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n @SuppressWarnings({\"unchecked\", \"rawtypes\"})\n-    protected final <T extends TruffleLanguage> Supplier<T> getLanguageSupplier(Class<T> languageClass) {\n+    protected final <T extends TruffleLanguage> LanguageReference<T> lookupLanguageReference(Class<T> languageClass) {\n         if (languageClass == null) {\n             CompilerDirectives.transferToInterpreter();\n             throw new NullPointerException();\n@@ -7,17 +7,17 @@\n\n         ExecutableNode executableNode = getExecutableNode();\n         if (executableNode != null) {\n             if (executableNode.language != null && executableNode.language.getClass() == languageClass) {\n-                return Node.ACCESSOR.engineSupport().getDirectLanguageSupplier(executableNode.sourceVM,\n+                return Node.ACCESSOR.engineSupport().getDirectLanguageReference(executableNode.sourceVM,\n                                 executableNode.language, languageClass);\n             } else {\n-                SupplierCache cache = executableNode.lookupSupplierCache(languageClass);\n+                ReferenceCache cache = executableNode.lookupReferenceCache(languageClass);\n                 if (cache != null) {\n-                    return (Supplier<T>) cache.languageReference;\n+                    return (LanguageReference<T>) cache.languageReference;\n                 } else {\n-                    return Node.ACCESSOR.engineSupport().lookupLanguageSupplier(executableNode.sourceVM,\n+                    return Node.ACCESSOR.engineSupport().lookupLanguageReference(executableNode.sourceVM,\n                                     executableNode.language, languageClass);\n                 }\n             }\n         }\n-        return lookupUncachedLanguageSupplier(languageClass);\n+        return lookupUncachedLanguageReference(languageClass);\n     }", "nl": "Returns a reference that returns the current language instance.", "old_nl": "Returns a supplier that returns the current language instance.", "peft": "474\tReturns a reference that returns the current language instance.\n", "icl": "474\tReturns a reference that returns the current language instance.\n", "slm": "Returns a reference that returns the current language instance.", "label": ["Ref"]}
{"diff": "@@ -1,2 +1 @@\n\n-public static void taskStartWithMinPeriod(\n-      PerfTag tag, long minPeriodNanos, @CompileTimeConstant String taskName) {}\n+public static void taskEnd(PerfTag tag, @CompileTimeConstant String taskName) {}", "nl": "End a Task with a Tag to identify it; a task represents some work that spans some time, and you are interested in both its start time and end time.", "old_nl": "Start a Task with a Tag to identify it and with a time threshold; a task represents some work that spans some time, and you are interested in both its start time and end time.", "peft": "479\tEnd a Task with a Tag to identify it; a task represents some work that spans some time, and you are interested in both its start time and end time.\n", "icl": "479\tThe method is renamed to taskEnd.\n", "slm": "End a Task with a Tag to identify it and with a time threshold; a task represents some work that spans some time, and you are interested in both its end time.", "label": ["Feat"]}
{"diff": "@@ -5,15 +5,15 @@\n\n         if (getArn() != null)\n             sb.append(\"Arn: \").append(getArn()).append(\",\");\n         if (getUserName() != null)\n-            sb.append(\"UserName: \").append(getUserName()).append(\",\");\n+            sb.append(\"UserName: \").append(\"***Sensitive Data Redacted***\").append(\",\");\n         if (getEnabled() != null)\n             sb.append(\"Enabled: \").append(getEnabled()).append(\",\");\n         if (getStatus() != null)\n             sb.append(\"Status: \").append(getStatus()).append(\",\");\n         if (getFirstName() != null)\n-            sb.append(\"FirstName: \").append(getFirstName()).append(\",\");\n+            sb.append(\"FirstName: \").append(\"***Sensitive Data Redacted***\").append(\",\");\n         if (getLastName() != null)\n-            sb.append(\"LastName: \").append(getLastName()).append(\",\");\n+            sb.append(\"LastName: \").append(\"***Sensitive Data Redacted***\").append(\",\");\n         if (getCreatedTime() != null)\n             sb.append(\"CreatedTime: \").append(getCreatedTime()).append(\",\");\n         if (getAuthenticationType() != null)", "nl": "Returns a string representation of this object.", "old_nl": "Returns a string representation of this object; useful for testing and debugging.", "peft": "504\tReturns a string representation of this object; useful for testing and debugging.\n", "icl": "504\tReturns a string representation of this object; useful for testing and debugging.\n", "slm": "This object is useful for testing and debugging.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n public static KeySpec publicKeySpec(String pem) throws IOException {\n-      return publicKeySpec(ByteStreams.newInputStreamSupplier(\n+      return publicKeySpec(ByteSource.wrap(\n          pem.getBytes(Charsets.UTF_8)));\n    }", "nl": "Executes {@link Pems#publicKeySpec(ByteSource)} on the string which contains an encoded public key in PEM format.", "old_nl": "Executes {@link Pems#publicKeySpec(InputSupplier)} on the string which contains an encoded public key in PEM format.", "peft": "579\tExecutes {@link Pems#publicKeySpec(ByteSource)} on the string which contains an encoded public key in PEM format.\n", "icl": "579\tExecutes {@link Pems#publicKeySpec(ByteSource)} on the string which contains an encoded public key in PEM format.\n", "slm": "Executes {@link Pems#publicKeySpec)} on the string which contains an encoded public key in JSON format.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public void setCrossorigin(Cors crossorigin) {\n-\t\tthis.crossorigin = crossorigin;\n+public void setCrossOrigin(Cors crossOrigin) {\n+\t\tthis.crossOrigin = crossOrigin;\n \t}", "nl": "USE_CREDENTIALS: Cross-origin CORS requests for the element will have the credentials flag set.", "old_nl": "use_credentials: Cross-origin CORS requests for the element will have the credentials flag set.", "peft": "599\tuseCredentials: Cross-origin CORS requests for the element will have the credentials flag set.\n", "icl": "599\tCross-origin CORS requests for the element will have the credentials flag set.\n", "slm": "Use the credentials: Cross-origin CORS requests for the element will have the credentials flag set.", "label": ["Ref"]}
{"diff": "@@ -1,5 +1,5 @@\n\n @Override\n-\tpublic java.lang.String getUPC () \n+\tpublic java.lang.String getUPC_TU () \n \t{\n-\t\treturn (java.lang.String)get_Value(COLUMNNAME_UPC);\n+\t\treturn (java.lang.String)get_Value(COLUMNNAME_UPC_TU);\n \t}", "nl": "Get TU-UPC.", "old_nl": "Get UPC/EAN.", "peft": "670\tGet UPC/EAN (TU).\n", "icl": "670\tGet UPC/EAN.\n", "slm": "Get UPC/EAN-TU.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public CorsFilter setAllowCredentials(boolean allowCredentials) {\n-        this.allowCredentials = allowCredentials;\n+public CorsFilter setAllowedCredentials(boolean allowedCredentials) {\n+        this.allowedCredentials = allowedCredentials;\n         return this;\n     }", "nl": "Setter for {@link #allowedCredentials}", "old_nl": "Setter for {@link #allowCredentials}", "peft": "741\tSetter for {@link #allowedCredentials}\n", "icl": "741\tSetter for {@link #allowedCredentials}\n", "slm": "Setter for {@link #allowedCredentials}", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,6 @@\n\n public void updateAllocationIdsFromMaster(\n-            final long applyingClusterStateVersion, final Set<String> activeAllocationIds, final Set<String> initializingAllocationIds) {\n-        globalCheckpointTracker.updateAllocationIdsFromMaster(applyingClusterStateVersion, activeAllocationIds, initializingAllocationIds);\n+            final long applyingClusterStateVersion, final Set<String> inSyncAllocationIds, final Set<String> initializingAllocationIds,\n+            final Set<String> pre60AllocationIds) {\n+        globalCheckpointTracker.updateFromMaster(applyingClusterStateVersion, inSyncAllocationIds, initializingAllocationIds,\n+            pre60AllocationIds);\n     }", "nl": "See {@link GlobalCheckpointTracker#updateFromMaster(long, Set, Set, Set)} for details.", "old_nl": "See {@link GlobalCheckpointTracker#updateAllocationIdsFromMaster(long, Set, Set)} for details.", "peft": "771\tSee {@link GlobalCheckpointTracker#updateFromMaster(long, Set, Set, Set)} for details.\n", "icl": "771\tSee {@link GlobalCheckpointTracker#updateFromMaster(long, Set, Set, Set)} for details.\n", "slm": "See {@link GlobalCheckpointTracker#updateFromMaster(long, Set, Set)} for details.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-static Builder builder(EventContext context, BaseEvent event) {\n+static Builder builder(EventContext context, CoreEvent event) {\n     return new DefaultEventBuilder((BaseEventContext) context, (InternalEvent) event);\n   }", "nl": "Create new {@link Builder} based on an existing {@link CoreEvent} instance and and {@link EventContext}.", "old_nl": "Create new {@link Builder} based on an existing {@link BaseEvent} instance and and {@link EventContext}.", "peft": "786\tCreate new {@link Builder} based on an existing {@link CoreEvent} instance and and {@link EventContext}.\n", "icl": "786\tCreate new {@link Builder} based on an existing {@link CoreEvent} instance and and {@link EventContext}.\n", "slm": "Create new {@link Builder} based on an existing {@link CoreEvent} instance and and {@link EventContext}.", "label": ["Ref"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public Builder issuerCurveGroups(Map<StandardId, LegalEntityGroup> issuerCurveGroups) {\n+public Builder issuerCurveGroups(Map<LegalEntityId, LegalEntityGroup> issuerCurveGroups) {\n       JodaBeanUtils.notNull(issuerCurveGroups, \"issuerCurveGroups\");\n       this.issuerCurveGroups = issuerCurveGroups;\n       return this;", "nl": "Sets the groups used to find an issuer curve by legal entity.", "old_nl": "Sets the groups used to find an issuer curve.", "peft": "822\tSets the groups used to find an issuer curve for a specific legal entity.\n", "icl": "822\tSets the groups used to find an issuer curve.\n", "slm": "Sets the groups used to find an issuer curve.", "label": ["Ref"]}
{"diff": "@@ -14,7 +14,7 @@\n\n         }\n       }\n     }\n-    if (version == null || version.equalsIgnoreCase(\"latest\") || version.equalsIgnoreCase(\"$LATEST\")) {\n+    if (version == null || version.equalsIgnoreCase(PackageUtils.LATEST) || version.equalsIgnoreCase(PackagePluginHolder.LATEST)) {\n       return latest;\n     } else return null;\n   }", "nl": "If version is null, PackageUtils.LATEST or PackagePluginHolder.LATEST, then it returns the highest version available in the system for the package.", "old_nl": "If version is null, \"latest\" or \"$LATEST\", then it returns the highest version available in the system for the package.", "peft": "826\tIf version is null, \"latest\" or \"latest\", then it returns the highest version available in the system for the package.\n", "icl": "826\tIf version is null, \"latest\" or \"$LATEST\", then it returns the highest version available in the system for the package.\n", "slm": "If version is null, PackageUtils.LATEST or PackagePluginHolder.LATEST, then it returns the highest version available in the system for the package.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public Flux<FeedResponse<CosmosUserProperties>> readAllUsers() {\n+public CosmosContinuablePagedFlux<CosmosUserProperties> readAllUsers() {\n         return readAllUsers(new FeedOptions());\n     }", "nl": "In case of failure the {@link CosmosContinuablePagedFlux} will error.", "old_nl": "In case of failure the {@link Flux} will error.", "peft": "853\tIn case of failure the {@link CosmosContinuablePagedFlux} will error.\n", "icl": "853\tIn case of failure the {@link CosmosContinuablePagedFlux} will error.\n", "slm": "In case of failure the CosmosContinuablePagedFlux will error.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-boolean useAlternameFilter() {\n+boolean useAlternateFilter() {\n             return false;\n         }", "nl": "Indicates whether this filter needs to use the secondary, non-SQL method applyAlternateFilter().", "old_nl": "Indicates whether this filter needs to use the secondary, non-SQL method getMatchingFiles().", "peft": "861\tIndicates whether this filter needs to use the secondary, non-SQL method getMatchingFiles().\n", "icl": "861\tIndicates whether this filter needs to use the secondary, non-SQL method getMatchingFiles().\n", "slm": "Indicates whether this filter needs to use the secondary.", "label": ["Ref"]}
{"diff": "@@ -2,7 +2,10 @@\n\n     {\n         if (freeGlyphs != null) {\n             for (Iterator<BasicGlyph> it = freeGlyphs.iterator(); it.hasNext();) {\n-                if (it.next().hasGroup(group)) {\n+                final Glyph glyph = it.next();\n+                final EnumSet<Group> glyphGroups = glyph.getGroups();\n+\n+                if ((glyphGroups.size() == 1) && glyphGroups.contains(group)) {\n                     it.remove();\n                 }\n             }", "nl": "Remove all free glyphs that are only assigned the provided group.", "old_nl": "Remove all free glyphs that are assigned the provided group.", "peft": "873\tRemove all free glyphs that are assigned the provided group, and that are not assigned to any other group.\n", "icl": "873\tRemove all free glyphs that are assigned the provided group.\n", "slm": "Remove all free glyphs that are assigned to the provided group.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,15 @@\n\n @Test\n-    public void canGetGenieHostInfo() throws UnknownHostException {\n-        final GenieHostInfo genieHostInfo = this.genieAwsApiAutoConfiguration.genieHostInfo();\n-        Assert.assertThat(genieHostInfo.getHostname(), Matchers.notNullValue());\n+    public void canGetGenieHostInfo() {\n+        // Check to see if EC2 is available\n+        if (AwsCloudEnvironmentCheckUtils.isRunningOnCloudEnvironment()) {\n+            final GenieHostInfo genieHostInfo = this.genieAwsApiAutoConfiguration.genieHostInfo();\n+            Assert.assertThat(genieHostInfo.getHostname(), Matchers.notNullValue());\n+        } else {\n+            try {\n+                this.genieAwsApiAutoConfiguration.genieHostInfo();\n+                Assert.fail(\"This test wasn't run on a box with AWS credentials and yet got an unexpected success\");\n+            } catch (final IllegalStateException ise) {\n+                // Expected\n+            }\n+        }\n     }", "nl": "Make sure we can get the {@link GenieHostInfo}.", "old_nl": "Make sure we can get the {@link GenieHostInfo} instance even if EC2 metadata fails.", "peft": "919\tMake sure we can get the {@link GenieHostInfo} instance even if EC2 metadata fails or EC2 is not available.\n", "icl": "919\tMake sure we can get the {@link GenieHostInfo} instance even if EC2 metadata fails.\n", "slm": "Make sure we can get the {@link GenieHostInfo} instance even if EC2 is enabled.", "label": ["Code&Doc"]}
{"diff": "@@ -1,9 +1,9 @@\n\n-public void deleteQueryAtPos(int position) {\n-        if (mShuffledQueries != null) {\n-            mShuffledQueries.remove((mShuffled ? mShuffledQueries : mQueries).get(position));\n+public void deleteEntryAtPos(int position) {\n+        if (mShuffledEntries != null) {\n+            mShuffledEntries.remove((mShuffled ? mShuffledEntries : mEntries).get(position));\n         }\n-        (mShuffled ? mShuffledQueries : mQueries).remove(position);\n-        if (mCurrentQueryIndex > (mShuffled ? mShuffledQueries : mQueries).size()) {\n+        (mShuffled ? mShuffledEntries : mEntries).remove(position);\n+        if (mCurrentQueryIndex > (mShuffled ? mShuffledEntries : mEntries).size()) {\n             mCurrentQueryIndex--;\n         }\n     }", "nl": "Remove the {@link PlaylistEntry} at the given position from this playlist", "old_nl": "Remove the {@link Query} at the given position from this playlist", "peft": "961\tRemove the {@link Entry} at the given position from this playlist\n", "icl": "961\tRemove the {@link Entry} at the given position from this playlist\n", "slm": "Remove the {@link Entry} at the given position from this playlist", "label": ["Feat"]}
{"diff": "@@ -1,56 +1,38 @@\n\n protected void runPigJob(String[] args, String pigLog, boolean resetSecurityManager, boolean retrieveStats) throws Exception {\n-        // running as from the command line\n-        boolean pigRunnerExists = true;\n-\n-        try {\n-            Class.forName(\"org.apache.pig.PigRunner\");\n-        }\n-        catch (ClassNotFoundException ex) {\n-            pigRunnerExists = false;\n+        PigStats stats = PigRunner.run(args, null);\n+        String jobIds = getHadoopJobIds(stats);\n+        if (jobIds != null && !jobIds.isEmpty()) {\n+            System.out.println(\"Hadoop Job IDs executed by Pig: \" + jobIds);\n+            File f = new File(System.getProperty(EXTERNAL_CHILD_IDS));\n+            writeExternalData(jobIds, f);\n         }\n \n-        if (pigRunnerExists) {\n-            System.out.println(\"Run pig script using PigRunner.run() for Pig version 0.8+\");\n-            PigStats stats = PigRunner.run(args, null);\n-            String jobIds = getHadoopJobIds(stats);\n-            if (jobIds != null && !jobIds.isEmpty()) {\n-                System.out.println(\"Hadoop Job IDs executed by Pig: \" + jobIds);\n-                File f = new File(System.getProperty(EXTERNAL_CHILD_IDS));\n-                writeExternalData(jobIds, f);\n+        if (!stats.isSuccessful()) {\n+            if (pigLog != null) {\n+                handleError(pigLog);\n             }\n-            // isSuccessful is the API from 0.9 supported by both PigStats and\n-            // EmbeddedPigStats\n-            if (!stats.isSuccessful()) {\n-                if (pigLog != null) {\n-                    handleError(pigLog);\n+            throw new LauncherMainException(PigRunner.ReturnCode.FAILURE);\n+        }\n+        else {\n+            // If pig command is ran with just the \"version\" option, then\n+            // return\n+            if (resetSecurityManager) {\n+                return;\n+            }\n+            // Retrieve stats only if user has specified in workflow\n+            // configuration\n+            if (retrieveStats) {\n+                ActionStats pigStats;\n+                String JSONString;\n+                try {\n+                    pigStats = new OoziePigStats(stats);\n+                    JSONString = pigStats.toJSON();\n+                } catch (UnsupportedOperationException uoe) {\n+                    throw new UnsupportedOperationException(\n+                            \"Pig stats are not supported for this type of operation\", uoe);\n                 }\n-                throw new LauncherMainException(PigRunner.ReturnCode.FAILURE);\n-            }\n-            else {\n-                // If pig command is ran with just the \"version\" option, then\n-                // return\n-                if (resetSecurityManager) {\n-                    return;\n-                }\n-                // Retrieve stats only if user has specified in workflow\n-                // configuration\n-                if (retrieveStats) {\n-                    ActionStats pigStats;\n-                    String JSONString;\n-                    try {\n-                        pigStats = new OoziePigStats(stats);\n-                        JSONString = pigStats.toJSON();\n-                    } catch (UnsupportedOperationException uoe) {\n-                        throw new UnsupportedOperationException(\n-                                \"Pig stats are not supported for this type of operation\", uoe);\n-                    }\n-                    File f = new File(System.getProperty(EXTERNAL_ACTION_STATS));\n-                    writeExternalData(JSONString, f);\n-                }\n+                File f = new File(System.getProperty(EXTERNAL_ACTION_STATS));\n+                writeExternalData(JSONString, f);\n             }\n         }\n-        else {\n-                System.out.println(\"Run pig script using Main.main() for Pig version before 0.8\");\n-                Main.main(args);\n-        }\n     }", "nl": "Runs the pig script using PigRunner.", "old_nl": "Runs the pig script using PigRunner API if version VERSION or above.", "peft": "992\tRuns the pig script using PigRunner API if version VERSION or above.\n", "icl": "992\t# Answer:\n", "slm": "Runs the pig script using PigRunner API if version VERSION or above.", "label": ["Code&Doc"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-public boolean isSimpleSelectorFactory(int index)\n+public boolean isSimpleSelectorFactory()\n         {\n             return false;\n         }", "nl": "Checks if this factory creates s that simply return a column value.", "old_nl": "Checks if this factory creates s that simply return the specified column.", "peft": "1102\tChecks if this factory creates s that simply return the specified column.\n", "icl": "1102\tChecks if this factory creates s that simply return the specified column.\n", "slm": "Checks if this factory creates a selector factory that returns the specified column.", "label": ["Feat"]}
{"diff": "@@ -1,37 +1,9 @@\n\n-@Test(dataProvider = \"keepAliveProvider\")\n+@Test(dataProvider = \"knownLocalsProvider\")\n     public static void checkLocalValues(StackFrame... frames) {\n-        if (debug) {\n-            System.out.println(\"Running checkLocalValues\");\n-            dumpStackWithLocals(frames);\n-        }\n-        Arrays.stream(frames).filter(f -> f.getMethodName()\n-                                           .equals(\"testLocalsKeepAlive\"))\n-                                           .forEach(\n-            f -> {\n-                Object[] locals = invokeGetLocals(f);\n-                for (int i = 0; i < locals.length; i++) {\n-                    // Value\n-                    String expected = Tester.LOCAL_VALUES[i];\n-                    Object observed = locals[i];\n-                    if (expected != null /* skip nulls in golden values */ &&\n-                            !expected.equals(observed.toString())) {\n-                        System.err.println(\"Local value mismatch:\");\n-                        if (!debug) { dumpStackWithLocals(frames); }\n-                        throw new RuntimeException(\"local \" + i + \" value is \" +\n-                                observed + \", expected \" + expected);\n-                    }\n-\n-                    // Type\n-                    expected = Tester.LOCAL_TYPES[i];\n-                    observed = type(locals[i]);\n-                    if (expected != null /* skip nulls in golden values */ &&\n-                            !expected.equals(observed)) {\n-                        System.err.println(\"Local type mismatch:\");\n-                        if (!debug) { dumpStackWithLocals(frames); }\n-                        throw new RuntimeException(\"local \" + i + \" type is \" +\n-                                observed + \", expected \" + expected);\n-                    }\n-                }\n-            }\n-        );\n+        dumpFramesIfDebug(frames);\n+        try {\n+            Stream.of(frames)\n+                  .filter(f -> KnownLocalsTester.TEST_METHODS.contains(f.getMethodName()))\n+                  .forEach(LocalsAndOperands::checkFrameLocals);\n+        } catch (Exception e) { dumpFramesIfNotDebug(frames); throw e; }\n     }", "nl": "Check for expected local values in the LiveStackFrame", "old_nl": "Check for expected local values and types in the LiveStackFrame", "peft": "1106\tCheck for expected local values and types in the StackFrame\n", "icl": "1106\tIt checks the local values and types in the LiveStackFrame.\n", "slm": "Check for knownLocals in the LiveStackFrame", "label": ["Code&Doc"]}
{"diff": "@@ -1,3 +1,6 @@\n\n-static long prepend(long indexCoder, byte[] buf, byte value) {\n-        return prepend(indexCoder, buf, (int)value);\n+static long prepend(long indexCoder, byte[] buf, String prefix, boolean value, String suffix) {\n+        if (suffix != null) indexCoder = prepend(indexCoder, buf, suffix);\n+        indexCoder = prepend(indexCoder, buf, value);\n+        if (prefix != null) indexCoder = prepend(indexCoder, buf, prefix);\n+        return indexCoder;\n     }", "nl": "Prepends constant and the stringly representation of value into buffer, given the coder and final index.", "old_nl": "Prepends the stringly representation of byte value into buffer, given the coder and final index.", "peft": "1360\tPrepends the stringly representation of boolean value into buffer, given the coder and final index.\n", "icl": "1360\tPrepends the stringly representation of byte value into buffer, given the coder and final index.\n", "slm": "Prepends the stringly representation of byte value into buffer, given the coder and final index, including the prefix.", "label": ["Feat"]}
{"diff": "@@ -1,8 +1,8 @@\n\n-private static DocumentBuilder acquireDOMBuilder()\n+private static PoolDOMBuilder acquireDOMBuilder()\n             throws TikaException {\n         int waiting = 0;\n         while (true) {\n-            DocumentBuilder builder = null;\n+            PoolDOMBuilder builder = null;\n             try {\n                 DOM_READ_WRITE_LOCK.readLock().lock();\n                 builder = DOM_BUILDERS.poll(100, TimeUnit.MILLISECONDS);", "nl": "Make sure to {@link #releaseDOMBuilder(PoolDOMBuilder)} in a  block every time you call this.", "old_nl": "Make sure to {@link #releaseParser(SAXParser)} in a  block every time you call this.", "peft": "1394\tMake sure to {@link #releaseParser(PoolParser)} in a  block every time you call this.\n", "icl": "1394\tMake sure to {@link #releaseParser(SAXParser)} in a  block every time you call this.\n", "slm": "Make sure to release the SAXParser in a  block every time you call this.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n public final boolean isRegistered() {\n-        return !canonicalName.startsWith(\"x-\") && !canonicalName.startsWith(\"X-\");\n+        return !name.startsWith(\"X-\") && !name.startsWith(\"x-\");\n     }", "nl": "Tells whether or not this charset is registered in the IANA Charset", "old_nl": "Indicates whether this charset is known to be registered in the IANA", "peft": "1423\tIndicates whether this charset is known to be registered in the IANA Charset Registry.\n", "icl": "1423\tIndicates whether this charset is known to be registered in the IANA\n", "slm": "Indicates whether this charset is known to be registered in the IANA database.", "label": ["Ref"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public boolean hasSequenceWithOffsetNeg1(String sequence) {\n+public boolean hasSequenceWithOffset(String sequence, int offset) {\n         return false;\n     }", "nl": "Returns whether or not this qualifier has sequence with the specified offset.", "old_nl": "Returns whether or not this qualifier has sequence with offset of -1.", "peft": "1492\tReturns whether or not this qualifier has sequence with offset.\n", "icl": "1492\tReturns whether or not this qualifier has sequence with offset of the given offset.\n", "slm": "Returns whether or not this qualifier has sequence with offset of {@code offset}.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,5 @@\n\n @ExperimentalApi(\"https://github.com/grpc/grpc-java/issues/6279\")\n-      public Builder setBlockingExecutor(Executor executor) {\n+      public Builder setOffloadExecutor(Executor executor) {\n         this.executor = executor;\n         return this;\n       }", "nl": "See {@link Args#getOffloadExecutor}.", "old_nl": "See {@link Args#getBlockingExecutor}.", "peft": "1504\tSee {@link Args#getOffloadExecutor}.\n", "icl": "1504\tSee {@link Args#getOffloadExecutor}.\n", "slm": "See {@link Args#getOffloadExecutor}.", "label": ["Ref"]}
{"diff": "@@ -1,30 +1,34 @@\n\n-public DescribeJobResult describeJob(DescribeJobRequest describeJobRequest) {\n+@Override\n+    public DescribeJobResult describeJob(DescribeJobRequest describeJobRequest) {\n         ExecutionContext executionContext = createExecutionContext(describeJobRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<DescribeJobRequest> request = null;\n         Response<DescribeJobResult> response = null;\n-        \n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new DescribeJobRequestMarshaller().marshall(super.beforeMarshalling(describeJobRequest));\n+                request = new DescribeJobRequestMarshaller(protocolFactory)\n+                        .marshall(super.beforeMarshalling(describeJobRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            Unmarshaller<DescribeJobResult, JsonUnmarshallerContext> unmarshaller =\n-                new DescribeJobResultJsonUnmarshaller();\n-            JsonResponseHandler<DescribeJobResult> responseHandler =\n-                new JsonResponseHandler<DescribeJobResult>(unmarshaller);\n-\n+            HttpResponseHandler<AmazonWebServiceResponse<DescribeJobResult>> responseHandler = protocolFactory\n+                    .createResponseHandler(new JsonOperationMetadata()\n+                            .withPayloadJson(true)\n+                            .withHasStreamingSuccessResponse(false),\n+                            new DescribeJobResultJsonUnmarshaller());\n             response = invoke(request, responseHandler, executionContext);\n \n             return response.getAwsResponse();\n+\n         } finally {\n-            \n-            endClientExecution(awsRequestMetrics, request, response, LOGGING_AWS_REQUEST_METRIC);\n+\n+            endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "For more information, see Access Control Using AWS Identity and Access Management (IAM).", "old_nl": "Access Control Using AWS Identity and Access Management (IAM)  .", "peft": "1511\tAccess Control Using AWS Identity and Access Management (IAM) .\n", "icl": "1511\tAccess Control Using AWS Identity and Access Management (IAM)\n", "slm": "Access Control Using AWS Identity and Access Management (IAM)", "label": ["Code&Doc"]}
{"diff": "@@ -1,8 +1,7 @@\n\n-private static void close(@Nullable Closeable closeable) {\n-\t\tif (closeable == null) return;\n+private static void close(Closeable closeable) {\n \t\ttry {\n \t\t\tcloseable.close();\n-\t\t} catch (IOException e) {\n-\t\t\t// do nothing\n+\t\t} catch (IOException ex) {\n+\t\t\t// ignore\n \t\t}\n \t}", "nl": "Attempt to close the supplied {@link Closeable}, silently swallowing any exceptions.", "old_nl": "Close the {@link Closeable} as a null-safety.", "peft": "1536\tClose the {@link Closeable}.\n", "icl": "1536\tClose the {@link Closeable} as a null-safety.\n", "slm": "Close the {@link Closeable} as a non-safety.", "label": ["Code&Doc"]}
{"diff": "@@ -1,30 +1,36 @@\n\n-public ListMultipartUploadsResult listMultipartUploads(ListMultipartUploadsRequest listMultipartUploadsRequest) {\n+@Override\n+    public ListMultipartUploadsResult listMultipartUploads(\n+            ListMultipartUploadsRequest listMultipartUploadsRequest) {\n         ExecutionContext executionContext = createExecutionContext(listMultipartUploadsRequest);\n-        AWSRequestMetrics awsRequestMetrics = executionContext.getAwsRequestMetrics();\n+        AWSRequestMetrics awsRequestMetrics = executionContext\n+                .getAwsRequestMetrics();\n         awsRequestMetrics.startEvent(Field.ClientExecuteTime);\n         Request<ListMultipartUploadsRequest> request = null;\n         Response<ListMultipartUploadsResult> response = null;\n-        \n+\n         try {\n             awsRequestMetrics.startEvent(Field.RequestMarshallTime);\n             try {\n-                request = new ListMultipartUploadsRequestMarshaller().marshall(super.beforeMarshalling(listMultipartUploadsRequest));\n+                request = new ListMultipartUploadsRequestMarshaller(\n+                        protocolFactory).marshall(super\n+                        .beforeMarshalling(listMultipartUploadsRequest));\n                 // Binds the request metrics to the current request.\n                 request.setAWSRequestMetrics(awsRequestMetrics);\n             } finally {\n                 awsRequestMetrics.endEvent(Field.RequestMarshallTime);\n             }\n \n-            Unmarshaller<ListMultipartUploadsResult, JsonUnmarshallerContext> unmarshaller =\n-                new ListMultipartUploadsResultJsonUnmarshaller();\n-            JsonResponseHandler<ListMultipartUploadsResult> responseHandler =\n-                new JsonResponseHandler<ListMultipartUploadsResult>(unmarshaller);\n-\n+            HttpResponseHandler<AmazonWebServiceResponse<ListMultipartUploadsResult>> responseHandler = protocolFactory\n+                    .createResponseHandler(new JsonOperationMetadata()\n+                            .withPayloadJson(true)\n+                            .withHasStreamingSuccessResponse(false),\n+                            new ListMultipartUploadsResultJsonUnmarshaller());\n             response = invoke(request, responseHandler, executionContext);\n \n             return response.getAwsResponse();\n+\n         } finally {\n-            \n-            endClientExecution(awsRequestMetrics, request, response, LOGGING_AWS_REQUEST_METRIC);\n+\n+            endClientExecution(awsRequestMetrics, request, response);\n         }\n     }", "nl": "For more information, see Access Control Using AWS Identity and Access Management (IAM).", "old_nl": "Access Control Using AWS Identity and Access Management (IAM)  .", "peft": "1561\tAccess Control Using AWS Identity and Access Management (IAM) .\n", "icl": "1561\tAccess Control Using AWS Identity and Access Management (IAM)\n", "slm": "Access Control Using AWS Identity and Access Management (IAM) role.", "label": ["Feat"]}
{"diff": "@@ -1,3 +1,3 @@\n\n-public static Instant getInstantMillisOffsetFromNow(long offsetInMillis) {\n-        return Instant.now().plus(Duration.ofMillis(offsetInMillis));\n+public static Instant getInstantHoursOffsetFromNow(long offsetInHours) {\n+        return Instant.now().plus(Duration.ofHours(offsetInHours));\n     }", "nl": "Returns an java.time.Instant object that is offset by a number of hours from now.", "old_nl": "Returns an java.time.Instant object that is offset by a number of milliseconds from now.", "peft": "1578\tReturns an java.time.Instant object that is offset by a number of hours from now.\n", "icl": "1578\tReturns an java.time.Instant object that is offset by a number of hours from now.\n", "slm": "Returns an java.time.Instant object that is offset by a number of hours from now.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,3 @@\n\n-@SuppressWarnings(\"deprecation\")\n-    static TranslatableText of(Translation translation, Object... args) {\n-        return Sponge.getRegistry().createBuilder(TranslatableText.Builder.class).translation(translation, checkNotNull(args, \"args\")).build();\n+static TranslatableText of(Nameable.Translatable nameable) {\n+        return builder(nameable).build();\n     }", "nl": "Creates a new unformatted {@link TranslatableText} with the given {@link Translation}.", "old_nl": "Creates a new unformatted {@link TranslatableText} with the given {@link Translation} and arguments.", "peft": "1608\tCreates a new unformatted {@link TranslatableText} with the given {@link Nameable.Translatable}.\n", "icl": "1608\tCreates a new unformatted {@link TranslatableText} with the given {@link Nameable.Translatable} and arguments.\n", "slm": "Creates a new unformatted {@link TranslatableText} with the given {@link Nameable.Translatable} instances.", "label": ["Feat"]}
{"diff": "@@ -1,42 +1,44 @@\n\n-static List<String> getJavaTypes(SoyType soyType) {\n+static final Optional<JavaType> getJavaType(SoyType soyType) {\n     switch (soyType.getKind()) {\n       case BOOL:\n-        return ImmutableList.of(\"boolean\");\n+        return Optional.of(SimpleJavaType.BOOLEAN);\n       case INT:\n-        return ImmutableList.of(\"long\");\n+        return Optional.of(SimpleJavaType.LONG);\n       case FLOAT:\n-        return ImmutableList.of(\"double\");\n+        return Optional.of(SimpleJavaType.DOUBLE);\n       case STRING:\n-        return ImmutableList.of(\"String\");\n+        return Optional.of(SimpleJavaType.STRING);\n       case HTML:\n-        return ImmutableList.of(\"SafeHtml\");\n+        return Optional.of(SimpleJavaType.HTML);\n       case JS:\n-        return ImmutableList.of(\"SafeScript\");\n+        return Optional.of(SimpleJavaType.JS);\n       case URI:\n-        return ImmutableList.of(\"SafeUrl\");\n+        return Optional.of(SimpleJavaType.URL);\n       case TRUSTED_RESOURCE_URI:\n-        return ImmutableList.of(\"TrustedResourceUrl\");\n+        return Optional.of(SimpleJavaType.TRUSTED_RESOURCE_URL);\n       case PROTO:\n         SoyProtoType asProto = (SoyProtoType) soyType;\n-        return ImmutableList.of(JavaQualifiedNames.getQualifiedName(asProto.getDescriptor()));\n+        return Optional.of(new ProtoJavaType(asProto.getDescriptor()));\n       case PROTO_ENUM:\n         SoyProtoEnumType asProtoEnum = (SoyProtoEnumType) soyType;\n-        return ImmutableList.of(JavaQualifiedNames.getQualifiedName(asProtoEnum.getDescriptor()));\n+        return Optional.of(new ProtoEnumJavaType(asProtoEnum.getDescriptor()));\n+      case LIST:\n+        Optional<JavaType> listElementType = getJavaType(((ListType) soyType).getElementType());\n+        return listElementType.map(elementType -> new ListJavaType(elementType));\n+      case ANY:\n       case UNKNOWN:\n-        return ImmutableList.of(\"Object\");\n+        return Optional.of(SimpleJavaType.OBJECT);\n       case ATTRIBUTES:\n       case CSS:\n-      case LIST:\n       case RECORD:\n       case LEGACY_OBJECT_MAP:\n       case MAP:\n       case UNION:\n-      case ANY:\n       case ERROR:\n       case NULL:\n       case VE:\n       case VE_DATA:\n-        return ImmutableList.of();\n+        return Optional.empty();\n     }\n     throw new AssertionError(\"impossible\");\n   }", "nl": "Gets Java type from Soy type.", "old_nl": "Gets Java types from Soy type.", "peft": "1645\tGets Java type from Soy type.\n", "icl": "1645\tGets Java types from Soy type.\n", "slm": "Gets the Java types from Soy type.", "label": ["Feat"]}
{"diff": "@@ -1,4 +1,4 @@\n\n-private synchronized static Map<String,String> getBuildInfo() {\n+public synchronized static Map<String,String> getBuildInfo() {\n \n         if (buildInfoRef.get() == null) {\n ", "nl": "Method used to discover and report on the bigdata build information.", "old_nl": "Use reflection to discover and report on the bigdata build information.", "peft": "1765\tUse reflection to discover and report on the build information.\n", "icl": "1765\tUse reflection to discover and report on the bigdata build information.\n", "slm": "Use reflection to discover and report on the bigdata build information.", "label": ["Ref"]}
{"diff": "@@ -1,7 +1,4 @@\n\n @SuppressWarnings(\"unchecked\")\n-    synchronized void registerCleanup() {\n-        if (cleanup != null) {\n-            cleanup.clear();\n-        }\n-        cleanup = FDCleanup.create(this);\n+    void registerCleanup() {\n+        registerCleanup(null);\n     }", "nl": "Register a cleanup for the current handle.", "old_nl": "Register a cleanup for the current raw fd.", "peft": "1896\tRegister a cleanup for the current raw fd, if any.\n", "icl": "1896\tRegister a cleanup for the current raw fd.\n", "slm": "Register a cleanup for the current fd.", "label": ["Feat"]}
{"diff": "@@ -1,5 +1,3 @@\n\n-public String mapPackageName(String name) {\n-        String fakeName = map(name + \".FakeClassName\");\n-        int index;\n-        return fakeName == null || (index = fakeName.lastIndexOf('.')) == -1 ? name: fakeName.substring(0, index);\n-    }\n+public String mapPackageName(final String name) {\n+    return name;\n+  }", "nl": "Maps a package name to its new name.", "old_nl": "Map package name to the new name.", "peft": "1925\tMap package name to the original name.\n", "icl": "1925\tMap package name to the new name.\n", "slm": "Map the package name to the new name.", "label": ["Feat"]}
