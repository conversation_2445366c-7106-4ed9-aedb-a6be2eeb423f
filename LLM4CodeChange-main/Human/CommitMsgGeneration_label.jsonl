{"id": 16601, "text": "\nmodule GRPC\n# GoogleRpcStatusUtils provides utilities to convert between a\n# GRPC::Core::Status and a deserialized Google::Rpc::Status proto\n+ # Returns nil if the grpc-status-details-bin trailer could not be\n+ # converted to a GoogleRpcStatus due to the server not providing\n+ # the necessary trailers.\n+ # Raises an error if the server did provide the necessary trailers\n+ # but they fail to deseriliaze into a GoogleRpcStatus protobuf.\nclass GoogleRpcStatusUtils\ndef self.extract_google_rpc_status(status)\nfail ArgumentError,'bad type'unless status.is_a?Struct::Status\n- Google::Rpc::Status.decode(status.metadata['grpc-status-details-bin'])\n+ grpc_status_details_bin_trailer='grpc-status-details-bin'\n+ return nil if status.metadata[grpc_status_details_bin_trailer].nil?\n+ Google::Rpc::Status.decode(status.metadata[grpc_status_details_bin_trailer])\nend\nend\nend\n\nexpect(exception.message.include?('bad type')).to be true\nend\n\n- it'fails with some error if the header key is missing'do\n+ it'returns nil if the header key is missing'do\nstatus=Struct::Status.new(1,'details',key:'val')\nexpect(status.metadata.nil?).to be false\n- expect do\n- GRPC::GoogleRpcStatusUtils.extract_google_rpc_status(status)\n- end.to raise_error(StandardError)\n+ expect(GRPC::GoogleRpcStatusUtils.extract_google_rpc_status(\n+ status)).to be(nil)\nend\n\nit'fails with some error if the header key fails to deserialize'do\ndef stop_server\nstatus_from_exception)).to eq(rpc_status)\nend\nend\n+\n+ # A test service that fails without explicitly setting the\n+ # grpc-status-details-bin trailer.Tests assumptions about value\n+ # of grpc-status-details-bin on the client side when the trailer wasn't\n+ # set explicitly.\n+ class NoStatusDetailsBinTestService\n+ include GRPC::GenericService\n+ rpc:an_rpc,EchoMsg,EchoMsg\n+\n+ def an_rpc(_,_)\n+ fail GRPC::Unknown\n+ end\n+ end\n+\n+ NoStatusDetailsBinTestServiceStub=NoStatusDetailsBinTestService.rpc_stub_class\n+\n+ describe'when the endpoint doesnt send grpc-status-details-bin'do\n+ def start_server\n+ @srv=GRPC::RpcServer.new(pool_size:1)\n+ @server_port=@srv.add_http2_port('localhost:0',\n+ :this_port_is_insecure)\n+ @srv.handle(NoStatusDetailsBinTestService)\n+ @server_thd=Thread.new{@srv.run}\n+ @srv.wait_till_running\n+ end\n+\n+ def stop_server\n+ expect(@srv.stopped?).to be(false)\n+ @srv.stop\n+ @server_thd.join\n+ expect(@srv.stopped?).to be(true)\n+ end\n+\n+ before(:each)do\n+ start_server\n+ end\n+\n+ after(:each)do\n+ stop_server\n+ end\n+\n+ it'should receive nil when we extract try to extract a google'\\\n+ 'rpc status from a BadStatus exception that didnt have it'do\n+ stub=NoStatusDetailsBinTestServiceStub.new(\"localhost:#{@server_port}\",\n+ :this_channel_is_insecure)\n+ begin\n+ stub.an_rpc(EchoMsg.new)\n+ rescue GRPC::Unknown=>e\n+ rpc_status=GRPC::GoogleRpcStatusUtils.extract_google_rpc_status(\n+ e.to_status)\n+ end\n+ expect(rpc_status).to be(nil)\n+ end\n+\n+ it'should receive nil when we extract try to extract a google'\\\n+ 'rpc status from an op views status object that didnt have it'do\n+ stub=NoStatusDetailsBinTestServiceStub.new(\"localhost:#{@server_port}\",\n+ :this_channel_is_insecure)\n+ op=stub.an_rpc(EchoMsg.new,return_op:true)\n+ begin\n+ op.execute\n+ rescue GRPC::Unknown=>e\n+ status_from_exception=e.to_status\n+ end\n+ expect(GRPC::GoogleRpcStatusUtils.extract_google_rpc_status(\n+ status_from_exception)).to be(nil)\n+ expect(GRPC::GoogleRpcStatusUtils.extract_google_rpc_status(\n+ op.status)).to be nil\n+ end\n+ end\n\n\npeft:1600\tAdd test for when the server doesn't set grpc-status-details-bin trailer.\n\n\nicl:1600\tMetrica\n\n\nslm:Raise an error if the grpc - status - details - bin trailer is missing\n\n\n", "peft": "1600\tAdd test for when the server doesn't set grpc-status-details-bin trailer.\n", "icl": "1600\tMetrica\n", "slm": "Raise an error if the grpc - status - details - bin trailer is missing\n", "nl": "Merge pull request from apolcyn/google_rpc_status_fix_not_present_case", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16602, "text": "proto_library(\n\"map_junction.proto\",\n\"map_lane.proto\",\n\"map_overlap.proto\",\n+ \"map_parking.proto\",\n\"map_road.proto\",\n\"map_signal.proto\",\n\"map_speed_bump.proto\",\nnew file mode 100644\nindex **********0..9c7fdb3a909\nmmm/dev/null\n\n+ syntax=\"proto2\";\n+\n+ package apollo.hdmap;\n+\n+ import\"modules/map/proto/map_geometry.proto\";\n+ import\"modules/map/proto/map_id.proto\";\n+\n+ //ParkingSpace is a place designated to park a car.\n+ message ParkingSpace{\n+ optional Id id=1;\n+\n+ optional Polygon polygon=2;\n+\n+ repeated Id overlap_id=3;\n+ }\n+\n+ //ParkingLot is a place for parking cars.\n+ message ParkingLot{\n+ optional Id id=1;\n+\n+ optional Polygon polygon=2;\n+\n+ repeated Id overlap_id=3;\n+ }\n\n\npeft:1390\tAdd parking space and parking lot proto.\n\n\nicl:1390\tUpdate ops - related pbtxt files.\n\n\nslm:add missing file\n\n\n", "peft": "1390\tAdd parking space and parking lot proto.\n", "icl": "1390\tUpdate ops - related pbtxt files.\n", "slm": "add missing file\n", "nl": "map:added parking lot info", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16603, "text": "class ClusterControllerData{\nFuture<Void>outstandingRequestChecker;\nFuture<Void>outstandingRemoteRequestChecker;\nAsyncTrigger updateDBInfo;\n- std::vector<Endpoint>updateDBInfoEndpoints;\n+ std::set<Endpoint>updateDBInfoEndpoints;\nstd::set<Endpoint>removedDBInfoEndpoints;\n\nDBInfo db;\nACTOR Future<Void>workerAvailabilityWatch(WorkerInterface worker,ProcessClass\n?Never()\n:waitFailureClient(worker.waitFailure,SERVER_KNOBS->WORKER_FAILURE_TIME);\ncluster->updateWorkerList.set(worker.locality.processId(),ProcessData(worker.locality,startingClass,worker.stableAddress()));\n- cluster->updateDBInfoEndpoints.push_back(worker.updateServerDBInfo.getEndpoint());\n+ cluster->updateDBInfoEndpoints.insert(worker.updateServerDBInfo.getEndpoint());\ncluster->updateDBInfo.trigger();\n//This switching avoids a race where the worker can be added to id_worker map after the workerAvailabilityWatch fails for the worker.\nwait(delay(0));\nACTOR Future<Void>statusServer(FutureStream<StatusRequest>requests,\n\n//Get status but trap errors to send back to client.\nvector<WorkerDetails>workers;\n- std::vector<std::pair<NetworkAddress,Standalone<VectorRef<StringRef>>>>workerIssues;\n+ std::vector<ProcessIssues>workerIssues;\n\nfor(auto&it:self->id_worker){\nworkers.push_back(it.second.details);\nif(it.second.issues.size()){\n- workerIssues.push_back(std::make_pair(it.second.details.interf.address(),it.second.issues));\n+ workerIssues.push_back(ProcessIssues(it.second.details.interf.address(),it.second.issues));\n}\n}\n\nACTOR Future<Void>dbInfoUpdater(ClusterControllerData * self){\nwhen(wait(dbInfoChange)){}\n}\n\n+ UpdateServerDBInfoRequest req;\nif(dbInfoChange.isReady()){\n- self->updateDBInfoEndpoints.clear();\nfor(auto&it:self->id_worker){\n- self->updateDBInfoEndpoints.push_back(it.second.details.interf.updateServerDBInfo.getEndpoint());\n+ req.broadcastInfo.push_back(it.second.details.interf.updateServerDBInfo.getEndpoint());\n}\n}else{\n- uniquify(self->updateDBInfoEndpoints);\n- for(int i=0;i<self->updateDBInfoEndpoints.size();i++){\n- if(self->removedDBInfoEndpoints.count(self->updateDBInfoEndpoints[i])){\n- self->updateDBInfoEndpoints[i--]=self->updateDBInfoEndpoints.back();\n- self->updateDBInfoEndpoints.pop_back();\n- }\n- }\n+ self->updateDBInfoEndpoints.erase(self->removedDBInfoEndpoints.begin(),self->removedDBInfoEndpoints.end());\n+ req.broadcastInfo=std::vector<Endpoint>(self->updateDBInfoEndpoints.begin(),self->updateDBInfoEndpoints.end());\n}\n\n+ self->updateDBInfoEndpoints.clear();\nself->removedDBInfoEndpoints.clear();\n+\ndbInfoChange=self->db.serverInfo->onChange();\nupdateDBInfo=self->updateDBInfo.onTrigger();\n\n- UpdateServerDBInfoRequest req;\nreq.serializedDbInfo=BinaryWriter::toValue(self->db.serverInfo->get(),AssumeVersion(currentProtocolVersion));\n- req.broadcastInfo=self->updateDBInfoEndpoints;\n\n- self->updateDBInfoEndpoints.clear();\nTraceEvent(\"DBInfoStartBroadcast\",self->id);\nchoose{\nwhen(std::vector<Endpoint>notUpdated=wait(broadcastDBInfoRequest(req,SERVER_KNOBS->DBINFO_SEND_AMOUNT,Optional<Endpoint>(),false))){\nACTOR Future<Void>dbInfoUpdater(ClusterControllerData * self){\nfor(auto&it:notUpdated){\nTraceEvent(\"DBInfoNotUpdated\",self->id).detail(\"Addr\",it.getPrimaryAddress());\n}\n- self->updateDBInfoEndpoints.insert(self->updateDBInfoEndpoints.end(),notUpdated.begin(),notUpdated.end());\n+ self->updateDBInfoEndpoints.insert(notUpdated.begin(),notUpdated.end());\nif(notUpdated.size()){\nself->updateDBInfo.trigger();\n}\nACTOR Future<Void>storageServerTracker(\n\nif(worstStatus!=DDTeamCollection::Status::NONE){\nTraceEvent(SevWarn,\"UndesiredStorageServer\",self->distributorId)\n- .detail(\"Server\",server->id)\n- .detail(\"Excluded\",worstAddr.toString());\n+ .detail(\"Server\",server->id)\n+ .detail(\"Excluded\",worstAddr.toString());\nstatus.isUndesired=true;\nstatus.isWrongConfiguration=true;\nif(worstStatus==DDTeamCollection::Status::FAILED){\nTraceEvent(SevWarn,\"FailedServerRemoveKeys\",self->distributorId)\n.detail(\"Server\",server->id)\n- .detail(\"Excluded\",worstAddr.toString());\n+ .detail(\"Excluded\",worstAddr.toString());\nwait(removeKeysFromFailedServer(cx,server->id,self->lock));\nif(BUGGIFY)wait(delay(5.0));\nself->shardsAffectedByTeamFailure->eraseServer(server->id);\nstatic std::string getIssueDescription(std::string name){\n}\n\nstatic std::map<std::string,std::vector<JsonBuilderObject>>getProcessIssuesAsMessages(\n- std::vector<std::pair<NetworkAddress,Standalone<VectorRef<StringRef>>>>const&issues){\n+ std::vector<ProcessIssues>const&issues){\nstd::map<std::string,std::vector<JsonBuilderObject>>issuesMap;\n\ntry{\nfor(auto processIssues:issues){\n- for(auto issue:processIssues.second){\n+ for(auto issue:processIssues.issues){\nstd::string issueStr=issue.toString();\n- issuesMap[processIssues.first.toString()].push_back(\n+ issuesMap[processIssues.address.toString()].push_back(\nJsonString::makeMessage(issueStr.c_str(),getIssueDescription(issueStr).c_str()));\n}\n}\nACTOR Future<StatusReply>clusterGetStatus(\nReference<AsyncVar<ServerDBInfo>>db,\nDatabase cx,\nvector<WorkerDetails>workers,\n- std::vector<std::pair<NetworkAddress,Standalone<VectorRef<StringRef>>>>workerIssues,\n+ std::vector<ProcessIssues>workerIssues,\nstd::map<NetworkAddress,std::pair<double,OpenDatabaseRequest>>* clientStatus,\nServerCoordinators coordinators,\nstd::vector<NetworkAddress>incompatibleConnections,\n\n# include\"fdbserver/MasterInterface.h\"\n# include\"fdbclient/ClusterInterface.h\"\n\n- Future<StatusReply>clusterGetStatus(Reference<AsyncVar<struct ServerDBInfo>>const&db,Database const&cx,vector<WorkerDetails>const&workers,std::vector<std::pair<NetworkAddress,Standalone<VectorRef<StringRef>>>>const&workerIssues,\n+ struct ProcessIssues{\n+ NetworkAddress address;\n+ Standalone<VectorRef<StringRef>>issues;\n+\n+ ProcessIssues(NetworkAddress address,Standalone<VectorRef<StringRef>>issues):address(address),issues(issues){}\n+ };\n+\n+ Future<StatusReply>clusterGetStatus(Reference<AsyncVar<struct ServerDBInfo>>const&db,Database const&cx,vector<WorkerDetails>const&workers,std::vector<ProcessIssues>const&workerIssues,\nstd::map<NetworkAddress,std::pair<double,OpenDatabaseRequest>>* const&clientStatus,ServerCoordinators const&coordinators,std::vector<NetworkAddress>const&incompatibleConnections,Version const&datacenterVersionDifference);\n\n# endif\nstruct ClusterControllerFullInterface{\nRequestStream<struct RegisterWorkerRequest>registerWorker;\nRequestStream<struct GetWorkersRequest>getWorkers;\nRequestStream<struct RegisterMasterRequest>registerMaster;\n- RequestStream<struct GetServerDBInfoRequest>getServerDBInfo;\n+ RequestStream<struct GetServerDBInfoRequest>getServerDBInfo;//only used by testers;the cluster controller will send the serverDBInfo to workers\n\nUID id()const{return clientInterface.id();}\nbool operator==(ClusterControllerFullInterface const&r)const{return id()==r.id();}\n\n\npeft:163\tFix bug in dbInfoUpdater\n\n\nicl:163\tdater(ClusterControllerData * self){\n\n\nslm:SERVER - 16824 Remove duplicate dbinfo endpoint from ClusterControllerData\n\n\n", "peft": "163\tFix bug in dbInfoUpdater\n", "icl": "163\tdater(ClusterControllerData * self){\n", "slm": "SERVER - 16824 Remove duplicate dbinfo endpoint from ClusterControllerData\n", "nl": "code cleanup based on review comments", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16604, "text": "LRESULT OS_Windows::WndProc(HWND hWnd,UINT uMsg,WPARAM wParam,LPARAM lParam)\ninput->set_mouse_in_window(false);\n\n}break;\n+ case WM_INPUT:{\n+ if(mouse_mode!=MOUSE_MODE_CAPTURED||!use_raw_input){\n+ break;\n+ }\n+\n+ UINT dwSize;\n+\n+ GetRawInputData((HRAWINPUT)lParam,RID_INPUT,NULL,&dwSize,sizeof(RAWINPUTHEADER));\n+ LPBYTE lpb=new BYTE[dwSize];\n+ if(lpb==NULL){\n+ return 0;\n+ }\n+\n+ if(GetRawInputData((HRAWINPUT)lParam,RID_INPUT,lpb,&dwSize,sizeof(RAWINPUTHEADER))!=dwSize)\n+ OutputDebugString(TEXT(\"GetRawInputData does not return correct size!\\n\"));\n+\n+ RAWINPUT * raw=(RAWINPUT *)lpb;\n+\n+ if(raw->header.dwType==RIM_TYPEMOUSE){\n+ Ref<InputEventMouseMotion>mm;\n+ mm.instance();\n+\n+ mm->set_control(control_mem);\n+ mm->set_shift(shift_mem);\n+ mm->set_alt(alt_mem);\n+\n+ mm->set_button_mask(last_button_state);\n+\n+ Point2i c(video_mode.width/2,video_mode.height/2);\n+\n+ //centering just so it works as before\n+ POINT pos={(int)c.x,(int)c.y};\n+ ClientToScreen(hWnd,&pos);\n+ SetCursorPos(pos.x,pos.y);\n+\n+ mm->set_position(c);\n+ mm->set_global_position(c);\n+ input->set_mouse_position(c);\n+ mm->set_speed(Vector2(0,0));\n+\n+ if(raw->data.mouse.usFlags==0){\n+ mm->set_relative(Vector2(raw->data.mouse.lLastX,raw->data.mouse.lLastY));\n+\n+ }else if(raw->data.mouse.usFlags==1){\n+ mm->set_relative(Vector2(raw->data.mouse.lLastX,raw->data.mouse.lLastY)-last_absolute_position);\n+ last_absolute_position=Vector2(raw->data.mouse.lLastX,raw->data.mouse.lLastY);\n+ }\n+\n+ if(window_has_focus&&main_loop)\n+ input->parse_input_event(mm);\n+ }\n+ delete[]lpb;\n+ }break;\ncase WM_MOUSEMOVE:{\n+ if(mouse_mode==MOUSE_MODE_CAPTURED&&use_raw_input){\n+ break;\n+ }\n\nif(input->is_emulating_mouse_from_touch()){\n//Universal translation enabled;ignore OS translation\nError OS_Windows::initialize(const VideoMode&p_desired,int p_video_driver,int\nreturn ERR_UNAVAILABLE;\n}\n\n+ use_raw_input=true;\n+\n+ RAWINPUTDEVICE Rid[1];\n+\n+ Rid[0].usUsagePage=0x01;\n+ Rid[0].usUsage=0x02;\n+ Rid[0].dwFlags=0;\n+ Rid[0].hwndTarget=0;\n+\n+ if(RegisterRawInputDevices(Rid,1,sizeof(Rid[0]))==FALSE){\n+ //registration failed.\n+ use_raw_input=false;\n+ }\n+\npre_fs_valid=true;\nif(video_mode.fullscreen){\n\nclass OS_Windows:public OS{\nbool force_quit;\nbool window_has_focus;\nuint32_t last_button_state;\n+ Vector2 last_absolute_position;\n+ bool use_raw_input;\n\nHCURSOR cursors[CURSOR_MAX]={NULL};\nCursorShape cursor_shape;\n\n\npeft:1835\tMerge pull request from k4\n\n\nicl:1835\t+\n\n\nslm:Merge pull request from mkortstiege/rawinput\n\n\n", "peft": "1835\tMerge pull request from k4\n", "icl": "1835\t+\n", "slm": "Merge pull request from mkortstiege/rawinput\n", "nl": "Relative motion based on raw input for Windows", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16605, "text": "Default font lacks support for all characters\n<row>`~</row>\n</keyboard>\n</layout>\n+ <layout name=\"Greek QWERTY\">\n+ <keyboard>\n+ <row>1234567890</row>\n+ <row>ςερτυθιοπ</row>\n+ <row>ασδφγηξκλ</row>\n+ <row>ζχψωβνμ</row>\n+ </keyboard>\n+ <keyboard modifiers=\"shift\">\n+ <row>1234567890</row>\n+ <row>ΕΡΤΥΘΙΟΠ</row>\n+ <row>ΑΣΔΦΓΗΞΚΛ</row>\n+ <row>ΖΧΨΩΒΝΜ</row>\n+ </keyboard>\n+ <keyboard modifiers=\"symbol,shift+symbol\">\n+ <row>)!@#$%^&amp;*(€</row>\n+ <row>[]{}-_=+;:~</row>\n+ <row>'\",.&lt;&gt;/?\\|`</row>\n+ <row>έύίόάήώϋϊΰΐ</row>\n+ </keyboard>\n+ </layout>\n</keyboardlayouts>\n\n\npeft:1786\tAdded Greek QWERTY layout.\n\n\nicl:1786\tUpdate ops - related pbtxt files.\n\n\nslm:Updated:[Qt]Greek Qt layout\n\n\n", "peft": "1786\tAdded Greek QWERTY layout.\n", "icl": "1786\tUpdate ops - related pbtxt files.\n", "slm": "Updated:[Qt]Greek Qt layout\n", "nl": "[keyboardlayout]Add Greek QWERTY", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16606, "text": "void ScriptEditor::_update_window_menu(){\nwindow_menu->get_popup()->clear();\nwindow_menu->get_popup()->add_item(\"Close\",WINDOW_CLOSE,KEY_MASK_CMD|KEY_W);\nwindow_menu->get_popup()->add_separator();\n- window_menu->get_popup()->add_item(\"Move Left\",WINDOW_MOVE_LEFT,KEY_MASK_CMD|KEY_LEFT);\n- window_menu->get_popup()->add_item(\"Move Right\",WINDOW_MOVE_RIGHT,KEY_MASK_CMD|KEY_RIGHT);\n+ window_menu->get_popup()->add_item(\"Move Left\",WINDOW_MOVE_LEFT,KEY_MASK_CMD|KEY_MASK_ALT|KEY_LEFT);\n+ window_menu->get_popup()->add_item(\"Move Right\",WINDOW_MOVE_RIGHT,KEY_MASK_CMD|KEY_MASK_ALT|KEY_RIGHT);\nwindow_menu->get_popup()->add_separator();\n\nidx=0;\nvoid ScriptEditor::_update_window_menu(){\nString n=ste->get_name();\nuint32_t accel=0;\nif(idx<9){\n- accel=KEY_MASK_ALT|(KEY_1+idx);\n+ accel=KEY_MASK_ALT|KEY_MASK_CMD|(KEY_1+idx);\n}\nwindow_menu->get_popup()->add_item(n,WINDOW_SELECT_BASE+idx,accel);\nidx++;\n\n\npeft:1162\tFix window menu accelerators on macOS.\n\n\nicl:1162\tFix window menu accelerators\n\n\nslm:Merge pull request from akien - mga/script - editor - fix\n\n\n", "peft": "1162\tFix window menu accelerators on macOS.\n", "icl": "1162\tFix window menu accelerators\n", "slm": "Merge pull request from akien - mga/script - editor - fix\n", "nl": "changed some shortcuts in text editor, maybe fixes", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16607, "text": "Expr * Parser::parseExprOperator(){\n///\n///expr-identifier:\n///identifier\n- ///identifier'::'identifier\n+ ///scope-qualifier identifier\nParseResult<Expr>Parser::parseExprIdentifier(){\nassert(Tok.is(tok::identifier));\nSMLoc Loc=Tok.getLoc();\nbool Parser::parseType(Type&Result){\n///type-simple'->'type\n///\n///type-simple:\n- ///identifier\n+ ///type-identifier\n///type-tuple\n///type-oneof\n///type-protocol\n///\n+ ///type-identifier:\n+ ///identifier\n+ ///scope-qualifier identifier\n+ ///\nbool Parser::parseType(Type&Result,const Twine&Message){\n//Parse type-simple first.\nswitch(Tok.getKind()){\n\n\npeft:1175\tMerge pull request from jonathanwright/fix - type - identifier - parsing\n\n\nicl:1175\tMerge pull request from siddhantbarua/type-identifier\n\n\nslm:Add scope - qualifier to type - simple and type - simple patterns\n\n\n", "peft": "1175\tMerge pull request from jonathanwright/fix - type - identifier - parsing\n", "icl": "1175\tMerge pull request from siddhantbarua/type-identifier\n", "slm": "Add scope - qualifier to type - simple and type - simple patterns\n", "nl": "Document the scope - qualifier grammar correctly in the parser comments.", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16608, "text": "def build_jobspec(self):\nself.name,\n'tools/dockerfile/grpc_artifact_linux_%s'%self.arch,\n'tools/run_tests/artifacts/build_artifact_csharp.sh',\n- environ={'CMAKE_ARCH_OPTION':cmake_arch_option}\n- )\n+ environ={\n+ 'CMAKE_ARCH_OPTION':cmake_arch_option\n+ })\nelse:\ncmake_arch_option=''# x64 is the default architecture\nif self.arch=='x86':\n\n\npeft:1093\tFix typo in build_artifact_csharp.sh.\n\n\nicl:1093\tFix dockerfile for grpc_artifact_linux_x86\n\n\nslm:Merge pull request from pooyadavoodi/patch - 1\n\n\n", "peft": "1093\tFix typo in build_artifact_csharp.sh.\n", "icl": "1093\tFix dockerfile for grpc_artifact_linux_x86\n", "slm": "Merge pull request from pooyadavoodi/patch - 1\n", "nl": "yapf code", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16609, "text": "DEFINE_BOOL(inline_construct,true,\"inline constructor calls\")\nDEFINE_BOOL(inline_arguments,true,\"inline functions with arguments object\")\nDEFINE_BOOL(inline_accessors,true,\"inline JavaScript accessors\")\nDEFINE_BOOL(inline_into_try,true,\"inline into try blocks\")\n- DEFINE_INT(escape_analysis_iterations,2,\n+ DEFINE_INT(escape_analysis_iterations,1,\n\"maximum number of escape analysis fix-point iterations\")\n\nDEFINE_BOOL(concurrent_recompilation,true,\nnew file mode 100644\nindex **********0..079cb968e1d\nmmm/dev/null\n\n+ //Copyright 2016 the V8 project authors.All rights reserved.\n+ //Use of this source code is governed by a BSD-style license that can be\n+ //found in the LICENSE file.\n+\n+ //Flags:--allow-natives-syntax\n+\n+ function h(y){return y.u;}\n+\n+ function g(){return h.apply(0,arguments);}\n+\n+ function f(x){\n+ var o={u:x};\n+ return g(o);\n+ }\n+\n+ f(42);\n+ f(0.1);\n+\n+ %OptimizeFunctionOnNextCall(f);\n+\n+ assertEquals(undefined,f(undefined));\n\n\npeft:1004\t[compiler]Fix escape analysis to not crash on arguments object.\n\n\nicl:1004\tFix inlining of functions with arguments object\n\n\nslm:[escaping]Fix escape analysis test\n\n\n", "peft": "1004\t[compiler]Fix escape analysis to not crash on arguments object.\n", "icl": "1004\tFix inlining of functions with arguments object\n", "slm": "[escaping]Fix escape analysis test\n", "nl": "[crankshaft]Disable escape analysis of nested objects.", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16610, "text": "void DocumentRange::endRange(Layer * toLayer,frame_t toFrame)\nselectFrameRange(m_selectingFromFrame,toFrame);\n}\n\n+ void DocumentRange::selectLayers(const SelectedLayers&selLayers)\n+ {\n+ for(auto layer:selLayers)\n+ m_selectedLayers.insert(layer);\n+ }\n+\nbool DocumentRange::contains(Layer * layer)const\n{\nif(enabled())\nnamespace app{\nvoid displace(layer_t layerDelta,frame_t frameDelta);\n\nbool contains(Layer * layer)const;\n-\nbool contains(frame_t frame)const{\nreturn m_selectedFrames.contains(frame);\n}\n-\nbool contains(Layer * layer,frame_t frame)const{\nreturn contains(layer)&&contains(frame);\n}\nnamespace app{\nvoid startRange(Layer * fromLayer,frame_t fromFrame,Type type);\nvoid endRange(Layer * toLayer,frame_t toFrame);\n\n+ void selectLayers(const SelectedLayers&selLayers);\n+\nframe_t firstFrame()const{return m_selectedFrames.firstFrame();}\nframe_t lastFrame()const{return m_selectedFrames.lastFrame();}\n\nnamespace app{\n\nusing namespace ui;\n\n+ MovingCelCollect::MovingCelCollect(Editor * editor,Layer * layer)\n+ :m_mainCel(nullptr)\n+ {\n+ ASSERT(editor);\n+\n+ if(layer&&layer->isImage())\n+ m_mainCel=layer->cel(editor->frame());\n+\n+ DocumentRange range=App::instance()->timeline()->range();\n+ if(!range.enabled()){\n+ range.startRange(editor->layer(),editor->frame(),DocumentRange::kCels);\n+ range.endRange(editor->layer(),editor->frame());\n+ }\n+\n+ DocumentRange range2=range;\n+ for(Layer * layer:range.selectedLayers()){\n+ if(layer&&layer->isGroup()){\n+ LayerList childrenList;\n+ static_cast<LayerGroup *>(layer)->allLayers(childrenList);\n+\n+ SelectedLayers selChildren;\n+ for(auto layer:childrenList)\n+ selChildren.insert(layer);\n+\n+ range2.selectLayers(selChildren);\n+ }\n+ }\n+\n+ //Record start positions of all cels in selected range\n+ for(Cel * cel:get_unique_cels(editor->sprite(),range2)){\n+ Layer * layer=cel->layer();\n+ ASSERT(layer);\n+\n+ if(layer&&layer->isMovable()&&!layer->isBackground())\n+ m_celList.push_back(cel);\n+ }\n+ }\n+\nMovingCelState::MovingCelState(Editor * editor,\nMouseMessage * msg,\n- const HandleType handle)\n+ const HandleType handle,\n+ const MovingCelCollect&collect)\n:m_reader(UIContext::instance(),500)\n+ ,m_cel(nullptr)\n+ ,m_celList(collect.celList())\n,m_celOffset(0.0,0.0)\n,m_celScale(1.0,1.0)\n,m_canceled(false)\nMovingCelState::MovingCelState(Editor * editor,\n{\nContextWriter writer(m_reader,500);\nDocument * document=editor->document();\n- auto range=App::instance()->timeline()->range();\n- LayerImage * layer=static_cast<LayerImage *>(editor->layer());\n- ASSERT(layer->isImage());\n-\n- m_cel=layer->cel(editor->frame());\n- ASSERT(m_cel);//The cel cannot be null\n-\n- if(!range.enabled())\n- range=DocumentRange(m_cel);\n+ ASSERT(!m_celList.empty());\n\n+ m_cel=collect.mainCel();\nif(m_cel)\nm_celMainSize=m_cel->boundsF().size();\n\n//Record start positions of all cels in selected range\n- for(Cel * cel:get_unique_cels(writer.sprite(),range)){\n+ for(Cel * cel:m_celList){\nLayer * layer=cel->layer();\nASSERT(layer);\n\nif(layer&&layer->isMovable()&&!layer->isBackground()){\n- m_celList.push_back(cel);\n-\n- if(cel->layer()->isReference()){\n+ if(layer->isReference()){\nm_celStarts.push_back(cel->boundsF());\nm_hasReference=true;\n}\nnamespace doc{\nnamespace app{\nclass Editor;\n\n+ class MovingCelCollect{\n+ public:\n+ MovingCelCollect(Editor * editor,Layer * layer);\n+\n+ bool empty()const{return m_celList.empty();}\n+\n+ Cel * mainCel()const{return m_mainCel;}\n+ const CelList&celList()const{return m_celList;}\n+\n+ private:\n+ Cel * m_mainCel;\n+ CelList m_celList;\n+ };\n+\nclass MovingCelState:public StandbyState{\npublic:\nMovingCelState(Editor * editor,\nui::MouseMessage * msg,\n- const HandleType handle);\n+ const HandleType handle,\n+ const MovingCelCollect&collect);\n\nvirtual bool onMouseUp(Editor * editor,ui::MouseMessage * msg)override;\nvirtual bool onMouseMove(Editor * editor,ui::MouseMessage * msg)override;\nbool StandbyState::onMouseDown(Editor * editor,MouseMessage * msg)\n}\n}\n\n- if(layer&&layer->isImage()){\n+ if(layer){\n//TODO we should be able to move the ` Background'with tiled mode\nif(layer->isBackground()){\nStatusBar::instance()->showTip(1000,\nbool StandbyState::onMouseDown(Editor * editor,MouseMessage * msg)\nStatusBar::instance()->showTip(1000,\n\"Layer'%s'is locked\",layer->name().c_str());\n}\n- else if(!layer->cel(editor->frame())){\n- StatusBar::instance()->showTip(1000,\n- \"Cel is empty,nothing to move\");\n- }\nelse{\n- try{\n- //Change to MovingCelState\n- HandleType handle=MoveHandle;\n- if(resizeCelBounds(editor).contains(msg->position()))\n- handle=ScaleSEHandle;\n-\n- MovingCelState * newState=new MovingCelState(editor,msg,handle);\n- editor->setState(EditorStatePtr(newState));\n+ MovingCelCollect collect(editor,layer);\n+ if(collect.empty()){\n+ StatusBar::instance()->showTip(\n+ 1000,\"Nothing to move\");\n}\n- catch(const LockedDocumentException&){\n- //TODO break the background task that is locking this sprite\n- StatusBar::instance()->showTip(1000,\n- \"Sprite is used by a backup/data recovery task\");\n+ else{\n+ try{\n+ //Change to MovingCelState\n+ HandleType handle=MoveHandle;\n+ if(resizeCelBounds(editor).contains(msg->position()))\n+ handle=ScaleSEHandle;\n+\n+ MovingCelState * newState=new MovingCelState(\n+ editor,msg,handle,collect);\n+ editor->setState(EditorStatePtr(newState));\n+ }\n+ catch(const LockedDocumentException&){\n+ //TODO break the background task that is locking this sprite\n+ StatusBar::instance()->showTip(\n+ 1000,\"Sprite is used by a backup/data recovery task\");\n+ }\n}\n}\n}\n\n\npeft:989\tAdded support for moving multiple cels at once.\n\n\nicl:989\t# Answer:\n\n\nslm:Fix crash in moving cel range\n\n\n", "peft": "989\tAdded support for moving multiple cels at once.\n", "icl": "989\t# Answer:\n", "slm": "Fix crash in moving cel range\n", "nl": "Add support to move layer groups with the move tool", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16611, "text": "new file mode 100644\nindex **********..b8816d216b\nmmm/dev/null\n\n+\n+ sealed trait TwoThree[T]{\n+ def insert(newEl:T):TwoThree[T]\n+ def height():Int\n+ def printSideways(indent:Int):Unit\n+ }\n+\n+ case class OneLeaf[T](data:T)(implicit ord:Ordering[T])extends TwoThree[T]{\n+ def insert(newEl:T):TwoThree[T]={\n+ if(newEl==data)this\n+ else if(ord.lt(newEl,data))TwoLeaf(newEl,data)\n+ else TwoLeaf(data,newEl)\n+ }\n+ def height():Int=1\n+ def printSideways(indent:Int):Unit={\n+ println(\"\\t\"* indent+data)\n+ }\n+ }\n+\n+ case class TwoLeaf[T](dataLeft:T,dataRight:T)(implicit ord:Ordering[T])extends TwoThree[T]{\n+ def insert(newEl:T):TwoThree[T]={\n+ if(newEl==dataLeft||newEl==dataRight)this\n+ else if(ord.lt(newEl,dataLeft))TwoNode(dataLeft,OneLeaf(newEl),OneLeaf(dataRight))\n+ else if(ord.lt(newEl,dataRight))TwoNode(newEl,OneLeaf(dataLeft),OneLeaf(dataRight))\n+ else TwoNode(dataRight,OneLeaf(dataLeft),OneLeaf(newEl))\n+ }\n+ def height():Int=1\n+ def printSideways(indent:Int):Unit={\n+ println(\"\\t\"* indent+dataRight)\n+ println(\"\\t\"* indent+dataLeft)\n+ }\n+ }\n+\n+ case class TwoNode[T](data:T,left:TwoThree[T],right:TwoThree[T])(implicit ord:Ordering[T])extends TwoThree[T]{\n+ def insert(newEl:T):TwoThree[T]={\n+ if(newEl==data){\n+ this\n+ }\n+ else if(ord.lt(newEl,data)){\n+ left match{\n+ case_:OneLeaf[T]=>copy(left=left.insert(newEl))\n+ case TwoLeaf(leftData,rightData)=>\n+ if(ord.lt(newEl,leftData)){\n+ ThreeNode(leftData,data,OneLeaf(newEl),OneLeaf(rightData),right)\n+ }\n+ else if(ord.lt(newEl,rightData)){\n+ ThreeNode(newEl,data,OneLeaf(leftData),OneLeaf(rightData),right)\n+ }\n+ else{\n+ ThreeNode(rightData,data,OneLeaf(leftData),OneLeaf(newEl),right)\n+ }\n+ case leftTwo:TwoNode[T]=>leftTwo.insert(newEl)match{\n+ case twoNodeChild:TwoNode[T]=>\n+ ThreeNode(twoNodeChild.data,data,twoNodeChild.left,twoNodeChild.right,right)\n+ case otherChild:TwoThree[T]=>this.copy(left=otherChild)\n+ }\n+ case t:ThreeNode[T]=>this.copy(left=t)\n+ }\n+ }\n+ else{\n+ right match{\n+ case_:OneLeaf[T]=>copy(right=right.insert(newEl))\n+ case TwoLeaf(leftData,rightData)=>\n+ if(ord.gt(newEl,rightData)){\n+ ThreeNode(data,rightData,left,OneLeaf(leftData),OneLeaf(newEl))\n+ }\n+ else if(ord.gt(newEl,leftData)){\n+ ThreeNode(data,newEl,left,OneLeaf(leftData),OneLeaf(rightData))\n+ }\n+ else{\n+ ThreeNode(data,leftData,left,OneLeaf(newEl),OneLeaf(rightData))\n+ }\n+ case rightTwo:TwoNode[T]=>rightTwo.insert(newEl)match{\n+ case twoNodeChild:TwoNode[T]=>\n+ ThreeNode(data,twoNodeChild.data,left,twoNodeChild.left,twoNodeChild.right)\n+ case otherChild:TwoThree[T]=>this.copy(right=otherChild)\n+ }\n+ case t:ThreeNode[T]=>this.copy(right=t)\n+ }\n+ }\n+ }\n+ def height():Int=1+left.height()\n+ def printSideways(indent:Int):Unit={\n+ right.printSideways(indent+1)\n+ println(\"\\t\"* indent+data)\n+ left.printSideways(indent+1)\n+ }\n+ }\n+\n+ case class ThreeNode[T](dataLeft:T,dataRight:T,left:TwoThree[T],mid:TwoThree[T],right:TwoThree[T])(implicit ord:Ordering[T])extends TwoThree[T]{\n+ def insert(newEl:T):TwoThree[T]={\n+ if(newEl==dataLeft||newEl==dataRight){\n+ this\n+ }\n+ else if(ord.lt(newEl,dataLeft)){\n+ left.insert(newEl)match{\n+ case t:TwoNode[T]=>\n+ TwoNode(dataLeft,\n+ t,\n+ TwoNode(dataRight,mid,right))\n+ case t:TwoThree[T]=>this.copy(left=t)\n+ }\n+ }\n+ else if(ord.lt(newEl,dataRight)){\n+ mid.insert(newEl)match{\n+ case TwoNode(d,l,r)=>\n+ TwoNode(d,\n+ TwoNode(dataLeft,left,l),\n+ TwoNode(dataRight,r,right))\n+ case t:TwoThree[T]=>this.copy(mid=t)\n+ }\n+ }\n+ else{\n+ right.insert(newEl)match{\n+ case t:TwoNode[T]=>\n+ TwoNode(dataRight,\n+ TwoNode(dataLeft,left,mid),\n+ t)\n+ case t:TwoThree[T]=>this.copy(right=t)\n+ }\n+ }\n+ }\n+ def height():Int=1+left.height()\n+ def printSideways(indent:Int):Unit={\n+ right.printSideways(indent+1)\n+ println(\"\\t\"* indent+dataRight)\n+ mid.printSideways(indent+1)\n+ println(\"\\t\"* indent+dataLeft)\n+ left.printSideways(indent+1)\n+ }\n+ }\n+\n+ object Main{\n+ def main(args:Array[String]):Unit={\n+ TwoLeaf(-3,5).printSideways(0)\n+ OneLeaf(5).printSideways(0)\n+ OneLeaf(5).insert(-3).printSideways(0)\n+ println(OneLeaf(5).insert(-3).insert(5).insert(6))\n+ println(OneLeaf(5).insert(-3).insert(4))\n+ println(OneLeaf(5).insert(-3).insert(4).insert(6))\n+ println(OneLeaf(5).insert(-3).insert(4).insert(6).insert(3))\n+ OneLeaf(5).insert(-3).insert(4).insert(6).insert(3).insert(7).printSideways(0)\n+ }\n+ }\n\n\npeft:270\tAdded a basic 2-3 tree implementation\n\n\nicl:270\tEl)match{\n\n\nslm:Merge pull request from nathanielmanistaatgoogle/one - leaf - insert\n\n\n", "peft": "270\tAdded a basic 2-3 tree implementation\n", "icl": "270\tEl)match{\n", "slm": "Merge pull request from nathanielmanistaatgoogle/one - leaf - insert\n", "nl": "adding 2 - 3 tree", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16612, "text": "tensorflow::Status CreateRemoteContexts(\n});\n}\ncounter.Wait();\n+ tensorflow::StatusGroup sg;\nfor(int i=0;i<num_remote_workers;i++){\n- TF_RETURN_IF_ERROR(statuses[i]);\n+ if(TF_PREDICT_FALSE(!statuses[i].ok())){\n+ sg.Update(statuses[i]);\n+ }\n}\n- return tensorflow::Status::OK();\n+ return sg.as_summary_status();\n}\n\ntensorflow::Status UpdateRemoteContexts(\ntensorflow::Status UpdateTFE_ContextWithServerDef(\n\n//Initialize remote eager workers.\nif(reset_context){\n- LOG_AND_RETURN_IF_ERROR(CreateRemoteContexts(\n+ const tensorflow::Status s=CreateRemoteContexts(\nctx,remote_workers,context_id,context_view_id,keep_alive_secs,\nserver_def,remote_eager_workers.get(),context->Executor().Async(),\n- context->LazyCopyFunctionRemoteInputs(),base_request));\n+ context->LazyCopyFunctionRemoteInputs(),base_request);\n+ //NOTE:the remote tasks could fail after ` GetAllRemoteDevices ` and cause\n+ //the CreateRemoteContexts to fail.We currently only log instead of\n+ //directly returning the error,since returning here will cause the server\n+ //object to be destroyed(which currently CHECK-fails).The client will\n+ //see additional errors if ops are subsequently sent to the failed workers.\n+ if(TF_PREDICT_FALSE(!s.ok())){\n+ LOG(ERROR)<<\"Error when creating contexts on remote targets:\"\n+ <<s.error_message()\n+ <<\"\\nExecuting remote ops or functions on these remote\"\n+ \"targets will fail.\";\n+ }\n}else{\n//The master's context_view_id will be incremented by one\n//the UpdateRemoteMaster call later.We want all new workers and\ntensorflow::Status UpdateTFE_ContextWithServerDef(\ngrpc_server->worker_env()->rendezvous_mgr->Find(context_id);\nauto * device_mgr=grpc_server->worker_env()->device_mgr;\nstd::shared_ptr<tensorflow::WorkerSession>worker_session;\n- TF_RETURN_IF_ERROR(grpc_server->worker_env()->session_mgr->CreateSession(\n- session_name,server_def,base_request.cluster_device_attributes(),\n- true));\n- TF_RETURN_IF_ERROR(\n+ LOG_AND_RETURN_IF_ERROR(\n+ grpc_server->worker_env()->session_mgr->CreateSession(\n+ session_name,server_def,base_request.cluster_device_attributes(),\n+ true));\n+ LOG_AND_RETURN_IF_ERROR(\ngrpc_server->worker_env()->session_mgr->WorkerSessionForSession(\nsession_name,&worker_session));\n\n//Initialize remote tensor communication based on worker session.\n- TF_RETURN_IF_ERROR(r->Initialize(worker_session.get()));\n+ LOG_AND_RETURN_IF_ERROR(r->Initialize(worker_session.get()));\n\ntensorflow::DistributedFunctionLibraryRuntime * cluster_flr=\ntensorflow::eager::CreateClusterFLR(context_id,context,\nStatus EagerContext::MaybeRegisterFunctionRemotely(const FunctionDef&fdef){\nif(!status.ok()){\nLOG(ERROR)<<\"Failed to register function remotely due to\"\n<<status.error_message()\n- <<\"\\nThis shouldn't happen,please file a bug to\"\n- \"tensorflow team.\";\n+ <<\"\\nThis could happen if the remote target has been\"\n+ \"disconnected from the client.\";\n}\ndelete response;\n});\nStatus EagerContext::RegisterExistingFunctionsOnRemoteWorkers(\nif(!s.ok()){\nLOG(ERROR)<<\"Failed to register function remotely due to\"\n<<s.error_message()\n- <<\"\\nThis shouldn't happen,please file a bug to\"\n- \"tensorflow team.\";\n+ <<\"\\nThis could happen if the remote target has been\"\n+ \"disconnected from the client.\";\n}\n});\n}\n\n\npeft:1798\t[Eager]Log error when remote function registration fails.\n\n\nicl:1798\t+ \n\n\nslm:Merge pull request from caisq/remote - workers - update\n\n\n", "peft": "1798\t[Eager]Log error when remote function registration fails.\n", "icl": "1798\t+ \n", "slm": "Merge pull request from caisq/remote - workers - update\n", "nl": "Adjust error reporting and messages with remote targets.", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16613, "text": "def GetExitStatus(exit_code):\nreturn-1\n\n\n+ def RunCommandSuppressOutput(command,working_dir=None):\n+ \"\"\"Changes into a specified directory,if provided,and executes a command.\n+ Restores the old directory afterwards.\n+\n+ Args:\n+ command:A command to run.\n+ working_dir:A directory to change into.\n+ \"\"\"\n+\n+ old_dir=None\n+ try:\n+ if working_dir is not None:\n+ old_dir=os.getcwd()\n+ os.chdir(working_dir)\n+ f=os.popen(command,'r')\n+ f.read()\n+ ret_code=f.close()\n+ finally:\n+ if old_dir is not None:\n+ os.chdir(old_dir)\n+ if ret_code is None:\n+ ret_code=0\n+ return ret_code\n+\n+\ndef Main():\n\"\"\"Runs the unit test.\"\"\"\n\ndef testOutfile2(self):\ndef_TestOutFile(self,test_name,expected_xml):\ngtest_prog_path=os.path.join(gtest_test_utils.GetBuildDir(),\ntest_name)\n- command=\"cd%s&&%s--gtest_output=xml:%s&>/dev/null\"%(\n- tempfile.mkdtemp(),gtest_prog_path,self.output_dir_)\n- status=os.system(command)\n+ command=\"%s--gtest_output=xml:%s\"%(gtest_prog_path,self.output_dir_)\n+ status=gtest_test_utils.RunCommandSuppressOutput(\n+ command,\n+ working_dir=tempfile.mkdtemp())\nself.assertEquals(0,gtest_test_utils.GetExitStatus(status))\n\n# TODO(<EMAIL>):libtool causes the built test binary to be\ndef testDefaultOutputFile(self):\nif e.errno!=errno.ENOENT:\nraise\n\n- status=os.system(\"cd%s&&%s%s=xml&>/dev/null\"\n- %(temp_dir,gtest_prog_path,\n- GTEST_OUTPUT_FLAG))\n+ status=gtest_test_utils.RunCommandSuppressOutput(\n+ \"%s%s=xml\"%(gtest_prog_path,GTEST_OUTPUT_FLAG),\n+ working_dir=temp_dir)\nself.assertEquals(0,gtest_test_utils.GetExitStatus(status))\nself.assert_(os.path.isfile(output_file))\n\ndef_TestXmlOutput(self,gtest_prog_name,expected_xml,expected_exit_code):\ngtest_prog_path=os.path.join(gtest_test_utils.GetBuildDir(),\ngtest_prog_name)\n\n- command=(\"%s%s=xml:%s&>/dev/null\"\n- %(gtest_prog_path,GTEST_OUTPUT_FLAG,xml_path))\n- status=os.system(command)\n+ command=(\"%s%s=xml:%s\"%(gtest_prog_path,GTEST_OUTPUT_FLAG,xml_path))\n+ status=gtest_test_utils.RunCommandSuppressOutput(command)\nif os.WIFSIGNALED(status):\nsignal=os.WTERMSIG(status)\nself.assert_(False,\n\n\npeft:1635\tAdded RunCommandSuppressOutput()to gtest_test_utils.py.\n\n\nicl:1635\tFix gtest_output flag\n\n\nslm:Add an option to suppress output from the tests.\n\n\n", "peft": "1635\tAdded RunCommandSuppressOutput()to gtest_test_utils.py.\n", "icl": "1635\tFix gtest_output flag\n", "slm": "Add an option to suppress output from the tests.\n", "nl": "Fixed two of the failing tests mentioned in issue 9", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16614, "text": "\n//RUN:%target-swift-frontend-assume-parsing-unqualified-ownership-sil-enable-large-loadable-types%s-emit-ir|%FileCheck%s--check-prefix=CHECK--check-prefix=CHECK-%target-ptrsize\n+ //REQUIRES:optimized_stdlib\n//UNSUPPORTED:resilient_stdlib\n\npublic struct BigStruct{\n\n\npeft:1325\t[stdlib]Add a comment to the test file to enable the optimized stdlib.\n\n\nicl:1325\tUpdate ops - related pbtxt files.\n\n\nslm:Fix copy - paste error.\n\n\n", "peft": "1325\t[stdlib]Add a comment to the test file to enable the optimized stdlib.\n", "icl": "1325\tUpdate ops - related pbtxt files.\n", "slm": "Fix copy - paste error.\n", "nl": "Merge pull request from jrose - apple/test - requires - optimized - stdlib", "lang": "cpp", "label": ["Doc-mod"], "Comments": []}
{"id": 16615, "text": "function intertyper(data,sidePass,baseLineNums){\nitem.type=item.tokens[1].text;\nTypes.needAnalysis[item.type]=0;\nwhile(['@','%'].indexOf(item.tokens[2].text[0])==-1&&!(item.tokens[2].text in PARSABLE_LLVM_FUNCTIONS)&&\n- item.tokens[2].text!='null'&&item.tokens[2].text!='asm'){\n+ item.tokens[2].text!='null'&&item.tokens[2].text!='asm'&&item.tokens[2].text!='undef'){\nassert(item.tokens[2].text!='asm','Inline assembly cannot be compiled to JavaScript!');\nitem.tokens.splice(2,1);\n}\nnew file mode 100644\nindex **********0..9dc1f93dcc3\nmmm/dev/null\n\n+ ;ModuleID='/dev/shm/tmp/src.cpp.o'\n+ ;Just test for compilation here\n+ target datalayout=\"e-p:32:32:32-i1:8:8-i8:8:8-i16:16:16-i32:32:32-i64:32:64-f32:32:32-f64:32:64-v64:64:64-v128:128:128-a0:0:64-f80:32:32-f128:128:128-n8:16:32\"\n+ target triple=\"i386-pc-linux-gnu\"\n+\n+ %struct.CPU_Regs=type{[8 x%union.GenReg32]}\n+ %union.GenReg32=type{[1 x i32]}\n+\n+ @cpu_regs=unnamed_addr global%struct.CPU_Regs zeroinitializer,align 32;[# uses=2]\n+ @.str=private unnamed_addr constant[14 x i8]c\"hello,world!\\00\",align 1;[# uses=1]\n+\n+ ;[# uses=0]\n+ define i32@main(){\n+ entry:\n+ %retval=alloca i32;[# uses=2]\n+ %0=alloca i32;[# uses=2]\n+ %\"alloca point\"=bitcast i32 0 to i32;[# uses=0]\n+ %1=load i32 * bitcast(i32 * getelementptr inbounds(%struct.CPU_Regs *@cpu_regs,i32 0,i32 0,i32 1,i32 0,i32 0)to i32 *),align 2;[# uses=1]\n+ store i16%1,i16 * bitcast(%struct.CPU_Regs *@cpu_regs to i16 *),align 2\n+ %2=call i32@puts(i8 * getelementptr inbounds([14 x i8]*@.str,i32 0,i32 0));[# uses=0]\n+ store i32 0,i32 *%0,align 4\n+ %3=load i32 *%0,align 4;[# uses=1]\n+ store i32%3,i32 *%retval,align 4\n+ br label%return\n+\n+ invoke void undef(%struct.CPU_Regs * noalias@cpu_regs,i32%99)\n+ to label%invcont33 unwind label%lpad106\n+\n+ invcont33:\n+ ret i32%retval1\n+\n+ lpad106:\n+ ret i32%retval1\n+\n+ return:;preds=%entry\n+ %retval1=load i32 *%retval;[# uses=1]\n+ ret i32%retval1\n+ }\n+\n+ ;[# uses=1]\n+ declare i32@puts(i8 *)\n\n\npeft:651\tAdded a test for undef()\n\n\nicl:651\tMerge pull request from huzaifafaruqui/master\n\n\nslm:test for compilation with i386\n\n\n", "peft": "651\tAdded a test for undef()\n", "icl": "651\tMerge pull request from huzaifafaruqui/master\n", "slm": "test for compilation with i386\n", "nl": "do not break on call/invoke to undef;fixes", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16616, "text": "def eval_input_fn_eval:# returns x,y\n\nArgs:\nestimator:An ` Estimator ` instance to train and evaluate.\n- train_spec:A ` TrainSpec instance to specify the training specification.\n- eval_spec:A ` EvalSpec instance to specify the evaluation and export\n+ train_spec:A ` TrainSpec ` instance to specify the training specification.\n+ eval_spec:A ` EvalSpec ` instance to specify the evaluation and export\nspecification.\n\nRaises:\n\n\npeft:1318\tFix typo in eval_input_fn_eval docstring.\n\n\nicl:1318\t+[all]scripts.md&&update the help\n\n\nslm:Fix typo()\n\n\n", "peft": "1318\tFix typo in eval_input_fn_eval docstring.\n", "icl": "1318\t+[all]scripts.md&&update the help\n", "slm": "Fix typo()\n", "nl": "Merge pull request from tensorflow/terrytangyuan - patch - 1", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16617, "text": "new file mode 100644\nindex 000000000..7d67af974\nBinary files/dev/null and b/src/translations/flags/eg.png differ\n\n<file alias=\"ko_KR\">kr.png</file>\n<file alias=\"tr_TR\">tr.png</file>\n<file alias=\"uk_UA\">ua.png</file>\n+ <file alias=\"ar_EG\">eg.png</file>\n</qresource>\n</RCC>\n\n\npeft:1627\tAdded Egyptian Arabic flag.\n\n\nicl:1627\tUpdate translations - related pbtxt files.\n\n\nslm:added ar_EG to the docs\n\n\n", "peft": "1627\tAdded Egyptian Arabic flag.\n", "icl": "1627\tUpdate translations - related pbtxt files.\n", "slm": "added ar_EG to the docs\n", "nl": "Add flag icon for Egyptian Arabic", "lang": "cpp", "label": ["Doc-mod"], "Comments": []}
{"id": 16618, "text": "bool CanvasItemEditor::_gui_input_select(const Ref<InputEvent>&p_event){\n//Retrieve the bones\n_get_bones_at_pos(click,selection);\n\n- for(int i=0;i<selection.size();i++){\n- if(editor_selection->is_selected(selection[i].item)){\n- //Drag the node(s)if requested\n- List<CanvasItem *>selection=_get_edited_canvas_items();\n-\n- drag_type=DRAG_MOVE;\n- drag_selection=selection;\n- drag_from=click;\n- _save_canvas_item_state(drag_selection);\n-\n- return true;\n- }\n- }\n-\n- if(!selection.empty())\n+ if(!selection.empty()){\ncanvas_item=selection[0].item;\n+ }\n\nif(!canvas_item){\n//Start a box selection\n\n\npeft:1316\tFix dragging a node when it is already selected.\n\n\nicl:1316\t[test]Update autolinking tests to match LLVM.\n\n\nslm:Merge pull request from Calinou/fix_3d_editor\n\n\n", "peft": "1316\tFix dragging a node when it is already selected.\n", "icl": "1316\t[test]Update autolinking tests to match LLVM.\n", "slm": "Merge pull request from Calinou/fix_3d_editor\n", "nl": "Merge pull request from groud/fix_2D_selection", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16619, "text": "\nimport os,shutil,logging\n\n- TAG='version_17'\n+ TAG='version_18'\n\ndef needed(settings,shared,ports):\nif not settings.BINARYEN:return False\n\n\npeft:1730\tUpdate version_18 tag.\n\n\nicl:1730\tFix version number\n\n\nslm:update binaryen to version_18:0x01, new type system()\n\n\n", "peft": "1730\tUpdate version_18 tag.\n", "icl": "1730\tFix version number\n", "slm": "update binaryen to version_18:0x01, new type system()\n", "nl": "update binaryen port to 18()", "lang": "cpp", "label": ["Doc-mod"], "Comments": []}
{"id": 16620, "text": "def__exit__(self,exc_type,exc_value,exc_tb):\ndel_CONTEXT[self.name]\n\nif self.clean_up:\n- import shutil\n- shutil.rmtree(self.directory)\n+ sh.rmtree(self.directory)\n\ndef_generate_train_config(self,optimizer,reader,override_existing):\n'''\ndef_generate_train_config(self,optimizer,reader,override_existing):\n'''\n\nmodel_dir=os.path.join(self.directory,'Models')\n- if os.path.exists(model_dir)and os.listdir(model_dir)==[]:\n+ if os.path.exists(model_dir):\nif override_existing:\nprint(\"Overriding the existing models\")\nsh.rmtree(model_dir)\nelse:\n- raise Exception(\"Directory'%s'already exists,set the flag override_existing to true if you want to override it\"\n- %self.directory)\n+ raise Exception(\"Directory'%s'already exists,set the\"+\n+ \"flag override_existing to true if you want to\"\n+ \"override it\"%self.directory)\n\ntmpl=open(CNTK_TRAIN_TEMPLATE_PATH,\"r\").read()\nmodel_filename=os.path.join(model_dir,self.name)\ndef train(self,optimizer,reader=None,override_existing=True):\ncan attach a reader directly to the input node.\n:param override_existing:if the folder exists already override it\n'''\n+\nconfig_content=self._generate_train_config(\noptimizer,reader,override_existing)\nreturn self._call_cntk(CNTK_TRAIN_CONFIG_FILENAME,config_content)\n\n\npeft:1677\tFix the exception message\n\n\nicl:1677\tFix the train function\n\n\nslm:Merge pull request from nathanielmanistaatgoogle/cntk - override\n\n\n", "peft": "1677\tFix the exception message\n", "icl": "1677\tFix the train function\n", "slm": "Merge pull request from nathanielmanistaatgoogle/cntk - override\n", "nl": "Proper clean up of old models when training", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16621, "text": "function traceMissingDoc(coll,doc,mongos){\nvar allOps=[];\nfor(var i=0;i<shards.length;i++){\nvar oplog=shards[i].conn.getCollection(\"local.oplog.rs\");\n- if(!oplog.findOne()){\n- oplog=shards[i].conn.getCollection(\"local.oplog.$main\");\n- }\n\nif(!oplog.findOne()){\njsTest.log(\"No oplog was found on shard\"+shards[i]._id);\nload('jstests/libs/trace_missing_docs.js');\nvar testDocMissing=function(useReplicaSet){\nvar options={\nrs:useReplicaSet,\n- shardOptions:{master:\"\",oplogSize:10},\n+ shardOptions:{oplogSize:10},\nrsOptions:{nodes:1,oplogSize:10}\n};\n\nload('jstests/libs/trace_missing_docs.js');\nassert.eq(ops[0].op,'i');\nassert.eq(ops.length,5);\n\n- jsTest.log(\"DONE!\"+(useReplicaSet?\"(using rs)\":\"(using master/slave)\"));\n+ jsTest.log(\"DONE!(using rs)\");\n\nst.stop();\n};\n\ntestDocMissing(true);\n- testDocMissing(false);\n-\n})();\nStatus addMongodOptions(moe::OptionSection * options){\nmoe::Switch,\n\"declare this is a shard db of a cluster;default port 27018\")\n.setSources(moe::SourceAllLegacy)\n- .incompatibleWith(\"configsvr\");\n+ .incompatibleWith(\"configsvr\")\n+ .incompatibleWith(\"master\")\n+ .incompatibleWith(\"slave\");\n\nsharding_options\n.addOptionChaining(\n\n\npeft:199\tSERVER-14888:fix traceMissingDoc()to work with master/slave replication\n\n\nicl:199\tMerge remote - tracking branch'origin/rb/2019 - MAY'into rb/2019 -\n\n\nslm:SERVER - 27019 Remove hard - coded master/slave from mongods.js\n\n\n", "peft": "199\tSERVER-14888:fix traceMissingDoc()to work with master/slave replication\n", "icl": "199\tMerge remote - tracking branch'origin/rb/2019 - MAY'into rb/2019 -\n", "slm": "SERVER - 27019 Remove hard - coded master/slave from mongods.js\n", "nl": "SERVER - 23920 Disallow - - master/- - slave with - - shardsvr", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16622, "text": "\n'OTHER_LDFLAGS':[\n'-lbsm',\n'-lm',\n- '-lssl',\n- '-lcrypto',\n'/usr/local/lib/liblzma.a',\n],\n},\n\n'/usr/local/macold/lib/libexif.a',\n'/usr/local/macold/lib/libc++.a',\n'/usr/local/macold/lib/libc++abi.a',\n+ '<(libs_loc)/macold/openssl-1.0.1h/libssl.a',\n+ '<(libs_loc)/macold/openssl-1.0.1h/libcrypto.a',\n],\n},\n'include_dirs':[\n\n'<(libs_loc)/macold/crashpad',\n'<(libs_loc)/macold/crashpad/third_party/mini_chromium/mini_chromium',\n],\n- 'library_dirs':[\n- '<(libs_loc)/macold/openssl-1.0.1h',\n- ],\n'configurations':{\n'Debug':{\n'library_dirs':[\n\n'/usr/local/lib/libavutil.a',\n'/usr/local/lib/libswscale.a',\n'/usr/local/lib/libswresample.a',\n+ '<(libs_loc)/openssl-xcode/libssl.a',\n+ '<(libs_loc)/openssl-xcode/libcrypto.a',\n],\n},\n'include_dirs':[\n\n'<(libs_loc)/crashpad/third_party/mini_chromium/mini_chromium',\n'<(libs_loc)/openssl-xcode/include'\n],\n- 'library_dirs':[\n- '<(libs_loc)/openssl-xcode',\n- ],\n'configurations':{\n'Debug':{\n'library_dirs':[\n\n\npeft:1322\tMerge pull request from johnnychen94/patch - 1\n\n\nicl:1322\tFix openssl library paths on mac\n\n\nslm:Merge pull request from EOSIO/update_build_scripts\n\n\n", "peft": "1322\tMerge pull request from johnnychen94/patch - 1\n", "icl": "1322\tFix openssl library paths on mac\n", "slm": "Merge pull request from EOSIO/update_build_scripts\n", "nl": "Fix openssl ldflags on macOS", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16623, "text": "if(Caffe2_EXTERNAL_DEPENDENCIES)\nendif()\n\n# Write the macros file.\n- file(MAKE_DIRECTORY${PROJECT_BINARY_DIR}/caffe2/core)\n- file(WRITE${PROJECT_BINARY_DIR}/caffe2/core/macros.h\n- \"//Automatically generated header file for caffe2 macros.These\\n\"\n- \"//macros are used to build the Caffe2 binary,and if you are\\n\"\n- \"//building a dependent library,they will need to be set as well\\n\"\n- \"//for your program to link correctly.\\n\\n\"\n- \"# pragma once\\n\\n\")\n- get_directory_property(tmp DIRECTORY${PROJECT_SOURCE_DIR}COMPILE_DEFINITIONS)\n- foreach(item${tmp})\n- if(${item}MATCHES\"CAFFE2.*\")\n- file(APPEND${PROJECT_BINARY_DIR}/caffe2/core/macros.h\n- \"\\n # ifndef${item}\\n # define${item}\\n # endif//${item}\\n\")\n- endif()\n- endforeach()\n+ configure_file(\n+ ${PROJECT_SOURCE_DIR}/caffe2/core/macros.h.in\n+ ${PROJECT_BINARY_DIR}/caffe2/core/macros.h)\n\n# Installing the header files\ninstall(DIRECTORY${CMAKE_CURRENT_LIST_DIR}\nnew file mode 100644\nindex **********00..fed702b3b93f\nmmm/dev/null\n\n+ //Automatically generated header file for caffe2 macros.These\n+ //macros are used to build the Caffe2 binary,and if you are\n+ //building a dependent library,they will need to be set as well\n+ //for your program to link correctly.\n+\n+ # pragma once\n+\n+ # cmakedefine CAFFE2_ANDROID\n+ # cmakedefine CAFFE2_FORCE_FALLBACK_CUDA_MPI\n+ # cmakedefine CAFFE2_HAS_MKL_DNN\n+ # cmakedefine CAFFE2_HAS_MKL_SGEMM_PACK\n+ # cmakedefine CAFFE2_PERF_WITH_AVX\n+ # cmakedefine CAFFE2_PERF_WITH_AVX2\n+ # cmakedefine CAFFE2_THREADPOOL_MAIN_IMBALANCE\n+ # cmakedefine CAFFE2_THREADPOOL_STATS\n+ # cmakedefine CAFFE2_UNIQUE_LONG_TYPEMETA\n+ # cmakedefine CAFFE2_USE_ACCELERATE\n+ # cmakedefine CAFFE2_USE_EIGEN_FOR_BLAS\n+ # cmakedefine CAFFE2_USE_FBCODE\n+ # cmakedefine CAFFE2_USE_GFLAGS\n+ # cmakedefine CAFFE2_USE_GOOGLE_GLOG\n+ # cmakedefine CAFFE2_USE_LITE_PROTO\n+ # cmakedefine CAFFE2_USE_MKL\n+ # cmakedefine CAFFE2_USE_NVTX\n\n//example,if your compiler did not specify-mavx,you should not provide\n//the CAFFE2_PERF_WITH_AVX macro.\n\n+ # include\"caffe2/core/common.h\"\n+\n# ifdef CAFFE2_PERF_WITH_AVX\n# ifndef__AVX__\n# error(\\\n\n//example,if your compiler did not specify-mavx2,you should not provide\n//the CAFFE2_PERF_WITH_AVX2 macro.\n\n+ # include\"caffe2/core/common.h\"\n+\n# ifdef CAFFE2_PERF_WITH_AVX2\n# ifndef__AVX2__\n# error(\\\nendif()\n\n# mmm[protobuf\nif(USE_LITE_PROTO)\n- add_definitions(-DCAFFE2_USE_LITE_PROTO)\n+ set(CAFFE2_USE_LITE_PROTO 1)\nendif()\n\n# mmm[BLAS\nmessage(STATUS\"The BLAS backend of choice:\"${BLAS})\n\nif(BLAS STREQUAL\"Eigen\")\n# Eigen is header-only and we do not have any dependent libraries\n- add_definitions(-DCAFFE2_USE_EIGEN_FOR_BLAS)\n+ set(CAFFE2_USE_EIGEN_FOR_BLAS 1)\nelseif(BLAS STREQUAL\"ATLAS\")\nfind_package(Atlas REQUIRED)\ninclude_directories(SYSTEM${ATLAS_INCLUDE_DIRS})\nelseif(BLAS STREQUAL\"MKL\")\nfind_package(MKL REQUIRED)\ninclude_directories(SYSTEM${MKL_INCLUDE_DIR})\nlist(APPEND Caffe2_DEPENDENCY_LIBS${MKL_LIBRARIES})\n- add_definitions(-DCAFFE2_USE_MKL)\n+ set(CAFFE2_USE_MKL 1)\nelseif(BLAS STREQUAL\"vecLib\")\nfind_package(vecLib REQUIRED)\ninclude_directories(SYSTEM${vecLib_INCLUDE_DIR})\nendif()\nif(USE_GLOG)\ninclude(\"cmake/External/glog.cmake\")\nif(GLOG_FOUND)\n- add_definitions(-DCAFFE2_USE_GOOGLE_GLOG)\n+ set(CAFFE2_USE_GOOGLE_GLOG 1)\ninclude_directories(SYSTEM${GLOG_INCLUDE_DIRS})\nlist(APPEND Caffe2_DEPENDENCY_LIBS${GLOG_LIBRARIES})\nelse()\nendif()\nif(USE_GFLAGS)\ninclude(\"cmake/External/gflags.cmake\")\nif(GFLAGS_FOUND)\n- add_definitions(-DCAFFE2_USE_GFLAGS)\n+ set(CAFFE2_USE_GFLAGS 1)\ninclude_directories(SYSTEM${GFLAGS_INCLUDE_DIRS})\nlist(APPEND Caffe2_DEPENDENCY_LIBS${GFLAGS_LIBRARIES})\nelse()\nif(USE_FFMPEG)\nendif()\n\n# mmm[EIGEN\n- add_definitions(-DEIGEN_MPL2_ONLY)\n+ set(EIGEN_MPL2_ONLY 1)\nfind_package(Eigen3 QUIET)\nif(EIGEN3_FOUND)\ninclude_directories(SYSTEM${EIGEN3_INCLUDE_DIRS})\nif(USE_MPI)\nmessage(STATUS\"Found OpenMPI with CUDA support built.\")\nelse()\nmessage(WARNING\"OpenMPI found,but it is not built with CUDA support.\")\n- add_definitions(-DCAFFE2_FORCE_FALLBACK_CUDA_MPI)\n+ set(CAFFE2_FORCE_FALLBACK_CUDA_MPI 1)\nendif()\nendif()\nelse()\nif(CAFFE2_LONG_IS_INT32_OR_64)\nmessage(STATUS\"Does not need to define long separately.\")\nelse()\nmessage(STATUS\"Need to define long as a separate typeid.\")\n- add_definitions(-DCAFFE2_UNIQUE_LONG_TYPEMETA)\n+ set(CAFFE2_UNIQUE_LONG_TYPEMETA 1)\nendif()\n\n\nif(CAFFE2_COMPILER_SUPPORTS_AVX2_EXTENSIONS)\n# in msvc.\n# Also see CMakeLists.txt under caffe2/perfkernels.\nif(NOT MSVC)\n- add_definitions(-DCAFFE2_PERF_WITH_AVX)\n- add_definitions(-DCAFFE2_PERF_WITH_AVX2)\n+ set(CAFFE2_PERF_WITH_AVX 1)\n+ set(CAFFE2_PERF_WITH_AVX2 1)\nendif()\nendif()\n\n\n\npeft:1715\tMerge pull request from caffe2/fix - macros - header - file\n\n\nicl:1715\tSERVER - 21262:Standalone vs.oplog comparison\n\n\nslm:Merge pull request from jcanizales/fix - caffe2\n\n\n", "peft": "1715\tMerge pull request from caffe2/fix - macros - header - file\n", "icl": "1715\tSERVER - 21262:Standalone vs.oplog comparison\n", "slm": "Merge pull request from jcanizales/fix - caffe2\n", "nl": "cmake:generate macros.h with configure_file()", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16624, "text": "String CollisionObject2D::get_configuration_warning()const{\nif(warning==String()){\nwarning+=\"\\n\";\n}\n- warning+=TTR(\"This node has no children shapes,so it can't interact with the space.\\nConsider adding CollisionShape2D or CollisionPolygon2D children nodes to define it's shape.\");\n+ warning+=TTR(\"This node has no children shapes,so it can't interact with the space.\\nConsider adding CollisionShape2D or CollisionPolygon2D children nodes to define its shape.\");\n}\n\nreturn warning;\n\n\npeft:385\tFixed typo in CollisionObject2D.cpp.\n\n\nicl:385\t[test]Update autolinking tests to match LLVM.\n\n\nslm:Cosmetics\n\n\n", "peft": "385\tFixed typo in CollisionObject2D.cpp.\n", "icl": "385\t[test]Update autolinking tests to match LLVM.\n", "slm": "Cosmetics\n", "nl": "Merge pull request from yurchor/patch - 1", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16625, "text": "all:bitcoind.exe\nobj/nogui/%.o:%.cpp$(HEADERS)\ni586-mingw32msvc-g++-c$(CFLAGS)-o$@$<\n\n- bitcoind.exe:$(OBJS:obj/%=obj/nogui/%)obj/ui_res.o\n+ bitcoind.exe:$(OBJS:obj/%=obj/nogui/%)\ni586-mingw32msvc-g++$(CFLAGS)-o$@$(LIBPATHS)$^$(LIBS)\n\n\nall:bitcoind.exe\nobj/nogui/%.o:%.cpp$(HEADERS)\ng++-c$(CFLAGS)-o$@$<\n\n- bitcoind.exe:$(OBJS:obj/%=obj/nogui/%)obj/ui_res.o\n+ bitcoind.exe:$(OBJS:obj/%=obj/nogui/%)\ng++$(CFLAGS)-o$@$(LIBPATHS)$^$(LIBS)\n\nobj/test/test_bitcoin.o:$(wildcard test/*.cpp)$(HEADERS)\n\n\npeft:305\tMerge pull request from jgarzik/fix_mingw_build\n\n\nicl:305\tFix bitcoind.exe build\n\n\nslm:Merge remote - tracking branch'origin/master'into master - next\n\n\n", "peft": "305\tMerge pull request from jgarzik/fix_mingw_build\n", "icl": "305\tFix bitcoind.exe build\n", "slm": "Merge remote - tracking branch'origin/master'into master - next\n", "nl": "Remove ui_res from makefiles", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16626, "text": "namespace eosio{namespace chain{\nEOS_ASSERT(checker.satisfied(p.first,p.second),unsatisfied_authorization,\n\"transaction declares authority'${auth}',\"\n\"but does not have signatures for it under a provided delay of${provided_delay}ms,\"\n- \"provided permissions${provided_permissions},and provided keys${provided_keys}\",\n+ \"provided permissions${provided_permissions},provided keys${provided_keys},\"\n+ \"and a delay max limit of${delay_max_limit_ms}ms\",\n(\"auth\",p.first)\n(\"provided_delay\",provided_delay.count()/1000)\n(\"provided_permissions\",provided_permissions)\nnamespace eosio{namespace chain{\n\nEOS_ASSERT(checker.satisfied({account,permission}),unsatisfied_authorization,\n\"permission'${auth}'was not satisfied under a provided delay of${provided_delay}ms,\"\n- \"provided permissions${provided_permissions},and provided keys${provided_keys}\",\n+ \"provided permissions${provided_permissions},provided keys${provided_keys},\"\n+ \"and a delay max limit of${delay_max_limit_ms}ms\",\n(\"auth\",permission_level{account,permission})\n(\"provided_delay\",provided_delay.count()/1000)\n(\"provided_permissions\",provided_permissions)\n\n\npeft:541\tAdd delay_max_limit_ms to the error message.\n\n\nicl:541\tAdding a delay max limit to the transaction authorization check.\n\n\nslm:Merge pull request from EOSIO/fix - extra - delay\n\n\n", "peft": "541\tAdd delay_max_limit_ms to the error message.\n", "icl": "541\tAdding a delay max limit to the transaction authorization check.\n", "slm": "Merge pull request from EOSIO/fix - extra - delay\n", "nl": "Correct assert message by including delay_max_limit_ms", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16627, "text": "def freeze_graph_with_def_protos(input_graph_def,\n\n\ndef_parse_input_graph_proto(input_graph,input_binary):\n- \"\"\"Parser input tensorflow graph into GraphDef proto.\"\"\"\n+ \"\"\"Parses input tensorflow graph into GraphDef proto.\"\"\"\nif not gfile.Exists(input_graph):\nprint(\"Input graph file'\"+input_graph+\"'does not exist!\")\nreturn-1\ndef_parse_input_graph_proto(input_graph,input_binary):\n\n\ndef_parse_input_meta_graph_proto(input_graph,input_binary):\n- \"\"\"Parser input tensorflow graph into MetaGraphDef proto.\"\"\"\n+ \"\"\"Parses input tensorflow graph into MetaGraphDef proto.\"\"\"\nif not gfile.Exists(input_graph):\nprint(\"Input meta graph file'\"+input_graph+\"'does not exist!\")\nreturn-1\ndef_parse_input_meta_graph_proto(input_graph,input_binary):\n\n\ndef_parse_input_saver_proto(input_saver,input_binary):\n- \"\"\"Parser input tensorflow Saver into SaverDef proto.\"\"\"\n+ \"\"\"Parses input tensorflow Saver into SaverDef proto.\"\"\"\nif not gfile.Exists(input_saver):\nprint(\"Input saver file'\"+input_saver+\"'does not exist!\")\nreturn-1\n\n\npeft:1996\tFix typo in comments.\n\n\nicl:1996\tFix freeze_graph_with_def_protos\n\n\nslm:Update proto_test.py\n\n\n", "peft": "1996\tFix typo in comments.\n", "icl": "1996\tFix freeze_graph_with_def_protos\n", "slm": "Update proto_test.py\n", "nl": "Merge pull request from dlaz:patch - 1", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16628, "text": "distance_approach_config:{\nipopt_print_timing_statistics:\"yes\"\nipopt_alpha_for_y:\"min\"\nipopt_recalc_y:\"yes\"\n- weight_d:1.0\n}\n- }\n-\n- ipopt_config:{\n- ipopt_print_level:0\n- mumps_mem_percent:6000\n- mumps_pivtol:1e-6\n- ipopt_max_iter:1000\n- ipopt_tol:1e-4\n- ipopt_acceptable_constr_viol_tol:1e-1\n- ipopt_min_hessian_perturbation:1e-12\n- ipopt_jacobian_regularization_value:1e-7\n- ipopt_print_timing_statistics:\"yes\"\n- ipopt_alpha_for_y:\"min\"\n- ipopt_recalc_y:\"yes\"\n- weight_d:1.0\n- }\n+ }\n\\No newline at end of file\n\n\npeft:1169\tMerge pull request from zhangyong/ipopt_config_fix\n\n\nicl:1169\t[test]Update autolinking tests to match LLVM.\n\n\nslm:Remove unused field ipopt_config\n\n\n", "peft": "1169\tMerge pull request from zhangyong/ipopt_config_fix\n", "icl": "1169\t[test]Update autolinking tests to match LLVM.\n", "slm": "Remove unused field ipopt_config\n", "nl": "control:fix open space config file conflicts", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16629, "text": "func@tpu_computation(%arg0:tensor<i32>,%arg1:tensor<i32>)->(tensor<i32>,t\n\nlet constructor=\"TFTPU::CreateTPUClusterFormationPass()\";\n}\n+\n+ def TPUExtractOutsideCompilationPass:Pass<\"tf-tpu-extract-outside-compilation\",\"ModuleOp\">{\n+ let summary=\"Extracts TPU outside compilation computation to a separate tf_device.parallel_execute region.\";\n+\n+ let description=[{\n+ This pass extracts a CPU computation cluster with `_xla_outside_compilation `\n+ annotation,which denotes ops that should be run on CPU/host,from a TPU cluster.\n+ Each outside compilation cluster is moved to\n+ a tf_device.parallel_execute region.The TPU cluster is also moved to a\n+ tf_device.parallel_execute region.Communication ops between device and host are\n+ added to pass inputs/outputs to/from the outside compiled region.\n+\n+ For example,the following tf_device.cluster with an op marked for ` xla_outside_compilation `:\n+\n+ ` ` ` mlir\n+ func@outside_compilation()->tensor<f32>{\n+ %0=\"tf_device.cluster\"()({\n+ %1=\"tf.Const\"(){_xla_outside_compilation=\"0\",value=dense<1.0>:tensor<f32>}:()->(tensor<f32>)\n+ %2=\"tf.Identity\"(%1){_xla_outside_compilation=\"0\"}:(tensor<f32>)->(tensor<f32>)\n+ %3=\"tf.AddV2\"(%1,%2):(tensor<f32>,tensor<f32>)->(tensor<f32>)\n+ tf_device.return%3:tensor<f32>\n+ }){num_cores_per_replica=1,topology=\"\",device_assignment=[]}:()->tensor<f32>\n+ return%0:tensor<f32>\n+ }\n+ ` ` `\n+\n+ will become a tf_device.parallel_execute op with a CPU/host region and\n+ a tf_device.cluster with communication ops to send data to/from device/host:\n+\n+ ` ` ` mlir\n+ func@outside_compilation()->tensor<f32>{\n+ %0=\"tf_device.parallel_execute\"()({\n+ \"tf_device.launch\"()({\n+ %1=\"tf._TPUCompileMlirPlaceholderProgramKey\"():()->tensor<3x!tf.string>\n+ %2=\"tf._XlaRecvAtHost\"(%1){device_ordinal=0:i64,key=\"host_compute_channel_0_0_args\"}:(tensor<3x!tf.string>)->tensor<f32>\n+ %3=\"tf.Identity\"(%2):(tensor<f32>)->tensor<f32>\n+ \"tf._XlaSendFromHost\"(%3,%1){device_ordinal=0:i64,key=\"host_compute_channel_0_0_retvals\"}:(tensor<f32>,tensor<3x!tf.string>)->()\n+ tf_device.return\n+ }){device=\"/job:worker/replica:0/task:0/device:CPU:0\"}:()->()\n+ tf_device.return\n+ },{\n+ %1=\"tf_device.cluster\"()({\n+ %2=\"tf.Const\"(){value=dense<1.000000e+00>:tensor<f32>}:()->tensor<f32>\n+ %3=\"tf._XlaHostComputeMlir\"(%2){recv_key=\"host_compute_channel_0_0_retvals\",send_key=\"host_compute_channel_0_0_args\",tpu_core=0:i64}:(tensor<f32>)->tensor<f32>\n+ %4=\"tf.AddV2\"(%2,%3):(tensor<f32>,tensor<f32>)->tensor<f32>\n+ tf_device.return%4:tensor<f32>\n+ }){device_assignment=[],num_cores_per_replica=1:i64,topology=\"\"}:()->tensor<f32>\n+ tf_device.return%1:tensor<f32>\n+ }):()->tensor<f32>\n+ return%0:tensor<f32>\n+ }\n+ ` ` `\n+ }];\n+\n+ let constructor=\"TFTPU::CreateTPUExtractOutsideCompilationPass()\";\n+ }\nlimitations under the License.\n# include\"tensorflow/compiler/mlir/tensorflow/ir/tf_device.h\"\n# include\"tensorflow/compiler/mlir/tensorflow/ir/tf_ops.h\"\n# include\"tensorflow/compiler/mlir/tensorflow/transforms/passes.h\"\n+ # include\"tensorflow/compiler/mlir/tensorflow/transforms/passes_detail.h\"\n# include\"tensorflow/compiler/mlir/tensorflow/utils/device_util.h\"\n# include\"tensorflow/compiler/mlir/tensorflow/utils/tpu_rewrite_device_util.h\"\n\nconstexpr char kXlaOutsideCompilationAttr[]=\"_xla_outside_compilation\";\nusing OutsideClusterMap=\nllvm::SmallDenseMap<llvm::StringRef,llvm::SmallVector<Operation *,8>,8>;\n\n- //This pass extracts a CPU computation cluster with `_xla_outside_compilation `\n- //annotation from a TPU cluster.Each outside compilation cluster is moved to\n- //a parallel_execute region.The TPU cluster is also moved to a\n- //parallel_execute region.Communication ops between device and host are\n- //added to pass inputs/outputs to/from the outside compiled region.\n- //\n- //A simple example:\n- //\"tf_device.cluster\"()({\n- //\"tf.A\"()\n- //\"tf.B\"(){_xla_outside_compilation=\"cluster1\"}\n- //\"tf.C\"()\n- //tf_device.return\n- //}){num_cores_per_replica=1,topology=\"\",device_assignment=[]}\n- //\n- //Would become the following ops(unimportant attribute,type are omitted):\n- //\"tf_device.parallel_execute\"()({\n- //\"tf_device.launch\"()({\n- //\"tf.B()\n- //tf_device.return\n- //})\n- //tf_device.return\n- //},{\n- //\"tf_device.cluster\"({\n- //\"tf.A\"()\n- //\"tf.C\"()\n- //tf_device.return\n- //})\n- //tf_device.return\n- //})\n-\nstruct TPUExtractOutsideCompilation\n- :public PassWrapper<TPUExtractOutsideCompilation,\n- OperationPass<ModuleOp>>{\n+ :public TF::TPUExtractOutsideCompilationPassBase<\n+ TPUExtractOutsideCompilation>{\nvoid runOnOperation()override;\n};\n\nCreateTPUExtractOutsideCompilationPass(){\nreturn std::make_unique<TPUExtractOutsideCompilation>();\n}\n\n- static PassRegistration<TPUExtractOutsideCompilation>pass(\n- \"tf-tpu-extract-outside-compilation\",\n- \"Extracts TPU outside compilation to separate parallel_execute.\");\n-\n}//namespace TFTPU\n}//namespace mlir\n\n\npeft:108\tAdd TPUExtractOutsideCompilationPass to TPU pass pipeline.\n\n\nicl:108\tMerge pull request from alibaba/html5 - feature - dist\n\n\nslm:When building a TPU extract_outside_compilation pass, add the_xla_outside_compilation attribute to the pass itself.\n\n\n", "peft": "108\tAdd TPUExtractOutsideCompilationPass to TPU pass pipeline.\n", "icl": "108\tMerge pull request from alibaba/html5 - feature - dist\n", "slm": "When building a TPU extract_outside_compilation pass, add the_xla_outside_compilation attribute to the pass itself.\n", "nl": "Migrate TPUExtractOutsideCompilation conversion pass to use declarative pass registration instead of manually defined pass registration(NFC).", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16630, "text": "static grpc_error * pollset_as_multipollable_locked(grpc_pollset * pollset,\ncase PO_EMPTY:\nPOLLABLE_UNREF(pollset->active_pollable,\"pollset\");\nerror=pollable_create(PO_MULTI,&pollset->active_pollable);\n+ /* Any workers currently polling on this pollset must now be woked up so\n+ * that they can pick up the new active_pollable */\n+ if(grpc_polling_trace.enabled()){\n+ gpr_log(GPR_DEBUG,\n+ \"PS:%p active pollable transition from empty to multi\",\n+ pollset);\n+ }\n+ static const char * err_desc=\n+ \"pollset_as_multipollable_locked(%p):empty->multi\";\n+ append_error(&error,pollset_kick_all(pollset),err_desc);\nbreak;\ncase PO_FD:\ngpr_mu_lock(&po_at_start->owner_fd->orphan_mu);\n\n\npeft:1437\tFix a race condition in pollset_as_multipollable_locked() where a worker could be\n\n\nicl:1437\tc + + skiplist implementation\n\n\nslm:Merge pull request from markdroth/pollset_as_multipollable_locked\n\n\n", "peft": "1437\tFix a race condition in pollset_as_multipollable_locked() where a worker could be\n", "icl": "1437\tc + + skiplist implementation\n", "slm": "Merge pull request from markdroth/pollset_as_multipollable_locked\n", "nl": "Fix an issue in epollex that was causing some workers to get indefinitely stuck in pollset_work()", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16631, "text": "def sigmoid(x):\nthe result of the function gets close to 1.\n\nSigmoid is equivalent to a 2-element Softmax,where the second element is\n- assumed to be zero.The sigmoid function always returns a value between 0 and 1.\n+ assumed to be zero.The sigmoid function always returns a value between\n+ 0 and 1.\n\nFor example:\n\n\n\npeft:1596\tFix typo in sigmoid docstring.\n\n\nicl:1596\tFixed sigmoid function\n\n\nslm:Whitespace\n\n\n", "peft": "1596\tFix typo in sigmoid docstring.\n", "icl": "1596\tFixed sigmoid function\n", "slm": "Whitespace\n", "nl": "Update activations.py", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16632, "text": "void RunBinaryLaneOpTest(\n# endif//V8_TARGET_ARCH_ARM||V8_TARGET_ARCH_X64||V8_TARGET_ARCH_MIPS||\n//V8_TARGET_ARCH_MIPS64\n\n- # if V8_TARGET_ARCH_ARM||V8_TARGET_ARCH_X64\n+ # if V8_TARGET_ARCH_ARM||V8_TARGET_ARCH_ARM64||V8_TARGET_ARCH_X64\nWASM_SIMD_TEST(I32x4AddHoriz){\nRunBinaryLaneOpTest<int32_t>(kExprI32x4AddHoriz,{{1,5,9,13}});\n}\nWASM_SIMD_TEST(F32x4AddHoriz){\n}\n# endif//V8_TARGET_ARCH_ARM\n\n- # if V8_TARGET_ARCH_ARM||V8_TARGET_ARCH_MIPS||V8_TARGET_ARCH_MIPS64\n+ # if V8_TARGET_ARCH_ARM||V8_TARGET_ARCH_ARM64||V8_TARGET_ARCH_MIPS||\\\n+ V8_TARGET_ARCH_MIPS64\n//Test some regular shuffles that may have special handling on some targets.\n//Test a normal and unary versions(where second operand isn't used).\nWASM_SIMD_TEST(S32x4ZipLeft){\n\n\npeft:519\tMerge pull request from v8/wasm - simd - shuffle - test - fix - arm64 - simd - shuffle - test - fix - arm64\n\n\nicl:519\tAdd the neccessary scripts to build the other documentation media\n\n\nslm:[wasm]Fix 32x4AddHoriz on ARM64 target\n\n\n", "peft": "519\tMerge pull request from v8/wasm - simd - shuffle - test - fix - arm64 - simd - shuffle - test - fix - arm64\n", "icl": "519\tAdd the neccessary scripts to build the other documentation media\n", "slm": "[wasm]Fix 32x4AddHoriz on ARM64 target\n", "nl": "[arm64]Re - enable wasm tests.", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16633, "text": "ConstraintSystem::addKeyPathApplicationRootConstraint(Type root,ConstraintLocat\nauto subscript=dyn_cast_or_null<SubscriptExpr>(anchor);\nif(!subscript)\nreturn;\n-\n- assert(path.size()==1&&\n- path[0].getKind()==ConstraintLocator::SubscriptMember);\n+\n+ assert((path.size()==1&&\n+ path[0].getKind()==ConstraintLocator::SubscriptMember)||\n+ (path.size()==2&&\n+ path[1].getKind()==ConstraintLocator::KeyPathDynamicMember));\nauto indexTuple=dyn_cast<TupleExpr>(subscript->getIndex());\nif(!indexTuple||indexTuple->getNumElements()!=1)\nreturn;\nprotocol ProtocolWithWrapper{\nstruct UsesProtocolWithWrapper:ProtocolWithWrapper{\n@Wrapper var foo:Int//expected-warning{{ignoring associated type'Wrapper'in favor of module-scoped property wrapper'Wrapper';please qualify the reference with'property_wrappers'}}{{4-4=property_wrappers.}}\n}\n+\n+ //rdar://problem/56350060-[Dynamic key path member lookup]Assertion when subscripting with a key path\n+ func test_rdar56350060(){\n+ @propertyWrapper\n+ @dynamicMemberLookup\n+ struct DynamicWrapper<Value>{\n+ var wrappedValue:Value{fatalError()}\n+\n+ subscript<T>(keyPath keyPath:KeyPath<Value,T>)->DynamicWrapper<T>{\n+ fatalError()\n+ }\n+\n+ subscript<T>(dynamicMember keyPath:KeyPath<Value,T>)->DynamicWrapper<T>{\n+ return self[keyPath:keyPath]//Ok\n+ }\n+ }\n+ }\n\n\npeft:815\t[KeyPath]Fix crash when subscripting with a key path.\n\n\nicl:815\tMerge pull request from rintaro/lex - multiline - nlescape - last\n\n\nslm:Merge remote - tracking branch'origin/master'into master - next\n\n\n", "peft": "815\t[KeyPath]Fix crash when subscripting with a key path.\n", "icl": "815\tMerge pull request from rintaro/lex - multiline - nlescape - last\n", "slm": "Merge remote - tracking branch'origin/master'into master - next\n", "nl": "[ConstraintSystem]Adjust keypath subscript assert to account for dynamic member lookup", "lang": "cpp", "label": ["Doc&Code"], "Comments": []}
{"id": 16634, "text": "CCParticleFire * CCParticleFire::create()\nreturn pRet;\n}\n\n- CCParticleFire * CCParticleFire::create(const char * plistFile)\n- {\n- CCParticleFire * pRet=new CCParticleFire();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleFire * CCParticleFire::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleFire * pRet=new CCParticleFire();\nCCParticleFireworks * CCParticleFireworks::create()\nreturn pRet;\n}\n\n- CCParticleFireworks * CCParticleFireworks::create(const char * plistFile)\n- {\n- CCParticleFireworks * pRet=new CCParticleFireworks();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleFireworks * CCParticleFireworks::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleFireworks * pRet=new CCParticleFireworks();\nCCParticleSun * CCParticleSun::create()\nreturn pRet;\n}\n\n- CCParticleSun * CCParticleSun::create(const char * plistFile)\n- {\n- CCParticleSun * pRet=new CCParticleSun();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleSun * CCParticleSun::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleSun * pRet=new CCParticleSun();\nCCParticleGalaxy * CCParticleGalaxy::create()\nreturn pRet;\n}\n\n- CCParticleGalaxy * CCParticleGalaxy::create(const char * plistFile)\n- {\n- CCParticleGalaxy * pRet=new CCParticleGalaxy();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleGalaxy * CCParticleGalaxy::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleGalaxy * pRet=new CCParticleGalaxy();\nCCParticleFlower * CCParticleFlower::create()\nreturn pRet;\n}\n\n- CCParticleFlower * CCParticleFlower::create(const char * plistFile)\n- {\n- CCParticleFlower * pRet=new CCParticleFlower();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleFlower * CCParticleFlower::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleFlower * pRet=new CCParticleFlower();\nCCParticleMeteor * CCParticleMeteor::create()\nreturn pRet;\n}\n\n- CCParticleMeteor * CCParticleMeteor::create(const char * plistFile)\n- {\n- CCParticleMeteor * pRet=new CCParticleMeteor();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleMeteor * CCParticleMeteor::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleMeteor * pRet=new CCParticleMeteor();\nCCParticleSpiral * CCParticleSpiral::create()\nreturn pRet;\n}\n\n- CCParticleSpiral * CCParticleSpiral::create(const char * plistFile)\n- {\n- CCParticleSpiral * pRet=new CCParticleSpiral();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleSpiral * CCParticleSpiral::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleSpiral * pRet=new CCParticleSpiral();\nCCParticleExplosion * CCParticleExplosion::create()\nreturn pRet;\n}\n\n- CCParticleExplosion * CCParticleExplosion::create(const char * plistFile)\n- {\n- CCParticleExplosion * pRet=new CCParticleExplosion();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleExplosion * CCParticleExplosion::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleExplosion * pRet=new CCParticleExplosion();\nCCParticleSmoke * CCParticleSmoke::create()\nreturn pRet;\n}\n\n- CCParticleSmoke * CCParticleSmoke::create(const char * plistFile)\n- {\n- CCParticleSmoke * pRet=new CCParticleSmoke();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleSmoke * CCParticleSmoke::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleSmoke * pRet=new CCParticleSmoke();\nCCParticleSnow * CCParticleSnow::create()\nreturn pRet;\n}\n\n- CCParticleSnow * CCParticleSnow::create(const char * plistFile)\n- {\n- CCParticleSnow * pRet=new CCParticleSnow();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleSnow * CCParticleSnow::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleSnow * pRet=new CCParticleSnow();\nCCParticleRain * CCParticleRain::create()\nreturn pRet;\n}\n\n- CCParticleRain * CCParticleRain::create(const char * plistFile)\n- {\n- CCParticleRain * pRet=new CCParticleRain();\n- if(pRet&&pRet->initWithFile(plistFile))\n- {\n- pRet->autorelease();\n- }\n- else\n- {\n- CC_SAFE_DELETE(pRet);\n- }\n- return pRet;\n- }\n-\nCCParticleRain * CCParticleRain::createWithTotalParticles(unsigned int numberOfParticles)\n{\nCCParticleRain * pRet=new CCParticleRain();\nclass CC_DLL CCParticleFire:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleFire * node();\n\nstatic CCParticleFire * create();\n- static CCParticleFire * create(const char * plistFile);\nstatic CCParticleFire * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleFireworks:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleFireworks * node();\n\nstatic CCParticleFireworks * create();\n- static CCParticleFireworks * create(const char * plistFile);\nstatic CCParticleFireworks * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleSun:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleSun * node();\n\nstatic CCParticleSun * create();\n- static CCParticleSun * create(const char * plistFile);\nstatic CCParticleSun * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleGalaxy:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleGalaxy * node();\n\nstatic CCParticleGalaxy * create();\n- static CCParticleGalaxy * create(const char * plistFile);\nstatic CCParticleGalaxy * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleFlower:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleFlower * node();\n\nstatic CCParticleFlower * create();\n- static CCParticleFlower * create(const char * plistFile);\nstatic CCParticleFlower * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleMeteor:public CCParticleSystemQuad\nvirtual bool initWithTotalParticles(unsigned int numberOfParticles);\nCC_DEPRECATED_ATTRIBUTE static CCParticleMeteor * node();\nstatic CCParticleMeteor * create();\n- static CCParticleMeteor * create(const char * plistFile);\nstatic CCParticleMeteor * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleSpiral:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleSpiral * node();\n\nstatic CCParticleSpiral * create();\n- static CCParticleSpiral * create(const char * plistFile);\nstatic CCParticleSpiral * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleExplosion:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleExplosion * node();\n\nstatic CCParticleExplosion * create();\n- static CCParticleExplosion * create(const char * plistFile);\nstatic CCParticleExplosion * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleSmoke:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleSmoke * node();\n\nstatic CCParticleSmoke * create();\n- static CCParticleSmoke * create(const char * plistFile);\nstatic CCParticleSmoke * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleSnow:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleSnow * node();\n\nstatic CCParticleSnow * create();\n- static CCParticleSnow * create(const char * plistFile);\nstatic CCParticleSnow * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\nclass CC_DLL CCParticleRain:public CCParticleSystemQuad\nCC_DEPRECATED_ATTRIBUTE static CCParticleRain * node();\n\nstatic CCParticleRain * create();\n- static CCParticleRain * create(const char * plistFile);\nstatic CCParticleRain * createWithTotalParticles(unsigned int numberOfParticles);\n};\n\n\n\npeft:1803\tMerge pull request from cocos2d/cocos2d - v3 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2\n\n\nicl:1803\tAdd more strided R1 tests.\n\n\nslm:Merge pull request from minggo/master\n\n\n", "peft": "1803\tMerge pull request from cocos2d/cocos2d - v3 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2 - 0 - 1 - 2\n", "icl": "1803\tAdd more strided R1 tests.\n", "slm": "Merge pull request from minggo/master\n", "nl": "fixed:Removed CCParticleFire::create(const char * pListFile)since particle can be constructed from plist by CCParticleSystemQuad::create(plist).", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16635, "text": "std::vector<std::string>Glob(const std::string&pattern){\nbool CopyFile(const std::string&from,const std::string&to){\nstd::ifstream src(from,std::ios::binary);\nif(!src){\n- AERROR<<\"Source path doesn't exist:\"<<from;\n- return false;\n+ AWARN<<\"Source path could not be normally opened:\"<<from;\n+ std::string command=\"cp-r\"+from+\"\"+to;\n+ ADEBUG<<command;\n+ const int ret=std::system(command.c_str());\n+ if(ret==0){\n+ ADEBUG<<\"Copy success,command returns\"<<ret;\n+ return true;\n+ }else{\n+ ADEBUG<<\"Copy error,command returns\"<<ret;\n+ return false;\n+ }\n}\n\nstd::ofstream dst(to,std::ios::binary);\n\n\npeft:1605\tfix the bug of CopyFile()\n\n\nicl:1605\tFix CopyFile\n\n\nslm:chore:improve error message, when copy fails\n\n\n", "peft": "1605\tfix the bug of CopyFile()\n", "icl": "1605\tFix CopyFile\n", "slm": "chore:improve error message, when copy fails\n", "nl": "cyber:support to copy softlink file", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16636, "text": "\n# Bazel installation\n\n# Must be in sync with tools/bazel\n- ENV BAZEL_VERSION 0.29.0\n+ ENV BAZEL_VERSION 0.28.1\n\n# The correct bazel version is already preinstalled,no need to use//tools/bazel wrapper.\nENV DISABLE_BAZEL_WRAPPER 1\nthen\nexec-a\"$0\"\"${BAZEL_REAL}\"\"$@\"\nfi\n\n- VERSION=0.29.0\n+ VERSION=0.28.1\n\necho\"INFO:Running bazel wrapper(see//tools/bazel for details),bazel version$VERSION will be used instead of system-wide bazel installation.\"\n\nRUN pip install futures==2.2.0 enum34==1.0.4 protobuf==3.5.2.post1 six==1.10.0 t\n# Bazel installation\n\n# Must be in sync with tools/bazel\n- ENV BAZEL_VERSION 0.29.0\n+ ENV BAZEL_VERSION 0.28.1\n\n# The correct bazel version is already preinstalled,no need to use//tools/bazel wrapper.\nENV DISABLE_BAZEL_WRAPPER 1\nENV CLANG_TIDY=clang-tidy\n# Bazel installation\n\n# Must be in sync with tools/bazel\n- ENV BAZEL_VERSION 0.29.0\n+ ENV BAZEL_VERSION 0.28.1\n\n# The correct bazel version is already preinstalled,no need to use//tools/bazel wrapper.\nENV DISABLE_BAZEL_WRAPPER 1\n\n\n@rem TODO(jtattermusch):make this generate less output\n@rem TODO(jtattermusch):use tools/bazel script to keep the versions in sync\n- choco install bazel-y--version 0.29.0--limit-output\n+ choco install bazel-y--version 0.26.0--limit-output\n\ncd github/grpc\nset PATH=C:\\tools\\msys64\\usr\\bin;C:\\Python27;%PATH%\npowershell-Command\"[guid]::NewGuid().ToString()\">%KOKORO_ARTIFACTS_DIR%/bazel\nset/p BAZEL_INVOCATION_ID=<%KOKORO_ARTIFACTS_DIR%/bazel_invocation_ids\n\n@rem TODO(jtattermusch):windows RBE should be able to use the same credentials as Linux RBE.\n- bazel--bazelrc=tools/remote_build/windows.bazelrc build--invocation_id=\"%BAZEL_INVOCATION_ID%\"--workspace_status_command=tools/remote_build/workspace_status_kokoro.sh:all--google_credentials=%KOKORO_GFILE_DIR%/rbe-windows-credentials.json\n+ bazel--bazelrc=tools/remote_build/windows.bazelrc build--invocation_id=\"%BAZEL_INVOCATION_ID%\"--workspace_status_command=tools/remote_build/workspace_status_kokoro.sh:all--incompatible_disallow_filetype=false--google_credentials=%KOKORO_GFILE_DIR%/rbe-windows-credentials.json\nset BAZEL_EXITCODE=%errorlevel%\n\n@rem TODO(jtattermusch):upload results to bigquery\n\nstartup--host_jvm_args=-Dbazel.DigestFunction=SHA256\n\n- build--remote_cache=grpcs://remotebuildexecution.googleapis.com\n- build--remote_executor=grpcs://remotebuildexecution.googleapis.com\n+ build--remote_cache=remotebuildexecution.googleapis.com\n+ build--remote_executor=remotebuildexecution.googleapis.com\n+ build--tls_enabled=true\n\nbuild--host_crosstool_top=//third_party/toolchains/bazel_0.26.0_rbe_windows:toolchain\nbuild--crosstool_top=//third_party/toolchains/bazel_0.26.0_rbe_windows:toolchain\ntest--test_env=GRPC_VERBOSITY=debug\n\n# Set flags for uploading to BES in order to view results in the Bazel Build\n# Results UI.\n- build--bes_backend=grpcs://buildeventservice.googleapis.com\n+ build--bes_backend=\"buildeventservice.googleapis.com\"\nbuild--bes_timeout=60s\nbuild--bes_results_url=\"https://source.cloud.google.com/results/invocations/\"\nbuild--project_id=grpc-testing\n\n\npeft:1944\tUpdate Bazel version to 0.28.1 in Windows CI.\n\n\nicl:1944\tAdded bazel 0.28.1 to the image.\n\n\nslm:use bazel 0.29.1 instead of 0.29.0\n\n\n", "peft": "1944\tUpdate Bazel version to 0.28.1 in Windows CI.\n", "icl": "1944\tAdded bazel 0.28.1 to the image.\n", "slm": "use bazel 0.29.1 instead of 0.29.0\n", "nl": "Merge pull request from grpc/revert - 20165 - windows_bazel_rbe_0_29", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16637, "text": "void MainWindow::showConnectionSettings()\n\nvoid MainWindow::minimizeWindow()\n{\n- setWindowState(windowState()^Qt::WindowMinimized);\n+ setWindowState(windowState()|Qt::WindowMinimized);\n}\n\nvoid MainWindow::on_actionExecutionLogs_triggered(bool checked)\n\n\npeft:464\tFix minimize window action.\n\n\nicl:464\t[test]Update autolinking tests to match LLVM.\n\n\nslm:Fix overflow in execution logs window\n\n\n", "peft": "464\tFix minimize window action.\n", "icl": "464\t[test]Update autolinking tests to match LLVM.\n", "slm": "Fix overflow in execution logs window\n", "nl": "The intended action is set the bit to 1, not flipping it.", "lang": "cpp", "label": ["Feat-mod"], "Comments": []}
{"id": 16638, "text": "\n# include\"src/objects/dictionary.h\"\n# include\"src/objects/literal-objects-inl.h\"\n# include\"src/objects/map.h\"\n+ # include\"src/objects/microtask-queue.h\"\n# include\"src/objects/microtask.h\"\n# include\"src/objects/module.h\"\n# include\"src/objects/promise.h\"\n\n# include\"src/objects/literal-objects-inl.h\"\n# include\"src/objects/maybe-object.h\"\n# include\"src/objects/microtask-inl.h\"\n+ # include\"src/objects/microtask-queue-inl.h\"\n# include\"src/objects/module-inl.h\"\n# include\"src/objects/promise-inl.h\"\n# include\"src/ostreams.h\"\n\n# include\"src/objects/js-proxy-inl.h\"\n# include\"src/objects/literal-objects.h\"\n# include\"src/objects/maybe-object-inl.h\"\n- # include\"src/objects/microtask-queue-inl.h\"\n# include\"src/objects/regexp-match-info.h\"\n# include\"src/objects/scope-info.h\"\n# include\"src/objects/template-objects.h\"\n\n# endif//V8_INTL_SUPPORT\n# include\"src/objects/literal-objects-inl.h\"\n# include\"src/objects/microtask-inl.h\"\n+ # include\"src/objects/microtask-queue-inl.h\"\n# include\"src/objects/module-inl.h\"\n# include\"src/objects/promise-inl.h\"\n# include\"src/ostreams.h\"\n\n# include\"src/objects/literal-objects-inl.h\"\n# include\"src/objects/map.h\"\n# include\"src/objects/microtask-inl.h\"\n+ # include\"src/objects/microtask-queue-inl.h\"\n# include\"src/objects/module-inl.h\"\n# include\"src/objects/promise-inl.h\"\n# include\"src/parsing/preparsed-scope-data.h\"\n\n//Use of this source code is governed by a BSD-style license that can be\n//found in the LICENSE file.\n\n- # include\"src/objects/microtask-queue.h\"\n+ # include\"src/objects/microtask-queue-inl.h\"\n\n# include\"test/unittests/test-utils.h\"\n\n\n\npeft:1707\t[runtime]Add missing include for microtask-queue-inl.h.\n\n\nicl:1707\tuse patch method of httparty\n\n\nslm:[intl]Extend intl source includes.\n\n\n", "peft": "1707\t[runtime]Add missing include for microtask-queue-inl.h.\n", "icl": "1707\tuse patch method of httparty\n", "slm": "[intl]Extend intl source includes.\n", "nl": "[iwyu]Don't include microtask - queue - inl.h from objects - inl.h", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16639, "text": "Load Balancing\n\noverview\nload_balancers\n- original_dst\n- overprovisioning\npriority\n- zone_aware\n- panic_threshold\nlocality_weight\n+ overprovisioning\n+ panic_threshold\n+ original_dst\n+ zone_aware\nsubsets\n\n\npeft:1292\tAdded overprovisioning and panic_threshold to the priority load balancer.\n\n\nicl:1292\tAdded locality_weight to priority load balancer\n\n\nslm:Update the master\n\n\n", "peft": "1292\tAdded overprovisioning and panic_threshold to the priority load balancer.\n", "icl": "1292\tAdded locality_weight to priority load balancer\n", "slm": "Update the master\n", "nl": "docs:reorder lb docs()", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16640, "text": "private func_stringCompareSlow(\nprivate func_findDiffIdx(\n_left:UnsafeBufferPointer<UInt8>,_right:UnsafeBufferPointer<UInt8>\n)->Int?{\n- var count=Swift.min(left.count,right.count)\n- for idx in 0..<count{\n+ let count=Swift.min(left.count,right.count)\n+ var idx=0\n+ while idx<count{\nguard left[_unchecked:idx]==right[_unchecked:idx]else{\nreturn idx\n}\n+ idx&+=1\n}\nreturn nil\n}\n\n\npeft:823\tFix the infinite loop in the slow path of string comparison.\n\n\nicl:823\tMerge pull request from rintaro/lex - multiline - nlescape - last\n\n\nslm:[stdlib]Fix Array.prototype.indexOf with>=1\n\n\n", "peft": "823\tFix the infinite loop in the slow path of string comparison.\n", "icl": "823\tMerge pull request from rintaro/lex - multiline - nlescape - last\n", "slm": "[stdlib]Fix Array.prototype.indexOf with>=1\n", "nl": "[String]Hand - increment loop variable for perf.", "lang": "cpp", "label": ["Ref-con"], "Comments": []}
{"id": 16641, "text": "typedef struct{\nMonoClass * interfaces[MONO_ZERO_LEN_ARRAY];\n}MonoRemoteClass;\n\n+ # define MONO_SIZEOF_REMOTE_CLASS(sizeof(MonoRemoteClass)-MONO_ZERO_LEN_ARRAY * SIZEOF_VOID_P)\n+\nMonoClass *\nmono_class_get(MonoImage * image,guint32 type_token);\n\nmono_remote_class(MonoDomain * domain,MonoString * class_name,MonoClass * proxy_\nkey=mp_key;\n\nif(proxy_class->flags&TYPE_ATTRIBUTE_INTERFACE){\n- rc=mono_domain_alloc(domain,sizeof(MonoRemoteClass)+sizeof(MonoClass *));\n+ rc=mono_domain_alloc(domain,MONO_SIZEOF_REMOTE_CLASS+sizeof(MonoClass *));\nrc->interface_count=1;\nrc->interfaces[0]=proxy_class;\nrc->proxy_class=mono_defaults.marshalbyrefobject_class;\n}else{\n- rc=mono_domain_alloc(domain,sizeof(MonoRemoteClass));\n+ rc=mono_domain_alloc(domain,MONO_SIZEOF_REMOTE_CLASS);\nrc->interface_count=0;\nrc->proxy_class=proxy_class;\n}\nclone_remote_class(MonoDomain * domain,MonoRemoteClass * remote_class,MonoClass\n\nif(extra_class->flags&TYPE_ATTRIBUTE_INTERFACE){\nint i,j;\n- rc=mono_domain_alloc(domain,sizeof(MonoRemoteClass)+sizeof(MonoClass *)*(remote_class->interface_count+1));\n+ rc=mono_domain_alloc(domain,MONO_SIZEOF_REMOTE_CLASS+sizeof(MonoClass *)*(remote_class->interface_count+1));\nrc->proxy_class=remote_class->proxy_class;\nrc->interface_count=remote_class->interface_count+1;\n\nclone_remote_class(MonoDomain * domain,MonoRemoteClass * remote_class,MonoClass\nrc->interfaces[j]=extra_class;\n}else{\n//Replace the old class.The interface array is the same\n- rc=mono_domain_alloc(domain,sizeof(MonoRemoteClass)+sizeof(MonoClass *)* remote_class->interface_count);\n+ rc=mono_domain_alloc(domain,MONO_SIZEOF_REMOTE_CLASS+sizeof(MonoClass *)* remote_class->interface_count);\nrc->proxy_class=extra_class;\nrc->interface_count=remote_class->interface_count;\nif(rc->interface_count>0)\n\n\npeft:153\t2005 - 03 - 23 Zoltan Varga\n\n\nicl:153\tMoved ensure_directory_exists to eglib.\n\n\nslm:Use MONO_SIZEOF_REMOTE_CLASS for sizeof(MonoRemoteClass)-1\n\n\n", "peft": "153\t2005 - 03 - 23 Zoltan Varga\n", "icl": "153\tMoved ensure_directory_exists to eglib.\n", "slm": "Use MONO_SIZEOF_REMOTE_CLASS for sizeof(MonoRemoteClass)-1\n", "nl": "2009 - 08 - 11 Gonzalo Paniagua Javier<<EMAIL>>", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16642, "text": "public static bool MemberIsHidden(MemberReference member)\nMethodDefinition method=member as MethodDefinition;\nif(method!=null&&(method.IsGetter||method.IsSetter||method.IsAddOn||method.IsRemoveOn))\nreturn true;\n+ if(method!=null&&method.Name.StartsWith(\"<\",StringComparison.Ordinal)&&method.IsCompilerGenerated())\n+ return true;\nTypeDefinition type=member as TypeDefinition;\nif(type!=null&&type.DeclaringType!=null&&type.Name.StartsWith(\"<>c__DisplayClass\",StringComparison.Ordinal)&&type.IsCompilerGenerated())\nreturn true;\n+ FieldDefinition field=member as FieldDefinition;\n+ if(field!=null&&field.Name.StartsWith(\"CS$<>\",StringComparison.Ordinal)&&field.IsCompilerGenerated())\n+ return true;\nreturn false;\n}\n\nvoid AddTypeMembers(TypeDeclaration astType,TypeDefinition typeDef)\n{\n//Add fields\nforeach(FieldDefinition fieldDef in typeDef.Fields){\n+ if(MemberIsHidden(fieldDef))continue;\nastType.AddChild(CreateField(fieldDef),TypeDeclaration.MemberRole);\n}\n\npublic void Run(AstNode compilationUnit)\n{\nTransformUsings(compilationUnit);\nTransformForeach(compilationUnit);\n+ TransformFor(compilationUnit);\n}\n\n- # region using\n- static readonly AstNode usingVarDeclPattern=new VariableDeclarationStatement{\n+ ///<summary>\n+ ///$type$variable=$initializer;\n+ ///</summary>\n+ static readonly AstNode variableDeclPattern=new VariableDeclarationStatement{\nType=new AnyNode(\"type\").ToType(),\nVariables={\nnew NamedNode(\n\"variable\",\nnew VariableInitializer{\n- Initializer=new AnyNode().ToExpression()\n+ Initializer=new AnyNode(\"initializer\").ToExpression()\n}\n).ToVariable()\n}\n};\n+\n+ ///<summary>\n+ ///Variable declaration without initializer.\n+ ///</summary>\nstatic readonly AstNode simpleVariableDefinition=new VariableDeclarationStatement{\nType=new AnyNode().ToType(),\nVariables={\nnew VariableInitializer()//any name but no initializer\n}\n};\n+\n+ # region using\nstatic readonly AstNode usingTryCatchPattern=new TryCatchStatement{\nTryBlock=new AnyNode(\"body\").ToBlock(),\nFinallyBlock=new BlockStatement{\npublic void Run(AstNode compilationUnit)\npublic void TransformUsings(AstNode compilationUnit)\n{\nforeach(AstNode node in compilationUnit.Descendants.ToArray()){\n- Match m1=usingVarDeclPattern.Match(node);\n+ Match m1=variableDeclPattern.Match(node);\nif(m1==null)continue;\nAstNode tryCatch=node.NextSibling;\nwhile(simpleVariableDefinition.Match(tryCatch)!=null)\npublic void TransformForeach(AstNode compilationUnit)\n}\n}\n# endregion\n+\n+ # region for\n+ WhileStatement forPattern=new WhileStatement{\n+ Condition=new BinaryOperatorExpression{\n+ Left=new NamedNode(\"ident\",new IdentifierExpression()).ToExpression(),\n+ Operator=BinaryOperatorType.Any,\n+ Right=new AnyNode(\"endExpr\").ToExpression()\n+ },\n+ EmbeddedStatement=new BlockStatement{\n+ new Repeat(new AnyNode(\"statement\")).ToStatement(),\n+ new NamedNode(\n+ \"increment\",\n+ new ExpressionStatement(\n+ new AssignmentExpression{\n+ Left=new Backreference(\"ident\").ToExpression(),\n+ Operator=AssignmentOperatorType.Any,\n+ Right=new AnyNode().ToExpression()\n+ })).ToStatement(),\n+ new ContinueStatement()\n+ }\n+ };\n+\n+ public void TransformFor(AstNode compilationUnit)\n+ {\n+ foreach(AstNode node in compilationUnit.Descendants.ToArray()){\n+ Match m1=variableDeclPattern.Match(node);\n+ if(m1==null)continue;\n+ AstNode next=node.NextSibling;\n+ while(simpleVariableDefinition.Match(next)!=null)\n+ next=next.NextSibling;\n+ Match m2=forPattern.Match(next);\n+ if(m2==null)continue;\n+ //ensure the variable in the for pattern is the same as in the declaration\n+ if(m1.Get<VariableInitializer>(\"variable\").Single().Name!=m2.Get<IdentifierExpression>(\"ident\").Single().Identifier)\n+ continue;\n+ WhileStatement loop=(WhileStatement)next;\n+ node.Remove();\n+ BlockStatement newBody=new BlockStatement();\n+ foreach(Statement stmt in m2.Get<Statement>(\"statement\"))\n+ newBody.Add(stmt.Detach());\n+ loop.ReplaceWith(\n+ new ForStatement{\n+ Initializers={(VariableDeclarationStatement)node},\n+ Condition=loop.Condition.Detach(),\n+ Iterators={m2.Get<Statement>(\"increment\").Single().Detach()},\n+ EmbeddedStatement=newBody\n+ });\n+ }\n+ }\n+ # endregion\n}\n}\n\n\npeft:552\tMerge pull request from icsharpcode/fix - for - loop - transformer\n\n\nicl:552\tatchPattern=new TryCatchStatement{\n\n\nslm:Fixes for compiler generated types\n\n\n", "peft": "552\tMerge pull request from icsharpcode/fix - for - loop - transformer\n", "icl": "552\tatchPattern=new TryCatchStatement{\n", "slm": "Fixes for compiler generated types\n", "nl": "Add pattern for\"for\"loops.Closes.", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16643, "text": "protected virtual NSToolbar ConfigureToolbar()\nAllowsUserCustomization=false,\nShowsBaselineSeparator=true,\nSizeMode=NSToolbarSizeMode.Regular,\n- Delegate=this,\n- CenteredItemIdentifier=TitleGroupIdentifier\n+ Delegate=this\n};\n\n+ if(Forms.IsMojaveOrNewer)\n+ toolbar.CenteredItemIdentifier=TitleGroupIdentifier;\n+\nreturn toolbar;\n}\n\npublic static class Forms\nstatic bool?s_isiOS11OrNewer;\nstatic bool?s_isiOS13OrNewer;\nstatic bool?s_respondsTosetNeedsUpdateOfHomeIndicatorAutoHidden;\n- # endif\n\n- # if__MOBILE__\ninternal static bool IsiOS9OrNewer\n{\nget\ninternal static bool RespondsToSetNeedsUpdateOfHomeIndicatorAutoHidden\nreturn s_respondsTosetNeedsUpdateOfHomeIndicatorAutoHidden.Value;\n}\n}\n+ # else\n+ static bool?s_isMojaveOrNewer;\n+\n+ internal static bool IsMojaveOrNewer\n+ {\n+ get\n+ {\n+ if(!s_isMojaveOrNewer.HasValue)\n+ s_isMojaveOrNewer=NSProcessInfo.ProcessInfo.IsOperatingSystemAtLeastVersion(new NSOperatingSystemVersion(10,14,0));\n+ return s_isMojaveOrNewer.Value;\n+ }\n+ }\n+\n# endif\n\nstatic IReadOnlyList<string>s_flags;\n\n\npeft:48\tFixes a crash on macOS 10.14 Mojave.\n\n\nicl:48\tMerge pull request from khellang/\n\n\nslm:Add Mojave check to Toolbar()\n\n\n", "peft": "48\tFixes a crash on macOS 10.14 Mojave.\n", "icl": "48\tMerge pull request from khellang/\n", "slm": "Add Mojave check to Toolbar()\n", "nl": "Implement fix()", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16644, "text": "@@-1+1,2@@\n/.config\n+ Test/test-config-file-*\n\n\npeft:1969\tAdded test-config-file-* to the list of ignored files.\n\n\nicl:1969\tAdded test config files\n\n\nslm:Updating submodule(s)\n\n\n", "peft": "1969\tAdded test-config-file-* to the list of ignored files.\n", "icl": "1969\tAdded test config files\n", "slm": "Updating submodule(s)\n", "nl": "[xbuild]Ignore test - config - file - *", "lang": "csharp", "label": ["Doc-mod"], "Comments": []}
{"id": 16645, "text": "namespace MahApps.Metro.Controls\n[TemplatePart(Name=\"PART_Header\",Type=typeof(ContentPresenter))]\npublic class Flyout:ContentControl\n{\n+\n+ public event EventHandler IsOpenChanged;\n+\npublic static readonly DependencyProperty HeaderProperty=DependencyProperty.Register(\"Header\",typeof(string),typeof(Flyout),new PropertyMetadata(default(string)));\npublic static readonly DependencyProperty PositionProperty=DependencyProperty.Register(\"Position\",typeof(Position),typeof(Flyout),new PropertyMetadata(Position.Left,PositionChanged));\npublic static readonly DependencyProperty IsPinnableProperty=DependencyProperty.Register(\"IsPinnable\",typeof(bool),typeof(Flyout),new PropertyMetadata(default(bool)));\nprivate static void IsOpenedChanged(DependencyObject dependencyObject,Dependenc\n{\nvar flyout=(Flyout)dependencyObject;\nVisualStateManager.GoToState(flyout,(bool)e.NewValue==false?\"Hide\":\"Show\",true);\n+ if(flyout.IsOpenChanged!=null)\n+ {\n+ flyout.IsOpenChanged(flyout,EventArgs.Empty);\n+ }\n}\n\nprivate static void PositionChanged(DependencyObject dependencyObject,DependencyPropertyChangedEventArgs e)\n\n\npeft:409\tFix IsOpenChanged event not firing.\n\n\nicl:409\tAdded event IsOpenChanged to Flyout\n\n\nslm:Make Flyout.IsOpenChanged event handler.\n\n\n", "peft": "409\tFix IsOpenChanged event not firing.\n", "icl": "409\tAdded event IsOpenChanged to Flyout\n", "slm": "Make Flyout.IsOpenChanged event handler.\n", "nl": "Add a event IsOpenChanged to Flyout", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16646, "text": "new file mode 100644\nindex **********0..b16d524eb11\nBinary files/dev/null and b/mods/ts/bits/typeglyphs.shp differ\n\nCategories:Infantry\nGrantConditionOnTunnelLayer:\nCondition:inside-tunnel\n+ WithDecoration@UNDERGROUND:\n+ RequiresCondition:inside-tunnel\n+ Position:Center\n+ Image:typeglyphs\n+ Sequence:infantry\n+ Palette:player\n+ IsPlayerPalette:true\n\n^RegularInfantryDeath:\nWithDeathAnimation@normal:\n\nCategories:Vehicle\nGrantConditionOnTunnelLayer:\nCondition:inside-tunnel\n+ WithDecoration@UNDERGROUND:\n+ RequiresCondition:inside-tunnel\n+ Position:Center\n+ Image:typeglyphs\n+ Sequence:vehicle\n+ Palette:player\n+ IsPlayerPalette:true\n\n^Tank:\nInherits:^Vehicle\nSAPC:\nSubterraneanTransitionSound:subdril1.aud\nCarryable:\nRequiresCondition:!submerged\n+ WithDecoration@UNDERGROUND:\n+ RequiresCondition:inside-tunnel||submerged\n\nSUBTANK:\nInherits:^Tank\nSUBTANK:\nSubterraneanTransitionSound:subdril1.aud\nCarryable:\nRequiresCondition:!submerged\n+ WithDecoration@UNDERGROUND:\n+ RequiresCondition:inside-tunnel||submerged\n\nSTNK:\nInherits:^Tank\nHARV:\nResourceSequences:\nTiberium:pip-green\nBlueTiberium:pip-blue\n+ WithDecoration@UNDERGROUND:\n+ Sequence:harvester\n\nLPST:\nInherits:^Tank\ndig:\nLength:*\nZOffset:511\nOffset:0,0,24\n+\n+ typeglyphs:\n+ infantry:\n+ vehicle:\n+ Start:1\n+ helicopter:\n+ Start:2\n+ harvester:\n+ Start:3\n+ structure:\n+ Start:4\n\n\npeft:1315\tAdded typeglyphs for underground units.\n\n\nicl:1315\tAdded typeglyphs.shp\n\n\nslm:Add sequence annotations to infantry deployment\n\n\n", "peft": "1315\tAdded typeglyphs for underground units.\n", "icl": "1315\tAdded typeglyphs.shp\n", "slm": "Add sequence annotations to infantry deployment\n", "nl": "Add a decoration glyph for friendly units in tunnels.", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16647, "text": "deploy:\nartifact:/.*\\.zip/\ndraft:true\non:\n- branch:master\nappveyor_repo_tag:true\n\n- provider:NuGet\napi_key:\nsecure:8BQmcR1hP9MoxvrmXJKMtlRyX6LI36H9ZTU8fSEetvzMGCl1PUlXKZRUGq+GVMja\non:\n- branch:master\nappveyor_repo_tag:true\n\n\npeft:1986\tUpdate appveyor.yml.\n\n\nicl:1986\t(maint)update tab expansion\n\n\nslm:Remove branch:master from.travis.yml\n\n\n", "peft": "1986\tUpdate appveyor.yml.\n", "icl": "1986\t(maint)update tab expansion\n", "slm": "Remove branch:master from.travis.yml\n", "nl": "Allow AppVeyor deployments on any branch", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16648, "text": "ServerTraits:\n\nLobbyDefaults:\nStartingUnitsClass:light\n- FragileAlliances:false\nTechLevel:Unrestricted\n\nChromeMetrics:\nServerTraits:\n\nLobbyDefaults:\nStartingUnitsClass:none\n- FragileAlliances:false\nTechLevel:Unrestricted\n\nChromeMetrics:\nServerTraits:\n\nLobbyDefaults:\nStartingUnitsClass:none\n- FragileAlliances:false\nTechLevel:Unrestricted\n\nChromeMetrics:\nServerTraits:\n\nLobbyDefaults:\nStartingUnitsClass:none\n- FragileAlliances:false\n\nChromeMetrics:\ncommon|metrics.yaml\n\n\npeft:1788\tFixes a bug where the fragile alliances setting was not being applied to the lobby.\n\n\nicl:1788\t(maint)update tab expansion\n\n\nslm:Remove FragileAlliances as it's no longer needed\n\n\n", "peft": "1788\tFixes a bug where the fragile alliances setting was not being applied to the lobby.\n", "icl": "1788\t(maint)update tab expansion\n", "slm": "Remove FragileAlliances as it's no longer needed\n", "nl": "Remove unused FragileAlliances default.", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16649, "text": "protected override void Update()\n\nplayButton.Icon=track.IsRunning?FontAwesome.fa_pause_circle_o:FontAwesome.fa_play_circle_o;\n\n- if(track.HasCompleted&&!beatmapBacking.Disabled&&playlist.BeatmapSets.Any())\n+ if(track.HasCompleted&&!track.Looping&&!beatmapBacking.Disabled&&playlist.BeatmapSets.Any())\nnext();\n}\nelse\n\n\npeft:1024\tFixes a bug where the play button would not change to a pause button when the song has completed.\n\n\nicl:1024\tFix a bug that causes the player to loop when the song ends\n\n\nslm:Fix incorrect handling of completed tracks\n\n\n", "peft": "1024\tFixes a bug where the play button would not change to a pause button when the song has completed.\n", "icl": "1024\tFix a bug that causes the player to loop when the song ends\n", "slm": "Fix incorrect handling of completed tracks\n", "nl": "fix MusicController ignoring Looping property of a Track", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16650, "text": "\ntv-search:[q,season,ep]\nmovie-search:[q]\n\n+ settings:\n+ -name:username\n+ type:text\n+ label:Username\n+ -name:password\n+ type:password\n+ label:Password\n+ -name:info_language\n+ type:info\n+ label:\"Languages\"\n+ default:\"This Indexer only works if your Vizuk account is set for the<b>Spanish</b>language.Using another language will not return results.\"\n+\nlogin:\npath:ajax/login.php\nmethod:post\n\n\npeft:426\tAdded settings for username and password.Added info message for language.\n\n\nicl:426\tAdded login page\n\n\nslm:Add localization\n\n\n", "peft": "426\tAdded settings for username and password.Added info message for language.\n", "icl": "426\tAdded login page\n", "slm": "Add localization\n", "nl": "vizuk:add note about Spanish.", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16651, "text": "private Task<Stream>InternalGetRequestStream()\n{\nCheckAbort();\n\n- if(RequestSubmitted)\n- {\n- throw new InvalidOperationException(SR.net_reqsubmitted);\n- }\n-\n//Match Desktop behavior:prevent someone from getting a request stream\n//if the protocol verb/method doesn't support it.Note that this is not\n//entirely compliant RFC2616 for the aforementioned compatibility reasons.\nprivate Task<Stream>InternalGetRequestStream()\nthrow new ProtocolViolationException(SR.net_nouploadonget);\n}\n\n+ if(RequestSubmitted)\n+ {\n+ throw new InvalidOperationException(SR.net_reqsubmitted);\n+ }\n+\n_requestStream=new RequestStream();\n\nreturn Task.FromResult((Stream)_requestStream);\npublic override IAsyncResult BeginGetRequestStream(AsyncCallback callback,Objec\n}\n\n_requestStreamCallback=callback;\n- _requestStreamOperation=GetRequestStreamTask().ToApm(callback,state);\n+ _requestStreamOperation=InternalGetRequestStream().ToApm(callback,state);\n\nreturn_requestStreamOperation.Task;\n}\npublic override Stream EndGetRequestStream(IAsyncResult asyncResult)\nreturn stream;\n}\n\n- private Task<Stream>GetRequestStreamTask()\n- {\n- CheckAbort();\n-\n- if(RequestSubmitted)\n- {\n- throw new InvalidOperationException(SR.net_reqsubmitted);\n- }\n-\n- //Match Desktop behavior:prevent someone from getting a request stream\n- //if the protocol verb/method doesn't support it.Note that this is not\n- //entirely compliant RFC2616 for the aforementioned compatibility reasons.\n- if(string.Equals(HttpMethod.Get.Method,_originVerb,StringComparison.OrdinalIgnoreCase)||\n- string.Equals(HttpMethod.Head.Method,_originVerb,StringComparison.OrdinalIgnoreCase)||\n- string.Equals(\"CONNECT\",_originVerb,StringComparison.OrdinalIgnoreCase))\n- {\n- throw new ProtocolViolationException(SR.net_nouploadonget);\n- }\n-\n- _requestStream=new RequestStream();\n-\n- return Task.FromResult((Stream)_requestStream);\n- }\n-\nprivate async Task<WebResponse>SendRequest()\n{\nif(RequestSubmitted)\npublic void BeginGetRequestStream_CreatePostRequestThenCallTwice_ThrowsInvalidOp\n}\n\n[Theory,MemberData(nameof(EchoServers))]\n- [SkipOnTargetFramework(TargetFrameworkMonikers.NetFramework,\"request stream not allowed for GET on netfx\")]\n- public void BeginGetRequestStream_CreateRequestThenBeginGetResponsePrior_ThrowsInvalidOperationException(Uri remoteServer)\n+ public void BeginGetRequestStream_CreateRequestThenBeginGetResponsePrior_ThrowsProtocolViolationException(Uri remoteServer)\n{\nHttpWebRequest request=HttpWebRequest.CreateHttp(remoteServer);\n\nIAsyncResult asyncResult=request.BeginGetResponse(null,null);\n- Assert.Throws<InvalidOperationException>(()=>\n+ Assert.Throws<ProtocolViolationException>(()=>\n{\nrequest.BeginGetRequestStream(null,null);\n});\n\n\npeft:381\tFix BeginGetRequestStream to throw ProtocolViolationException when called after BeginGetResponse.\n\n\nicl:381\tFixes #12345\n\n\nslm:Merge pull request dotnet/corefx from safern/HttpWebRequest\n\n\n", "peft": "381\tFix BeginGetRequestStream to throw ProtocolViolationException when called after BeginGetResponse.\n", "icl": "381\tFixes #12345\n", "slm": "Merge pull request dotnet/corefx from safern/HttpWebRequest\n", "nl": "Match desktop behavior GetRequestStream/BeginGetRequestStream throws on invalid verb", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16652, "text": "\n- {id:167,cat:TV/Other,desc:\"TV Programs\"}\n- {id:185,cat:TV/Documentary,desc:\"TV/Documentary\"}\n- {id:168,cat:TV/Other,desc:\"TV/Other\"}\n+ -{id:169,cat:TV/Other,desc:\"TV/Boxset\"}\n+\n+ -{id:191,cat:TV,desc:\"TV/BluTv\"}\n+ -{id:192,cat:TV,desc:\"TV/BluTv Series\"}\n+ -{id:193,cat:Movies,desc:\"TV/BluTv Film\"}\n+ -{id:199,cat:TV,desc:\"TV/Netflix\"}\n+ -{id:189,cat:TV,desc:\"TV/Netflix Series\"}\n+ -{id:190,cat:Movies,desc:\"TV/Netflix Film\"}\n\n- {id:171,cat:Audio,desc:\"Music\"}\n- {id:172,cat:Audio,desc:\"Music/Turkish\"}\n\ntype:info\nlabel:Layout\ndefault:\"<ol><li>Only the English Classic profile is supported.<li>Make sure to set the<b>Torrent Listing(Listeleme Biçimi)</b>option in your profile to<b>Classic(Klasik)</b><li>And set the<b>Language(Dil)</b>to<b>English</b><li>Using the<i>Modern</i>theme will prevent results,and using<i>Turkish</i>will prevent upload dates.</ol>\"\n+ -name:sort\n+ type:select\n+ label:Sort requested from site\n+ default:\"added\"\n+ options:\n+ \"added\":\"created\"\n+ \"seeders\":\"seeders\"\n+ \"size\":\"size\"\n+ -name:type\n+ type:select\n+ label:Order requested from site\n+ default:\"desc\"\n+ options:\n+ \"desc\":\"desc\"\n+ \"asc\":\"asc\"\n\nlogin:\npath:?p=home&pid=1\n\nform:form # loginbox_form\nsubmitpath:ajax/login.php\ninputs:\n- action:\"login\"\n+ action:login\nloginbox_membername:\"{{.Config.username}}\"\nloginbox_password:\"{{.Config.password}}\"\nloginbox_remember:1\n\n- name:re_replace\nargs:[\"[^a-zA-Z0-9]+\",\"%25\"]\ninputs:\n- p:\"torrents\"\n- pid:\"32\"\n- $raw:\"{{range.Categories}}cid[]={{.}}&{{end}}\"\n+ p:torrents\n+ pid:32\n+ $raw:\"{{range.Categories}}cid[]={{.}}&{{end}}\"\nkeywords:\"{{.Keywords}}\"\n- search_type:\"name\"\n- searchin:\"title\"\n+ search_type:name\n+ searchin:title\n+ \"sortOptions[sortBy]\":\"{{.Config.sort}}\"\n+ \"sortOptions[sortOrder]\":\"{{.Config.type}}\"\nerror:\n- selector:div.error:not(:contains(\"Hiçbir sonuç bulunamadı.\"))\n+\nrows:\nselector:table # torrents_table_classic>tbody>tr:has(td.torrent_name)\n+\nfields:\ntitle:\nselector:a[href *=\"?p=torrents&pid=10&action=details\"]\n\nattribute:href\ndownloadvolumefactor:\ncase:\n- \"img[title=\\\"FREE!\\\"]\":\"0\"\n- \"img[title=\\\"Download Multiplier:0.5\\\"]\":\"0.5\"\n- \"*\":\"1\"\n+ \"img[title=\\\"FREE!\\\"]\":0\n+ \"img[title=\\\"Download Multiplier:0.5\\\"]\":0.5\n+ \"*\":1\nuploadvolumefactor:\ncase:\n- \"img[title=\\\"Upload Multiplier:2\\\"]\":\"2\"\n- \"*\":\"1\"\n+ \"img[title=\\\"Upload Multiplier:2\\\"]\":2\n+ \"*\":1\n\n\npeft:1783\tAdded new categories and sort options.\n\n\nicl:1783\tAdded a new category\n\n\nslm:pleasuredone:update categories.resolves\n\n\n", "peft": "1783\tAdded new categories and sort options.\n", "icl": "1783\tAdded a new category\n", "slm": "pleasuredone:update categories.resolves\n", "nl": "torrent - turk:refresh categories", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16653, "text": "private void InitializeComponent()\n//\n//buttonRemove\n//\n+ this.buttonRemove.Enabled=false;\nthis.buttonRemove.Image=global::mRemoteNG.Resources.key_delete;\nthis.buttonRemove.ImageAlign=System.Drawing.ContentAlignment.MiddleLeft;\nthis.buttonRemove.Location=new System.Drawing.Point(102,343);\n\n﻿ using System;\n- using System.Collections;\nusing System.Collections.Generic;\nusing System.Collections.Specialized;\nusing System.Linq;\npublic CredentialManagerForm(IEnumerable<ICredentialRecord>credentialRecords)\nApplyThemes();\nobjectListView1.AddObjects(credentialRecords.ToList());\nobjectListView1.CellClick+=HandleCellDoubleClick;\n+ objectListView1.SelectionChanged+=ObjectListView1OnSelectionChanged;\n}\n\n# region Form stuff\nprivate void buttonRemove_Click(object sender,EventArgs e)\nRaiseCollectionChangedEvent(NotifyCollectionChangedAction.Remove,selectedCredential);\n}\n\n+ private void ObjectListView1OnSelectionChanged(object sender,EventArgs eventArgs)\n+ {\n+ buttonRemove.Enabled=objectListView1.SelectedObjects.Count!=0;\n+ }\n+\npublic event NotifyCollectionChangedEventHandler CollectionChanged;\n\nprivate void RaiseCollectionChangedEvent(NotifyCollectionChangedAction action,ICredentialRecord changedItem)\n\n\npeft:1382\tDisable the remove button when no item is selected.\n\n\nicl:1382\tMerge pull request 205 from dev/push - location into develop\n\n\nslm:CredentialManagerForm:disable the button when there is only one object selected.\n\n\n", "peft": "1382\tDisable the remove button when no item is selected.\n", "icl": "1382\tMerge pull request 205 from dev/push - location into develop\n", "slm": "CredentialManagerForm:disable the button when there is only one object selected.\n", "nl": "Remove button is disabled when no credential selected", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16654, "text": "public class foo\n}\",\"!--\",\"![CDATA[\",\"completionlist\",\"example\",\"exception\",\"include\",\"permission\",\"remarks\",\"see\",\"seealso\",\"summary\");\n}\n\n- ///<r\n- public void bar(){}\n-\n[WorkItem(8322,\"https://github.com/dotnet/roslyn/issues/8322\")]\n[Fact,Trait(Traits.Feature,Traits.Features.Completion)]\npublic async Task PartialTagCompletionNestedTags()\n\n\npeft:677\tMerge pull request dotnet/roslyn from jaredpar/fix_8322_completion_in_xml_doc_comments\n\n\nicl:677\tFix completion list for nested tags()\n\n\nslm:Remove bar()\n\n\n", "peft": "677\tMerge pull request dotnet/roslyn from jaredpar/fix_8322_completion_in_xml_doc_comments\n", "icl": "677\tFix completion list for nested tags()\n", "slm": "Remove bar()\n", "nl": "Removed junk code in test that was accidentally inserted.", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16655, "text": "private SyncedItem GetJobItemInfo(SyncJobItem jobItem)\n\nvar libraryItem=_libraryManager.GetItemById(jobItem.ItemId);\n\n+ if(libraryItem==null)\n+ {\n+ return null;\n+ }\n+\nvar syncedItem=new SyncedItem\n{\nSyncJobId=jobItem.JobId,\npublic List<SyncedItem>GetReadySyncItems(string targetId)\nStatuses=new List<SyncJobItemStatus>{SyncJobItemStatus.Transferring}\n});\n\n- return jobItemResult.Items.Select(GetJobItemInfo)\n+ return jobItemResult.Items\n+ .Select(GetJobItemInfo)\n+ .Where(i=>i!=null)\n.ToList();\n}\n\n\n\npeft:337\tFix null reference exception when getting synced item info.\n\n\nicl:337\tCorrectly handle null library items.\n\n\nslm:handle null library item\n\n\n", "peft": "337\tFix null reference exception when getting synced item info.\n", "icl": "337\tCorrectly handle null library items.\n", "slm": "handle null library item\n", "nl": "rework detail page a little", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16656, "text": "\n+ 2005-05-27 Zoltan Varga<<EMAIL>>\n+\n+ * MethodInfoTest.cs:Reenable the pseudo attribute test.\n+\n2005-05-25 Zoltan Varga<<EMAIL>>\n\n* TypeDelegatorTest.cs:New file.\nnamespace MonoTests.System.Reflection\n[TestFixture]\npublic class MethodInfoTest:Assertion\n{\n- //[DllImport(\"libfoo\",EntryPoint=\"foo\",CharSet=CharSet.Unicode,ExactSpelling=false,PreserveSig=true,SetLastError=true,BestFitMapping=true,ThrowOnUnmappableChar=true)]\n- //bug # 73161\n- [DllImport(\"libfoo\",EntryPoint=\"foo\",CharSet=CharSet.Unicode,ExactSpelling=false,PreserveSig=true,SetLastError=true)]\n+ [DllImport(\"libfoo\",EntryPoint=\"foo\",CharSet=CharSet.Unicode,ExactSpelling=false,PreserveSig=true,SetLastError=true,BestFitMapping=true,ThrowOnUnmappableChar=true)]\npublic static extern void dllImportMethod();\n\n[MethodImplAttribute(MethodImplOptions.PreserveSig)]\npublic class MethodInfoTest:Assertion\n}\n\n# if NET_2_0\n- [Category(\"NotWorking\")]\n[Test]\npublic void PseudoCustomAttributes()\n{\n\n\npeft:1968\t2005 - 05 - 27 Zoltan Varga\n\n\nicl:1968\tMerge pull request from khellang/appveyor\n\n\nslm:2005 - 06 - 01 Zoltan Varga<<EMAIL>>\n\n\n", "peft": "1968\t2005 - 05 - 27 Zoltan Varga\n", "icl": "1968\tMerge pull request from khellang/appveyor\n", "slm": "2005 - 06 - 01 Zoltan Varga<<EMAIL>>\n", "nl": "2005 - 05 - 27 Zoltan Varga<<EMAIL>>", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16657, "text": "public async Task<IEnumerable<ReleaseInfo>>PerformQuery(TorznabQuery query)\n{\nCQ dom=results.Content;\ndom[\"# needseed\"].Remove();\n- var rows=dom[\"table[width='750']>tbody\"].Children();\n- foreach(var row in rows.Skip(1))\n+ foreach(var table in dom[\"table[width='785']>tbody\"])\n{\n-\n- var release=new ReleaseInfo();\n-\n- var qRow=row.Cq();\n- var qLink=qRow.Children().ElementAt(2).Cq().Children(\"a\").First();\n-\n- release.MinimumRatio=1;\n- release.MinimumSeedTime=172800;\n- release.Title=qLink.Attr(\"title\");\n- if(!query.MatchQueryStringAND(release.Title))\n- continue;\n- release.Files=ParseUtil.CoerceLong(qRow.Find(\"td:nth-child(4)\").Text());\n- release.Grabs=ParseUtil.CoerceLong(qRow.Find(\"td:nth-child(8)\").Text());\n- release.Guid=new Uri(qLink.Attr(\"href\"));\n- release.Comments=release.Guid;\n- release.Link=new Uri(string.Format(DownloadUrl,qLink.Attr(\"href\").Split('=')[1]));\n-\n- var catUrl=qRow.Children().ElementAt(1).FirstElementChild.Cq().Attr(\"href\");\n- var catNum=catUrl.Split(new char[]{'=','&'})[1];\n- release.Category=MapTrackerCatToNewznab(catNum);\n-\n- //This tracker cannot search multiple cats at a time,so search all cats then filter out results from different cats\n- if(trackerCats.Count>0&&!trackerCats.Contains(catNum))\n- continue;\n-\n- var dateString=qRow.Children().ElementAt(5).Cq().Text().Trim();\n- var pubDate=DateTime.ParseExact(dateString,\"yyyy-MM-dd HH:mm:ss\",CultureInfo.InvariantCulture);\n- release.PublishDate=DateTime.SpecifyKind(pubDate,DateTimeKind.Local);\n-\n- var sizeStr=qRow.Children().ElementAt(6).Cq().Text();\n- release.Size=ReleaseInfo.GetBytes(sizeStr);\n-\n- release.Seeders=ParseUtil.CoerceInt(qRow.Children().ElementAt(8).Cq().Text().Trim());\n- release.Peers=ParseUtil.CoerceInt(qRow.Children().ElementAt(9).Cq().Text().Trim())+release.Seeders;\n-\n- var bgcolor=qRow.Attr(\"bgcolor\");\n- if(bgcolor==\"# DDDDDD\")\n- {\n- release.DownloadVolumeFactor=1;\n- release.UploadVolumeFactor=2;\n- }\n- else if(bgcolor==\"# FFFF99\")\n- {\n- release.DownloadVolumeFactor=0;\n- release.UploadVolumeFactor=1;\n- }\n- else if(bgcolor==\"# CCFF99\")\n- {\n- release.DownloadVolumeFactor=0;\n- release.UploadVolumeFactor=2;\n- }\n- else\n- {\n- release.DownloadVolumeFactor=1;\n- release.UploadVolumeFactor=1;\n+ var rows=table.Cq().Children();\n+ foreach(var row in rows.Skip(1))\n+ {\n+\n+ var release=new ReleaseInfo();\n+\n+ var qRow=row.Cq();\n+ var qLink=qRow.Children().ElementAt(2).Cq().Children(\"a\").First();\n+\n+ release.MinimumRatio=1;\n+ release.MinimumSeedTime=172800;\n+ release.Title=qLink.Attr(\"title\");\n+ if(!query.MatchQueryStringAND(release.Title))\n+ continue;\n+ release.Files=ParseUtil.CoerceLong(qRow.Find(\"td:nth-child(4)\").Text());\n+ release.Grabs=ParseUtil.CoerceLong(qRow.Find(\"td:nth-child(8)\").Text());\n+ release.Guid=new Uri(qLink.Attr(\"href\"));\n+ release.Comments=release.Guid;\n+ release.Link=new Uri(string.Format(DownloadUrl,qLink.Attr(\"href\").Split('=')[1]));\n+\n+ var catUrl=qRow.Children().ElementAt(1).FirstElementChild.Cq().Attr(\"href\");\n+ var catNum=catUrl.Split(new char[]{'=','&'})[1];\n+ release.Category=MapTrackerCatToNewznab(catNum);\n+\n+ //This tracker cannot search multiple cats at a time,so search all cats then filter out results from different cats\n+ if(trackerCats.Count>0&&!trackerCats.Contains(catNum))\n+ continue;\n+\n+ var dateString=qRow.Children().ElementAt(5).Cq().Text().Trim();\n+ var pubDate=DateTime.ParseExact(dateString,\"yyyy-MM-dd HH:mm:ss\",CultureInfo.InvariantCulture);\n+ release.PublishDate=DateTime.SpecifyKind(pubDate,DateTimeKind.Local);\n+\n+ var sizeStr=qRow.Children().ElementAt(6).Cq().Text();\n+ release.Size=ReleaseInfo.GetBytes(sizeStr);\n+\n+ release.Seeders=ParseUtil.CoerceInt(qRow.Children().ElementAt(8).Cq().Text().Trim());\n+ release.Peers=ParseUtil.CoerceInt(qRow.Children().ElementAt(9).Cq().Text().Trim())+release.Seeders;\n+\n+ var bgcolor=qRow.Attr(\"bgcolor\");\n+ if(bgcolor==\"# DDDDDD\")\n+ {\n+ release.DownloadVolumeFactor=1;\n+ release.UploadVolumeFactor=2;\n+ }\n+ else if(bgcolor==\"# FFFF99\")\n+ {\n+ release.DownloadVolumeFactor=0;\n+ release.UploadVolumeFactor=1;\n+ }\n+ else if(bgcolor==\"# CCFF99\")\n+ {\n+ release.DownloadVolumeFactor=0;\n+ release.UploadVolumeFactor=2;\n+ }\n+ else\n+ {\n+ release.DownloadVolumeFactor=1;\n+ release.UploadVolumeFactor=1;\n+ }\n+ releases.Add(release);\n}\n- releases.Add(release);\n}\n}\ncatch(Exception ex)\n\n\npeft:1812\tMerge pull request from jamesnk/fix - tracker - table - parsing\n\n\nicl:1812\t);\n\n\nslm:Newznab parsing improvements\n\n\n", "peft": "1812\tMerge pull request from jamesnk/fix - tracker - table - parsing\n", "icl": "1812\t);\n", "slm": "Newznab parsing improvements\n", "nl": "Fix Bit - HDTV()", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16658, "text": "\n- # Migrating the Silverlight Toolkit TreeView control into the UWP and the Uno Platform\n+ # Migrating the Silverlight Toolkit TreeView control to UWP and the Uno Platform\n\n- _You can find[the code of the ` TreeView `](https://github.com/nventive/Uno.UI.Toolkit.SL/tree/master/Uno.UI.Toolkit.SL/Controls/TreeView)for this article in the[Uno.UI.Toolkit.SL](https://github.com/nventive/Uno.UI.Toolkit.SL),in which we will add new controls and accept contributions for controls as they are made available._\n+ _You can find[the code of the ` TreeView `](https://github.com/nventive/Uno.UI.Toolkit.SL/tree/master/Uno.UI.Toolkit.SL/Controls/TreeView)for this article in the[Uno.UI.Toolkit.SL](https://github.com/nventive/Uno.UI.Toolkit.SL)repository,in which we will add new controls and accept contributions for controls as they are made available._\n\nIn this blog post series,we're going to cover the migration of the code for the\n[Silverlight Toolkit TreeView control](https://github.com/MicrosoftArchive/SilverlightToolkit)[TreeView control](https://github.com/MicrosoftArchive/SilverlightToolkit/tree/master/Release/Silverlight4/Source/Controls/TreeView)to UWP and the[Uno Platform](https://github.com/nventive/Uno),a control widely used in many line of business applications still in use today.\nWhen running the sample,a few things stand out:\n- The glyph next to the nodes changes state once,but does not animate back\n- There are some exceptions regarding the ` ItemContainerGenerator ` which has been deprecated in UWP.\n\n- We'll take a look at those the next parts of this series.\n\\No newline at end of file\n+ We'll take a look at those the next parts of this series.\n\n\npeft:177\tFix typo in the blog post title.\n\n\nicl:177\tAdded TreeView to Uno.UI.Toolkit.SL\n\n\nslm:Merge pull request from unoplatform/dev/djo/sl - exception - fix\n\n\n", "peft": "177\tFix typo in the blog post title.\n", "icl": "177\tAdded TreeView to Uno.UI.Toolkit.SL\n", "slm": "Merge pull request from unoplatform/dev/djo/sl - exception - fix\n", "nl": "Fixed typos", "lang": "csharp", "label": ["Doc-mod"], "Comments": []}
{"id": 16659, "text": "\nusing osu.Framework.Platform;\nusing osu.Game.Graphics;\nusing osu.Game.Graphics.Sprites;\n- using osu.Game.Graphics.UserInterface;\nusing osu.Game.Tournament.Components;\nusing osu.Game.Tournament.Models;\nusing osu.Game.Tournament.Screens.Drawings.Components;\nprivate void load(TextureStore textures,Storage storage)\n//Control panel container\nnew ControlPanel\n{\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\n\nText=\"Begin random\",\nAction=teamsContainer.StartScrolling,\n},\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\n\nText=\"Stop random\",\nAction=teamsContainer.StopScrolling,\n},\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\n\nprivate void load(TextureStore textures,Storage storage)\nAction=reloadTeams\n},\nnew ControlPanel.Spacer(),\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\n\n\nusing osu.Framework.Graphics.Containers;\nusing osu.Framework.Graphics.Shapes;\nusing osu.Game.Graphics;\n- using osu.Game.Graphics.UserInterface;\nusing osu.Game.Online.API;\nusing osu.Game.Online.API.Requests;\nusing osu.Game.Overlays.Settings;\npublic class TeamEditorScreen:TournamentEditorScreen<TeamEditorScreen.TeamRow,\n[BackgroundDependencyLoader]\nprivate void load()\n{\n- ControlPanel.Add(new OsuButton\n+ ControlPanel.Add(new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Add all countries\",\n\nusing osu.Framework.Graphics.Shapes;\nusing osu.Game.Graphics;\nusing osu.Game.Graphics.Containers;\n- using osu.Game.Graphics.UserInterface;\nusing osu.Game.Overlays.Settings;\nusing osu.Game.Tournament.Components;\nusing osuTK;\nprivate void load()\n{\nChildren=new Drawable[]\n{\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Add new\",\nprivate void load(LadderInfo ladder,MatchIPCInfo ipc)\n{\nChildren=new Drawable[]\n{\n- warmupButton=new OsuButton\n+ warmupButton=new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Toggle warmup\",\nAction=()=>warmup.Toggle()\n},\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Toggle chat\",\npublic MapPoolScreen()\n{\nText=\"Current Mode\"\n},\n- buttonRedBan=new OsuButton\n+ buttonRedBan=new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Red Ban\",\nAction=()=>setMode(TeamColour.Red,ChoiceType.Ban)\n},\n- buttonBlueBan=new OsuButton\n+ buttonBlueBan=new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Blue Ban\",\nAction=()=>setMode(TeamColour.Blue,ChoiceType.Ban)\n},\n- buttonRedPick=new OsuButton\n+ buttonRedPick=new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Red Pick\",\nAction=()=>setMode(TeamColour.Red,ChoiceType.Pick)\n},\n- buttonBluePick=new OsuButton\n+ buttonBluePick=new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Blue Pick\",\nAction=()=>setMode(TeamColour.Blue,ChoiceType.Pick)\n},\nnew ControlPanel.Spacer(),\n- new OsuButton\n+ new TourneyButton\n{\nRelativeSizeAxes=Axes.X,\nText=\"Reset\",\n\nusing osu.Framework.Platform;\nusing osu.Game.Beatmaps;\nusing osu.Game.Graphics;\n- using osu.Game.Graphics.UserInterface;\nusing osu.Game.Online.API.Requests;\nusing osu.Game.Tournament.IPC;\nusing osu.Game.Tournament.Models;\nprivate void load(Storage storage,FrameworkConfigManager frameworkConfig)\n\nAddRange(new[]\n{\n- new OsuButton\n+ new TourneyButton\n{\nText=\"Save Changes\",\nWidth=140,\n\nusing osu.Framework.Graphics.Containers;\nusing osu.Framework.Graphics.Shapes;\nusing osu.Framework.Platform;\n- using osu.Game.Graphics.UserInterface;\nusing osu.Game.Tournament.Components;\nusing osu.Game.Tournament.Models;\nusing osu.Game.Tournament.Screens;\nprivate void load(LadderInfo ladder,Storage storage)\nDirection=FillDirection.Vertical,\nChildren=new Drawable[]\n{\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Setup\",Action=()=>SetScreen(typeof(SetupScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Setup\",Action=()=>SetScreen(typeof(SetupScreen))},\nnew Container{RelativeSizeAxes=Axes.X,Height=50},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Team Editor\",Action=()=>SetScreen(typeof(TeamEditorScreen))},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Rounds Editor\",Action=()=>SetScreen(typeof(RoundEditorScreen))},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Bracket Editor\",Action=()=>SetScreen(typeof(LadderEditorScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Team Editor\",Action=()=>SetScreen(typeof(TeamEditorScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Rounds Editor\",Action=()=>SetScreen(typeof(RoundEditorScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Bracket Editor\",Action=()=>SetScreen(typeof(LadderEditorScreen))},\nnew Container{RelativeSizeAxes=Axes.X,Height=50},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Drawings\",Action=()=>SetScreen(typeof(DrawingsScreen))},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Showcase\",Action=()=>SetScreen(typeof(ShowcaseScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Drawings\",Action=()=>SetScreen(typeof(DrawingsScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Showcase\",Action=()=>SetScreen(typeof(ShowcaseScreen))},\nnew Container{RelativeSizeAxes=Axes.X,Height=50},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Schedule\",Action=()=>SetScreen(typeof(ScheduleScreen))},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Bracket\",Action=()=>SetScreen(typeof(LadderScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Schedule\",Action=()=>SetScreen(typeof(ScheduleScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Bracket\",Action=()=>SetScreen(typeof(LadderScreen))},\nnew Container{RelativeSizeAxes=Axes.X,Height=50},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"TeamIntro\",Action=()=>SetScreen(typeof(TeamIntroScreen))},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"MapPool\",Action=()=>SetScreen(typeof(MapPoolScreen))},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Gameplay\",Action=()=>SetScreen(typeof(GameplayScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"TeamIntro\",Action=()=>SetScreen(typeof(TeamIntroScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"MapPool\",Action=()=>SetScreen(typeof(MapPoolScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Gameplay\",Action=()=>SetScreen(typeof(GameplayScreen))},\nnew Container{RelativeSizeAxes=Axes.X,Height=50},\n- new OsuButton{RelativeSizeAxes=Axes.X,Text=\"Win\",Action=()=>SetScreen(typeof(TeamWinScreen))},\n+ new TourneyButton{RelativeSizeAxes=Axes.X,Text=\"Win\",Action=()=>SetScreen(typeof(TeamWinScreen))},\n}\n},\n},\nnew file mode 100644\nindex **********0..12872d31976\nmmm/dev/null\n\n+ //Copyright(c)ppy Pty Ltd<<EMAIL>>.Licensed under the MIT Licence.\n+ //See the LICENCE file in the repository root for full licence text.\n+\n+ using osu.Game.Graphics.UserInterface;\n+\n+ namespace osu.Game.Tournament\n+ {\n+ public class TourneyButton:OsuButton\n+ {\n+ public TourneyButton()\n+ :base(null)\n+ {\n+ }\n+ }\n+ }\npublic Color4 BackgroundColour\nprotected Box Background;\nprotected SpriteText SpriteText;\n\n- public OsuButton()\n+ public OsuButton(HoverSampleSet?hoverSounds=HoverSampleSet.Loud)\n{\nHeight=40;\n\npublic OsuButton()\nDepth=float.MinValue\n},\nSpriteText=CreateText(),\n- new HoverClickSounds(HoverSampleSet.Loud),\n}\n});\n\n+ if(hoverSounds.HasValue)\n+ AddInternal(new HoverClickSounds(hoverSounds.Value));\n+\nEnabled.BindValueChanged(enabledChanged,true);\n}\n\n\n\npeft:1130\tUse the new button style for all buttons in the map pool screen.\n\n\nicl:1130\tAction=()=\n\n\nslm:Replace OsuButton with OsuButton\n\n\n", "peft": "1130\tUse the new button style for all buttons in the map pool screen.\n", "icl": "1130\tAction=()=\n", "slm": "Replace OsuButton with OsuButton\n", "nl": "Merge pull request from peppy/fix - tourney - button - sounds", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16660, "text": "namespace Volo.Abp.AspNetCore.Mvc.UI.Widgets\npublic class WidgetAttribute:Attribute\n{\n[CanBeNull]\n- public string[]StyleSrcs{get;set;}\n+ public string[]StyleFiles{get;set;}\n\n[CanBeNull]\npublic Type[]StyleTypes{get;set;}\n\n[CanBeNull]\n- public string[]ScriptSrcs{get;set;}\n+ public string[]ScriptFiles{get;set;}\n\n[CanBeNull]\npublic Type[]ScriptTypes{get;set;}\nprivate static List<WidgetResourceItem>GetStyles(WidgetAttribute widgetAttribut\n{\nvar styles=new List<WidgetResourceItem>();\n\n- if(!widgetAttribute.StyleSrcs.IsNullOrEmpty())\n+ if(!widgetAttribute.StyleFiles.IsNullOrEmpty())\n{\n- styles.AddRange(widgetAttribute.StyleSrcs.Select(src=>new WidgetResourceItem(src)));\n+ styles.AddRange(widgetAttribute.StyleFiles.Select(src=>new WidgetResourceItem(src)));\n}\n\nif(!widgetAttribute.StyleTypes.IsNullOrEmpty())\nprivate static List<WidgetResourceItem>GetScripts(WidgetAttribute widgetAttribu\n{\nvar scripts=new List<WidgetResourceItem>();\n\n- if(!widgetAttribute.ScriptSrcs.IsNullOrEmpty())\n+ if(!widgetAttribute.ScriptFiles.IsNullOrEmpty())\n{\n- scripts.AddRange(widgetAttribute.ScriptSrcs.Select(src=>new WidgetResourceItem(src)));\n+ scripts.AddRange(widgetAttribute.ScriptFiles.Select(src=>new WidgetResourceItem(src)));\n}\n\nif(!widgetAttribute.ScriptTypes.IsNullOrEmpty())\n\n- ﻿<div class=\"my-simple-widget\">\n+ ﻿@model DashboardDemo.Web.Pages.Components.MySimpleWidget.MySimpleWidgetViewModel\n+ <div class=\"my-simple-widget\">\n<h2>My Simple Widget</h2>\n- <p>This is a simple widget!</p>\n+ <p>Hello<strong>@Model.Name</strong>!This is a simple widget!</p>\n</div>\n\\No newline at end of file\n\n- ﻿ using System.Collections.Generic;\n- using Microsoft.AspNetCore.Mvc;\n+ ﻿ using Microsoft.AspNetCore.Mvc;\nusing Volo.Abp.AspNetCore.Mvc;\n- using Volo.Abp.AspNetCore.Mvc.UI.Bundling;\nusing Volo.Abp.AspNetCore.Mvc.UI.Widgets;\n\nnamespace DashboardDemo.Web.Pages.Components.MySimpleWidget\n{\n[Widget(\n- StyleTypes=new[]{typeof(MySimpleWidgetStyleBundleContributor)},\n- ScriptTypes=new[]{typeof(MySimpleWidgetScriptBundleContributor)}\n+ StyleFiles=new[]{\"/Pages/Components/MySimpleWidget/Default.css\"},\n+ ScriptFiles=new[]{\"/Pages/Components/MySimpleWidget/Default.js\"}\n)]\npublic class MySimpleWidgetViewComponent:AbpViewComponent\n{\n- public IViewComponentResult Invoke()\n+ public IViewComponentResult Invoke(string name)\n{\n- return View(\"~/Pages/Components/MySimpleWidget/Default.cshtml\");\n- }\n- }\n-\n- public class MySimpleWidgetStyleBundleContributor:BundleContributor\n- {\n- public override void ConfigureBundle(BundleConfigurationContext context)\n- {\n- context.Files.AddIfNotContains(\"/Pages/Components/MySimpleWidget/Default.css\");\n- }\n- }\n-\n- public class MySimpleWidgetScriptBundleContributor:BundleContributor\n- {\n- public override void ConfigureBundle(BundleConfigurationContext context)\n- {\n- context.Files.AddIfNotContains(\"/Pages/Components/MySimpleWidget/Default.js\");\n+ return View(new MySimpleWidgetViewModel{Name=name});\n}\n}\n}\n\\No newline at end of file\nnew file mode 100644\nindex **********..f4fd1b0a95\nmmm/dev/null\n\n+ ﻿ namespace DashboardDemo.Web.Pages.Components.MySimpleWidget\n+ {\n+ public class MySimpleWidgetViewModel\n+ {\n+ public string Name{get;set;}\n+ }\n+ }\n\\No newline at end of file\n\n﻿@page\n- @using DashboardDemo.Web.Pages.Components.MySimpleWidget\n@model DashboardDemo.Web.Pages.MyWidgetsModel\n\n- @await Component.InvokeAsync(typeof(MySimpleWidgetViewComponent))\n- <hr/>\n- @await Component.InvokeAsync(\"MySimpleWidget\")\n+ @* Example usage by widget name *@\n+ @await Component.InvokeAsync(\"MySimpleWidget\",new{name=\"John\"})\n+\n<hr/>\n- <vc:my-custom-name-widget></vc:my-custom-name-widget>\n+\n+ @* Example usage by widget type *@\n+ @await Component.InvokeAsync(\"MySimpleWidget\",new{name=\"Mete\"})\n\\No newline at end of file\n\n\npeft:1809\tAdded a view model to the widget and changed the widget to use it.\n\n\nicl:1809\tInvoke(string name)\n\n\nslm:Make sure that the`MySimpleWidget`style srcs is not empty()\n\n\n", "peft": "1809\tAdded a view model to the widget and changed the widget to use it.\n", "icl": "1809\tInvoke(string name)\n", "slm": "Make sure that the`MySimpleWidget`style srcs is not empty()\n", "nl": "Renamed StyleSrcs to StyleFiles and ScriptSrcs to ScriptFiles", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16661, "text": "static partial class GamePad\n{\ninternal static bool Back;\n\n+ private static int PlatformGetMaxIndex()\n+ {\n+ return 16;\n+ }\n+\nprivate static GamePadCapabilities PlatformGetCapabilities(int index)\n{\nif(index>=WGI.Gamepad.Gamepads.Count)\n\n\npeft:1891\tMerge pull request from xnadev/master\n\n\nicl:1891\tAdd support for up to 16 gamepads\n\n\nslm:Fixed the PlatformGetMaxIndex method.\n\n\n", "peft": "1891\tMerge pull request from xnadev/master\n", "icl": "1891\tAdd support for up to 16 gamepads\n", "slm": "Fixed the PlatformGetMaxIndex method.\n", "nl": "Added missing method in Gamepad.UWP.cs", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16662, "text": "public void UpdateCommands(MenuInfo info)\ncmdOpenItemWith.Enabled=info.OpenItemWith;\ncmdOpenItemLocation.Enabled=info.OpenItemLocation;\ncmdOpenOutputItemLocation.Enabled=info.OpenOutputItemLocation;\n- cmdCopyAssetPath.Enabled=info.CopyAssetPath;\n+ cmdCopyAssetName.Enabled=info.CopyAssetPath;\ncmdRebuildItem.Enabled=info.RebuildItem;\n\n//Visibility of menu items can't be changed so\npublic PipelineMenuBar()\npublic Command cmdBuild,cmdRebuild,cmdClean,cmdCancelBuild;\npublic CheckCommand cmdDebugMode;\npublic Command cmdHelp,cmdAbout;\n- public Command cmdOpenItem,cmdOpenItemWith,cmdOpenItemLocation,cmdOpenOutputItemLocation,cmdCopyAssetPath,cmdRebuildItem;\n+ public Command cmdOpenItem,cmdOpenItemWith,cmdOpenItemLocation,cmdOpenOutputItemLocation,cmdCopyAssetName,cmdRebuildItem;\n\nToolBar toolbar;\nButtonMenuItem menuFile,menuRecent,menuEdit,menuAdd,menuView,menuBuild,menuHelp;\nprivate void InitializeComponent()\ncmdOpenItemWith.Executed+=CmdOpenItemWith_Executed;\ncmdOpenItemLocation.Executed+=CmdOpenItemLocation_Executed;\ncmdOpenOutputItemLocation.Executed+=CmdOpenOutputItemLocation_Executed;\n- cmdCopyAssetPath.Executed+=CmdCopyAssetPath_Executed;\n+ cmdCopyAssetName.Executed+=CmdCopyAssetPath_Executed;\ncmdRebuildItem.Executed+=CmdRebuildItem_Executed;\n}\n\nprivate void InitalizeCommands()\ncmdOpenOutputItemLocation=new Command();\ncmdOpenOutputItemLocation.MenuText=\"Open Output Directory\";\n\n- cmdCopyAssetPath=new Command();\n- cmdCopyAssetPath.MenuText=\"Copy Asset Path\";\n+ cmdCopyAssetName=new Command();\n+ cmdCopyAssetName.MenuText=\"Copy Asset Name\";\n\ncmdRebuildItem=new Command();\ncmdRebuildItem.Image=Global.GetEtoIcon(\"Commands.Rebuild.png\");\nprivate void InitalizeContextMenu()\n\ncmOpenItemLocation=cmdOpenItemLocation.CreateMenuItem();\ncmOpenOutputItemLocation=cmdOpenOutputItemLocation.CreateMenuItem();\n- cmCopyAssetPath=cmdCopyAssetPath.CreateMenuItem();\n+ cmCopyAssetPath=cmdCopyAssetName.CreateMenuItem();\ncmRebuildItem=cmdRebuildItem.CreateMenuItem();\ncmExclude=cmdExclude.CreateMenuItem();\ncmRename=cmdRename.CreateMenuItem();\n\n\npeft:1218\tRename Copy Asset Path to Copy Asset Name.\n\n\nicl:1218\tcmRename=\n\n\nslm:Rename and Rename assets\n\n\n", "peft": "1218\tRename Copy Asset Path to Copy Asset Name.\n", "icl": "1218\tcmRename=\n", "slm": "Rename and Rename assets\n", "nl": "Merge pull request from cra0zy/name", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16663, "text": "\n+ 2007-07-22 Zoltan Varga<<EMAIL>>\n+\n+ * loader.c(mono_method_get_param_token):Make this work on generic methods.\n+ Return 0x8000000 for return parameters.Fixes # 82161.\n+\n2007-07-21 Marek Habersack<<EMAIL>>\n\n* appdomain.c(get_shadow_assembly_location):append the current\nmono_method_get_param_token(MonoMethod * method,int index)\nMonoTableInfo * methodt;\nguint32 idx;\n\n- if(klass->generic_class)\n- g_assert_not_reached();\n-\nmono_class_init(klass);\n\nif(klass->image->dynamic){\nmono_method_get_param_token(MonoMethod * method,int index)\nif(idx>0){\nguint param_index=mono_metadata_decode_row_col(methodt,idx-1,MONO_METHOD_PARAMLIST);\n\n- return mono_metadata_make_token(MONO_TABLE_PARAM,param_index+index);\n+ if(index==-1)\n+ /* Return value */\n+ return mono_metadata_make_token(MONO_TABLE_PARAM,0);\n+ else\n+ return mono_metadata_make_token(MONO_TABLE_PARAM,param_index+index);\n}\n\nreturn 0;\n\n\npeft:954\t2007 - 07 - 22 Zoltan Varga\n\n\nicl:954\tMoved mono_method_get_param_token to eglib.\n\n\nslm:2007 - 07 - 22 Marek Habersack<<EMAIL>>\n\n\n", "peft": "954\t2007 - 07 - 22 Zoltan Varga\n", "icl": "954\tMoved mono_method_get_param_token to eglib.\n", "slm": "2007 - 07 - 22 Marek Habersack<<EMAIL>>\n", "nl": "2007 - 07 - 22 Zoltan Varga<<EMAIL>>", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16664, "text": "The following labels are used on PRs:\n\n* ` Review-Needed `:The PR is being reviewed.Please see[Pull Request-Code Review](../../.github/CONTRIBUTING.md # pull-request-code-review)\n* ` Review-Waiting on Author `:The PR was reviewed by the team and requires changes or comments from the author before being accepted.\n+ * ` Review-Abandoned `:The PR was not updated for significant number of days(the exact number could vary in time).\n+ Maintainers should look into such PRs and re-evaluate them.\n\n# # # Random labels\n\n\n\npeft:1832\tThe following labels are used on PRs:\n\n\nicl:1832\tMerge pull request from ajpinedam/dev/anpi/docs.started.vscode\n\n\nslm:Update PRs.md\n\n\n", "peft": "1832\tThe following labels are used on PRs:\n", "icl": "1832\tMerge pull request from ajpinedam/dev/anpi/docs.started.vscode\n", "slm": "Update PRs.md\n", "nl": "Merge pull request from vors/label - abandoned", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16665, "text": "find_typespec_for_class(MonoAotCompile * acfg,MonoClass * klass)\n\n/* FIXME:Search referenced images as well */\nfor(i=0;i<acfg->image->tables[MONO_TABLE_TYPESPEC].rows;++i){\n- /* Since we don't compile generic methods,the context is empty */\nk=mono_class_get_full(acfg->image,MONO_TOKEN_TYPE_SPEC|(i+1),NULL);\nif(k==klass)\nbreak;\n}\n\n- g_assert(i<acfg->image->tables[MONO_TABLE_TYPESPEC].rows);\n-\n- return MONO_TOKEN_TYPE_SPEC|(i+1);\n+ if(i<acfg->image->tables[MONO_TABLE_TYPESPEC].rows)\n+ return MONO_TOKEN_TYPE_SPEC|(i+1);\n+ else\n+ return 0;\n}\n\nstatic void\nencode_klass_ref(MonoAotCompile * acfg,MonoClass * klass,guint8 * buf,guint8 * * endbuf)\n{\nif(klass->generic_class){\n+ guint32 token;\ng_assert(klass->type_token);\n\n- encode_value(find_typespec_for_class(acfg,klass),buf,&buf);\n- encode_value(get_image_index(acfg,acfg->image),buf,&buf);\n+ /* Find a typespec for a class if possible */\n+ token=find_typespec_for_class(acfg,klass);\n+ if(token){\n+ encode_value(token,buf,&buf);\n+ encode_value(get_image_index(acfg,acfg->image),buf,&buf);\n+ }else{\n+ MonoClass * gclass=klass->generic_class->container_class;\n+ MonoGenericInst * inst=klass->generic_class->context.class_inst;\n+ int i;\n+\n+ /* Encode it ourselves */\n+ /* Marker */\n+ encode_value(MONO_TOKEN_TYPE_SPEC,buf,&buf);\n+ encode_klass_ref(acfg,gclass,buf,&buf);\n+ encode_value(inst->type_argc,buf,&buf);\n+ for(i=0;i<inst->type_argc;++i)\n+ encode_klass_ref(acfg,mono_class_from_mono_type(inst->type_argv[i]),buf,&buf);\n+ }\n}else if(klass->type_token){\ng_assert(mono_metadata_token_code(klass->type_token)==MONO_TOKEN_TYPE_DEF);\nencode_value(klass->type_token-MONO_TOKEN_TYPE_DEF,buf,&buf);\ndecode_klass_ref(MonoAotModule * module,guint8 * buf,guint8 * * endbuf)\n{\nMonoImage * image;\nMonoClass * klass,* eklass;\n- guint32 token,rank,image_index;\n+ guint32 token,rank;\n\ntoken=decode_value(buf,&buf);\nif(token==0){\n* endbuf=buf;\nreturn NULL;\n}\n- image_index=decode_value(buf,&buf);\n- image=load_image(module,image_index);\n- if(!image)\n- return NULL;\nif(mono_metadata_token_table(token)==0){\n+ image=load_image(module,decode_value(buf,&buf));\n+ if(!image)\n+ return NULL;\nklass=mono_class_get(image,MONO_TOKEN_TYPE_DEF+token);\n}else if(mono_metadata_token_table(token)==MONO_TABLE_TYPESPEC){\n- klass=mono_class_get(image,token);\n+ if(token==MONO_TOKEN_TYPE_SPEC){\n+ MonoClass * gclass;\n+ int i;\n+ MonoGenericContext ctx;\n+ MonoGenericInst inst;\n+\n+ gclass=decode_klass_ref(module,buf,&buf);\n+ g_assert(gclass->generic_container);\n+\n+ memset(&ctx,0,sizeof(ctx));\n+ memset(&inst,0,sizeof(inst));\n+ ctx.class_inst=&inst;\n+ inst.type_argc=decode_value(buf,&buf);\n+ inst.type_argv=g_new0(MonoType *,inst.type_argc);\n+ for(i=0;i<inst.type_argc;++i){\n+ MonoClass * pclass=decode_klass_ref(module,buf,&buf);\n+ if(!pclass){\n+ g_free(inst.type_argv);\n+ return NULL;\n+ }\n+ inst.type_argv[i]=&pclass->byval_arg;\n+ }\n+ klass=mono_class_from_mono_type(mono_class_inflate_generic_type(&gclass->byval_arg,&ctx));\n+ }else{\n+ image=load_image(module,decode_value(buf,&buf));\n+ if(!image)\n+ return NULL;\n+ klass=mono_class_get(image,token);\n+ }\n}else if(token==MONO_TOKEN_TYPE_DEF){\n/* Array */\n+ image=load_image(module,decode_value(buf,&buf));\n+ if(!image)\n+ return NULL;\nrank=decode_value(buf,&buf);\neklass=decode_klass_ref(module,buf,&buf);\nklass=mono_array_class_get(eklass,rank);\n\n\npeft:1426\t2008 - 02 - 28 Zoltan Varga\n\n\nicl:1426\t[arm]Added support for generic methods in the AOT\n\n\nslm:2009 - 02 - 17 Zoltan Varga<<EMAIL>>\n\n\n", "peft": "1426\t2008 - 02 - 28 Zoltan Varga\n", "icl": "1426\t[arm]Added support for generic methods in the AOT\n", "slm": "2009 - 02 - 17 Zoltan Varga<<EMAIL>>\n", "nl": "2007 - 11 - 30 Zoltan Varga<<EMAIL>>", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16666, "text": "\n<ClCompile Include=\"..\\mono\\metadata\\environment.c\"/>\n<ClCompile Include=\"..\\mono\\metadata\\exception.c\"/>\n<ClCompile Include=\"..\\mono\\metadata\\file-io.c\"/>\n+ <ClCompile Include=\"..\\mono\\metadata\\file-mmap-windows.c\"/>\n<ClCompile Include=\"..\\mono\\metadata\\filewatcher.c\"/>\n<ClCompile Include=\"..\\mono\\metadata\\gc-memfuncs.c\"/>\n<ClCompile Include=\"..\\mono\\metadata\\gc.c\"/>\n\n\npeft:1919\tAdd file-mmap-windows.c to the list of files to compile on Windows.\n\n\nicl:1919\tAdded file-mmap-windows.c to the project\n\n\nslm:Add missing file - mmap - windows.c to build.\n\n\n", "peft": "1919\tAdd file-mmap-windows.c to the list of files to compile on Windows.\n", "icl": "1919\tAdded file-mmap-windows.c to the project\n", "slm": "Add missing file - mmap - windows.c to build.\n", "nl": "Added file - mmap - windows.c to libmonoruntime.vcxproj", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16667, "text": "mono_domain_fire_assembly_load(MonoAssembly * assembly,gpointer user_data)\ng_assert(assembly_load_field);\n}\n\n- mono_field_get_value(domain->domain,assembly_load_field,&load_value);\n+ mono_field_get_value((MonoObject *)domain->domain,assembly_load_field,&load_value);\nif(load_value==NULL){\n/* No events waiting to be triggered */\nreturn;\nsetup_generic_vtable(MonoClass * class,MonoMethod * * overrides,int onum)\nmono_class_init(gklass);\nclass->vtable_size=gklass->vtable_size;\n\n- class->vtable=g_new0(gpointer,class->vtable_size);\n- memcpy(class->vtable,gklass->vtable,sizeof(gpointer)* class->vtable_size);\n+ class->vtable=g_new0(MonoMethod *,class->vtable_size);\n+ memcpy(class->vtable,gklass->vtable,sizeof(MonoMethod *)* class->vtable_size);\n\nfor(i=0;i<class->vtable_size;i++){\nMonoMethod * m=class->vtable[i];\nsetup_generic_vtable(MonoClass * class,MonoMethod * * overrides,int onum)\n}\n\nclass->max_interface_id=gklass->max_interface_id;\n- class->interface_offsets=g_new0(gpointer,gklass->max_interface_id+1);\n+ class->interface_offsets=g_new0(gint,gklass->max_interface_id+1);\nmemcpy(class->interface_offsets,gklass->interface_offsets,\n- sizeof(gpointer)*(gklass->max_interface_id+1));\n+ sizeof(gint)*(gklass->max_interface_id+1));\n}\n\n/*\nmono_class_setup_vtable_general(MonoClass * class,MonoMethod * * overrides,int o\ncontinue;\n\nif(ic->generic_class){\n- MonoClass * the_ic=ic->generic_class->container_class;\nthe_cname=mono_type_get_name_full(&ic->byval_arg,MONO_TYPE_NAME_FORMAT_IL);\ncname=the_cname;\n}else{\nstatic void\nmono_class_create_generic_2(MonoGenericClass * gclass)\n{\nMonoClass * klass,* gklass;\n- GList * list;\nint i;\n\nklass=gclass->klass;\nmono_string_hash(MonoString * s)\nreturn h;\n}\n\n- gboolean\n+ static gboolean\nmono_ptrarray_equal(gpointer * s1,gpointer * s2)\n{\nint len=GPOINTER_TO_INT(s1[0]);\nmono_ptrarray_equal(gpointer * s1,gpointer * s2)\nreturn memcmp(s1+1,s2+1,len * sizeof(gpointer))==0;\n}\n\n- guint\n+ static guint\nmono_ptrarray_hash(gpointer * s)\n{\nint i;\n\n\npeft:1524\t2005 - 03 - 23 Zoltan Varga\n\n\nicl:1524\tFixing the vtable for generic classes.\n\n\nslm:2005 - 03 - 11 Zoltan Varga<<EMAIL>>\n\n\n", "peft": "1524\t2005 - 03 - 23 Zoltan Varga\n", "icl": "1524\tFixing the vtable for generic classes.\n", "slm": "2005 - 03 - 11 Zoltan Varga<<EMAIL>>\n", "nl": "2005 - 06 - 26 Ben Maurer<<EMAIL>>", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16668, "text": "if($args.Length-eq 0)\necho\"'dependencies'commands to restore removed files.\"\necho\"test Tests the default mods for errors.\"\necho\"\"\n- $command=Read-Host\"Enter command\"\n+ $command=(Read-Host\"Enter command\").Split('',2)\n}\nelse\n{\nelseif($command-eq\"clean\")\n}\n}\nelseif($command-eq\"version\")\n- {\n- $version=git name-rev--name-only--tags--no-undefined HEAD 2>$null\n- if($version-eq$null)\n+ {\n+ if($command.Length-gt 1)\n{\n- $version=\"git-\"+(git rev-parse--short HEAD)\n+ $version=$command[1]\n}\n- $mods=@(\"mods/ra/mod.yaml\",\"mods/cnc/mod.yaml\",\"mods/d2k/mod.yaml\",\"mods/modchooser/mod.yaml\")\n- foreach($mod in$mods)\n+ elseif(Get-Command'git'-ErrorAction SilentlyContinue)\n{\n- $replacement=(gc$mod)-Replace\"Version:.*\",(\"Version:{0}\"-f$version)\n- sc$mod$replacement\n+ $version=git name-rev--name-only--tags--no-undefined HEAD 2>$null\n+ if($version-eq$null)\n+ {\n+ $version=\"git-\"+(git rev-parse--short HEAD)\n+ }\n+ }\n+ else\n+ {\n+ echo\"Unable to locate Git.The version will remain unchanged.\"\n+ }\n+\n+ if($version-ne$null)\n+ {\n+ $mods=@(\"mods/ra/mod.yaml\",\"mods/cnc/mod.yaml\",\"mods/d2k/mod.yaml\",\"mods/modchooser/mod.yaml\")\n+ foreach($mod in$mods)\n+ {\n+ $replacement=(gc$mod)-Replace\"Version:.*\",(\"Version:{0}\"-f$version)\n+ sc$mod$replacement\n+ }\n+ echo(\"Version strings set to'{0}'.\"-f$version)\n}\n- echo(\"Version strings set to'{0}'.\"-f$version)\n}\nelseif($command-eq\"dependencies\")\n{\n\n\npeft:753\tAdded version command to update version strings in mod.yaml files.\n\n\nicl:753\tAdded a version command to the build script.\n\n\nslm:Merge pull request from sergeybykov/fix - git\n\n\n", "peft": "753\tAdded version command to update version strings in mod.yaml files.\n", "icl": "753\tAdded a version command to the build script.\n", "slm": "Merge pull request from sergeybykov/fix - git\n", "nl": "Update make version to check for git.", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16669, "text": "\nusing Microsoft.Extensions.DependencyInjection;\nusing Microsoft.Extensions.Logging;\nusing Microsoft.IdentityModel.Tokens;\n+ using Stripe;\n\nnamespace Bit.Api.Utilities\n{\npublic override void OnException(ExceptionContext context)\n}\n\nvar badRequestException=exception as BadRequestException;\n+ var stripeException=exception as StripeException;\nif(badRequestException!=null)\n{\ncontext.HttpContext.Response.StatusCode=400;\npublic override void OnException(ExceptionContext context)\nerrorModel.Message=badRequestException.Message;\n}\n}\n+ else if(stripeException!=null&&stripeException?.StripeError?.ErrorType==\"card_error\")\n+ {\n+ context.HttpContext.Response.StatusCode=400;\n+ errorModel=new ErrorResponseModel(stripeException.StripeError.Parameter,stripeException.Message);\n+ }\nelse if(exception is ApplicationException)\n{\ncontext.HttpContext.Response.StatusCode=402;\npublic ErrorResponseModel(ModelStateDictionary modelState)\n}\n}\n\n+ public ErrorResponseModel(Dictionary<string,IEnumerable<string>>errors)\n+ :this(\"Errors have occurred.\",errors)\n+ {}\n+\n+ public ErrorResponseModel(string errorKey,string errorValue)\n+ :this(errorKey,new string[]{errorValue})\n+ {}\n+\n+ public ErrorResponseModel(string errorKey,IEnumerable<string>errorValues)\n+ :this(new Dictionary<string,IEnumerable<string>>{{errorKey,errorValues}})\n+ {}\n+\n+ public ErrorResponseModel(string message,Dictionary<string,IEnumerable<string>>errors)\n+ :this()\n+ {\n+ Message=message;\n+ ValidationErrors=errors;\n+ }\n+\npublic string Message{get;set;}\npublic Dictionary<string,IEnumerable<string>>ValidationErrors{get;set;}\n//For use in development environments.\n\n\npeft:265\tAdded StripeException handling.\n\n\nicl:265\tAdd StripeException handling to the exception filter\n\n\nslm:Add stripe error handling to ErrorResponseModel\n\n\n", "peft": "265\tAdded StripeException handling.\n", "icl": "265\tAdd StripeException handling to the exception filter\n", "slm": "Add stripe error handling to ErrorResponseModel\n", "nl": "handle stripe card errors", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16670, "text": "\n+ 2005-10-04 Sebastien Pouliot<<EMAIL>>\n+\n+ * ComponentDesigner.cs:Added a call to GC.SuppressFinalize in Dispose\n+ method(even if we throw an NotImplementedException afterward).This\n+ will remove warnings from gendarme.\n+\n2004-06-13 Gert Driesen<<EMAIL>>\n\n* DisplayMode.cs:changed enum field values to match MS.NET\npublic virtual DesignerVerbCollection Verbs\n[MonoTODO]\npublic void Dispose()\n{\n+ GC.SuppressFinalize(this);\nthrow new NotImplementedException();\n}\n\n\n\npeft:1354\t2005 - 10 - 04 Sebastien Pouliot\n\n\nicl:1354\t* ComponentDesigner.cs\n\n\nslm:2004 - 06 - 15 Gonzalo Paniagua Javier<<EMAIL>>\n\n\n", "peft": "1354\t2005 - 10 - 04 Sebastien Pouliot\n", "icl": "1354\t* ComponentDesigner.cs\n", "slm": "2004 - 06 - 15 Gonzalo Paniagua Javier<<EMAIL>>\n", "nl": "2005 - 10 - 04 Sebastien Pouliot<<EMAIL>>", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16671, "text": "public object Get(GetPluginSecurityInfo request)\n///</summary>\n///<param name=\"request\"></param>\n///<returns></returns>\n- public async Task Post(RegisterAppstoreSale request)\n+ public void Post(RegisterAppstoreSale request)\n{\n- await_securityManager.RegisterAppStoreSale(request.Parameters);\n+ var task=_securityManager.RegisterAppStoreSale(request.Parameters);\n+\n+ Task.WaitAll(task);\n}\n\n///<summary>\n\n\npeft:1338\tFix RegisterAppstoreSale to be synchronous.\n\n\nicl:1338\tAdded a new method to the security manager to register app store sales.\n\n\nslm:appstoreSale post method now waits for appstoreSale to be register\n\n\n", "peft": "1338\tFix RegisterAppstoreSale to be synchronous.\n", "icl": "1338\tAdded a new method to the security manager to register app store sales.\n", "slm": "appstoreSale post method now waits for appstoreSale to be register\n", "nl": "fix post request", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16672, "text": "public void ProjectOutputBinPathChange_CPS()\n\n[WpfFact,WorkItem(14520,\"https://github.com/dotnet/roslyn/issues/14520\")]\n[Trait(Traits.Feature,Traits.Features.ProjectSystemShims)]\n- public void InvalidProjectOutputBinPaths_CPS()\n+ public void InvalidProjectOutputBinPaths_CPS1()\n{\nusing(var environment=new TestEnvironment())\nusing(var project1=CSharpHelpers.CreateCSharpCPSProject(environment,\"Test\",binOutputPath:null))//Null binOutputPath\n- using(var project2=CSharpHelpers.CreateCSharpCPSProject(environment,\"Test2\",binOutputPath:String.Empty))//Empty binOutputPath\n- using(var project3=CSharpHelpers.CreateCSharpCPSProject(environment,\"Test3\",binOutputPath:\"Test.dll\"))//Non-rooted binOutputPath\n{\n//Null output path is allowed.\nAssert.Equal(null,project1.BinOutputPath);\n+ }\n+ }\n\n+ [WpfFact,WorkItem(14520,\"https://github.com/dotnet/roslyn/issues/14520\")]\n+ [Trait(Traits.Feature,Traits.Features.ProjectSystemShims)]\n+ public void InvalidProjectOutputBinPaths_CPS2()\n+ {\n+ using(var environment=new TestEnvironment())\n+ using(var project2=CSharpHelpers.CreateCSharpCPSProject(environment,\"Test2\",binOutputPath:String.Empty))//Empty binOutputPath\n+ {\n//Empty output path is not allowed,it gets reset to null.\nAssert.Equal(null,project2.BinOutputPath);\n+ }\n+ }\n\n+ [WpfFact,WorkItem(14520,\"https://github.com/dotnet/roslyn/issues/14520\")]\n+ [Trait(Traits.Feature,Traits.Features.ProjectSystemShims)]\n+ public void InvalidProjectOutputBinPaths_CPS3()\n+ {\n+ using(var environment=new TestEnvironment())\n+ using(var project3=CSharpHelpers.CreateCSharpCPSProject(environment,\"Test3\",binOutputPath:\"Test.dll\"))//Non-rooted binOutputPath\n+ {\n//Non-rooted output path is not allowed,it gets reset to a temp rooted path.\nAssert.Equal(Path.Combine(Path.GetTempPath(),\"Test.dll\"),project3.BinOutputPath);\n}\n\n\npeft:1023\tFixing the tests for https://github.com/dotnet/roslyn/issues/14520\n\n\nicl:1023\tFix the issue of the\n\n\nslm:Disable null check for CSharpHelpers.CreateProjectCPSProject\n\n\n", "peft": "1023\tFixing the tests for https://github.com/dotnet/roslyn/issues/14520\n", "icl": "1023\tFix the issue of the\n", "slm": "Disable null check for CSharpHelpers.CreateProjectCPSProject\n", "nl": "Split a single test into three tests", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16673, "text": "\n- using System;\n- using System.Linq;\nusing FluentAssertions;\nusing NUnit.Framework;\n- using NzbDrone.Common.Expansive;\nusing NzbDrone.Core.Parser;\nusing NzbDrone.Core.Test.Framework;\n- using NzbDrone.Core.Tv;\n- using NzbDrone.Test.Common;\n\nnamespace NzbDrone.Core.Test.ParserTests\n{\npublic class MultiEpisodeParserFixture:CoreTest\n[TestCase(\"extant.10708.hdtv-lol.mp4\",\"Extant\",1,new[]{7,8})]\n[TestCase(\"extant.10910.hdtv-lol.mp4\",\"Extant\",1,new[]{9,10})]\n[TestCase(\"E.010910.HDTVx264REPACKLOL.mp4\",\"E\",1,new[]{9,10})]\n+ [TestCase(\"World Series of Poker-2013x15-2013x16-HD TV.mkv\",\"World Series of Poker\",2013,new[]{15,16})]\n//[TestCase(\"Adventure Time-5x01-x02-Finn the Human(2)&Jake the Dog(3)\",\"Adventure Time\",5,new[]{1,2})]\npublic void should_parse_multiple_episodes(string postTitle,string title,int season,int[]episodes)\n{\npublic static class Parser\nRegexOptions.IgnoreCase|RegexOptions.Compiled),\n\n//Multi-Part episodes without a title(S01E05.S01E06)\n- new Regex(@\"^(?:\\W * S?(?<season>(?<!\\d+)\\d{1,2}(?!\\d+))(?:(?:[ex]){1,2}(?<episode>\\d{1,3}(?!\\d+)))+){2,}\",\n+ new Regex(@\"^(?:\\W * S?(?<season>(?<!\\d+)(?:\\d{1,2}|\\d{4})(?!\\d+))(?:(?:[ex]){1,2}(?<episode>\\d{1,3}(?!\\d+)))+){2,}\",\nRegexOptions.IgnoreCase|RegexOptions.Compiled),\n\n//Multi-episode Repeated(S01E05-S01E06,1x05-1x06,etc)\n- new Regex(@\"^(?<title>.+?)(?:(\\W|_)+S?(?<season>(?<!\\d+)\\d{1,2}(?!\\d+))(?:(?:[ex]){1,2}(?<episode>\\d{1,3}(?!\\d+)))+){2,}\",\n+ new Regex(@\"^(?<title>.+?)(?:(\\W|_)+S?(?<season>(?<!\\d+)(?:\\d{1,2}|\\d{4})(?!\\d+))(?:(?:[ex]){1,2}(?<episode>\\d{1,3}(?!\\d+)))+){2,}\",\nRegexOptions.IgnoreCase|RegexOptions.Compiled),\n\n//Episodes without a title,Single(S01E05,1x05)AND Multi(S01E04E05,1x04x05,etc)\n\n\npeft:952\tAdded World Series of Poker 2013x15-2013x16-HD TV.mkv to the parser tests.\n\n\nicl:952\t)\n\n\nslm:Fixed:Parsing multiple episode without title.\n\n\n", "peft": "952\tAdded World Series of Poker 2013x15-2013x16-HD TV.mkv to the parser tests.\n", "icl": "952\t)\n", "slm": "Fixed:Parsing multiple episode without title.\n", "nl": "Fixed:Parse extended multi episode with 4 digit(year)seasons", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16674, "text": "\n<NoWarn>@DISABLEDWARNINGS@</NoWarn>\n<LangVersion>@LANGVERSION@</LangVersion>\n<HostPlatform Condition=\"'$(HostPlatform)'==''and'$(OS)'=='Windows_NT'\">win32</HostPlatform>\n- <HostPlatform Condition=\"'$(HostPlatform)'==''and'$(OS)'=='Unix'and$([System.IO.File]::Exists('/usr/lib/libc.dylib'))\">darwin</HostPlatform>\n+ <HostPlatform Condition=\"'$(HostPlatform)'==''and'$(OS)'=='Unix'and$([System.IO.File]::Exists('/usr/lib/libc.dylib'))\">macos</HostPlatform>\n<HostPlatform Condition=\"'$(HostPlatform)'==''and'$(OS)'=='Unix'\">linux</HostPlatform>\n<GenerateTargetFrameworkAttribute>false</GenerateTargetFrameworkAttribute>\n@NOSTDLIB@\n\n</PropertyGroup>\n\n<PropertyGroup>\n- <!--Set AddAdditionalExplicitAssemblyReferences to false,otherwise if targetting.NET4.0,\n+ <!--Set AddAdditionalExplicitAssemblyReferences to false,otherwise if targetting.NET4.0,\nMicrosoft.NETFramework.props will force a dependency on the assembly System.Core.This\nis a problem to compile the Mono mscorlib.dll-->\n<AddAdditionalExplicitAssemblyReferences>false</AddAdditionalExplicitAssemblyReferences>\n\n\npeft:30\tMerge pull request from xamarin/mono - master\n\n\nicl:30\tUpdate the build system to support the new macos platform\n\n\nslm:Merge pull request from jaredpar/fix - macos\n\n\n", "peft": "30\tMerge pull request from xamarin/mono - master\n", "icl": "30\tUpdate the build system to support the new macos platform\n", "slm": "Merge pull request from jaredpar/fix - macos\n", "nl": "[build]Fixed csproj.tmpl for macos:Conflict in naming of HostPlatform.", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16675, "text": "void IRequestHandler.OnResourceResponse(IWebBrowser browser,string url,int sta\n{\n\n}\n- bool IRequestHandler.GetAuthCredentials(IWebBrowser browser,bool isProxy,string host,int port,string realm,string scheme,ref string username,ref string password)\n+ bool IRequestHandler.GetAuthCredentials(IWebBrowser browser,bool isProxy,string host,int port,string realm,string scheme,ref string username,ref string password)\n{\nreturn false;\n}\n+\n# endregion\n\n# region ICookieVisitor Members\nnamespace CefSharp\n\nreturn ret;\n}\n- bool ClientAdapter::GetAuthCredentials(CefRefPtr browser,bool isProxy,const CefString&host,int port,const CefString&realm,const CefString&scheme,CefString&username,CefString&password)\n+\n+ bool ClientAdapter::GetAuthCredentials(CefRefPtr<CefBrowser>browser,bool isProxy,const CefString&host,int port,const CefString&realm,const CefString&scheme,CefString&username,CefString&password)\n{\nIRequestHandler^handler=_browserControl->RequestHandler;\nif(handler==nullptr)\n{\nreturn false;\n}\n- String^uname=nullptr;\n- String^pass=nullptr;\n- bool handled=handler->GetAuthCredentials(_browserControl,isProxy,toClr(host),port,toClr(realm),toClr(scheme),uname,pass);\n+\n+ String^usernameString=nullptr;\n+ String^passwordString=nullptr;\n+ bool handled=handler->GetAuthCredentials(_browserControl,isProxy,toClr(host),port,toClr(realm),toClr(scheme),usernameString,passwordString);\n\n- if(uname!=nullptr)\n- username=toNative(uname);\n- if(pass!=nullptr)\n- password=toNative(pass);\n+ if(usernameString!=nullptr)\n+ {\n+ username=toNative(usernameString);\n+ }\n+ if(passwordString!=nullptr)\n+ {\n+ password=toNative(passwordString);\n+ }\n\nreturn handled;\n}\n+\nvoid ClientAdapter::OnResourceResponse(CefRefPtr<CefBrowser>browser,const CefString&url,CefRefPtr<CefResponse>response,CefRefPtr<CefContentFilter>&filter)\n{\nIRequestHandler^handler=_browserControl->RequestHandler;\nnamespace CefSharp\nvirtual DECL bool OnBeforeBrowse(CefRefPtr<CefBrowser>browser,CefRefPtr<CefFrame>frame,CefRefPtr<CefRequest>request,NavType navType,bool isRedirect)OVERRIDE;\nvirtual DECL bool OnBeforeResourceLoad(CefRefPtr<CefBrowser>browser,CefRefPtr<CefRequest>request,CefString&redirectUrl,CefRefPtr<CefStreamReader>&resourceStream,CefRefPtr<CefResponse>response,int loadFlags)OVERRIDE;\nvirtual DECL void OnResourceResponse(CefRefPtr<CefBrowser>browser,const CefString&url,CefRefPtr<CefResponse>response,CefRefPtr<CefContentFilter>&filter)OVERRIDE;\n- virtual DECL bool GetAuthCredentials(CefRefPtr browser,bool isProxy,const CefString&host,int port,const CefString&realm,const CefString&scheme,CefString&username,CefString&password)OVERRIDE;\n+ virtual DECL bool GetAuthCredentials(CefRefPtr<CefBrowser>browser,bool isProxy,const CefString&host,int port,const CefString&realm,const CefString&scheme,CefString&username,CefString&password)OVERRIDE;\n\n//CefDisplayHandler\nvirtual DECL void OnAddressChange(CefRefPtr<CefBrowser>browser,CefRefPtr<CefFrame>frame,const CefString&url)OVERRIDE;\nnamespace CefSharp\nbool OnBeforeBrowse(IWebBrowser^browser,IRequest^request,NavigationType naigationvType,bool isRedirect);\nbool OnBeforeResourceLoad(IWebBrowser^browser,IRequestResponse^requestResponse);\nvoid OnResourceResponse(IWebBrowser^browser,String^url,int status,String^statusText,String^mimeType,WebHeaderCollection^headers);\n- bool GetAuthCredentials(IWebBrowser^browser,bool isProxy,String^host,int port,String^realm,String^scheme,String^%username,String^%password);\n+ bool GetAuthCredentials(IWebBrowser^browser,bool isProxy,String^host,int port,String^realm,String^scheme,String^%username,String^%password);\n};\n}\n\n\npeft:1533\tFixes issue with GetAuthCredentials not being called.\n\n\nicl:1533\tStreamReader\n\n\nslm:removed unused method.\n\n\n", "peft": "1533\tFixes issue with GetAuthCredentials not being called.\n", "icl": "1533\tStreamReader\n", "slm": "removed unused method.\n", "nl": "clean up GetAuthCredentials()pr formatting", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16676, "text": "public override void WriteCommentLine(ITextOutput output,string comment)\nbreak;\ndefault:\noutput.WriteLine(\"WRN:Unexpected variable location type\");\n+ break;\n}\noutput.WriteLine(\"\");\n}\n\n\npeft:112\tAdded a warning message for unhandled variable location types.\n\n\nicl:112\tFix comment writing\n\n\nslm:Added missing break\n\n\n", "peft": "112\tAdded a warning message for unhandled variable location types.\n", "icl": "112\tFix comment writing\n", "slm": "Added missing break\n", "nl": "Fix build.", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16677, "text": "public CatchPlayfield(BeatmapDifficulty difficulty,Func<CatchHitObject,Drawabl\n\nSize=new Vector2(0.86f);//matches stable's vertical offset for catcher plate\n\n- InternalChild=new Container\n+ InternalChild=new PlayfieldLayer\n{\n- Anchor=Anchor.Centre,\n- Origin=Anchor.Centre,\nRelativeSizeAxes=Axes.Both,\n- FillMode=FillMode.Fit,\n- FillAspectRatio=4f/3,\n- Child=new PlayfieldLayer\n+ Children=new Drawable[]\n{\n- RelativeSizeAxes=Axes.Both,\n- Children=new Drawable[]\n+ explodingFruitContainer=new Container\n{\n- explodingFruitContainer=new Container\n- {\n- RelativeSizeAxes=Axes.Both,\n- },\n- catcherArea=new CatcherArea(difficulty)\n- {\n- GetVisualRepresentation=getVisualRepresentation,\n- ExplodingFruitTarget=explodingFruitContainer,\n- Anchor=Anchor.BottomLeft,\n- Origin=Anchor.TopLeft,\n- },\n- HitObjectContainer\n- }\n+ RelativeSizeAxes=Axes.Both,\n+ },\n+ catcherArea=new CatcherArea(difficulty)\n+ {\n+ GetVisualRepresentation=getVisualRepresentation,\n+ ExplodingFruitTarget=explodingFruitContainer,\n+ Anchor=Anchor.BottomLeft,\n+ Origin=Anchor.TopLeft,\n+ },\n+ HitObjectContainer\n}\n};\n}\n\n//Copyright(c)2007-2018 ppy Pty Ltd<<EMAIL>>.\n//Licensed under the MIT Licence-https://raw.githubusercontent.com/ppy/osu/master/LICENCE\n\n+ using osu.Framework.Graphics;\nusing osu.Framework.Graphics.Containers;\nusing OpenTK;\n\nnamespace osu.Game.Rulesets.Catch.UI\n{\npublic class PlayfieldLayer:Container\n{\n- protected override void Update()\n+ protected override Container<Drawable>Content=>content;\n+ private readonly Container content;\n+\n+ public PlayfieldLayer()\n+ {\n+ InternalChild=new Container\n+ {\n+ Anchor=Anchor.Centre,\n+ Origin=Anchor.Centre,\n+ RelativeSizeAxes=Axes.Both,\n+ FillMode=FillMode.Fit,\n+ FillAspectRatio=4f/3,\n+ Child=content=new ScalingContainer{RelativeSizeAxes=Axes.Both}\n+ };\n+ }\n+\n+ ///<summary>\n+ ///A<see cref=\"Container\"/>which scales its content relative to a target width.\n+ ///</summary>\n+ private class ScalingContainer:Container\n{\n- base.Update();\n+ protected override void Update()\n+ {\n+ base.Update();\n\n- Scale=new Vector2(Parent.ChildSize.X/CatchPlayfield.BASE_WIDTH);\n- Size=Vector2.Divide(Vector2.One,Scale);\n+ Scale=new Vector2(Parent.ChildSize.X/CatchPlayfield.BASE_WIDTH);\n+ Size=Vector2.Divide(Vector2.One,Scale);\n+ }\n}\n}\n}\n\n\npeft:497\tFix scaling of playfield layer.\n\n\nicl:497\tRe - design Catch\n\n\nslm:Remove unnecessary code\n\n\n", "peft": "497\tFix scaling of playfield layer.\n", "icl": "497\tRe - design Catch\n", "slm": "Remove unnecessary code\n", "nl": "Move aspect adjustments out of CatchPlayfield", "lang": "csharp", "label": ["Doc&Code"], "Comments": []}
{"id": 16678, "text": "This is Mono.\n4.Git submodules maintenance\n=============================\n\n- These instructions are for developers working on code in this repository.End users do not need to be\n- concerned with the procedures described below.The description applies to each of the submodules used\n- by mono.To list the submodules in use run:\n-\n- git submodule\n-\n- If you have write access to the submodule repository do your work on it in a separate location,do not ever\n- do any work in the mono copy of the submodule.\n-\n- All submodules should reside under the external/directory off the top level Mono directory.If you want to add\n- a new submodule,issue the following command from the Mono topmost directory:\n-\n- git submodule add REPOSITORY_URL external/MODULE_NAME\n-\n- After that commit and push the changes to.gitmodule and the submodule itself.\n-\n- 4.1 Submodule repository of origin maintenance\n- ==============================================\n-\n- The submodule repository of origin(at the REPOSITORY_URL above)must always be modified outside the Mono directory.\n- The repository may be a fork or clone of yet another GIT repository,either on github or elsewhere.If this is the case,\n- you must configure your clone of it by adding a remote reference to the upstream repository:\n-\n- git remote add upstream UPSTREAM_URL\n-\n- When there exist upstream changes you need to merge,the following command needs to be used:\n-\n- git fetch upstream/master\n-\n- followed by\n-\n- git merge upstream/BRANCH_NAME\n-\n- and as soon as all the possible confilits are resolved,push the freshly integrated changes back to our repository\n-\n- git push origin/BRANCH_NAME\n-\n- 4.2 Submodule maintenance in Mono repository\n- ============================================\n-\n- When the submodule repository of origin is ready to be used by Mono,you need to go to the top level directory of\n- Mono repository clone and make sure the submodules are checked out and up to date:\n-\n- git submodule init\n- git submodule update--recursive\n-\n- Then in order to integrate changes from the submodule repository of origin:\n-\n- cd external/MODULE_NAME\n- git pull\n-\n- you can,of course,use a a specific commit in the'git pull'above instead of the default HEAD.When the changes are\n- checked out,commit the changes to the Mono repository:\n-\n- cd../..\n- git add external/MODULE_NAME\n- git commit-m\"Module MODULE_NAME updated\"\n- git push\n-\n- It is_extremely_important not to end the'external/MODULE_NAME'reference above with a/since that will make git remove\n- the submodule reference and commit all its contents as normal files to Mono directory.It is also required that you_always_\n- push the submodule changes_before_any and all work requiring the changes is done in the Mono repository.\n+ Read documentation at http://mono-project.com/Git_Submodule_Maintenance\n\n\npeft:228\tAdded a few more instructions to the submodule maintenance document.\n\n\nicl:228\t(maint)update tab expansion\n\n\nslm:Update readme.md()\n\n\n", "peft": "228\tAdded a few more instructions to the submodule maintenance document.\n", "icl": "228\t(maint)update tab expansion\n", "slm": "Update readme.md()\n", "nl": "Replaced git submodules docs with a link to the wiki", "lang": "csharp", "label": ["Doc-mod"], "Comments": []}
{"id": 16679, "text": "function Redirect-Temp(){\nCopy-Item(Join-Path$repoDir\"src\\Workspaces\\CoreTestUtilities\\Resources\\Directory.Build.props\")$temp\nCopy-Item(Join-Path$repoDir\"src\\Workspaces\\CoreTestUtilities\\Resources\\Directory.Build.targets\")$temp\nCopy-Item(Join-Path$repoDir\"src\\Workspaces\\CoreTestUtilities\\Resources\\Directory.Build.rsp\")$temp\n+ Copy-Item(Join-Path$repoDir\"src\\Workspaces\\CoreTestUtilities\\Resources\\NuGet.Config\")$temp\n${env:TEMP}=$temp\n${env:TMP}=$temp\n}\nnew file mode 100644\nindex **********00..76b75d726b75\nmmm/dev/null\n\n+ ﻿<?xml version=\"1.0\"encoding=\"utf-8\"?>\n+ <configuration>\n+ <!--Provide a default package restore source for test projects.-->\n+ <packageRestore>\n+ <add key=\"enabled\"value=\"true\"/>\n+ </packageRestore>\n+ <packageSources>\n+ <clear/>\n+ <add key=\"api.nuget.org\"value=\"https://api.nuget.org/v3/index.json\"protocolVersion=\"3\"/>\n+ <add key=\"dotnet-core\"value=\"https://dotnet.myget.org/F/dotnet-core/api/v3/index.json\"protocolVersion=\"3\"/>\n+ </packageSources>\n+ </configuration>\n\n\npeft:1479\tAdd NuGet.Config to CoreTestUtilities to enable package restore for test projects.\n\n\nicl:1479\tAdd NuGet.Config to\n\n\nslm:Merge pull request from dotnet/core - setup\n\n\n", "peft": "1479\tAdd NuGet.Config to CoreTestUtilities to enable package restore for test projects.\n", "icl": "1479\tAdd NuGet.Config to\n", "slm": "Merge pull request from dotnet/core - setup\n", "nl": "Merge pull request from sharwell/nuget - config - for - testing", "lang": "csharp", "label": ["Feat-mod"], "Comments": []}
{"id": 16680, "text": "protected Listener(ServiceContext serviceContext):base(serviceContext)\n///</summary>\nprotected abstract UvStreamHandle CreateListenSocket(string host,int port);\n\n- protected void ConnectionCallback(UvStreamHandle stream,int status,Exception error,object state)\n+ protected static void ConnectionCallback(UvStreamHandle stream,int status,Exception error,object state)\n{\n+ var listener=(Listener)state;\nif(error!=null)\n{\n- Log.LogError(\"Listener.ConnectionCallback\",error);\n+ listener.Log.LogError(\"Listener.ConnectionCallback\",error);\n}\nelse\n{\n- ((Listener)state).OnConnection(stream,status);\n+ listener.OnConnection(stream,status);\n}\n}\n\npublic void Init(UvLoopHandle loop)\nLibuv.pipe_connect(this,pipe,name,_uv_connect_cb);\n}\n\n- private static void UvConnectCb(IntPtr ptr,int status)\n+ private static void UvConnectCb(IntPtr ptr,int status)\n{\nvar req=FromIntPtr<UvConnectRequest>(ptr);\nreq.Unpin();\n\n\npeft:1725\tFix crash when logging.\n\n\nicl:1725\tFixes a crash when connecting to a named pipe.\n\n\nslm:Cleanup UvConnectRequest.ConnectionCallback usage\n\n\n", "peft": "1725\tFix crash when logging.\n", "icl": "1725\tFixes a crash when connecting to a named pipe.\n", "slm": "Cleanup UvConnectRequest.ConnectionCallback usage\n", "nl": "Make ConnectionCallback static again.", "lang": "csharp", "label": ["Ref-con"], "Comments": []}
{"id": 16681, "text": "protected boolean isNewObject(DBNNode objectValue)\nreturn!objectValue.isPersisted();\n}\n\n+ @Override\n+ protected boolean isReadOnlyList(){\n+ DBPDataSourceContainer container=getDataSourceContainer();\n+ return container!=null&&container.isConnectionReadOnly();\n+ }\n+\n@NotNull\n@Override\nprotected String getListConfigId(List<Class<?>>classList){\nprotected boolean isNewObject(OBJECT_TYPE objectValue){\nreturn false;\n}\n\n+ protected boolean isReadOnlyList(){\n+ return false;\n+ }\n+\n@NotNull\nprotected Set<DBPPropertyDescriptor>getAllProperties(){\nObjectColumn[]columns=columnController.getColumnsData(ObjectColumn.class);\npublic EditorActivationStrategy(ColumnViewer viewer){\n\n@Override\nprotected boolean isEditorActivationEvent(ColumnViewerEditorActivationEvent event){\n+ if(ObjectListControl.this.isReadOnlyList()){\n+ return false;\n+ }\nViewerCell cell=(ViewerCell)event.getSource();\nif(renderer.isHyperlink(getCellValue(cell.getElement(),cell.getColumnIndex()))&&\ngetItemsViewer().getControl().getCursor()==getItemsViewer().getControl().getDisplay().getSystemCursor(SWT.CURSOR_HAND)){\nprivate boolean supportsObjectRename(){\n}\n\nprivate void createPropertyEditor(Composite group,DBPPropertyDescriptor prop){\n+ DBSObject databaseObject=input.getDatabaseObject();\n+ boolean isReadOnlyCon=databaseObject==null||databaseObject.getDataSource().getContainer().isConnectionReadOnly();\nif(prop==null){\nUIUtils.createEmptyLabel(group,2,1);\n}else{\n- boolean editable=prop.isEditable(curPropertySource.getEditableValue())||\n- (prop.getId().equals(DBConstants.PROP_ID_NAME)&&supportsObjectRename());\n+ boolean editable=!isReadOnlyCon&&(prop.isEditable(curPropertySource.getEditableValue())||\n+ (prop.getId().equals(DBConstants.PROP_ID_NAME)&&supportsObjectRename()));\nClass<?>propType=prop.getDataType();\nObject propertyValue=curPropertySource.getPropertyValue(null,prop.getId());\nif(propertyValue==null&&prop instanceof ObjectPropertyDescriptor&&((ObjectPropertyDescriptor)prop).isOptional()){\n\n\npeft:1339\tFix read only list\n\n\nicl:1339\tAdds\"- fno - autolink\"to ObjC compilation\n\n\nslm:Object list object editor fix(use readonly list)\n\n\n", "peft": "1339\tFix read only list\n", "icl": "1339\tAdds\"- fno - autolink\"to ObjC compilation\n", "slm": "Object list object editor fix(use readonly list)\n", "nl": "Read - only connections:disable metadata editor", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16682, "text": "public void shouldGetAllCarsNonUnique(){\nAssert.assertEquals(1,cars.size());\n}\n}\n+\n+ @Test\n+ public void shouldGetAllCarsAndDetectAssociationType(){\n+ try(SqlSession sqlSession=sqlSessionFactory.openSession()){\n+ Mapper mapper=sqlSession.getMapper(Mapper.class);\n+ List<Car>cars=mapper.getCarsAndDetectAssociationType();\n+ Assert.assertEquals(4,cars.size());\n+ Assert.assertEquals(\"VW\",cars.get(0).getType());\n+ Assert.assertNotNull(cars.get(0).getEngine());\n+ Assert.assertNull(cars.get(0).getBrakes());\n+ Assert.assertEquals(\"Opel\",cars.get(1).getType());\n+ Assert.assertNull(cars.get(1).getEngine());\n+ Assert.assertNotNull(cars.get(1).getBrakes());\n+ }\n+ }\n\n}\n\n\nList<Car>getCars();\nList<Car>getCars2();\n- List<Car>getCars3();\n-\n+ List<Car>getCarsAndDetectAssociationType();\n}\n\nselect * from cars\n</select>\n\n- <select id=\"getCarsNonUnique\"resultMap=\"carResult\">\n- select 1 as carid,cartype,enginetype,enginecylinders,brakestype from cars\n- </select>\n-\n<select id=\"getCars2\"resultMap=\"carResult\">\nselect 1 as carid,cartype,enginetype,enginecylinders,brakestype from cars where carid in(1,2)\n</select>\n\n+ <resultMap type=\"org.apache.ibatis.submitted.associationtest.Car\"id=\"carResultTypeDetect\">\n+ <id column=\"carid\"property=\"id\"/>\n+ <result column=\"cartype\"property=\"type\"/>\n+ <association property=\"engine\">\n+ <result column=\"enginetype\"property=\"type\"/>\n+ <result column=\"enginecylinders\"property=\"cylinders\"/>\n+ </association>\n+ <association property=\"brakes\">\n+ <result column=\"brakesType\"property=\"type\"/>\n+ </association>\n+ </resultMap>\n+\n+ <select id=\"getCarsAndDetectAssociationType\"resultMap=\"carResultTypeDetect\">\n+ select * from cars\n+ </select>\n+\n</mapper>\n\n\npeft:473\tAdd test to check association type detection\n\n\nicl:473\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file\n\n\nslm:Merge pull request from wenshao/master\n\n\n", "peft": "473\tAdd test to check association type detection\n", "icl": "473\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file\n", "slm": "Merge pull request from wenshao/master\n", "nl": "Merge pull request from berniegp/berniegp/1381", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16683, "text": "function nextCommand(){\nvar xmlHttp=XmlHttp.create();\n\ntry{\n- alert(\"postResult==\"+postResult);\n+ //alert(\"postResult==\"+postResult);\nif(postResult==\"START\"){\nxmlHttp.open(\"GET\",\"driver?seleniumStart=true\",false);\n}else{\n\n\npeft:827\tFixing a bug\n\n\nicl:827\tMINOR:Fix state transition diagram for stream threads()\n\n\nslm:Commented out debugging code\n\n\n", "peft": "827\tFixing a bug\n", "icl": "827\tMINOR:Fix state transition diagram for stream threads()\n", "slm": "Commented out debugging code\n", "nl": "remove debugging that stops tests in tracks", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16684, "text": "public static RequestOptions centerCropTransform(){\nreturn centerCropOptions;\n}\n\n- /* *\n- * Returns a{@link RequestOptions}object with{@link RequestOptions # circleCrop()}set.\n- *\n- *@deprecated Use{@link # circleCropTransform()}.\n- */\n- @Deprecated\n- public static RequestOptions circleCropTransform(Context context){\n- return circleCropTransform();\n- }\n-\n/* *\n* Returns a{@link RequestOptions}object with{@link RequestOptions # circleCrop()}set.\n*/\n\n\npeft:1628\tMerge pull request from jacobmonteiro/master\n\n\nicl:1628\tFix YAML test for RequestOptions.circleCropTransform()\n\n\nslm:Remove deprecated RequestOptions.circleCrop()\n\n\n", "peft": "1628\tMerge pull request from jacobmonteiro/master\n", "icl": "1628\tFix YAML test for RequestOptions.circleCropTransform()\n", "slm": "Remove deprecated RequestOptions.circleCrop()\n", "nl": "Remove circleCropTransform(Context)", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16685, "text": "private HttpServer createHttpServer(){\nif(getCompression()!=null&&getCompression().getEnabled()){\noptions.compression(getCompression().getMinResponseSize());\n}\n+ options.preferNative(false);\napplyCustomizers(options);\n}).build();\n}\n\n\npeft:1832\tFix:Netty server does not work with GraalVM native image\n\n\nicl:1832\tMerge pull request elastic/elasticsearch from jbudz/\n\n\nslm:Do not prefer native compression on Android Https\n\n\n", "peft": "1832\tFix:Netty server does not work with GraalVM native image\n", "icl": "1832\tMerge pull request elastic/elasticsearch from jbudz/\n", "slm": "Do not prefer native compression on Android Https\n", "nl": "Disable Reactor Netty's use of kqueue/epoll", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16686, "text": "\n\nimport org.apache.flink.configuration.Configuration;\nimport org.apache.flink.configuration.RestOptions;\n+ import org.apache.flink.configuration.SecurityOptions;\nimport org.apache.flink.runtime.clusterframework.BootstrapTools;\nimport org.apache.flink.util.TestLogger;\n+ import org.apache.flink.yarn.YarnConfigKeys;\n\nimport org.apache.hadoop.yarn.api.ApplicationConstants;\nimport org.junit.ClassRule;\n\n\nimport static org.hamcrest.Matchers.equalTo;\nimport static org.hamcrest.Matchers.is;\n+ import static org.hamcrest.Matchers.nullValue;\nimport static org.junit.Assert.assertThat;\n\n/* *\npublic void testRestPortAndBindingPortSpecified()throws IOException{\nassertThat(configuration.getString(RestOptions.BIND_PORT),is(equalTo(bindingPortRange)));\n}\n\n+ @Test\n+ public void testParsingValidKerberosEnv()throws IOException{\n+ final Configuration initialConfiguration=new Configuration();\n+ Map<String,String>env=new HashMap<>();\n+ File keytabFile=TEMPORARY_FOLDER.newFile();\n+ env.put(YarnConfigKeys.LOCAL_KEYTAB_PATH,keytabFile.getAbsolutePath());\n+ env.put(YarnConfigKeys.KEYTAB_PRINCIPAL,\"starlord\");\n+\n+ Configuration configuration=loadConfiguration(initialConfiguration,env);\n+\n+ assertThat(configuration.get(SecurityOptions.KERBEROS_LOGIN_KEYTAB),is(keytabFile.getAbsolutePath()));\n+ assertThat(configuration.get(SecurityOptions.KERBEROS_LOGIN_PRINCIPAL),is(\"starlord\"));\n+ }\n+\n+ @Test\n+ public void testParsingKerberosEnvWithMissingKeytab()throws IOException{\n+ final Configuration initialConfiguration=new Configuration();\n+ Map<String,String>env=new HashMap<>();\n+ env.put(YarnConfigKeys.LOCAL_KEYTAB_PATH,\"/hopefully/doesnt/exist\");\n+ env.put(YarnConfigKeys.KEYTAB_PRINCIPAL,\"starlord\");\n+\n+ Configuration configuration=loadConfiguration(initialConfiguration,env);\n+\n+ //both keytab and principal should be null\n+ assertThat(configuration.get(SecurityOptions.KERBEROS_LOGIN_KEYTAB),nullValue());\n+ assertThat(configuration.get(SecurityOptions.KERBEROS_LOGIN_PRINCIPAL),nullValue());\n+ }\n+\n@Nonnull\nprivate static Configuration loadConfiguration(Configuration initialConfiguration)throws IOException{\n+ final Map<String,String>env=new HashMap<>();\n+ return loadConfiguration(initialConfiguration,env);\n+ }\n+\n+ @Nonnull\n+ private static Configuration loadConfiguration(Configuration initialConfiguration,Map<String,String>env)throws IOException{\nfinal File workingDirectory=TEMPORARY_FOLDER.newFolder();\n- final Map<String,String>env=new HashMap<>(4);\nenv.put(ApplicationConstants.Environment.NM_HOST.key(),\"foobar\");\n-\nBootstrapTools.writeConfiguration(initialConfiguration,new File(workingDirectory,\"flink-conf.yaml\"));\nreturn YarnEntrypointUtils.loadConfiguration(workingDirectory.getAbsolutePath(),env);\n}\n-\n}\n\n\npeft:561\t[FLINK - 15797][yarn]Add test for parsing valid Kerberos env variables\n\n\nicl:561\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required changes\n\n\nslm:[FLINK - 15917][yarn]Add tests for Kerberos config parsing\n\n\n", "peft": "561\t[FLINK - 15797][yarn]Add test for parsing valid Kerberos env variables\n", "icl": "561\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required changes\n", "slm": "[FLINK - 15917][yarn]Add tests for Kerberos config parsing\n", "nl": "[FLINK - 11088]Add unit test for Kerberos parsing in YarnEntrypointUtilsTest", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16687, "text": "THE SOFTWARE.\n- ->\n\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\">\n- <f:entry title=\"Command\"\n- description=\"See&lt;a href='${rootURL}/env-vars.html'target=_new>the list of available environment variables&lt;/a>\">\n+ <f:entry title=\"${%Command}\"\n+ description=\"${%description(rootURL)}\">\n<f:textarea name=\"batchFile\"value=\"${instance.command}\"/>\n</f:entry>\n</j:jelly>\n\\No newline at end of file\nnew file mode 100644\nindex **********0..e416300f208\nmmm/dev/null\n\n+ # The MIT License\n+ #\n+ # Copyright(c)2004-2009,Sun Microsystems,Inc.,Kohsuke Kawaguchi\n+ #\n+ # Permission is hereby granted,free of charge,to any person obtaining a copy\n+ # of this software and associated documentation files(the\"Software\"),to deal\n+ # in the Software without restriction,including without limitation the rights\n+ # to use,copy,modify,merge,publish,distribute,sublicense,and/or sell\n+ # copies of the Software,and to permit persons to whom the Software is\n+ # furnished to do so,subject to the following conditions:\n+ #\n+ # The above copyright notice and this permission notice shall be included in\n+ # all copies or substantial portions of the Software.\n+ #\n+ # THE SOFTWARE IS PROVIDED\"AS IS\",WITHOUT WARRANTY OF ANY KIND,EXPRESS OR\n+ # IMPLIED,INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n+ # FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE\n+ # AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,DAMAGES OR OTHER\n+ # LIABILITY,WHETHER IN AN ACTION OF CONTRACT,TORT OR OTHERWISE,ARISING FROM,\n+ # OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n+ # THE SOFTWARE.\n+\n+ description=See<a href=\"{0}/env-vars.html\"target=_new>the list of available environment variables</a>\nnew file mode 100644\nindex **********0..a5e9d4d2d84\nmmm/dev/null\n\n+ # The MIT License\n+ #\n+ # Copyright(c)2004-2009,Sun Microsystems,Inc.,Kohsuke Kawaguchi\n+ #\n+ # Permission is hereby granted,free of charge,to any person obtaining a copy\n+ # of this software and associated documentation files(the\"Software\"),to deal\n+ # in the Software without restriction,including without limitation the rights\n+ # to use,copy,modify,merge,publish,distribute,sublicense,and/or sell\n+ # copies of the Software,and to permit persons to whom the Software is\n+ # furnished to do so,subject to the following conditions:\n+ #\n+ # The above copyright notice and this permission notice shall be included in\n+ # all copies or substantial portions of the Software.\n+ #\n+ # THE SOFTWARE IS PROVIDED\"AS IS\",WITHOUT WARRANTY OF ANY KIND,EXPRESS OR\n+ # IMPLIED,INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n+ # FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT.IN NO EVENT SHALL THE\n+ # AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,DAMAGES OR OTHER\n+ # LIABILITY,WHETHER IN AN ACTION OF CONTRACT,TORT OR OTHERWISE,ARISING FROM,\n+ # OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN\n+ # THE SOFTWARE.\n+\n+ Command=\\u30B3\\u30DE\\u30F3\\u30C9\n+ description=<a href=\"{0}/env-vars.html\"target=_new>\\u30D3\\u30EB\\u30C9\\u304B\\u3089\\u5229\\u7528\\u53EF\\u80FD\\u306A\\u74B0\\u5883\\u5909\\u6570\\u306E\\u4E00\\u89A7</a>\n\n\npeft:1741\t[FIXED - 1199]Translated the batch file configuration page\n\n\nicl:1741\t[Flink - 15135][e2e][Mesos]Adding Dockerfiles relevant to Mesos setup\n\n\nslm:i18n and added Japanese localization.\n\n\n", "peft": "1741\t[FIXED - 1199]Translated the batch file configuration page\n", "icl": "1741\t[Flink - 15135][e2e][Mesos]Adding Dockerfiles relevant to Mesos setup\n", "slm": "i18n and added Japanese localization.\n", "nl": "i18N and L10N(ja)", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16688, "text": "otvorenie okna na popredí</string>\n<string name=\"title_history_view\">Pozreté</string>\n<string name=\"history_disabled\">História je zakázaná</string>\n<string name=\"action_history\">História</string>\n- <string name=\"history_empty\">História je prázdna.</string>\n+ <string name=\"history_empty\">História je prázdna</string>\n<string name=\"history_cleared\">História bola vyčistená</string>\n\n<string name=\"settings_category_player_title\">Prehrávač</string>\n\n\npeft:1027\tTranslated using Weblate(Slovak)\n\n\nicl:1027\tMINOR:Fix typo in history view()\n\n\nslm:Translated using Weblate(Czech)\n\n\n", "peft": "1027\tTranslated using Weblate(Slovak)\n", "icl": "1027\tMINOR:Fix typo in history view()\n", "slm": "Translated using Weblate(Czech)\n", "nl": "Translated using Weblate(Slovak)", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16689, "text": "void init(Channel channel)throws Exception{\np.addLast(new ChannelInitializer<Channel>(){\n@Override\npublic void initChannel(Channel ch)throws Exception{\n- ChannelPipeline pipeline=ch.pipeline();\n+ final ChannelPipeline pipeline=ch.pipeline();\nChannelHandler handler=config.handler();\nif(handler!=null){\npipeline.addLast(handler);\n}\n- pipeline.addLast(new ServerBootstrapAcceptor(\n- currentChildGroup,currentChildHandler,currentChildOptions,currentChildAttrs));\n+\n+ //We add this handler via the EventLoop as the user may have used a ChannelInitializer as handler.\n+ //In this case the initChannel(...)method will only be called after this method returns.Because\n+ //of this we need to ensure we add our handler in a delayed fashion so all the users handler are\n+ //placed in front of the ServerBootstrapAcceptor.\n+ ch.eventLoop().execute(new Runnable(){\n+ @Override\n+ public void run(){\n+ pipeline.addLast(new ServerBootstrapAcceptor(\n+ currentChildGroup,currentChildHandler,currentChildOptions,currentChildAttrs));\n+ }\n+ });\n}\n});\n}\n\n*/\npackage io.netty.bootstrap;\n\n+ import io.netty.channel.Channel;\n+ import io.netty.channel.ChannelHandler;\nimport io.netty.channel.ChannelHandlerAdapter;\nimport io.netty.channel.ChannelHandlerContext;\nimport io.netty.channel.ChannelInboundHandlerAdapter;\n+ import io.netty.channel.ChannelInitializer;\n+ import io.netty.channel.DefaultEventLoopGroup;\n+ import io.netty.channel.EventLoopGroup;\n+ import io.netty.channel.local.LocalAddress;\n+ import io.netty.channel.local.LocalChannel;\nimport io.netty.channel.local.LocalEventLoopGroup;\nimport io.netty.channel.local.LocalServerChannel;\nimport org.junit.Test;\n\n+ import java.util.UUID;\nimport java.util.concurrent.CountDownLatch;\nimport java.util.concurrent.atomic.AtomicReference;\n\npublic void handlerAdded(ChannelHandlerContext ctx)throws Exception{\ngroup.shutdownGracefully();\n}\n}\n+\n+ @Test(timeout=3000)\n+ public void testParentHandler()throws Exception{\n+ testParentHandler(false);\n+ }\n+\n+ @Test(timeout=3000)\n+ public void testParentHandlerViaChannelInitializer()throws Exception{\n+ testParentHandler(true);\n+ }\n+\n+ private static void testParentHandler(boolean channelInitializer)throws Exception{\n+ final LocalAddress addr=new LocalAddress(UUID.randomUUID().toString());\n+ final CountDownLatch readLatch=new CountDownLatch(1);\n+ final CountDownLatch initLatch=new CountDownLatch(1);\n+\n+ final ChannelHandler handler=new ChannelInboundHandlerAdapter(){\n+ @Override\n+ public void handlerAdded(ChannelHandlerContext ctx)throws Exception{\n+ initLatch.countDown();\n+ super.handlerAdded(ctx);\n+ }\n+\n+ @Override\n+ public void channelRead(ChannelHandlerContext ctx,Object msg)throws Exception{\n+ readLatch.countDown();\n+ super.channelRead(ctx,msg);\n+ }\n+ };\n+\n+ EventLoopGroup group=new DefaultEventLoopGroup(1);\n+ Channel sch=null;\n+ Channel cch=null;\n+ try{\n+ ServerBootstrap sb=new ServerBootstrap();\n+ sb.channel(LocalServerChannel.class)\n+ .group(group)\n+ .childHandler(new ChannelInboundHandlerAdapter());\n+ if(channelInitializer){\n+ sb.handler(new ChannelInitializer<Channel>(){\n+ @Override\n+ protected void initChannel(Channel ch)throws Exception{\n+ ch.pipeline().addLast(handler);\n+ }\n+ });\n+ }else{\n+ sb.handler(handler);\n+ }\n+\n+ Bootstrap cb=new Bootstrap();\n+ cb.group(group)\n+ .channel(LocalChannel.class)\n+ .handler(new ChannelInboundHandlerAdapter());\n+\n+ sch=sb.bind(addr).syncUninterruptibly().channel();\n+\n+ cch=cb.connect(addr).syncUninterruptibly().channel();\n+\n+ initLatch.await();\n+ readLatch.await();\n+ }finally{\n+ if(sch!=null){\n+ sch.close().syncUninterruptibly();\n+ }\n+ if(cch!=null){\n+ cch.close().syncUninterruptibly();\n+ }\n+ group.shutdownGracefully();\n+ }\n+ }\n}\n\n\npeft:730\tAdded a test to ensure that the ServerBootstrapAcceptor is added to the pipeline in the correct order.\n\n\nicl:730\tAccept alpha[0, 255]()\n\n\nslm:Add EventLoop to ServerBootstrapAcceptor.initChannel\n\n\n", "peft": "730\tAdded a test to ensure that the ServerBootstrapAcceptor is added to the pipeline in the correct order.\n", "icl": "730\tAccept alpha[0, 255]()\n", "slm": "Add EventLoop to ServerBootstrapAcceptor.initChannel\n", "nl": "[]Ensure using a ChannelInitializer via ServerBootstrap.handler(...)produce correct ordering.", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16690, "text": "\n<li class=rfe>\nOffer alternate error message for pattern-based project naming strategy.\n(<a href=\"https://github.com/jenkinsci/jenkins/pull/914\">pull request 914</a>)\n+ <li class=rfe>\n+ Add support for hiding build slaves from users.\n+ (<a href=\"https://issues.jenkins-ci.org/browse/JENKINS-16502\">issue 16502</a>)\n</ul>\n</div><!--=TRUNK-END=-->\n\npublic boolean accept(File dir,String name){\npublic static final Permission DISCONNECT=new Permission(PERMISSIONS,\"Disconnect\",Messages._Computer_DisconnectPermission_Description(),Jenkins.ADMINISTER,PermissionScope.COMPUTER);\npublic static final Permission CONNECT=new Permission(PERMISSIONS,\"Connect\",Messages._Computer_ConnectPermission_Description(),DISCONNECT,PermissionScope.COMPUTER);\npublic static final Permission BUILD=new Permission(PERMISSIONS,\"Build\",Messages._Computer_BuildPermission_Description(),Permission.WRITE,PermissionScope.COMPUTER);\n+ /* *\n+ *@since 1.533\n+ */\n+ public static final Permission VIEW=new Permission(PERMISSIONS,\"View\",Messages._Computer_ViewPermission_Description(),Permission.READ,PermissionScope.COMPUTER);\n\nprivate static final Logger LOGGER=Logger.getLogger(Computer.class.getName());\n}\npublic boolean isUpgradedFromBefore(VersionNumber v){\n* Gets the read-only list of all{@link Computer}s.\n*/\npublic Computer[]getComputers(){\n- Computer[]r=computers.values().toArray(new Computer[computers.size()]);\n+ Collection<Computer>computers=new ArrayList<Computer>(this.computers.size());\n+ for(Computer c:this.computers.values()){\n+ if(c.hasPermission(Computer.VIEW))\n+ computers.add(c);\n+ }\n+ Computer[]r=computers.toArray(new Computer[computers.size()]);\nArrays.sort(r,new Comparator<Computer>(){\nfinal Collator collator=Collator.getInstance();\npublic int compare(Computer lhs,Computer rhs){\npublic Computer getComputer(@Argument(required=true,metaVar=\"NAME\",usage=\"Node n\n\nfor(Computer c:computers.values()){\nif(c.getName().equals(name))\n- return c;\n+ return c.hasPermission(Computer.VIEW)?c:null;\n}\nreturn null;\n}\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\"xmlns:i=\"jelly:fmt\">\n- <l:layout title=\"${it.displayName}\">\n+ <l:layout title=\"${it.displayName}\"permission=\"${it.VIEW}\">\n<st:include page=\"sidepanel.jelly\"/>\n<l:main-panel>\n<h1>\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\"xmlns:i=\"jelly:fmt\">\n- <l:layout>\n+ <l:layout permission=\"${it.DELETE}\">\n<st:include page=\"sidepanel.jelly\"/>\n<l:main-panel>\n<form method=\"post\"action=\"doDelete\">\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\"xmlns:i=\"jelly:fmt\">\n- <l:layout title=\"${it.displayName}\">\n+ <l:layout title=\"${it.displayName}\"permission=\"${it.VIEW}\">\n<st:include page=\"sidepanel.jelly\"/>\n<l:main-panel>\n\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\"xmlns:i=\"jelly:fmt\">\n- <l:layout title=\"${it.displayName}Load Statistics\">\n+ <l:layout title=\"${it.displayName}Load Statistics\"permission=\"${it.VIEW}\">\n<st:include page=\"sidepanel.jelly\"/>\n<l:main-panel>\n<st:include page=\"main.jelly\"from=\"${it.loadStatistics}\"/>\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\"xmlns:i=\"jelly:fmt\">\n- <l:layout title=\"${%title(it.displayName)}\"norefresh=\"true\">\n+ <l:layout title=\"${%title(it.displayName)}\"norefresh=\"true\"permission=\"${it.VIEW}\">\n<st:include page=\"sidepanel.jelly\"/>\n<l:main-panel>\n<l:hasPermission permission=\"${it.DISCONNECT}\">\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\"xmlns:i=\"jelly:fmt\">\n- <l:layout title=\"${%title(it.displayName)}\"norefresh=\"true\">\n+ <l:layout title=\"${%title(it.displayName)}\"norefresh=\"true\"permission=\"${it.VIEW}\">\n<st:include page=\"sidepanel.jelly\"/>\n<l:main-panel>\n<l:hasPermission permission=\"${it.DISCONNECT}\">\nComputer.CreatePermission.Description=This permission allows users to create sla\nComputer.ConnectPermission.Description=This permission allows users to connect slaves or mark slaves as online.\nComputer.DisconnectPermission.Description=This permission allows users to disconnect slaves or mark slaves as temporarily offline.\nComputer.BuildPermission.Description=This permission allows users to run jobs as them on slaves.\n+ Computer.ViewPermission.Description=This permission allows users to see the slaves.\nComputer.BadChannel=Slave node offline or not a remote channel(such as master node).\n\nComputerSet.NoSuchSlave=No such slave:{0}\n\n\npeft:86\tAdded view permission to Computer.\n\n\nicl:86\tBAEL - 3896:OpenAPI JSON Objects in Query Params - READ\n\n\nslm:[FIXED JENKINS - 16502]Add support for showing build slaves from users\n\n\n", "peft": "86\tAdded view permission to Computer.\n", "icl": "86\tBAEL - 3896:OpenAPI JSON Objects in Query Params - READ\n", "slm": "[FIXED JENKINS - 16502]Add support for showing build slaves from users\n", "nl": "[JENKINS - 16502]Permission to see an executor/slave", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16691, "text": "\nprotected volatile int queryTimeout;\nprotected volatile int transactionQueryTimeout;\n\n- protected long createErrorCount;\n+ protected AtomicLong createErrorCount=new AtomicLong();\n\nprotected long createTimespan;\n\n\n\nprivate final AtomicLong executeCount=new AtomicLong();\n\n- protected Throwable createError;\n- protected Throwable lastError;\n- protected long lastErrorTimeMillis;\n- protected Throwable lastCreateError;\n- protected long lastCreateErrorTimeMillis;\n+ protected volatile Throwable createError;\n+ protected volatile Throwable lastError;\n+ protected volatile long lastErrorTimeMillis;\n+ protected volatile Throwable lastCreateError;\n+ protected volatile long lastCreateErrorTimeMillis;\n\nprotected boolean isOracle=false;\n\npublic void setInitialSize(int initialSize){\n}\n\npublic long getCreateErrorCount(){\n- return createErrorCount;\n+ return createErrorCount.get();\n}\n\npublic int getMaxActive(){\npublic Connection createPhysicalConnection()throws SQLException{\nvalidateConnection(conn);\ncreateError=null;\n}catch(SQLException ex){\n- createErrorCount++;\n+ createErrorCount.incrementAndGet();\ncreateError=ex;\nlastCreateError=ex;\nlastCreateErrorTimeMillis=System.currentTimeMillis();\nthrow ex;\n}catch(RuntimeException ex){\n- createErrorCount++;\n+ createErrorCount.incrementAndGet();\ncreateError=ex;\nlastCreateError=ex;\nlastCreateErrorTimeMillis=System.currentTimeMillis();\nthrow ex;\n}catch(Error ex){\n- createErrorCount++;\n+ createErrorCount.incrementAndGet();\nthrow ex;\n}finally{\nlong nano=System.nanoTime()-startNano;\n\npublic DruidDataSource(){\nthis(true);\n}\n-\n+\npublic DruidDataSource(boolean fairLock){\n- super(fairLock);\n+ super(fairLock);\n}\n\npublic String getInitStackTrace(){\npublic DruidPooledConnection getConnectionDirect(long maxWaitMillis)throws SQLE\n*@param realConnection\n*@throws SQLException\n*/\n- private void discardConnection(Connection realConnection)throws SQLException{\n+ private void discardConnection(Connection realConnection){\nJdbcUtils.close(realConnection);\n\nlock.lock();\nprotected void recycle(DruidPooledConnection pooledConnection)throws SQLExcepti\n}\n}\n\n- try{\n- final boolean isAutoCommit=holder.isUnderlyingAutoCommit();\n- final boolean isReadOnly=holder.isUnderlyingReadOnly();\n+ final boolean isAutoCommit=holder.isUnderlyingAutoCommit();\n+ final boolean isReadOnly=holder.isUnderlyingReadOnly();\n+ final boolean testOnReturn=this.isTestOnReturn();\n\n+ try{\n//check need to rollback?\nif((!isAutoCommit)&&(!isReadOnly)){\npooledConnection.rollback();\nprotected void recycle(DruidPooledConnection pooledConnection)throws SQLExcepti\n//reset holder,restore default settings,clear warnings\nholder.reset();\n\n- if(isTestOnReturn()){\n+ if(testOnReturn){\nboolean validate=testConnectionInternal(physicalConnection);\nif(!validate){\nJdbcUtils.close(physicalConnection);\npublic void run(){\nLOG.error(\"create connection holder error\",ex);\nbreak;\n}\n-\n+\nlock.lock();\ntry{\nconnections[poolingCount++]=holder;\npublic DruidDataSource cloneDruidDataSource(){\n}\n\npublic Map<String,Object>getStatData(){\n+ final int activeCount;\n+ final int activePeak;\n+ final Date activePeakTime;\n+\n+ final int poolingCount;\n+ final int poolingPeak;\n+ final Date poolingPeakTime;\n+\n+ final long connectCount;\n+ final long closeCount;\n+\n+ lock.lock();\n+ try{\n+ poolingCount=this.poolingCount;\n+ poolingPeak=this.poolingPeak;\n+ poolingPeakTime=this.getPoolingPeakTime();\n+\n+ activeCount=this.activeCount;\n+ activePeak=this.activePeak;\n+ activePeakTime=this.getActivePeakTime();\n+\n+ connectCount=this.connectCount;\n+ closeCount=this.closeCount;\n+ }finally{\n+ lock.unlock();\n+ }\nMap<String,Object>dataMap=new LinkedHashMap<String,Object>();\n\ndataMap.put(\"Identity\",System.identityHashCode(this));\npublic DruidDataSource cloneDruidDataSource(){\ndataMap.put(\"NotEmptyWaitCount\",this.getNotEmptyWaitCount());\ndataMap.put(\"NotEmptyWaitMillis\",this.getNotEmptyWaitMillis());\n\n- dataMap.put(\"PoolingCount\",this.getPoolingCount());\n- dataMap.put(\"PoolingPeak\",this.getPoolingPeak());\n- dataMap.put(\"PoolingPeakTime\",this.getPoolingPeakTime()==null?null:this.getPoolingPeakTime().toString());\n+ dataMap.put(\"PoolingCount\",poolingCount);\n+ dataMap.put(\"PoolingPeak\",poolingPeak);\n+ dataMap.put(\"PoolingPeakTime\",poolingPeakTime);\n\n- dataMap.put(\"ActiveCount\",this.getActiveCount());\n- dataMap.put(\"ActivePeak\",this.getActivePeak());\n- dataMap.put(\"ActivePeakTime\",this.getActivePeakTime()==null?null:this.getActivePeakTime().toString());\n+ dataMap.put(\"ActiveCount\",activeCount);\n+ dataMap.put(\"ActivePeak\",activePeak);\n+ dataMap.put(\"ActivePeakTime\",activePeakTime);\n\ndataMap.put(\"InitialSize\",this.getInitialSize());\ndataMap.put(\"MinIdle\",this.getMinIdle());\npublic DruidDataSource cloneDruidDataSource(){\ndataMap.put(\"DefaultReadOnly\",this.isDefaultAutoCommit());\ndataMap.put(\"DefaultTransactionIsolation\",this.getDefaultTransactionIsolation());\n\n- dataMap.put(\"LogicConnectCount\",this.getConnectCount());\n- dataMap.put(\"LogicCloseCount\",this.getCloseCount());\n+ dataMap.put(\"LogicConnectCount\",connectCount);\n+ dataMap.put(\"LogicCloseCount\",closeCount);\ndataMap.put(\"LogicConnectErrorCount\",this.getConnectErrorCount());\n\ndataMap.put(\"PhysicalConnectCount\",this.getCreateCount());\n\n\npeft:1866\tadd AtomicLong createErrorCount\n\n\nicl:1866\tAdd SPARC benchmarks\n\n\nslm:refactor\n\n\n", "peft": "1866\tadd AtomicLong createErrorCount\n", "icl": "1866\tAdd SPARC benchmarks\n", "slm": "refactor\n", "nl": "optimized DruidDataSource.getStatData lock", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16692, "text": "command.org.jkiss.dbeaver.core.object.filter.config.name=Configure filter\ncommand.org.jkiss.dbeaver.core.object.filter.config.description=Configure objects filter\ncommand.org.jkiss.dbeaver.core.object.filter.clear.name=Clear filter\ncommand.org.jkiss.dbeaver.core.object.filter.clear.description=Clear filter settings\n+ command.org.jkiss.dbeaver.core.object.filter.toggle.name=Toggle filter\n+ command.org.jkiss.dbeaver.core.object.filter.toggle.description=Toggles(enables/disables)defined filter\ncommand.org.jkiss.dbeaver.core.object.filter.add.exclude.name=Hide selected object(s)\ncommand.org.jkiss.dbeaver.core.object.filter.add.exclude.description=Hide selected object(s)\ncommand.org.jkiss.dbeaver.core.object.filter.add.include.name=Show only selected objects\n\n<command id=\"org.jkiss.dbeaver.core.object.filter.add.exclude\"name=\"%command.org.jkiss.dbeaver.core.object.filter.add.exclude.name\"description=\"%command.org.jkiss.dbeaver.core.object.filter.add.exclude.description\"categoryId=\"org.jkiss.dbeaver.core.database\"/>\n<command id=\"org.jkiss.dbeaver.core.object.filter.add.include\"name=\"%command.org.jkiss.dbeaver.core.object.filter.add.include.name\"description=\"%command.org.jkiss.dbeaver.core.object.filter.add.include.description\"categoryId=\"org.jkiss.dbeaver.core.database\"/>\n<command id=\"org.jkiss.dbeaver.core.object.filter.clear\"name=\"%command.org.jkiss.dbeaver.core.object.filter.clear.name\"description=\"%command.org.jkiss.dbeaver.core.object.filter.clear.description\"categoryId=\"org.jkiss.dbeaver.core.database\"/>\n+ <command id=\"org.jkiss.dbeaver.core.object.filter.toggle\"name=\"%command.org.jkiss.dbeaver.core.object.filter.toggle.name\"description=\"%command.org.jkiss.dbeaver.core.object.filter.toggle.description\"categoryId=\"org.jkiss.dbeaver.core.database\"/>\n\n<command id=\"org.jkiss.dbeaver.ui.editors.sql.run.statement\"name=\"%command.org.jkiss.dbeaver.ui.editors.sql.run.statement.name\"description=\"%command.org.jkiss.dbeaver.ui.editors.sql.run.statement.description\"categoryId=\"org.jkiss.dbeaver.core.sql\"/>\n<command id=\"org.jkiss.dbeaver.ui.editors.sql.run.statementNew\"name=\"%command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.name\"description=\"%command.org.jkiss.dbeaver.ui.editors.sql.run.statementNew.description\"categoryId=\"org.jkiss.dbeaver.core.sql\"/>\n\n</with>\n</enabledWhen>\n</handler>\n+ <handler commandId=\"org.jkiss.dbeaver.core.object.filter.toggle\"class=\"org.jkiss.dbeaver.ui.actions.navigator.NavigatorHandlerFilterToggle\">\n+ <enabledWhen>\n+ <with variable=\"selection\">\n+ <count value=\"1\"/>\n+ <iterate operator=\"and\">\n+ <and>\n+ <instanceof value=\"org.jkiss.dbeaver.model.navigator.DBNNode\"/>\n+ <test property=\"org.jkiss.dbeaver.core.object.hasFilter\"value=\"defined\"/>\n+ </and>\n+ </iterate>\n+ </with>\n+ </enabledWhen>\n+ </handler>\n<handler commandId=\"org.jkiss.dbeaver.core.object.filter.add.exclude\"class=\"org.jkiss.dbeaver.ui.actions.navigator.NavigatorHandlerFilterExclude\">\n<enabledWhen>\n<with variable=\"selection\">\n\n<command commandId=\"org.jkiss.dbeaver.core.object.filter.config\">\n<visibleWhen checkEnabled=\"true\"/>\n</command>\n+ <command commandId=\"org.jkiss.dbeaver.core.object.filter.toggle\">\n+ <visibleWhen checkEnabled=\"true\"/>\n+ </command>\n<command commandId=\"org.jkiss.dbeaver.core.object.filter.clear\">\n<visibleWhen checkEnabled=\"true\"/>\n</command>\npublic boolean test(Object receiver,String property,Object[]args,Object expe\n}\nif(node instanceof DBNDatabaseFolder&&((DBNDatabaseFolder)node).getItemsMeta()!=null){\nDBSObjectFilter filter=((DBNDatabaseFolder)node).getNodeFilter(((DBNDatabaseFolder)node).getItemsMeta(),true);\n- return filter!=null&&!filter.isNotApplicable();\n+ if(\"defined\".equals(expectedValue)){\n+ return filter!=null&&!filter.isEmpty();\n+ }else{\n+ return filter!=null&&!filter.isNotApplicable();\n+ }\n}\nbreak;\n}\nnew file mode 100644\nindex **********..3664ae360c\nmmm/dev/null\n\n+ /*\n+ * DBeaver-Universal Database Manager\n+ * Copyright(C)2010-2016 Serge Rieder(<EMAIL>)\n+ *\n+ * This program is free software;you can redistribute it and/or modify\n+ * it under the terms of the GNU General Public License(version 2)\n+ * as published by the Free Software Foundation.\n+ *\n+ * This program is distributed in the hope that it will be useful,\n+ * but WITHOUT ANY WARRANTY;without even the implied warranty of\n+ * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.See the\n+ * GNU General Public License for more details.\n+ *\n+ * You should have received a copy of the GNU General Public License along\n+ * with this program;if not,write to the Free Software Foundation,Inc.,\n+ * 51 Franklin Street,Fifth Floor,Boston,MA 02110-1301 USA.\n+ */\n+ package org.jkiss.dbeaver.ui.actions.navigator;\n+\n+ import org.eclipse.core.commands.AbstractHandler;\n+ import org.eclipse.core.commands.ExecutionEvent;\n+ import org.eclipse.core.commands.ExecutionException;\n+ import org.eclipse.jface.viewers.ISelection;\n+ import org.eclipse.ui.handlers.HandlerUtil;\n+ import org.jkiss.dbeaver.model.navigator.DBNDatabaseFolder;\n+ import org.jkiss.dbeaver.model.navigator.DBNDatabaseItem;\n+ import org.jkiss.dbeaver.model.navigator.DBNNode;\n+ import org.jkiss.dbeaver.model.navigator.meta.DBXTreeItem;\n+ import org.jkiss.dbeaver.model.struct.DBSObjectFilter;\n+ import org.jkiss.dbeaver.ui.navigator.NavigatorUtils;\n+\n+ import java.util.Collections;\n+\n+ public class NavigatorHandlerFilterToggle extends AbstractHandler{\n+\n+ @Override\n+ public Object execute(ExecutionEvent event)throws ExecutionException{\n+ final ISelection selection=HandlerUtil.getCurrentSelection(event);\n+ DBNNode node=NavigatorUtils.getSelectedNode(selection);\n+ if(node instanceof DBNDatabaseItem){\n+ node=node.getParentNode();\n+ }\n+ if(node instanceof DBNDatabaseFolder){\n+ final DBNDatabaseFolder folder=(DBNDatabaseFolder)node;\n+ DBXTreeItem itemsMeta=folder.getItemsMeta();\n+ if(itemsMeta!=null){\n+ final DBSObjectFilter nodeFilter=folder.getNodeFilter(itemsMeta,true);\n+ if(nodeFilter!=null){\n+ nodeFilter.setEnabled(!nodeFilter.isEnabled());\n+ NavigatorHandlerRefresh.refreshNavigator(Collections.singleton(folder));\n+ }\n+ }\n+\n+ }\n+ return null;\n+ }\n+\n+ }\n\\No newline at end of file\n\n\npeft:1583\tTranslated using Weblate(DBeaver)\n\n\nicl:1583\tAccept alpha[0, 255]()\n\n\nslm:Toggle options menu\n\n\n", "peft": "1583\tTranslated using Weblate(DBeaver)\n", "icl": "1583\tAccept alpha[0, 255]()\n", "slm": "Toggle options menu\n", "nl": "Navigator filter toggle command", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16693, "text": "\nimport org.springframework.boot.autoconfigure.SpringBootApplication;\nimport org.springframework.scheduling.annotation.EnableScheduling;\n\n- import java.net.UnknownHostException;\n-\n/* *\n* Config main\n*\n\n@SpringBootApplication\npublic class Config{\n\n- public static void main(String[]args)throws UnknownHostException{\n+ public static void main(String[]args){\nSpringApplication.run(Config.class,args);\n}\n}\nold mode 100755\nnew mode 100644\nsimilarity index 100%\nrename from config/src/main/resources/META-INF/logback/nacos-included.xml\nrename to config/src/main/resources/META-INF/logback/config-included.xml\n\nimport org.springframework.core.Ordered;\nimport org.springframework.core.env.ConfigurableEnvironment;\n\n+ import java.io.File;\nimport java.io.IOException;\nimport java.util.List;\nimport java.util.concurrent.ScheduledExecutorService;\nprivate void logClusterConf(){\n}\n\nprivate void logFilePath(){\n- LOGGER.info(\"Nacos Log files:{}/logs/\",NACOS_HOME);\n- LOGGER.info(\"Nacos Conf files:{}/conf/\",NACOS_HOME);\n- LOGGER.info(\"Nacos Data files:{}/data/\",NACOS_HOME);\n+ String[]dirNames=new String[]{\"logs\",\"conf\",\"data\"};\n+ for(String dirName:dirNames){\n+ LOGGER.info(\"Nacos Log files:{}{}{}{}\",NACOS_HOME,File.separatorChar,dirName,File.separatorChar);\n+ }\n}\n\nprivate void logStarting(){\n\n<include resource=\"org/springframework/boot/logging/logback/defaults.xml\"/>\n\n<include resource=\"META-INF/logback/nacos-included.xml\"/>\n+ <include optional=\"true\"resource=\"META-INF/logback/config-included.xml\"/>\n+ <include optional=\"true\"resource=\"META-INF/logback/naming-included.xml\"/>\n\n<appender name=\"CONSOLE\"class=\"ch.qos.logback.core.ConsoleAppender\">\n<encoder>\nsimilarity index 100%\nrename from naming/src/main/resources/META-INF/logback/nacos-included.xml\nrename to naming/src/main/resources/META-INF/logback/naming-included.xml\n\n\npeft:336\tMerge pull request from zhengxiaoxu/master\n\n\nicl:336\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required changes\n\n\nslm:Include logs and nacos included files in logback\n\n\n", "peft": "336\tMerge pull request from zhengxiaoxu/master\n", "icl": "336\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required changes\n", "slm": "Include logs and nacos included files in logback\n", "nl": "correct log config", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16694, "text": "class HappoSnapshotter(\nprivate val happoSecretKey=BC.HappoSecretKey\nprivate val gitBranch=URLEncoder.encode((if(BC.BITRISE_GIT_BRANCH==\"null\")BC.GIT_BRANCH else BC.BITRISE_GIT_BRANCH).replace(\"/\",\"_\"),\"UTF-8\")\nprivate val androidVersion=\"android${Build.VERSION.SDK_INT}\"\n- private val reportNames=listOfNotNull(\n- \"${BC.GIT_SHA}-$androidVersion\",\n- \"$gitBranch-$androidVersion\",\n- \"${BuildConfig.VERSION_NAME}-$androidVersion\"\n- )\n+ private val reportNamePrefixes=listOf(BC.GIT_SHA,gitBranch,BuildConfig.VERSION_NAME).filter{it.isNotBlank()}\n+ private val reportNames=reportNamePrefixes.map{\"$it-$androidVersion\"}\n\nprivate val okhttp=OkHttpClient()\n\n\n\npeft:929\tAdd reportNamePrefixes\n\n\nicl:929\tMerge remote - tracking branch'dakrone/add - dir - locals.el\n\n\nslm:Merge pull request from JakeWharton/jw/report - names\n\n\n", "peft": "929\tAdd reportNamePrefixes\n", "icl": "929\tMerge remote - tracking branch'dakrone/add - dir - locals.el\n", "slm": "Merge pull request from JakeWharton/jw/report - names\n", "nl": "Filter out empty environment variables()", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16695, "text": "\n\"USE\"\n\"VIEWS\"\n\"WATERMARK\"\n+ \"WATERMARKS\"\n]\n\n# List of keywords from\"keywords\"section that are not reserved.\nSqlTableLikeOption SqlTableLikeOption():\n<OPTIONS>{featureOption=FeatureOption.OPTIONS;}\n|\n<PARTITIONS>{featureOption=FeatureOption.PARTITIONS;}\n+ |\n+ <WATERMARKS>{featureOption=FeatureOption.WATERMARKS;}\n)\n\n{\n\n*<li>ALL-a shortcut to change the default merging strategy if none provided</li>\n*<li>CONSTRAINTS-constraints such as primary and unique keys</li>\n*<li>GENERATED-computed columns</li>\n+ *<li>WATERMARKS-watermark declarations</li>\n*<li>PARTITIONS-partition of the tables</li>\n*<li>OPTIONS-connector options that decribed connector and format properties</li>\n*</ul>\n\nALL,\nCONSTRAINTS,\nGENERATED,\n+ OPTIONS,\nPARTITIONS,\n- OPTIONS\n+ WATERMARKS\n}\n\nprivate final SqlIdentifier sourceTable;\npublic void testCreateTableLike()throws Exception{\n\")\\n\"+\n\"LIKE b(\\n\"+\n\"EXCLUDING PARTITIONS\\n\"+\n+ \"EXCLUDING CONSTRAINTS\\n\"+\n+ \"EXCLUDING WATERMARKS\\n\"+\n\"OVERWRITING GENERATED\\n\"+\n\"OVERWRITING OPTIONS\\n\"+\n\")\")\npublic void testCreateTableLike()throws Exception{\npointsTo(\"b\"),\nhasOptions(\noption(MergingStrategy.EXCLUDING,FeatureOption.PARTITIONS),\n+ option(MergingStrategy.EXCLUDING,FeatureOption.CONSTRAINTS),\n+ option(MergingStrategy.EXCLUDING,FeatureOption.WATERMARKS),\noption(MergingStrategy.OVERWRITING,FeatureOption.GENERATED),\noption(MergingStrategy.OVERWRITING,FeatureOption.OPTIONS)\n)\n\n\npeft:1671\t[FLINK - 15797]Add 'WATERMARKS' to the list of keywords that can be excluded from a CREATE TABLE LIKE statement\n\n\nicl:1671\t[UTIL]Added a JSON files generator\n\n\nslm:Merge pull request from tristaZero/dev\n\n\n", "peft": "1671\t[FLINK - 15797]Add 'WATERMARKS' to the list of keywords that can be excluded from a CREATE TABLE LIKE statement\n", "icl": "1671\t[UTIL]Added a JSON files generator\n", "slm": "Merge pull request from tristaZero/dev\n", "nl": "[FLINK - 17003]Added WATERMARKS as additional LIKE clause option in", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16696, "text": "public static boolean isValidLatitude(double latitude){\n\n/* * Returns true if longitude is actually a valid longitude value.*/\npublic static boolean isValidLongitude(double longitude){\n- if(Double.isNaN(longitude)||Double.isNaN(longitude)||longitude<GeoUtils.MIN_LON||longitude>GeoUtils.MAX_LON){\n+ if(Double.isNaN(longitude)||Double.isInfinite(longitude)||longitude<GeoUtils.MIN_LON||longitude>GeoUtils.MAX_LON){\nreturn false;\n}\nreturn true;\n\n\npeft:223\tFix GeoUtils.isValidLongitude()\n\n\nicl:223\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required changes\n\n\nslm:Check for NaN/INfinite in GeoUtils.isValidLongitude()\n\n\n", "peft": "223\tFix GeoUtils.isValidLongitude()\n", "icl": "223\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required changes\n", "slm": "Check for NaN/INfinite in GeoUtils.isValidLongitude()\n", "nl": "Fix typo in GeoUtils#isValidLongitude()", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16697, "text": "new file mode 100644\nindex **********0..e4a8c83d1a6\nmmm/dev/null\n\n+ <div>\n+ Bu modda,giri&# 351;yapm&# 305;&# 351;her kullan&# 305;c&# 305;,Hudson&# 252;zerinde tam yetkiye sahip olur.\n+ Tam yetkiye sahip olmayan tek kullan&# 305;c&# 305;t&# 252;r&# 252;giri&# 351;yapmam&# 305;&# 351;(anonymous)kullan&# 305;c&# 305;lard&# 305;r ki\n+ bu kullan&# 305;c&# 305;lar&# 305;n sadece okuma yetkisi vard&# 305;r.\n+ <p>\n+ Bu mod,kullan&# 305;clar&# 305;,aksiyon almak i&# 231;in giri&# 351;yapmaya zorlar.B&# 246;ylece kimin\n+ ne yapt&# 305;&# 287;&# 305;ile ilgili kay&# 305;tlar&# 305;tutabilirsiniz.Bu ayar&# 305;a&# 231;man&# 305;n bir sebebi de\n+ Hudson'&# 305;public olarak kullan&# 305;ma a&# 231;m&# 305;&# 351;olmakt&# 305;r.Bu&# 351;ekilde,sadece g&# 252;venilir\n+ kullan&# 305;clar&# 305;n kullan&# 305;c&# 305;hesaplar&# 305;olur,di&# 287;er t&# 252;m herkes kullan&# 305;c&# 305;giri&# 351;i yapmadan\n+ (anonymous olarak)kullan&# 305;r.\n+ </div>\n\\No newline at end of file\n\n\npeft:873\t[FIXED]Hudson is now in read only mode by default\n\n\nicl:873\tAccept alpha[0, 255]()\n\n\nslm:Translated using Weblate(Kurdish)\n\n\n", "peft": "873\t[FIXED]Hudson is now in read only mode by default\n", "icl": "873\tAccept alpha[0, 255]()\n", "slm": "Translated using Weblate(Kurdish)\n", "nl": "Issue number:No issue", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16698, "text": "public SetupWizard(Jenkins j)throws IOException,InterruptedException{\n}\n}\n\n- String setupKey=iapf.readToString().trim();\n- String ls=System.lineSeparator();\n- LOGGER.info(ls+ls+\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n- +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n- +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n- +ls\n- +\"Jenkins initial setup is required.An admin user has been created and\"\n- +\"a password generated.\"+ls\n- +\"Please use the following password to proceed to installation:\"+ls\n- +ls\n- +setupKey+ls\n- +ls\n- +\"This may also be found at:\"+iapf.getRemote()+ls\n- +ls\n- +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n- +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n- +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls);\n+ if(iapf.exists()){\n+ String setupKey=iapf.readToString().trim();\n+ String ls=System.lineSeparator();\n+ LOGGER.info(ls+ls+\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n+ +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n+ +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n+ +ls\n+ +\"Jenkins initial setup is required.An admin user has been created and\"\n+ +\"a password generated.\"+ls\n+ +\"Please use the following password to proceed to installation:\"+ls\n+ +ls\n+ +setupKey+ls\n+ +ls\n+ +\"This may also be found at:\"+iapf.getRemote()+ls\n+ +ls\n+ +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n+ +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls\n+ +\"* * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * * *\"+ls);\n+ }\n\ntry{\nPluginServletFilter.addFilter(FORCE_SETUP_WIZARD_FILTER);\npublic SetupWizard(Jenkins j)throws IOException,InterruptedException{\nthrow new AssertionError(e);\n}\n}\n+\n+ /* *\n+ * Indicates a generated password should be used-e.g.this is a new install,no security realm set up\n+ */\n+ public boolean useGeneratedPassword(){\n+ try{\n+ return getInitialAdminPasswordFile().exists();\n+ }catch(Exception e){\n+ //ignore\n+ }\n+ return false;\n+ }\n\n/* *\n* Gets the file used to store the initial admin password\nTHE SOFTWARE.\n\n<?jelly escape-by-default='true'?>\n<j:jelly xmlns:j=\"jelly:core\"xmlns:st=\"jelly:stapler\"xmlns:d=\"jelly:define\"xmlns:l=\"/lib/layout\"xmlns:t=\"/lib/hudson\"xmlns:f=\"/lib/form\">\n- <j:if test=\"${it.setupWizard!=null}\">\n+ <j:choose>\n+ <j:when test=\"${it.setupWizard.useGeneratedPassword()}\">\n<st:include it=\"${it.setupWizard}\"page=\"authenticate-security-token\"/>\n- </j:if>\n- <j:if test=\"${it.setupWizard==null}\">\n+ </j:when>\n+ <j:otherwise>\n<l:layout norefresh=\"true\">\n<l:hasPermission permission=\"${app.READ}\">\n<st:include page=\"sidepanel.jelly\"/>\nTHE SOFTWARE.\n</div>\n</l:main-panel>\n</l:layout>\n- </j:if>\n+ </j:otherwise>\n+ </j:choose>\n</j:jelly>\n\n\npeft:1871\t[FIXED - JENKINS - 25997]Remove redundant initial setup message\n\n\nicl:1871\tMerge pull request from jtakakura/patch - gdx - setup2\n\n\nslm:Revert\"i18n and JENKINS - 63838\"introduction to the setup wizard\"\n\n\n", "peft": "1871\t[FIXED - JENKINS - 25997]Remove redundant initial setup message\n", "icl": "1871\tMerge pull request from jtakakura/patch - gdx - setup2\n", "slm": "Revert\"i18n and JENKINS - 63838\"introduction to the setup wizard\"\n", "nl": "JENKINS - 33800 - fileNotFound exception if jenkins 1 installed, security", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16699, "text": "public class MyCommand extends DefaultCommand{\n}\nmmm-\n\n- Place the jar containing the command into the classpath of your fat-jar(or include it inside)or in the ` lib `\n+ You also need an implementation of ` link:../../apidocs/io/vertx/core/spi/launcher/CommandFactory.html[CommandFactory]`:\n+\n+ [source,java]\n+ mmm-\n+ public class HelloCommandFactory extends DefaultCommandFactory<HelloCommand>{\n+ public HelloCommandFactory(){\n+ super(HelloCommand.class);\n+ }\n+ }\n+ mmm-\n+\n+ Then create the ` src/main/resources/META-INF/services/io.vertx.core.spi.launcher.CommandFactory ` and add a line\n+ indicating the fully qualified name of the factory:\n+\n+ mmm-\n+ io.vertx.core.launcher.example.HelloCommandFactory\n+ mmm-\n+\n+ Builds the jar containing the command.Be sure to includes the SPI file\n+ (` META-INF/services/io.vertx.core.spi.launcher.CommandFactory `).\n+\n+ Then,place the jar containing the command into the classpath of your fat-jar(or include it inside)or in the ` lib `\ndirectory of your vert.x distribution,and you would be able to execute:\n\n[source]\n\n*}\n* mmm-\n*\n- * Place the jar containing the command into the classpath of your fat-jar(or include it inside)or in the ` lib `\n+ * You also need an implementation of{@link io.vertx.core.spi.launcher.CommandFactory}:\n+ *\n+ *[source,java]\n+ * mmm-\n+ * public class HelloCommandFactory extends DefaultCommandFactory<HelloCommand>{\n+ * public HelloCommandFactory(){\n+ * super(HelloCommand.class);\n+ *}\n+ *}\n+ * mmm-\n+ *\n+ * Then,create the ` src/main/resources/META-INF/services/io.vertx.core.spi.launcher.CommandFactory ` and add a line\n+ * indicating the fully qualified name of the factory:\n+ *\n+ * mmm-\n+ * io.vertx.core.launcher.example.HelloCommandFactory\n+ * mmm-\n+ *\n+ * Builds the jar containing the command.Be sure to includes the SPI file\n+ *(` META-INF/services/io.vertx.core.spi.launcher.CommandFactory `).\n+ *\n+ * Then,place the jar containing the command into the classpath of your fat-jar(or include it inside)or in the ` lib `\n* directory of your vert.x distribution,and you would be able to execute:\n*\n*[source]\n\n\npeft:1253\tAdded example for custom command factory\n\n\nicl:1253\tDocumenting access to modules in node via require\n\n\nslm:Merge pull request from eclipse/javadoc - formatting\n\n\n", "peft": "1253\tAdded example for custom command factory\n", "icl": "1253\tDocumenting access to modules in node via require\n", "slm": "Merge pull request from eclipse/javadoc - formatting\n", "nl": "Extend the launcher documentation to cover the implementation of new commands.", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16700, "text": "public Result invoke(Invoker<?>invoker,Invocation invocation)throws RpcExcept\nEntry methodEntry=null;\ntry{\nString resourceName=getResourceName(invoker,invocation);\n- ContextUtil.enter(resourceName);\ninterfaceEntry=SphU.entry(invoker.getInterface().getName(),EntryType.OUT);\nmethodEntry=SphU.entry(resourceName,EntryType.OUT);\n\npublic Result invoke(Invoker<?>invoker,Invocation invocation)throws RpcExcept\nif(interfaceEntry!=null){\ninterfaceEntry.exit();\n}\n- ContextUtil.exit();\n}\n}\n}\n\nString resourceName=methodDescriptor.getFullMethodName();\nEntry entry=null;\ntry{\n- ContextUtil.enter(resourceName);\nentry=SphU.entry(resourceName,EntryType.OUT);\n//Allow access,forward the call.\nreturn new ForwardingClientCall.SimpleForwardingClientCall<ReqT,RespT>(\npublic void sendMessage(ReqT message){\nif(entry!=null){\nentry.exit();\n}\n- ContextUtil.exit();\n}\n}\n\n\n\npeft:1133\tfix dubbo tracing\n\n\nicl:1133\t[GR - 27316]Ensure explicit timelimit is not overridden.\n\n\nslm:Remove redundant ContextUtil.exit()calls\n\n\n", "peft": "1133\tfix dubbo tracing\n", "icl": "1133\t[GR - 27316]Ensure explicit timelimit is not overridden.\n", "slm": "Remove redundant ContextUtil.exit()calls\n", "nl": "Remove ContextUtil.enter()in client/consumer side", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16701, "text": "\nimport org.openqa.selenium.logging.LocalLogs;\nimport org.openqa.selenium.logging.NeedsLocalLogs;\nimport org.openqa.selenium.logging.LoggingPreferences;\n- import org.openqa.selenium.logging.Logs;\nimport org.openqa.selenium.remote.Command;\nimport org.openqa.selenium.remote.CommandExecutor;\nimport org.openqa.selenium.remote.DesiredCapabilities;\n\n\npeft:1624\tFixing a typo\n\n\nicl:1624\tMerge pull request from amit2103/BAEL - 9018\n\n\nslm:Deleting an unused import\n\n\n", "peft": "1624\tFixing a typo\n", "icl": "1624\tMerge pull request from amit2103/BAEL - 9018\n", "slm": "Deleting an unused import\n", "nl": "SimonStewart:Clean up imports.No logical changes here either.", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16702, "text": "public Variable emitLoad(Kind kind,Value address,DeoptimizingNode access){\nAMD64AddressValue loadAddress=asAddressValue(address);\nVariable result=newVariable(kind);\nassert access==null||access instanceof HeapAccess;\n- if(runtime().config.useCompressedOops&&isCompressCandidate(access)){\n- append(new LoadCompressedPointer(kind,result,loadAddress,access!=null?state(access):null,runtime().config.narrowOopBase,runtime().config.narrowOopShift,\n- runtime().config.logMinObjAlignment));\n+ if(isCompressCandidate(access)){\n+ if(runtime().config.useCompressedOops&&kind==Kind.Object){\n+ append(new LoadCompressedPointer(kind,result,loadAddress,access!=null?state(access):null,runtime().config.narrowOopBase,runtime().config.narrowOopShift,\n+ runtime().config.logMinObjAlignment));\n+ }else if(runtime().config.useCompressedKlassPointers&&kind==Kind.Long){\n+ append(new LoadCompressedPointer(kind,result,loadAddress,access!=null?state(access):null,runtime().config.narrowKlassBase,runtime().config.narrowKlassShift,\n+ runtime().config.logKlassAlignment));\n+ }else{\n+ append(new LoadOp(kind,result,loadAddress,access!=null?state(access):null));\n+ }\n}else{\nappend(new LoadOp(kind,result,loadAddress,access!=null?state(access):null));\n}\npublic void emitStore(Kind kind,Value address,Value inputVal,DeoptimizingNode\nif(isConstant(inputVal)){\nConstant c=asConstant(inputVal);\nif(canStoreConstant(c)){\n- append(new StoreConstantOp(kind,storeAddress,c,state,runtime().config.useCompressedOops&&isCompressCandidate(access)));\n+ if(inputVal.getKind()==Kind.Object){\n+ append(new StoreConstantOp(kind,storeAddress,c,state,runtime().config.useCompressedOops&&isCompressCandidate(access)));\n+ }else if(inputVal.getKind()==Kind.Long){\n+ append(new StoreConstantOp(kind,storeAddress,c,state,runtime().config.useCompressedKlassPointers&&isCompressCandidate(access)));\n+ }else{\n+ append(new StoreConstantOp(kind,storeAddress,c,state,false));\n+ }\nreturn;\n}\n}\nVariable input=load(inputVal);\n- if(runtime().config.useCompressedOops&&isCompressCandidate(access)){\n- if(input.getKind()==Kind.Object){\n+ if(isCompressCandidate(access)){\n+ if(runtime().config.useCompressedOops&&kind==Kind.Object){\nVariable scratch=newVariable(Kind.Long);\nappend(new StoreCompressedPointer(kind,storeAddress,input,scratch,state,runtime().config.narrowOopBase,runtime().config.narrowOopShift,runtime().config.logMinObjAlignment));\n+ }else if(runtime().config.useCompressedKlassPointers&&kind==Kind.Long){\n+ Variable scratch=newVariable(Kind.Long);\n+ append(new StoreCompressedPointer(kind,storeAddress,input,scratch,state,runtime().config.narrowKlassBase,runtime().config.narrowKlassShift,runtime().config.logKlassAlignment));\n}else{\n- append(new StoreOp(input.getKind(),storeAddress,input,state));\n+ append(new StoreOp(kind,storeAddress,input,state));\n}\n}else{\nappend(new StoreOp(kind,storeAddress,input,state));\npublic LoadCompressedPointer(Kind kind,AllocatableValue result,AMD64AddressVal\nthis.base=base;\nthis.shift=shift;\nthis.alignment=alignment;\n- assert kind==Kind.Object;\n+ assert kind==Kind.Object||kind==Kind.Long;\n}\n\n@Override\npublic void emitMemAccess(AMD64MacroAssembler masm){\nRegister resRegister=asRegister(result);\nmasm.movl(resRegister,address.toAddress());\n- decodePointer(masm,resRegister,base,shift,alignment);\n+ if(kind==Kind.Object){\n+ decodePointer(masm,resRegister,base,shift,alignment);\n+ }else{\n+ decodeKlassPointer(masm,resRegister,base,shift,alignment);\n+ }\n}\n}\n\npublic StoreCompressedPointer(Kind kind,AMD64AddressValue address,AllocatableV\nthis.address=address;\nthis.state=state;\nthis.input=input;\n- assert kind==Kind.Object;\n+ assert kind==Kind.Object||kind==Kind.Long;\n}\n\n@Override\npublic void emitCode(TargetMethodAssembler tasm,AMD64MacroAssembler masm){\nmasm.movq(asRegister(scratch),asRegister(input));\n- encodePointer(masm,asRegister(scratch),base,shift,alignment);\n+ if(kind==Kind.Object){\n+ encodePointer(masm,asRegister(scratch),base,shift,alignment);\n+ }else{\n+ encodeKlassPointer(masm,asRegister(scratch),base,shift,alignment);\n+ }\nif(state!=null){\ntasm.recordImplicitException(masm.codeBuffer.position(),state);\n}\npublic void emitMemAccess(AMD64MacroAssembler masm){\nbreak;\ncase Long:\nif(NumUtil.isInt(input.asLong())){\n- masm.movslq(address.toAddress(),(int)input.asLong());\n+ if(compress){\n+ masm.movl(address.toAddress(),(int)input.asLong());\n+ }else{\n+ masm.movslq(address.toAddress(),(int)input.asLong());\n+ }\n}else{\nthrow GraalInternalError.shouldNotReachHere(\"Cannot store 64-bit constants to memory\");\n}\nprivate static void decodePointer(AMD64MacroAssembler masm,Register resRegister\n}\n}\n\n+ private static void encodeKlassPointer(AMD64MacroAssembler masm,Register scratchRegister,long base,int shift,int alignment){\n+ if(base!=0){\n+ masm.subq(scratchRegister,AMD64.r12);\n+ }\n+ if(shift!=0){\n+ assert alignment==shift:\"Encode algorithm is wrong\";\n+ masm.shrq(scratchRegister,alignment);\n+ }\n+ }\n+\n+ private static void decodeKlassPointer(AMD64MacroAssembler masm,Register resRegister,long base,int shift,int alignment){\n+ if(shift!=0){\n+ assert alignment==shift:\"Decode algorighm is wrong\";\n+ masm.shlq(resRegister,alignment);\n+ if(base!=0){\n+ masm.addq(resRegister,AMD64.r12);\n+ }\n+ }else{\n+ assert base==0:\"Sanity\";\n+ }\n+ }\n+\n+ public static void decodeKlassPointer(AMD64MacroAssembler masm,Register register,AMD64Address address,long narrowKlassBase,int narrowKlassShift,int logKlassAlignment){\n+ masm.movl(register,address);\n+ decodeKlassPointer(masm,register,narrowKlassBase,narrowKlassShift,logKlassAlignment);\n+ }\n}\n\n\npeft:351\tMerge pull request from jon - mc - carthy/compress-oops\n\n\nicl:351\tAdds\"- fno - autolink\"to ObjC compilation actions to avoid\n\n\nslm:Add support for LoadCompressedPointer and Store constants.\n\n\n", "peft": "351\tMerge pull request from jon - mc - carthy/compress-oops\n", "icl": "351\tAdds\"- fno - autolink\"to ObjC compilation actions to avoid\n", "slm": "Add support for LoadCompressedPointer and Store constants.\n", "nl": "Add compressed klass pointers support in LIR", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16703, "text": "\n<arg value=\"-libraryjars${WTK-home}/lib/cldcapi11.jar\"/>\n<arg value=\"-renamesourcefileattribute SourceFile\"/>\n<arg value=\"-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,* Annotation *,EnclosingMethod\"/>\n- <arg value=\"-keep class *{!private *;}\"/>\n+ <arg value=\"-keep public class com.google.zxing.*{public protected *;}\"/>\n+ <arg value=\"-keep public class com.google.zxing.client.result.*{public protected *;}\"/>\n+ <arg value=\"-keep public class com.google.zxing.common.*{public protected *;}\"/>\n<arg value=\"-optimizationpasses 7\"/>\n<arg value=\"-overloadaggressively\"/>\n<arg value=\"-allowaccessmodification\"/>\n\n<delete file=\"temp.jar\"/>\n</target>\n\n- <target name=\"build-test\"depends=\"init,build-optimized\">\n+ <target name=\"build-test\"depends=\"init,build\">\n<fail message=\"Please build'javase'first\">\n<condition>\n<not>\n\n\npeft:429\tAdded -keep public class com.google.zxing.*{public protected *;}to proguard.cfg\n\n\nicl:429\tMINOR:Fix state transition diagram for stream threads()\n\n\nslm:Merge pull request from eugenp/eric - martin - patch - 1\n\n\n", "peft": "429\tAdded -keep public class com.google.zxing.*{public protected *;}to proguard.cfg\n", "icl": "429\tMINOR:Fix state transition diagram for stream threads()\n", "slm": "Merge pull request from eugenp/eric - martin - patch - 1\n", "nl": "Adjust optimization options:make sure to actually exclude more unused code, but keep what Android client needs;this means tests have to run against unoptimized version", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16704, "text": "\n\nc.before do|example|\nguards=WebDriver::SpecSupport::Guards.new(example)\n- if guards.except.satisfied.any?||guards.only.unsatisfied.any?\n+ if guards.exclude.any?\n+ skip'Bug Prevents Execution.'\n+ elsif guards.except.satisfied.any?||guards.only.unsatisfied.any?\npending'Guarded.'\nend\nend\nmodule SpecSupport\nclass Guards\ninclude Enumerable\n\n- GUARD_TYPES=%i[except only].freeze\n+ GUARD_TYPES=%i[except only exclude].freeze\n\ndef initialize(example,guards=nil)\n@example=example\ndef only\nself.class.new(@example,@guards.select(&:only?))\nend\n\n+ def exclude\n+ self.class.new(@example,@guards.select(&:exclude?)).satisfied\n+ end\n+\ndef satisfied\nself.class.new(@example,@guards.select(&:satisfied?))\nend\ndef only?\n@type==:only\nend\n\n+ def exclude?\n+ @type==:exclude\n+ end\n+\ndef satisfied?\nsatisfies_driver?&&satisfies_browser?&&satisfies_platform?&&satisfies_window_manager?\nend\n\n\npeft:466\tMerge pull request from jari/exclude\n\n\nicl:466\tMINOR:Fix guards for specs()\n\n\nslm:JariBakken:Add exclude spec support.\n\n\n", "peft": "466\tMerge pull request from jari/exclude\n", "icl": "466\tMINOR:Fix guards for specs()\n", "slm": "JariBakken:Add exclude spec support.\n", "nl": "[rb]implement spec guard for exclude", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16705, "text": "fi\nassert_file\"$ESCONFIG/elasticsearch.keystore\"f root elasticsearch 660\nfi\n}\n-\n-\n\n\npeft:209\t[DOCS]Fix indentation in test_elasticsearch_keystore\n\n\nicl:209\tSchedule'LibGraal'benchmarks.\n\n\nslm:minor fix\n\n\n", "peft": "209\t[DOCS]Fix indentation in test_elasticsearch_keystore\n", "icl": "209\tSchedule'LibGraal'benchmarks.\n", "slm": "minor fix\n", "nl": "Remove extraneous newlines from keystore.bash", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16706, "text": "public static void setHintManager(final HintManager hintManager){\n*@param values sharding value\n*/\npublic static void setDatabaseShardingValue(final Comparable<?>...values){\n+ DATABASE_SHARDING_VALUES.clear();\nfor(Comparable<?>each:values){\naddDatabaseShardingValue(DB_TABLE_NAME,each);\n}\n\n\npeft:1629\tmodify setDatabaseShardingValue()\n\n\nicl:1629\tMerge pull request elastic/elasticsearch from jbudz/ui - kibana -\n\n\nslm:for, clear database sharding values\n\n\n", "peft": "1629\tmodify setDatabaseShardingValue()\n", "icl": "1629\tMerge pull request elastic/elasticsearch from jbudz/ui - kibana -\n", "slm": "for, clear database sharding values\n", "nl": "DATABASE_SHARDING_VALUES.clear();", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16707, "text": "\n+ jenkins(1.445)unstable;urgency=low\n+\n+ * See http://jenkins-ci.org/changelog for more details.\n+\n+ --Kohsuke Kawaguchi<<EMAIL>>Mon,26 Dec 2011 10:28:38-0800\n+\njenkins(1.444)unstable;urgency=low\n\n* See http://jenkins-ci.org/changelog for more details.\n\n\npeft:124\t[FIXED]JENKINS - 1.445\n\n\nicl:124\tMINOR:Fix state transition diagram for stream threads()\n\n\nslm:updated changelog as a part of the release\n\n\n", "peft": "124\t[FIXED]JENKINS - 1.445\n", "icl": "124\tMINOR:Fix state transition diagram for stream threads()\n", "slm": "updated changelog as a part of the release\n", "nl": "updated changelog as a part of the release", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16708, "text": "private void removeUnprocessedDefaultProfiles(){\nprivate void addProfiles(Set<Profile>profiles){\nfor(Profile profile:profiles){\nthis.profiles.add(profile);\n- if(!this.environment.acceptsProfiles(profile.getName())){\n+ if(!environmentHasActiveProfile(profile.getName())){\n//If it's already accepted we assume the order was set\n//intentionally\nprependProfile(this.environment,profile);\nprivate void addProfiles(Set<Profile>profiles){\n}\n}\n\n+ private boolean environmentHasActiveProfile(String profile){\n+ for(String activeProfile:this.environment.getActiveProfiles()){\n+ if(activeProfile.equals(profile)){\n+ return true;\n+ }\n+ }\n+ return false;\n+ }\n+\nprivate void prependProfile(ConfigurableEnvironment environment,\nProfile profile){\nSet<String>profiles=new LinkedHashSet<String>();\npublic void customDefaultProfileAndActiveFromFile()throws Exception{\nConfigurableEnvironment environment=this.context.getEnvironment();\nassertThat(environment.containsProperty(\"customprofile\")).isTrue();\nassertThat(environment.containsProperty(\"customprofile-specific\")).isTrue();\n- assertThat(environment.containsProperty(\"customprofile-customdefault\")).isFalse();\n+ assertThat(environment.containsProperty(\"customprofile-customdefault\")).isTrue();\n+ assertThat(this.context.getEnvironment().acceptsProfiles(\"customdefault\"))\n+ .isTrue();\n}\n\nprivate Condition<ConfigurableEnvironment>matchingPropertySource(\n@@-1+1,2@@\n+ spring.profiles.include=specific\ncustomprofile-customdefault=true\n\n- spring.profiles.active=specific\n+ spring.profiles.active=customdefault\ncustomprofile=true\n\n\npeft:425\tAdded support for profiles that are not active but are included in the default profile\n\n\nicl:425\tAdds\"- fno - autolink\"to ObjC compilation actions to avoid using\n\n\nslm:Allow registering profiles with active profiles\n\n\n", "peft": "425\tAdded support for profiles that are not active but are included in the default profile\n", "icl": "425\tAdds\"- fno - autolink\"to ObjC compilation actions to avoid using\n", "slm": "Allow registering profiles with active profiles\n", "nl": "Allow default profile to also be set in properties", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16709, "text": "\npublic static boolean GenSafepoints=true;\npublic static boolean GenLoopSafepoints=true;\npublic static boolean UseTypeCheckHints=true;\n- public static boolean InlineVTableStubs=____;\n+ public static boolean InlineVTableStubs=true;\n\npublic static boolean GenAssertionCode=____;\npublic static boolean AlignCallsForPatching=true;\n\n\npeft:33\tEnable InlineVTableStubs by default\n\n\nicl:33\tMINOR:Fix type check hints()\n\n\nslm:Enable InlineVTableStubs\n\n\n", "peft": "33\tEnable InlineVTableStubs by default\n", "icl": "33\tMINOR:Fix type check hints()\n", "slm": "Enable InlineVTableStubs\n", "nl": "enable VTable stub inlining", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16710, "text": "default:\nifeq($(OS),darwin)\nSO_EXT=.dylib\nORIGIN_TOKEN='@loader_path'\n- SO_LDFLAGS=-Wl,-install_name,@rpath/$(notdir$@)\n+ SO_LDFLAGS=-Wl,-install_name,@rpath/$(notdir$@)-Wl,-undefined,suppress-Wl,-flat_namespace\nelse\nSO_EXT=.so\nORIGIN_TOKEN='$$ORIGIN'\ndynLink/cycle/libB$(SO_EXT):dynLink/cycle/b.c dynLink/cycle/Makefile dynLink/cy\n\ndynLink/cycle/libC$(SO_EXT):dynLink/cycle/c.c dynLink/cycle/Makefile dynLink/cycle/libA$(SO_EXT)\n@mkdir-p$(shell dirname$@)\n- $(QUIETLY)$(CLANG)$(CPPFLAGS)$(CFLAGS)$(LDFLAGS)$(SO_LDFLAGS)-fPIC-shared-rpath$(ORIGIN_TOKEN)-LdynLink/cycle-lA-o$@$<\n+ $(QUIETLY)$(CLANG)$(CPPFLAGS)$(CFLAGS)$(LDFLAGS)$(SO_LDFLAGS)-fPIC-shared-rpath$(ORIGIN_TOKEN)-LdynLink/cycle-Lfake-lA-o$@$<\n\ndynLink/cycle/ref.out:dynLink/cycle/main.c dynLink/cycle/Makefile dynLink/cycle/libA$(SO_EXT)dynLink/cycle/libB$(SO_EXT)\n@mkdir-p$(shell dirname$@)\n\n\npeft:1060\tMerge pull request from jgraham/master\n\n\nicl:1060\tMINOR:Fix dynLink/cycle/Makefile\n\n\nslm:[GR - 26363]Deopt on - flat_namespace.\n\n\n", "peft": "1060\tMerge pull request from jgraham/master\n", "icl": "1060\tMINOR:Fix dynLink/cycle/Makefile\n", "slm": "[GR - 26363]Deopt on - flat_namespace.\n", "nl": "sulong:shared libraries built by the linker tests should allow undefined symbols on darwin", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16711, "text": "To set up the watch:\n\"search\":{\n\"request\":{\n\"indices\":[\n- \"<logstash-{now-1h}>\",<1>\n- \"<logstash-{now}>\"\n+ \"logstash\"<1>\n],\n\"body\":{\n\"size\":0,\nTo set up the watch:\n\"aggs\":{\n\"group_by_city\":{\n\"terms\":{\n- \"field\":\"group.group_city.raw\",<3>\n+ \"field\":\"group.group_city.keyword\",<3>\n\"size\":5\n},\n\"aggs\":{\n\"group_by_event\":{\n\"terms\":{\n- \"field\":\"event.event_url.raw\",<4>\n+ \"field\":\"event.event_url.keyword\",<4>\n\"size\":5\n},\n\"aggs\":{\nTo set up the watch:\n\"aggs\":{\n\"group_by_event_name\":{\n\"terms\":{\n- \"field\":\"event.event_name.raw\"<6>\n+ \"field\":\"event.event_name.keyword\"<6>\n}\n}\n}\nTo set up the watch:\n},\nmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm-\n//NOTCONSOLE\n- <1>Elasticsearch Date math is used to select the Logstash indices that contain the meetup data.The second pattern is needed in case the previous hour crosses days.\n+ <1>` logstash ` is the default<<indices-add-alias,index alias>>for the{ls}\n+ indices containing the meetup data.By default,the{ls}\n+ <<index-lifecycle-management,{ilm}({ilm-init})>>policy rolls this alias to a\n+ new index when the index size reaches 50GB or becomes 30 days old.For more\n+ information,see\n+ {logstash-ref}/plugins-outputs-elasticsearch.html # plugins-outputs-elasticsearch-ilm[{ilm-init}\n+ defaults in Logstash].\n<2>Find all of the RSVPs with ` Open Source ` as a topic.\n<3>Group the RSVPs by city.\n<4>Consolidate multiple RSVPs for the same event.\nTo set up the watch:\n- -\n[source,js]\nmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n- \"compare\":{\"ctx.payload.hits.total.value\":{\"gt\":0}}\n+ \"compare\":{\"ctx.payload.hits.total\":{\"gt\":0}}\nmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n//NOTCONSOLE\n- -\nPUT_watcher/watch/meetup\n\"search\":{\n\"request\":{\n\"indices\":[\n- \"<logstash-{now-1h}>\",\n- \"<logstash-{now}>\"\n+ \"logstash\"\n],\n\"body\":{\n\"size\":0,\nPUT_watcher/watch/meetup\n\"aggs\":{\n\"group_by_city\":{\n\"terms\":{\n- \"field\":\"group.group_city.raw\",\n+ \"field\":\"group.group_city.keyword\",\n\"size\":5\n},\n\"aggs\":{\n\"group_by_event\":{\n\"terms\":{\n- \"field\":\"event.event_url.raw\",\n+ \"field\":\"event.event_url.keyword\",\n\"size\":5\n},\n\"aggs\":{\nPUT_watcher/watch/meetup\n\"aggs\":{\n\"group_by_event_name\":{\n\"terms\":{\n- \"field\":\"event.event_name.raw\"\n+ \"field\":\"event.event_name.keyword\"\n}\n}\n}\nPUT_watcher/watch/meetup\n},\n\"condition\":{\n\"compare\":{\n- \"ctx.payload.hits.total.value\":{\n+ \"ctx.payload.hits.total\":{\n\"gt\":0\n}\n}\n\n\npeft:1283\t[DOCS]Fix watcher example for Logstash 6.0.0\n\n\nicl:1283\tMerge pull request from openzipkin/interop\n\n\nslm:[DOCS]Clarify logstash aggs()\n\n\n", "peft": "1283\t[DOCS]Fix watcher example for Logstash 6.0.0\n", "icl": "1283\tMerge pull request from openzipkin/interop\n", "slm": "[DOCS]Clarify logstash aggs()\n", "nl": "[DOCS]Correct watcher event data example()", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16712, "text": "dialog_connection_description=Param\\u00E8tres de connexion\\u00E0 la base de d\n\ndialog_connection_driver=Nom du pilote:\n\n+ dialog_connection_driver_project=Projet\n+\n+ dialog_connection_driver_treecontrol_initialText=Tapez une partie du nom de la base de donn\\u00E9es/du pilote\\u00E0 filtrer\n+\ndialog_connection_edit_driver_button=Editer les param\\u00E8tres de pilotes\n\ndialog_connection_events_checkbox_show_process=Affichier le panneau des processus\ndialog_connection_wizard_final_group_security=S\\u00E9curit\\u00E9\ndialog_connection_wizard_final_header=Achever la cr\\u00E9ation de la connexion\n\ndialog_connection_wizard_final_label_connection_name=Nom de la connexion\n+ dialog_connection_wizard_final_label_connection_folder=R\\u00E9pertoire de connexion\n+ dialog_connection_wizard_final_label_connection_name=Nom de la connexion\n+ dialog_connection_wizard_final_label_connection_type=Type de connexion\n+ dialog_connection_wizard_final_label_default_schema=Sch\\u00E9ma par d\\u00E9faut\n+ dialog_connection_wizard_final_label_default_schema_tooltip=Nom du sch\\u00E9ma ou catalogue assign\\u00E9 par d\\u00E9faut.\n+ dialog_connection_wizard_final_label_edit=Editer\n+ dialog_connection_wizard_final_label_isolation_level=Niveau d\\u0027isolation\n+ dialog_connection_wizard_final_label_isolation_level_tooltip=Niveau d\\u0027isolation par d\\u00E9faut des transactions.\n+ dialog_connection_wizard_final_label_keepalive=Maintenir en vie\n+ dialog_connection_wizard_final_label_keepalive_tooltip=Interval de maintien en vie(en secondes).Z\\u00E9ro\\u00E9teint le maintien\n+ dialog_connection_wizard_final_label_shell_command=Commandes Shell\n+\n\ndialog_connection_wizard_start_connection_monitor_close=Fermer la connexion\n\ndialog_edit_driver_title_create_driver=Cr\\u00E9er un nouveau pilote\n\ndialog_edit_driver_title_edit_driver=Editer le pilote''\n\n+ # # Driver download # #\n+ dialog_driver_download_button_edit_dirver=Editer le pilote\n+ dialog_driver_download_button_add_jars=Ajouter des JARs\n+\n+ dialog_driver_download_wizard_title_setting=Param\\u00E8tres du pilote\n+ dialog_driver_download_wizard_title_upload_files=Mettre\\u00E0 jour les fichiers du pilote\n+ dialog_driver_download_wizard_title_setup_files=Installer des fichiers de pilote\n+ dialog_driver_download_wizard_download=T\\u00E9l\\u00E9charger\n+ dialog_driver_download_wizard_open_download=Ouvrir la page de t\\u00E9l\\u00E9chargement\n+\n+ dialog_driver_download_page_vendor_link=<a>Site internet du fournisseur</a>\n+ dialog_driver_download_page_download_conf_link=<a>configuration du t\\u00E9l\\u00E9chargement</a>\n+\n+ dialog_driver_download_manual_page_config_driver_file=Param\\u00E8trer les fichiers du pilote\n+ dialog_driver_download_manual_page_download_driver_file=T\\u00E9l\\u00E9charger des fichiers de pilote\n+\n+ dialog_driver_download_manual_page_download_config_driver_file=T\\u00E9l\\u00E9charger&configurer{0}fichiers de pilote\n+ dialog_driver_download_manual_page_driver_file_missing_text=Les fichiers de pilote{0}sont absents.\\n\\nSelon la politique du fournisseur ce pilote n\\u0027 pas disponible publiquement\\net il vous faut le t\\u00E9l\\u00E9charger manuellement depuis le site internet du fournisseur.\\n\\nAp\\u00E8s avoir t\\u00E9l\\u00E9charg\\u00E9 le pilote avec succ\\u00E8s vous devrez<a>ajouter les fichiers JAR</a>dans la liste des biblioth\\u00E8ques de DBeaver.\n+ dialog_driver_download_manual_page_driver_file=Fichiers de pilote\n+ dialog_driver_download_manual_page_column_file=Fichier\n+ dialog_driver_download_manual_page_column_required=Requis\n+ dialog_driver_download_manual_page_column_description=Description\n+ dialog_driver_download_manual_page_yes=Oui\n+ dialog_driver_download_manual_page_no=Non\n+\n+ dialog_driver_download_auto_page_auto_download=T\\u00E9l\\u00E9chargement automatique\n+ dialog_driver_download_auto_page_download_driver_files=T\\u00E9l\\u00E9charger les fichiers du pilote\n+ dialog_driver_download_auto_page_download_specific_driver_files=T\\u00E9l\\u00E9charger{0}fichiers du pilote\n+ dialog_driver_download_auto_page_driver_file_missing_text={0}fichiers du pilote sont absents.\\nDBeaver peut le t\\u00E9l\\u00E9charger automatiquement.\\n\\n\n+ dialog_driver_download_auto_page_force_download=Forcer le t\\u00E9l\\u00E9chargement/\\u00E9crasement\n+ dialog_driver_download_auto_page_force_download_tooltip=Forcer le t\\u00E9l\\u00E9chargement des fichiers.Les fichiers seront t\\u00E9l\\u00E9charg\\u00E9s m\\u00EAme s\\u0027ils sont d\\u00E9j\\u00E0 sur le disque\n+ dialog_driver_download_auto_page_required_files=Fichiers requis par le pilote\n+ dialog_driver_download_auto_page_change_driver_version_text=Vous pouvez changer la version du pilote en cliquant sur la colonne Version.\\nVous pouvez ensuite choisir l\\u0027une des versions disponibles.\n+ dialog_driver_download_auto_page_obtain_driver_files_text=\\nOu bien vous pouvez obtenir vous-m\\u00EAme des fichiers pilotes et les ajouter dans l\\u0027\\u00E9diteur de pilotes.\n+ dialog_driver_download_auto_page_cannot_resolve_libraries_text=Biblioth\\u00E8ques introuvables.V\\u00E9rifiez vous param\\u00E8tres r\\u00E9seau\n+ dialog_driver_download_auto_page_driver_download_error=T\\u00E9l\\u00E9chargement de pilote\n+ dialog_driver_download_auto_page_driver_download_error_msg=Erreur lors du t\\u00E9l\\u00E9chargement de fichiers de pilotes\n+ dialog_driver_download_auto_page_driver_security_warning=Avertissement de s\\u00E9curit\\u00E9\n+ dialog_driver_download_auto_page_driver_security_warning_msg=La biblioth\\u00E8que''{0}''n\\u0027a pas\\u00E9t\\u00E9 trouv\\u00E9e dans un d\\u00E9p\\u00F4t s\\u00E9curis\\u00E9.\\nSeule verion non s\\u00E9curis\\u00E9e trouv\\u00E9e:{1}.\\n\\nIl n\\u0027est pas recommand\\u00E9 d\\u0027utiliser des d\\u00E9p\\u00F4ts non s\\u00E9curis\\u00E9s en raison de la possibilit\\u00E9 d\\u0027infection par des malwares.\\n\\nEtes-vous s\\u00FBre(e)de vouloir continuer?\n+ dialog_driver_download_auto_page_download_rate=T\\u00E9l\\u00E9chargement{0}/{1}\n+ dialog_driver_download_auto_page_download_failed_msg=Le t\\u00E9l\\u00E9chargement du fichier pilote a\\u00E9chou\\u00E9\\nVoulez-vous r\\u00E9essayer?\n+ # # Driver download # #\n+\ndialog_filter_button_add=Ajouter\n\ndialog_filter_button_clear=Effacer\nmodel_project_cant_open_bookmark=Impossible d'ouvrir le favoris\n\nmodel_project_open_bookmark=Ouvrir le favoris\n\n+ model_ssh_configurator_group_settings=Param\\u00E8tres\n+ model_ssh_configurator_group_advanced=Avanc\\u00E9\nmodel_ssh_configurator_checkbox_save_pass=Enregistrer le mot de passe\n-\nmodel_ssh_configurator_combo_auth_method=M\\u00E9thode d'authentification\n-\nmodel_ssh_configurator_combo_password=Mot de passe\n-\nmodel_ssh_configurator_combo_pub_key=Clef publique\n-\nmodel_ssh_configurator_dialog_choose_private_key=S\\u00E9lectionner le fichier de clef priv\\u00E9e\n-\nmodel_ssh_configurator_label_host_ip=H\\u00F4te/IP\n-\nmodel_ssh_configurator_label_keep_alive=Intervale de maintien en vie(ms)\n-\nmodel_ssh_configurator_label_passphrase=Phrase passe\n-\nmodel_ssh_configurator_label_password=Mot de passe\n-\nmodel_ssh_configurator_label_port=Port\n-\nmodel_ssh_configurator_label_private_key=Clef priv\\u00E9e\n-\n- model_ssh_configurator_label_tunnel_timeout=Tiemour de connexion tunnel(ms)\n-\n+ model_ssh_configurator_label_tunnel_timeout=Timeout de connexion tunnel(ms)\n+ model_ssh_configurator_label_local_port=Port local\n+ model_ssh_configurator_label_local_port_description=Port local pour le tunnel.Si renseign\\u00E9<=0 alors un port libre au hasard(>10000)sera attribu\\u00E9\nmodel_ssh_configurator_label_user_name=Nom d'utilisateur\n\npref_page_confirmations_combo_always=Toujours\nnew file mode 100644\nindex **********..29285d068d\nmmm/dev/null\n\n+\n+ dialog_connection_general_tab=G\\u00E9n\\u00E9ral\n+ dialog_connection_general_tab_tooltip=Propri\\u00E9t\\u00E9s g\\u00E9n\\u00E9rales de connexion\n+ dialog_connection_advanced_tab=Avanc\\u00E9\n+ dialog_connection_advanced_tab_tooltip=Propri\\u00E9t\\u00E9s avanc\\u00E9es/personnelles de pilotes\n+ dialog_connection_host=Serveur H\\u00F4te\n+ dialog_connection_port=Port\n+ dialog_connection_database=Base de donn\\u00E9es\n+ dialog_connection_user_name=Nom d\\u0027utilisateur\n+ dialog_connection_password=Mot de passe\n+ dialog_connection_test_connection=Tester la connexion...\n+\n+ edit_catalog_manager_dialog_schema_name=Nom du sch\\u00E9ma\n+ edit_command_change_user_action_create_new_user=Cr\\u00E9er un nouvel utilisateur\n+ edit_command_change_user_action_update_user_record=Mettre\\u00E0 jour l\\u0027enregistrement Utilisateur\n+ edit_command_change_user_name=Mettre\\u00E0 jour l\\u0027utilisateur\n+ edit_command_grant_privilege_action_grant_privilege=Attribuer(Grant)des privil\\u00E8ges\n+ edit_command_grant_privilege_name_revoke_privilege=Revoquer des privil\\u00E8ges\n+ edit_constraint_manager_title=Cr\\u00E9er une contrainte\n+ edit_foreign_key_manager_title=Cr\\u00E9er un clef\\u00E9trang\\u00E8re\n+ edit_index_manager_title=Cr\\u00E9er un index\n+ edit_procedure_manager_body=Corps\n+ edit_user_manager_command_create_user=Cr\\u00E9er un utilisateur\n+ edit_user_manager_command_drop_user=Supprimer l\\u0027utilisateur\n+ edit_user_manager_command_flush_privileges=D\\u00E9verser(Flush)les privil\\u00E8ges\n+ edit_view_manager_definition=D\\u00E9finition\n+ editors_session_editor_action_kill_Session=Tuer la session\n+ editors_session_editor_action_terminate_Query=Tuer la requ\\u00EAte\n+ editors_session_editor_confirm={0}\"{1}\".Etes-vous s\\u00FBr(e)?\n+ editors_user_editor_abstract_load_grants=Charger les attributions(grants)\n+ editors_user_editor_general_control_dba_privileges=Droits d\\u0027administration(DBA)\n+ editors_user_editor_general_group_limits=Limites\n+ editors_user_editor_general_group_login=Login\n+ editors_user_editor_general_label_confirm=Confirmer\n+ editors_user_editor_general_label_host=H\\u00F4te\n+ editors_user_editor_general_label_password=Mot de passe\n+ editors_user_editor_general_label_user_name=Nom d\\u0027utilisateur\n+ editors_user_editor_general_service_load_catalog_privileges=Charger le catalogue des privil\\u00E8ges\n+ editors_user_editor_general_spinner_max_connections=Connexions maximales\n+ editors_user_editor_general_spinner_max_queries=Requ\\u00EAtes maximales\n+ editors_user_editor_general_spinner_max_updates=Mises\\u00E0 jour maximales\n+ editors_user_editor_general_spinner_max_user_connections=Nombre maximal de connexions utilisateurs\n+ editors_user_editor_privileges_column_catalog=Catalogue\n+ editors_user_editor_privileges_column_table=Table\n+ editors_user_editor_privileges_control_other_privileges=Autres privil\\u00E8ges\n+ editors_user_editor_privileges_control_table_privileges=Privil\\u00E8ges de table\n+ editors_user_editor_privileges_group_catalogs=Catalogues\n+ editors_user_editor_privileges_group_tables=Tables\n+ editors_user_editor_privileges_service_load_privileges=Charger les privil\\u00E8ges\n+ editors_user_editor_privileges_service_load_tables=Charger les tables\n+ tools_db_export_wizard_job_dump_log_reader=D\\u00E9verser le lecteur de journaux\n+ tools_db_export_wizard_message_export_completed=Export de la base de donn\\u00E9es\"{0}\"termin\\u00E9\n+ tools_db_export_wizard_monitor_bytes={0}bytes\n+ tools_db_export_wizard_monitor_export_db=Exporter la base de donn\\u00E9es\n+ tools_db_export_wizard_page_settings_checkbox_add_drop=Ajouter des instructions DROP\n+ tools_db_export_wizard_page_settings_checkbox_addnl_comments=Commentaires additionnels\n+ tools_db_export_wizard_page_settings_checkbox_remove_definer=Enlever DEFINER\n+ tools_db_export_wizard_page_settings_checkbox_binary_hex=Vider les binaires en hex\n+ tools_db_export_wizard_page_settings_checkbox_no_data=Structure seulement\n+ tools_db_export_wizard_page_settings_checkbox_disable_keys=D\\u00E9sactiver les clefs\n+ tools_db_export_wizard_page_settings_checkbox_dump_events=D\\u00E9verser les\\u00E9v\\u00E8nements\n+ tools_db_export_wizard_page_settings_checkbox_ext_inserts=Insertions\\u00E9tendues\n+ tools_db_export_wizard_page_settings_checkbox_no_create=Pas d\\u0027instruction CREATE\n+ tools_db_export_wizard_page_settings_combo_item_lock_tables=V\\u00E9rouiller toutes les tables\n+ tools_db_export_wizard_page_settings_combo_item_normal=Normal(pas de verrou)\n+ tools_db_export_wizard_page_settings_combo_item_online_backup=Sauvegarde en ligne en transaction unique\n+ tools_db_export_wizard_page_settings_file_selector_title=Choisir un fichier de sortie\n+ tools_db_export_wizard_page_settings_group_exe_method=M\\u00E9thode d\\u0027ex\\u00E9cution\n+ tools_db_export_wizard_page_settings_group_objects=Objets\n+ tools_db_export_wizard_page_settings_group_output=Sortie\n+ tools_db_export_wizard_page_settings_group_settings=Param\\u00E8tres\n+ tools_db_export_wizard_page_settings_label_out_text=R\\u00E9pertoire de sortie\n+ tools_db_export_wizard_page_settings_page_description=Param\\u00E9trer l\\u0027export de bases de donn\\u00E9es\n+ tools_db_export_wizard_page_settings_page_name=Configuration d\\u0027export\n+ tools_db_export_wizard_task_name=Exporter\n+ tools_db_export_wizard_title=Export de base de donn\\u00E9es\n+ tools_script_execute_wizard_db_import=Import de base de donn\\u00E9es\n+ tools_script_execute_wizard_execute_script=Executer un script\n+ tools_script_execute_wizard_page_settings_group_input=Entr\\u00E9e\n+ tools_script_execute_wizard_page_settings_group_settings=Param\\u00E8tres\n+ tools_script_execute_wizard_page_settings_import_configuration=Configuration de l\\u0027import\n+ tools_script_execute_wizard_page_settings_label_input_file=Fichier en entr\\u00E9e\n+ tools_script_execute_wizard_page_settings_label_log_level=Niveau de traces\n+ tools_script_execute_wizard_page_settings_script_configuration=Configuration du script\n+ tools_script_execute_wizard_page_settings_set_db_import_settings=Configurer les param\\u00E8tres d\\u0027import de bases de donn\\u00E9es\n+ tools_script_execute_wizard_page_settings_set_script_execution_settings=Configurer les param\\u00E8tres d\\u0027ex\\u00E9cution des scripts\n\n\npeft:577\tTranslated using Weblate(French)\n\n\nicl:577\tBAEL - 2980 Getting started with CRaSH()\n\n\nslm:Merge pull request from eugenp/lor6 - patch - 2\n\n\n", "peft": "577\tTranslated using Weblate(French)\n", "icl": "577\tBAEL - 2980 Getting started with CRaSH()\n", "slm": "Merge pull request from eugenp/lor6 - patch - 2\n", "nl": "Merge pull request from madcollectordev/devel", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16713, "text": "\n*/\nprivate String displayName;\n\n+ private transient ModuleName moduleName;\n+\n/* package */MavenModule(MavenModuleSet parent,PomInfo pom){\nsuper(parent,pom.name.toFileSystemName());\ndisplayName=pom.displayName;\n}\n\n+ protected void doSetName(String name){\n+ super.doSetName(name);\n+ moduleName=ModuleName.fromFileSystemName(name);\n+ }\n+\n@Override\npublic void onLoad(ItemGroup<?extends Item>parent,String name)throws IOException{\nsuper.onLoad(parent,name);\npublic FilePath getWorkspace(){\nthrow new UnsupportedOperationException();\n}\n\n+ public ModuleName getModuleName(){\n+ return moduleName;\n+ }\n+\n@Override\npublic String getDisplayName(){\nreturn displayName;\npublic void doConfigSubmit(StaplerRequest req,StaplerResponse rsp)throws IOExc\n\nsave();\n}\n-\n- static{\n- Items.XSTREAM.alias(\"maven2\",MavenModule.class);\n- }\n}\n\n*/\npublic class MavenModuleSet extends AbstractItem implements TopLevelItem,ItemGroup<MavenModule>{\n/* *\n- * All{@link MavenModule}s,keyed by their{@link MavenModule # getName()name}s.\n+ * All{@link MavenModule}s,keyed by their{@link MavenModule # getModuleName()}module name}s.\n*/\n- transient/* final */Map<String,MavenModule>modules=new CopyOnWriteMap.Tree<String,MavenModule>();\n+ transient/* final */Map<ModuleName,MavenModule>modules=new CopyOnWriteMap.Tree<ModuleName,MavenModule>();\n\nprivate SCM scm=new NullSCM();\n\npublic synchronized void setJDK(JDK jdk)throws IOException{\nreturn modules.values();\n}\n\n+ public Collection<MavenModule>getModules(){\n+ return getItems();\n+ }\n+\npublic MavenModule getItem(String name){\n- return modules.get(name);\n+ return modules.get(ModuleName.fromString(name));\n+ }\n+\n+ public MavenModule getModule(String name){\n+ return getItem(name);\n}\n\npublic File getRootDirFor(MavenModule child){\npublic boolean accept(File child){\nreturn child.isDirectory();\n}\n});\n- modules=new CopyOnWriteMap.Tree<String,MavenModule>();\n+ modules=new CopyOnWriteMap.Tree<ModuleName,MavenModule>();\nfor(File subdir:subdirs){\ntry{\nMavenModule item=(MavenModule)Items.load(this,subdir);\n- modules.put(item.getName(),item);\n+ modules.put(item.getModuleName(),item);\n}catch(IOException e){\ne.printStackTrace();//TODO:logging\n}\nprivate void toPomInfo(MavenProject mp,List<PomInfo>infos){\nfor(PomInfo pom:poms){\nMavenModule mm=new MavenModule(this,pom);\nmm.save();\n- modules.put(mm.getName(),mm);\n+ modules.put(mm.getModuleName(),mm);\n}\n}\n\n\n*\n*<AUTHOR> Kawaguchi\n*/\n- public class ModuleName{\n+ public class ModuleName implements Comparable<ModuleName>{\npublic final String groupId;\npublic final String artifactId;\n\npublic String toFileSystemName(){\nreturn groupId+'$'+artifactId;\n}\n\n+ public static ModuleName fromFileSystemName(String n){\n+ int idx=n.indexOf('$');\n+ if(idx<0)throw new IllegalArgumentException(n);\n+ return new ModuleName(n.substring(0,idx),n.substring(idx+1));\n+ }\n+\n+ public static ModuleName fromString(String n){\n+ int idx=n.indexOf(':');\n+ if(idx<0)throw new IllegalArgumentException(n);\n+ return new ModuleName(n.substring(0,idx),n.substring(idx+1));\n+ }\n+\npublic boolean equals(Object o){\nif(this==o)return true;\nif(o==null||getClass()!=o.getClass())return false;\npublic int hashCode(){\nresult=31 * result+artifactId.hashCode();\nreturn result;\n}\n+\n+ public int compareTo(ModuleName that){\n+ int r=this.groupId.compareTo(that.groupId);\n+ if(r!=0)return r;\n+ return this.artifactId.compareTo(that.artifactId);\n+ }\n}\n\n\nimport hudson.XmlFile;\nimport hudson.maven.MavenModuleSet;\n+ import hudson.maven.MavenModule;\nimport hudson.util.XStream2;\n\nimport java.util.Collection;\npublic static XmlFile getConfigFile(Item item){\nXSTREAM.alias(\"project\",Project.class);\n\n//hide maven support until it gets ready\n- if(Boolean.getBoolean(\"hudson.maven\"))\n+ if(Boolean.getBoolean(\"hudson.maven\")){\nLIST.add(MavenModuleSet.DESCRIPTOR);\n+ Items.XSTREAM.alias(\"maven2\",MavenModule.class);\n+ }\n}\n}\n\n\n<!--inject main part here-->\n\n+ <h2>Modules</h2>\n+ <ul>\n+ <j:forEach var=\"m\"items=\"${it.modules}\">\n+ <li><a href=\"module/${m.moduleName}/\">${m.displayName}</a></li>\n+ </j:forEach>\n+ </ul>\n+\n<h2>Permalinks</h2>\n<ul>\n<j:if test=\"${it.lastBuild!=null}\">\n\n\npeft:302\t[JENKINS - 2099]MavenModuleSet.getItem()should return a MavenModule,not a Module\n\n\nicl:302\tAdd SPARC benchmarks\n\n\nslm:fixing a bug where the module name was not being read from the item\n\n\n", "peft": "302\t[JENKINS - 2099]MavenModuleSet.getItem()should return a MavenModule,not a Module\n", "icl": "302\tAdd SPARC benchmarks\n", "slm": "fixing a bug where the module name was not being read from the item\n", "nl": "hooked MavenModule into MavenModuleSet.", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16714, "text": "\nimport java.nio.ByteBuffer;\nimport java.util.ArrayDeque;\nimport java.util.ArrayList;\n- import java.util.Collections;\nimport java.util.List;\n\n/* *\nprivate void maybeInitCodecWithFallback(\ntry{\nList<MediaCodecInfo>allAvailableCodecInfos=\ngetAvailableCodecInfos(mediaCryptoRequiresSecureDecoder);\n+ availableCodecInfos=new ArrayDeque<>();\nif(enableDecoderFallback){\n- availableCodecInfos=new ArrayDeque<>(allAvailableCodecInfos);\n- }else{\n- availableCodecInfos=\n- new ArrayDeque<>(Collections.singletonList(allAvailableCodecInfos.get(0)));\n+ availableCodecInfos.addAll(allAvailableCodecInfos);\n+ }else if(!allAvailableCodecInfos.isEmpty()){\n+ availableCodecInfos.add(allAvailableCodecInfos.get(0));\n}\npreferredDecoderInitializationException=null;\n}catch(DecoderQueryException e){\n\n\npeft:503\tFix:Decoder fallback is not working when enableDecoderFallback is false.\n\n\nicl:503\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required\n\n\nslm:Avoid unnecessary collection creation in MediaCodecManager\n\n\n", "peft": "503\tFix:Decoder fallback is not working when enableDecoderFallback is false.\n", "icl": "503\tBAEL - 3896:OpenAPI JSON Objects in Query Params - README file revert + required\n", "slm": "Avoid unnecessary collection creation in MediaCodecManager\n", "nl": "Fix IndexOutOfBounds when there are no available codecs", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16715, "text": "deleted file mode 100644\nindex d4a1d1ed6b..**********\nBinary files a/extensions/stb-truetype/libs/windows32/test.dll and/dev/null differ\n\n\npeft:960\tDelete test.dll\n\n\nicl:960\tDanielWagnerHall and TedMao:Implemented screenshot taking for SeleneseBackedWeb\n\n\nslm:Windows 32 bumps\n\n\n", "peft": "960\tDelete test.dll\n", "icl": "960\tDanielWagnerHall and TedMao:Implemented screenshot taking for SeleneseBackedWeb\n", "slm": "Windows 32 bumps\n", "nl": "[removed]libs folders", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16716, "text": "Do not report crashes in the GitHub issue tracker.NewPipe has an automated cras\n* Search the[existing issues](https://github.com/theScrabi/NewPipe/issues)first to make sure your issue/feature hasn't been reported/requested before\n* Check if this issue/feature is already fixed/implemented in the repository\n* If you are an android/java developer you are always welcome to fix/implement an issue/a feature yourself\n+ * Use english\n\n# # Bugfixing\n* If you want to help NewPipe getting bug free,you can send me a <NAME_EMAIL> to let me know that you intent to help,and than register at our[sentry](https://support.schabi.org)setup.\n\n\npeft:1209\t[FIXED]Added english to the list of supported languages\n\n\nicl:1209\tMerge pull request from daveray/clj - fn - metadata\n\n\nslm:Update CONTRIBUTING.md\n\n\n", "peft": "1209\t[FIXED]Added english to the list of supported languages\n", "icl": "1209\tMerge pull request from daveray/clj - fn - metadata\n", "slm": "Update CONTRIBUTING.md\n", "nl": "add use english into contribution notes", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16717, "text": "\npublic interface RMap<K,V>extends ConcurrentMap<K,V>,RExpirable,RMapAsync<K,V>{\n\n/* *\n- * Loads all map entries to this Redis map.\n+ * Loads all map entries to this Redis map using{@link org.redisson.api.map.MapLoader}.\n*\n*@param replaceExistingValues-<code>true</code>if existed values should be replaced,<code>false</code>otherwise.\n*@param parallelism-parallelism level,used to increase speed of process execution\n\nvoid loadAll(boolean replaceExistingValues,int parallelism);\n\n/* *\n- * Loads map entries whose keys are listed in defined<code>keys</code>parameter.\n+ * Loads map entries using{@link org.redisson.api.map.MapLoader}whose keys are listed in defined<code>keys</code>parameter.\n*\n*@param keys-map keys\n*@param replaceExistingValues-<code>true</code>if existed values should be replaced,<code>false</code>otherwise.\n\n*/\nInteger revRank(V o);\n\n+ /* *\n+ * Returns score of element or<code>null</code>if it doesn't exist.\n+ *\n+ *@param o-element\n+ *@return score\n+ */\nDouble getScore(V o);\n\n/* *\n\n\nRFuture<Integer>revRankAsync(V o);\n\n+ /* *\n+ * Returns score of element or<code>null</code>if it doesn't exist.\n+ *\n+ *@param o-element\n+ *@return score\n+ */\nRFuture<Double>getScoreAsync(V o);\n\n/* *\n\n\npeft:693\tMerge pull request from mrnikolovic/master\n\n\nicl:693\tMINOR:Fix state transition diagram for stream threads()\n\n\nslm:RMap javadocs update\n\n\n", "peft": "693\tMerge pull request from mrnikolovic/master\n", "icl": "693\tMINOR:Fix state transition diagram for stream threads()\n", "slm": "RMap javadocs update\n", "nl": "Comments added", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16718, "text": "public static void load(String originalName,ClassLoader loader){\nInputStream in=null;\nOutputStream out=null;\nFile tmpFile=null;\n- URL url=loader.getResource(path);\n+ URL url;\n+ if(loader==null){\n+ url=ClassLoader.getSystemResource(path);\n+ }else{\n+ url=loader.getResource(path);\n+ }\ntry{\nif(url==null){\nif(PlatformDependent.isOsx()){\nString fileName=path.endsWith(\".jnilib\")?NATIVE_RESOURCE_HOME+\"lib\"+name+\".dynlib\":\nNATIVE_RESOURCE_HOME+\"lib\"+name+\".jnilib\";\n- url=loader.getResource(fileName);\n+ if(loader==null){\n+ url=ClassLoader.getSystemResource(fileName);\n+ }else{\n+ url=loader.getResource(fileName);\n+ }\nif(url==null){\nFileNotFoundException fnf=new FileNotFoundException(fileName);\nThrowableUtil.addSuppressedAndClear(fnf,suppressed);\npublic Object run(){\nprivate static Class<?>tryToLoadClass(final ClassLoader loader,final Class<?>helper)\nthrows ClassNotFoundException{\ntry{\n- return loader.loadClass(helper.getName());\n+ return Class.forName(helper.getName(),false,loader);\n}catch(ClassNotFoundException e1){\n+ if(loader==null){\n+ //cannot defineClass inside bootstrap class loader\n+ throw e1;\n+ }\ntry{\n//The helper class is NOT found in target ClassLoader,we have to define the helper class.\nfinal byte[]classBinary=classToByteArray(helper);\n\n\nimport org.junit.Test;\n\n+ import java.lang.reflect.Method;\nimport java.io.FileNotFoundException;\nimport java.util.UUID;\n\n\n\npublic class NativeLibraryLoaderTest{\n\n+ private static final Method getSupressedMethod=getGetSuppressed();\n+\n@Test\npublic void testFileNotFound(){\ntry{\npublic void testFileNotFound(){\nfail();\n}catch(UnsatisfiedLinkError error){\nassertTrue(error.getCause()instanceof FileNotFoundException);\n+ if(getSupressedMethod!=null){\n+ verifySuppressedException(error,UnsatisfiedLinkError.class);\n+ }\n+ }\n+ }\n+\n+ @Test\n+ public void testFileNotFoundWithNullClassLoader(){\n+ try{\n+ NativeLibraryLoader.load(UUID.randomUUID().toString(),null);\n+ fail();\n+ }catch(UnsatisfiedLinkError error){\n+ assertTrue(error.getCause()instanceof FileNotFoundException);\n+ if(getSupressedMethod!=null){\n+ verifySuppressedException(error,ClassNotFoundException.class);\n+ }\n+ }\n+ }\n+\n+ private static void verifySuppressedException(UnsatisfiedLinkError error,\n+ Class<?>expectedSuppressedExceptionClass){\n+ try{\n+ Throwable[]suppressed=(Throwable[])getSupressedMethod.invoke(error.getCause());\n+ assertTrue(suppressed.length==1);\n+ assertTrue(suppressed[0]instanceof UnsatisfiedLinkError);\n+ suppressed=(Throwable[])getSupressedMethod.invoke(suppressed[0]);\n+ assertTrue(suppressed.length==1);\n+ assertTrue(expectedSuppressedExceptionClass.isInstance(suppressed[0]));\n+ }catch(Exception e){\n+ throw new RuntimeException(e);\n+ }\n+ }\n+\n+ private static Method getGetSuppressed(){\n+ if(PlatformDependent.javaVersion()<7){\n+ return null;\n+ }\n+ try{\n+ return Throwable.class.getDeclaredMethod(\"getSuppressed\");\n+ }catch(NoSuchMethodException e){\n+ throw new RuntimeException(e);\n}\n}\n}\n\n\npeft:1760\tFix NativeLibraryLoaderTest\n\n\nicl:1760\tAdds\"- fno - autolink\"to ObjC compilation actions to avoid using the autolink feature, as it doesn\n\n\nslm:Fix NativeLibraryLoader.loadClass to use ClassLoader.getSystemResource if available.\n\n\n", "peft": "1760\tFix NativeLibraryLoaderTest\n", "icl": "1760\tAdds\"- fno - autolink\"to ObjC compilation actions to avoid using the autolink feature, as it doesn\n", "slm": "Fix NativeLibraryLoader.loadClass to use ClassLoader.getSystemResource if available.\n", "nl": "Support running Netty in bootstrap class loader", "lang": "java", "label": ["Feat-mod"], "Comments": []}
{"id": 16719, "text": "\n*<AUTHOR> Andreou\n*/\npublic class BloomFilterTest extends TestCase{\n+ @SuppressUnderAndroid//OutOfMemoryError\npublic void testLargeBloomFilterDoesntOverflow(){\nlong numBits=Integer.MAX_VALUE;\nnumBits++;\npublic void testOptimalSize(){\n}\n\n@SuppressWarnings(\"CheckReturnValue\")\n+ @SuppressUnderAndroid//OutOfMemoryError\npublic void testLargeNumberOfInsertions(){\n//We use horrible FPPs here to keep Java from OOM'ing\nBloomFilter.create(Funnels.unencodedCharsFunnel(),42L+Integer.MAX_VALUE,0.28);\nnew file mode 100644\nindex **********..8a1df1d172\nmmm/dev/null\n\n+ /*\n+ * Copyright(C)2015 The Guava Authors\n+ *\n+ * Licensed under the Apache License,Version 2.0(the\"License\");\n+ * you may not use this file except in compliance with the License.\n+ * You may obtain a copy of the License at\n+ *\n+ * http://www.apache.org/licenses/LICENSE-2.0\n+ *\n+ * Unless required by applicable law or agreed to in writing,software\n+ * distributed under the License is distributed on an\"AS IS\"BASIS,\n+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,either express or implied.\n+ * See the License for the specific language governing permissions and\n+ * limitations under the License.\n+ */\n+\n+ package com.google.common.hash;\n+\n+ import static java.lang.annotation.ElementType.ANNOTATION_TYPE;\n+ import static java.lang.annotation.ElementType.CONSTRUCTOR;\n+ import static java.lang.annotation.ElementType.FIELD;\n+ import static java.lang.annotation.ElementType.METHOD;\n+ import static java.lang.annotation.ElementType.TYPE;\n+ import static java.lang.annotation.RetentionPolicy.CLASS;\n+\n+ import com.google.common.annotations.GwtCompatible;\n+\n+ import java.lang.annotation.Retention;\n+ import java.lang.annotation.Target;\n+\n+ /* *\n+ * Signifies that a test should not be run under Android.This annotation is respected only by our\n+ * Google-internal Android suite generators.Note that those generators also suppress any test\n+ * annotated with MediumTest or LargeTest.\n+ */\n+ @Retention(CLASS)\n+ @Target({ANNOTATION_TYPE,CONSTRUCTOR,FIELD,METHOD,TYPE})\n+ @GwtCompatible\n+ @interface SuppressUnderAndroid{}\npublic void testCreateWarmupParameterValidation(){\n}\n}\n\n+ @SuppressUnderAndroid//difference in String.format rounding?\npublic void testWarmUp(){\nRateLimiter limiter=RateLimiter.create(stopwatch,2.0,4000,MILLISECONDS,3.0);\nfor(int i=0;i<8;i++){\npublic void testWarmUpWithColdFactor1(){\n\"R0.00,R0.20,R0.20,R0.20,R0.20,R0.20,R0.20,R0.20\");//# 3\n}\n\n+ @SuppressUnderAndroid//difference in String.format rounding?\npublic void testWarmUpAndUpdate(){\nRateLimiter limiter=RateLimiter.create(stopwatch,2.0,4000,MILLISECONDS,3.0);\nfor(int i=0;i<8;i++){\npublic String toString(){\n}\n}\n\n- public void testMocking()throws Exception{\n- RateLimiter mockito=Mockito.mock(RateLimiter.class);\n- RateLimiter easyMock=EasyMock.createNiceMock(RateLimiter.class);\n- EasyMock.replay(easyMock);\n+ public void testMockingMockito()throws Exception{\n+ RateLimiter mock=Mockito.mock(RateLimiter.class);\n+ doTestMocking(mock);\n+ }\n+\n+ @SuppressUnderAndroid//EasyMock Class Extension doesn't appear to work on Android.\n+ public void testMockingEasyMock()throws Exception{\n+ RateLimiter mock=EasyMock.createNiceMock(RateLimiter.class);\n+ EasyMock.replay(mock);\n+ doTestMocking(mock);\n+ }\n+\n+ private static void doTestMocking(RateLimiter mock)throws Exception{\nfor(Method method:RateLimiter.class.getMethods()){\nif(!isStatic(method.getModifiers())\n&&!NOT_WORKING_ON_MOCKS.contains(method.getName())\n&&!method.getDeclaringClass().equals(Object.class)){\n- method.invoke(mockito,arbitraryParameters(method));\n- method.invoke(easyMock,arbitraryParameters(method));\n+ method.invoke(mock,arbitraryParameters(method));\n}\n}\n}\nnew file mode 100644\nindex **********..b9b9074033\nmmm/dev/null\n\n+ /*\n+ * Copyright(C)2015 The Guava Authors\n+ *\n+ * Licensed under the Apache License,Version 2.0(the\"License\");\n+ * you may not use this file except in compliance with the License.\n+ * You may obtain a copy of the License at\n+ *\n+ * http://www.apache.org/licenses/LICENSE-2.0\n+ *\n+ * Unless required by applicable law or agreed to in writing,software\n+ * distributed under the License is distributed on an\"AS IS\"BASIS,\n+ * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND,either express or implied.\n+ * See the License for the specific language governing permissions and\n+ * limitations under the License.\n+ */\n+\n+ package com.google.common.util.concurrent;\n+\n+ import static java.lang.annotation.ElementType.ANNOTATION_TYPE;\n+ import static java.lang.annotation.ElementType.CONSTRUCTOR;\n+ import static java.lang.annotation.ElementType.FIELD;\n+ import static java.lang.annotation.ElementType.METHOD;\n+ import static java.lang.annotation.ElementType.TYPE;\n+ import static java.lang.annotation.RetentionPolicy.CLASS;\n+\n+ import com.google.common.annotations.GwtCompatible;\n+\n+ import java.lang.annotation.Retention;\n+ import java.lang.annotation.Target;\n+\n+ /* *\n+ * Signifies that a test should not be run under Android.This annotation is respected only by our\n+ * Google-internal Android suite generators.Note that those generators also suppress any test\n+ * annotated with MediumTest or LargeTest.\n+ */\n+ @Retention(CLASS)\n+ @Target({ANNOTATION_TYPE,CONSTRUCTOR,FIELD,METHOD,TYPE})\n+ @GwtCompatible\n+ @interface SuppressUnderAndroid{}\n\n\npeft:956\tAdd @SuppressUnderAndroid to RateLimiterTest.java\n\n\nicl:956\t# Answer:\n\n\nslm:Add a test for large number of insertions that are not OOM.\n\n\n", "peft": "956\tAdd @SuppressUnderAndroid to RateLimiterTest.java\n", "icl": "956\t# Answer:\n", "slm": "Add a test for large number of insertions that are not OOM.\n", "nl": "Suppress tests under Android:", "lang": "java", "label": ["Doc&Code"], "Comments": []}
{"id": 16720, "text": "\n<jmustache.version>1.10</jmustache.version>\n<joda-time.version>2.7</joda-time.version>\n<jolokia.version>1.2.3</jolokia.version>\n- <json-path.version>0.9.1</json-path.version>\n+ <json-path.version>2.0.0</json-path.version>\n<jstl.version>1.2</jstl.version>\n<junit.version>4.12</junit.version>\n<liquibase.version>3.3.2</liquibase.version>\n\n\npeft:1828\tUpgrade json-path to 2.0.0\n\n\nicl:1828\tMINOR:Upgrade json-path to 2.0.0\n\n\nslm:Upgrade to JsonPath 2.0.0\n\n\n", "peft": "1828\tUpgrade json-path to 2.0.0\n", "icl": "1828\tMINOR:Upgrade json-path to 2.0.0\n", "slm": "Upgrade to JsonPath 2.0.0\n", "nl": "Upgrade to JsonPath 2.0.0", "lang": "java", "label": ["Ref-con"], "Comments": []}
{"id": 16721, "text": "\n# Order of focus:\n\n*[]Lock scrolling on Menu(enable with prop)\n- *[x]Pass more(and consistent?)props and state from Select to Components\n- *[]Fix issue with how the mouse hover interacts with keyboard scrolling\n+ *[]Pass more(and consistent?)props and state from Select to Components\n+ *[x]Fix issue with how the mouse hover interacts with keyboard scrolling\n+ *[x]Ability to customise built-in strings\n*[]Tags mode(Creatable)\n- *[]Ability to customise built-in strings\n*[-]Make inputValue a controllable prop\n*[]Make menuIsOpen a controllable prop\n- *[]Ensure on{Event}compatibility/parity(where possible)with v1\n*[]Render menu always open|as a dropdown|not at all\n*[]Handle changing of isDisabled prop\n*[]Virtualisation\n\n\nmmm\n\n+ # Review:\n+\n+ *[]Make sure all on{Event}props that make sense are implemented\n+ *[]Check old props that may need to be brought forward:\n+ *[]* ` autoBlur `(was for mobile--still necessary?)\n+ *[]* ` autosize `(not sure why this needs to be supported?)\n+ *[]* ` backspaceToRemoveMessage `\n+ *[]* ` className `(for container)\n+ *[]* ` clearable `(can just provide no component)\n+ *[]* ` clearAllText `/` clearValueText `(can override component?)\n+ *[]* ` id `(for container)/` inputId `\n+ *[]* ` onBlurResetsInput `/` onCloseResetsInput `/` onSelectResetsInput `\n+ *[]* ` onClose `/` onOpen `\n+ *[]* ` onMenuScrollToBottom `\n+ *[]* ` openOnClick `/` openOnFocus `\n+ *[]* ` pageSize `\n+ *[]* ` required `\n+ *[]* ` resetValue `\n+ *[]* ` rtl `\n+ *[]* ` scrollMenuIntoView `\n+ *[]* ` searchable `/` searchPromptText `\n+ *[]* ` tabIndex `\n+\n+ mmm\n+\n# Maybe:\n\n*[]Prevent values from being popped,was ` option.clearableValue===false `\n\n*[]Scroll behaviour:can we overscroll up to show the group heading?\n*[]Internal Required State\n*[]Touch event handling\n+ *[]Scroll the menu into view when it opens\n*[]* handle ` touchOutside `?\n*[]Async w/pagination\n+ *[]Extention point to reorder/change menu options array when it's created\n\nmmm\n\n\n\npeft:203\t[v2]Pass more(and consistent?)props and state from Select to Components\n\n\nicl:203\tadd docs\n\n\nslm:add some doc\n\n\n", "peft": "203\t[v2]Pass more(and consistent?)props and state from Select to Components\n", "icl": "203\tadd docs\n", "slm": "add some doc\n", "nl": "Updating TODO list", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16722, "text": "\n\"stacktrace-parser\":\"git://github.com/frantic/stacktrace-parser.git # 493c5e5638\",\n\"uglify-js\":\"~2.4.16\",\n\"underscore\":\"1.7.0\",\n- \"worker-farm\":\"^1.3.0\",\n+ \"worker-farm\":\"^1.3.1\",\n\"ws\":\"0.4.31\",\n\"yargs\":\"1.3.2\"\n},\n\n\npeft:1648\tUpdate worker - farm to 1.3.1\n\n\nicl:1648\tMerge pull request from github/package.json - fix worker-farm\n\n\nslm:bump worker farm\n\n\n", "peft": "1648\tUpdate worker - farm to 1.3.1\n", "icl": "1648\tMerge pull request from github/package.json - fix worker-farm\n", "slm": "bump worker farm\n", "nl": "[react - packager]Update worker - farm to get custom error props", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16723, "text": "Animation.prototype={\nthis.paused=false;\n},\n\n+ addFrame:function(config)\n+ {\n+ return this.addFrameAt(0,config);\n+ },\n+\n+ addFrameAt:function(index,config)\n+ {\n+ if(index===undefined){index=0;}\n+\n+ var newFrames=GetFrames(this.manager.textureManager,config);\n+\n+ if(newFrames.length>0)\n+ {\n+ var pre=this.frames.slice(0,index);\n+ var post=this.frames.slice(index);\n+\n+ this.frames=pre.concat(newFrames,post);\n+ }\n+\n+ return this.updateFrameSequence();\n+ },\n+\n+ updateFrameSequence:function()\n+ {\n+ var len=this.frames.length;\n+ var slice=1/(len-1);\n+\n+ for(var i=0;i<len;i++)\n+ {\n+ var frame=this.frames[i];\n+\n+ frame.index=i+1;\n+ frame.isFirst=false;\n+ frame.isLast=false;\n+ frame.progress=i * slice;\n+\n+ if(i===0)\n+ {\n+ frame.isFirst=true;\n+ frame.isLast=(len===1);\n+ frame.prevFrame=this.frames[len-1];\n+ frame.nextFrame=this.frames[i+1];\n+ }\n+ else if(i===len-1)\n+ {\n+ frame.isLast=true;\n+ frame.prevFrame=this.frames[len-2];\n+ frame.nextFrame=this.frames[0];\n+ }\n+ else if(len>1)\n+ {\n+ frame.prevFrame=this.frames[i-1];\n+ frame.nextFrame=this.frames[i+1];\n+ }\n+ }\n+\n+ return this;\n+ },\n+\ncheckFrame:function(index)\n{\nreturn(index<this.frames.length);\n\nvar CHECKSUM={\n- build:'b62a75c0-2f4e-11e7-bf40-cda08d192aff'\n+ build:'e8ec59b0-2f67-11e7-8a6d-15f444d89077'\n};\nmodule.exports=CHECKSUM;\n\\No newline at end of file\n\n\npeft:74\tAdded addFrame and addFrameAt methods to Animation.\n\n\nicl:74\ttests(css):newlines around multiple and combinator selectors()\n\n\nslm:Added new checks for the textures manager\n\n\n", "peft": "74\tAdded addFrame and addFrameAt methods to Animation.\n", "icl": "74\ttests(css):newlines around multiple and combinator selectors()\n", "slm": "Added new checks for the textures manager\n", "nl": "Added Animation.addFrame and addFrameAt including sequence updating.", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16724, "text": "\n- 3.x\n- ===\n+ 3.19.1/2015-01-20\n+ ===================\n\n* deps:connect@2.28.2\n- deps:body-parser@~1.10.2\n\n{\n\"name\":\"express\",\n\"description\":\"Sinatra inspired web development framework\",\n- \"version\":\"3.19.0\",\n+ \"version\":\"3.19.1\",\n\"author\":\"TJ Holowaychuk<<EMAIL>>\",\n\"contributors\":[\n\"Aaron Heckmann<<EMAIL>>\",\n\n\npeft:839\t3.19.1/2015-01-20\n\n\nicl:839\tbump version to 3.19.1\n\n\nslm:3.19.1\n\n\n", "peft": "839\t3.19.1/2015-01-20\n", "icl": "839\tbump version to 3.19.1\n", "slm": "3.19.1\n", "nl": "3.19.1", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16725, "text": "\ne_preventDefault(e);\n\nvar ourRange,ourIndex,startSel=doc.sel;\n- if(addNew){\n+ if(addNew&&!e.shiftKey){\nourIndex=doc.sel.contains(start);\nif(ourIndex>-1)\nourRange=doc.sel.ranges[ourIndex];\n\n\npeft:1072\tMerge pull request from johnkpaul/patch - 1\n\n\nicl:1072\ttests(js):remove unused variable()\n\n\nslm:fix search for shift key\n\n\n", "peft": "1072\tMerge pull request from johnkpaul/patch - 1\n", "icl": "1072\ttests(js):remove unused variable()\n", "slm": "fix search for shift key\n", "nl": "Make shift - ctrl - click extend the primary selection", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16726, "text": "pushd\"${__root}\">/dev/null 2>&1\nexit 0\nfi\nif[[!\"$TRAVIS_COMMIT_MESSAGE\"=~^Release *]];then\n- echo\"On Travis(TRAVIS is'${TRAVIS}'),I'm not pushing releases to the CDN unless commit message starts with'Release'(TRAVIS_COMMIT is'${$TRAVIS_COMMIT_MESSAGE}')\"\n+ echo\"On Travis(TRAVIS is'${TRAVIS}'),I'm not pushing releases to the CDN unless commit message starts with'Release'(TRAVIS_COMMIT is'${TRAVIS_COMMIT_MESSAGE}')\"\nexit 0\nfi\nfi\n\n\npeft:171\tFix typo in commit message.\n\n\nicl:171\trc17(same as rc16)\n\n\nslm:Update travis.yml\n\n\n", "peft": "171\tFix typo in commit message.\n", "icl": "171\trc17(same as rc16)\n", "slm": "Update travis.yml\n", "nl": "typo", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16727, "text": "class Project extends Model\nmessage='Use Project::observeBuffers instead'\nmessage+=';Project::observeBuffers no longer accepts a subscriber parameter.Call ` subscriber.subscribe(atom.project.observeBuffers(cb))`'if subscriber?\n\n- disposable=observeBuffers(callback)\n+ disposable=@observeBuffers(callback)\nsubscriber.subscribe(disposable)if subscriber?\ndisposable\n\n\n\npeft:1576\tUse Project::observeBuffers instead;Project::observeBuffers no longer accepts a subscriber parameter.Call ` subscriber.subscribe(atom.project.observeBuffers(cb))`'if subscriber?\n\n\nicl:1576\tfeat(www):add type tabs to code snippets()\n\n\nslm:Add missing@\n\n\n", "peft": "1576\tUse Project::observeBuffers instead;Project::observeBuffers no longer accepts a subscriber parameter.Call ` subscriber.subscribe(atom.project.observeBuffers(cb))`'if subscriber?\n", "icl": "1576\tfeat(www):add type tabs to code snippets()\n", "slm": "Add missing@\n", "nl": "Fix observeBuffers", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16728, "text": "Template.accountProfile.helpers\n\nusername:->\nreturn Meteor.user().username\n+\n+ allowUsernameChange:->\n+ return Settings.findOne(\"Accounts_AllowUsernameChange\").value\n+\n+ usernameChangeDisabled:->\n+ return t('Username_Change_Disabled')\n+\n+\n\nTemplate.accountProfile.onCreated->\nsettingsTemplate=this.parentTemplate(3)\nTemplate.accountProfile.onCreated->\n\n@save=->\ninstance=@\n-\n+ if!Settings.findOne(\"Allow_Username_Change\").value\n+ toastr.error t('Username_Change_Disabled')\n+ return callback()\noldPassword=_.trim($('# oldPassword').val())\nnewPassword=_.trim($('# password').val())\n\n<h2>\n<div class=\"input-line\">\n<label for=\"username\">{{_\"Username\"}}</label>\n<div>\n- <input type=\"text\"name=\"username\"id=\"username\"placeholder=\"{{username}}\"/>\n+ {{# if allowUsernameChange}}\n+ <input type=\"text\"name=\"username\"id=\"username\"placeholder=\"{{username}}\"/>\n+ {{else}}\n+ <input type=\"text\"name=\"username\"id=\"username\"placeholder=\"{{username}}\"disabled=\"disabled\"title=\"{{usernameChangeDisabled}}\"/>\n+ {{/if}}\n</div>\n</div>\n<div class=\"input-line\">\n\n\"Access_online_demo\":\"Access the online demo\",\n\"Access_Online_Demo\":\"Access the Online Demo\",\n\"Accounts\":\"Accounts\",\n+ \"Accounts_AllowUsernameChange\":\"Allow Username Change\",\n\"Accounts_denyUnverifiedEmail\":\"Deny unverified e-mail\",\n\"Accounts_EmailVerification\":\"E-mail Verification\",\n\"Accounts_OAuth_Facebook\":\"Facebook Login\",\nRocketChat.settings.add'Accounts_OAuth_Twitter',false,{type:'boolean',grou\nRocketChat.settings.add'Accounts_OAuth_Twitter_id','',{type:'string',group:'Accounts',section:'Twitter'}\nRocketChat.settings.add'Accounts_OAuth_Twitter_secret','',{type:'string',group:'Accounts',section:'Twitter'}\n\n+ RocketChat.settings.add'Accounts_AllowUsernameChange',true,{type:'boolean',group:'Accounts',section:'General'}\n+\nRocketChat.settings.addGroup'General'\nRocketChat.settings.add'Site_Url',__meteor_runtime_config__?.ROOT_URL,{type:'string',group:'General',i18nDescription:'Site_Url_Description',public:true}\nRocketChat.settings.add'Site_Name','Rocket.Chat',{type:'string',group:'General',public:true}\n\n\npeft:772\tAdded setting to allow username change\n\n\nicl:772\ttests(settings):allow username change()\n\n\nslm:Adds setting to allow/deny username change\n\n\n", "peft": "772\tAdded setting to allow username change\n", "icl": "772\ttests(settings):allow username change()\n", "slm": "Adds setting to allow/deny username change\n", "nl": "no message", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16729, "text": "\n\"dependencies\":{\n\"image-size\":{\n\"version\":\"0.4.0\"\n+ },\n+ \"mime-types\":{\n+ \"version\":\"2.1.9\",\n+ \"dependencies\":{\n+ \"mime-db\":{\n+ \"version\":\"1.21.0\"\n+ }\n+ }\n}\n}\n}\nPackage.onUse(function(api){\n});\n\nNpm.depends({\n- \"image-size\":\"0.4.0\"\n+ \"image-size\":\"0.4.0\",\n+ \"mime-types\":\"2.1.9\"\n});\n\nPackage.onTest(function(api){\n\nsizeOf=Npm.require'image-size'\n-\n+ mime=Npm.require'mime-types'\n\n@RocketChatAssetsInstance=new RocketChatFile.GridFS\nname:'assets'\nassets=\ndefaultUrl:'favicon.ico?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/vnd.microsoft.icon'\n- extention:'ico'\n+ extension:'ico'\nwidth:undefined\nheight:undefined\n'favicon.svg':\nassets=\ndefaultUrl:'/images/logo/icon.svg?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/svg+xml'\n- extention:'svg'\n+ extension:'svg'\nwidth:undefined\nheight:undefined\n'favicon_64.png':\nassets=\ndefaultUrl:'images/logo/favicon-64x64.png?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/png'\n- extention:'png'\n+ extension:'png'\nwidth:64\nheight:64\n'favicon_96.png':\nassets=\ndefaultUrl:'images/logo/favicon-96x96.png?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/png'\n- extention:'png'\n+ extension:'png'\nwidth:96\nheight:96\n'favicon_128.png':\nassets=\ndefaultUrl:'images/logo/favicon-128x128.png?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/png'\n- extention:'png'\n+ extension:'png'\nwidth:128\nheight:128\n'favicon_192.png':\nassets=\ndefaultUrl:'images/logo/android-chrome-192x192.png?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/png'\n- extention:'png'\n+ extension:'png'\nwidth:192\nheight:192\n'favicon_256.png':\nassets=\ndefaultUrl:'images/logo/favicon-256x256.png?v=3'\nconstraints:\ntype:'image'\n- contentType:'image/png'\n- extention:'png'\n+ extension:'png'\nwidth:256\nheight:256\n\nMeteor.methods\nif not assets[asset]?\nthrow new Meteor.Error\"Invalid_asset\"\n\n- if contentType isnt assets[asset].constraints.contentType\n- throw new Meteor.Error\"Invalid_file_type\"\n+ if mime.extension(contentType)isnt assets[asset].constraints.extension\n+ throw new Meteor.Error\"Invalid_file_type\",contentType\n\nfile=new Buffer(binaryContent,'binary')\n\nTemplate.admin.events\nfor blob in files\ntoastr.info TAPi18n.__'Uploading_file'\n\n- <EMAIL> isnt blob.type\n- toastr.error TAPi18n.__'Invalid_file_type'\n- return\n+ # <EMAIL> isnt blob.type\n+ # toastr.error blob.type,TAPi18n.__'Invalid_file_type'\n+ # return\n\nreader=new FileReader()\nreader.readAsBinaryString(blob)\nreader.onloadend==>\nMeteor.call'setAsset',reader.result,blob.type,@asset,(err,data)->\nif err?\n- toastr.error TAPi18n.__err.error\n- console.log err.error\n+ toastr.error err.reason,TAPi18n.__err.error\n+ console.log err\nreturn\n\ntoastr.success TAPi18n.__'File_uploaded'\n\n\npeft:1163\tFix mime-types dependency\n\n\nicl:1163\tAdded mime-types dependency for checking file type\n\n\nslm:chore:remove image - size for windows\n\n\n", "peft": "1163\tFix mime-types dependency\n", "icl": "1163\tAdded mime-types dependency for checking file type\n", "slm": "chore:remove image - size for windows\n", "nl": "Merge pull request from RocketChat/improvements/check - mime - for - assets", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16730, "text": "describe('1.0',function(){\nconst posts=result[0].data.map((model)=>model.toJSON(options));\n\nposts.length.should.eql(2);\n- posts[0].html.should.eql('<p></p>');\n+ should(posts[0].html).eql(null);\nposts[0].mobiledoc.should.eql('{\"version\":\"0.3.1\",\"markups\":[],\"atoms\":[],\"cards\":[],\"sections\":[[1,\"p\",[[0,[],0,\"\"]]]]}');\n\n- posts[1].html.should.eql('<p></p>');\n+ should(posts[1].html).eql(null);\nposts[1].mobiledoc.should.eql('{\"version\":\"0.3.1\",\"markups\":[],\"atoms\":[],\"cards\":[],\"sections\":[[1,\"p\",[[0,[],0,\"\"]]]]}');\n});\n});\ndescribe('1.0',function(){\nconst posts=result[0].data.map((model)=>model.toJSON(options));\n\nposts.length.should.eql(1);\n- posts[0].html.should.eql('<p></p>');\n+ should(posts[0].html).eql(null);\nposts[0].mobiledoc.should.eql('{\"version\":\"0.3.1\",\"markups\":[],\"atoms\":[],\"cards\":[],\"sections\":[[1,\"p\",[[0,[],0,\"\"]]]]}');\n});\n});\ndescribe('1.0',function(){\nconst posts=result[0].data.map((model)=>model.toJSON(options));\n\nposts.length.should.eql(1);\n- posts[0].html.should.eql('<p></p>');\n+ should(posts[0].html).eql(null);\nposts[0].mobiledoc.should.eql('{\"version\":\"0.3.1\",\"markups\":[],\"atoms\":[],\"cards\":[],\"sections\":[[1,\"p\",[[0,[],0,\"\"]]]]}');\n});\n});\n\n\npeft:193\tFixes issue with html being null when no html is present in the mobiledoc.\n\n\nicl:193\ttests(css):newlines around multiple and combinator selectors()\n\n\nslm:Merge pull request from brendandahl/master\n\n\n", "peft": "193\tFixes issue with html being null when no html is present in the mobiledoc.\n", "icl": "193\ttests(css):newlines around multiple and combinator selectors()\n", "slm": "Merge pull request from brendandahl/master\n", "nl": "Fixed intergration tests", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16731, "text": "See[here](https://github.com/ai/browserslist)for how to specify browser ranges\n\n# # # Dev Server Proxy\n\n- If your frontend app and the backend API server are not running on the same host,you will need to proxy API requests to the API server during development.This is configurable via the ` devServer.proxy ` option in ` vue.cofnig.js `.\n+ If your frontend app and the backend API server are not running on the same host,you will need to proxy API requests to the API server during development.This is configurable via the ` devServer.proxy ` option in ` vue.config.js `.\n\nSee[Configuring Proxy](./cli-service.md # configuring-proxy)for more details.\n\n\n\npeft:1755\tUpdate vue.config.js.md\n\n\nicl:1755\tAdd proxy to vue.config.js\n\n\nslm:revert:cofnig ->config\n\n\n", "peft": "1755\tUpdate vue.config.js.md\n", "icl": "1755\tAdd proxy to vue.config.js\n", "slm": "revert:cofnig ->config\n", "nl": "docs:fixed typo()", "lang": "javascript", "label": ["Doc-mod"], "Comments": []}
{"id": 16732, "text": "\n{\n\"name\":\"redux-saga\",\n- \"version\":\"0.15.0\",\n+ \"version\":\"0.15.1\",\n\"description\":\"Saga middleware for Redux to handle Side Effects\",\n\"main\":\"lib/index.js\",\n\"module\":\"es/index.js\",\n\n\npeft:1889\t0.15.1\n\n\nicl:1889\tUpdate package.json to 0.15.1\n\n\nslm:0.15.1\n\n\n", "peft": "1889\t0.15.1\n", "icl": "1889\tUpdate package.json to 0.15.1\n", "slm": "0.15.1\n", "nl": "0.15.1", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16733, "text": "\n\"message\":\"chore(release):publish%s\"\n}\n},\n- \"version\":\"2.0.0-alpha-23620191020004\"\n+ \"version\":\"2.0.0-alpha-23720191023001\"\n}\n\n{\n\"name\":\"@dcloudio/uni-app-plus-nvue\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app app-plus-nvue\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-app-plus\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app app-plus\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-cli-shared\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-cli-shared\",\n\"main\":\"lib/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-h5-ui\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app h5 ui\",\n\"main\":\"dist/index.umd.min.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-h5\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app h5\",\n\"main\":\"dist/index.umd.min.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-mp-alipay\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app mp-alipay\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-mp-baidu\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app mp-baidu\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-mp-qq\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app mp-qq\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-mp-toutiao\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app mp-toutiao\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-mp-weixin\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app mp-weixin\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-stat\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"\",\n\"main\":\"dist/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/uni-template-compiler\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-template-compiler\",\n\"main\":\"lib/index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/vue-cli-plugin-hbuilderx\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"HBuilderX plugin for vue-cli 3\",\n\"main\":\"index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/vue-cli-plugin-uni-optimize\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app optimize plugin for vue-cli 3\",\n\"main\":\"index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/vue-cli-plugin-uni\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app plugin for vue-cli 3\",\n\"main\":\"index.js\",\n\"repository\":{\n\n\"author\":\"fxy060608\",\n\"license\":\"Apache-2.0\",\n\"dependencies\":{\n- \"@dcloudio/uni-stat\":\"^2.0.0-alpha-23620191020004\",\n+ \"@dcloudio/uni-stat\":\"^2.0.0-alpha-23720191023001\",\n\"copy-webpack-plugin\":\"^4.6.0\",\n\"cross-env\":\"^5.2.0\",\n\"envinfo\":\"^6.0.1\",\n\n{\n\"name\":\"@dcloudio/webpack-uni-mp-loader\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"webpack-uni-mp-loader\",\n\"main\":\"index.js\",\n\"repository\":{\n\n{\n\"name\":\"@dcloudio/webpack-uni-pages-loader\",\n- \"version\":\"2.0.0-alpha-23620191020004\",\n+ \"version\":\"2.0.0-alpha-23720191023001\",\n\"description\":\"uni-app pages.json loader\",\n\"main\":\"lib/index.js\",\n\"repository\":{\n\n\npeft:1431\tchore(release):publish 2.0.0 - alpha - 23720191023001\n\n\nicl:1431\tUpdate package version\n\n\nslm:chore(release):publish v2.0.0 - alpha -23620191020004\n\n\n", "peft": "1431\tchore(release):publish 2.0.0 - alpha - 23720191023001\n", "icl": "1431\tUpdate package version\n", "slm": "chore(release):publish v2.0.0 - alpha -23620191020004\n", "nl": "chore(release):publish v2.0.0 - alpha - 23720191023001", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16734, "text": "export default()=>{\nasync function ping(){\ntry{\nconst url=`/_next/on-demand-entries-ping?page=${Router.pathname}`\n- const res=await fetch(url)\n+ const res=await fetch(url,{\n+ credentials:'same-origin'\n+ })\nconst payload=await res.json()\nif(payload.invalid){\n//Payload can be invalid even if the page is not exists.\n//So,we need to make sure it's exists before reloading.\n- const pageRes=await fetch(location.href)\n+ const pageRes=await fetch(location.href,{\n+ credentials:'same-origin'\n+ })\nif(pageRes.status===200){\nlocation.reload()\n}\n\n\npeft:117\tFix on-demand-entries-ping request credentials.\n\n\nicl:117\ttests(css):newlines around multiple and combinator selectors()\n\n\nslm:Add credentials param to async/v1 API calls()\n\n\n", "peft": "117\tFix on-demand-entries-ping request credentials.\n", "icl": "117\ttests(css):newlines around multiple and combinator selectors()\n", "slm": "Add credentials param to async/v1 API calls()\n", "nl": "Send credentials with on - demand - entries - ping()", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16735, "text": "class PDFPageProxy{\npageIndex:this._pageIndex,\nintent:renderingIntent,\nrenderInteractiveForms:renderInteractiveForms===true,\n- annotationStorage,\n+ annotationStorage:\n+ (annotationStorage&&annotationStorage.getAll())||null,\n});\n}\n\nclass BaseViewer{\nconst pagesCount=pdfDocument.numPages;\nconst firstPagePromise=pdfDocument.getPage(1);\n\n+ const annotationStorage=pdfDocument.annotationStorage;\n+\nthis._pagesCapability.promise.then(()=>{\nthis.eventBus.dispatch(\"pagesloaded\",{\nsource:this,\nclass BaseViewer{\neventBus:this.eventBus,\nid:pageNum,\nscale,\n+ annotationStorage,\ndefaultViewport:viewport.clone(),\nrenderingQueue:this.renderingQueue,\ntextLayerFactory,\nclass BaseViewer{\ncreateAnnotationLayerBuilder(\npageDiv,\npdfPage,\n+ annotationStorage=null,\nimageResourcesPath=\"\",\nrenderInteractiveForms=false,\nl10n=NullL10n\nclass BaseViewer{\nreturn new AnnotationLayerBuilder({\npageDiv,\npdfPage,\n+ annotationStorage,\nimageResourcesPath,\nrenderInteractiveForms,\nlinkService:this.linkService,\ndownloadManager:this.downloadManager,\n- annotationStorage:this.pdfDocument.annotationStorage,\nl10n,\n});\n}\nfunction composePage(\ntransform:[PRINT_UNITS,0,0,PRINT_UNITS,0,0],\nviewport:pdfPage.getViewport({scale:1,rotation:size.rotation}),\nintent:\"print\",\n- annotationStorage:pdfDocument.annotationStorage.getAll(),\n+ annotationStorage:pdfDocument.annotationStorage,\n};\nreturn pdfPage.render(renderContext).promise;\n})\nclass IPDFAnnotationLayerFactory{\n/* *\n*@param{HTMLDivElement}pageDiv\n*@param{PDFPage}pdfPage\n- *@param{AnnotationStorage}[annotationStorage]\n+ *@param{AnnotationStorage}[annotationStorage]-Storage for annotation\n+ * data in forms.\n*@param{string}[imageResourcesPath]-Path for image resources,mainly\n* for annotation icons.Include trailing slash.\n*@param{boolean}renderInteractiveForms\nimport{viewerCompatibilityParams}from\"./viewer_compatibility.js\";\n*@property{number}id-The page unique ID(normally its number).\n*@property{number}scale-The page scale display.\n*@property{PageViewport}defaultViewport-The page viewport.\n+ *@property{AnnotationStorage}[annotationStorage]-Storage for annotation\n+ * data in forms.The default value is ` null `.\n*@property{PDFRenderingQueue}renderingQueue-The rendering queue object.\n*@property{IPDFTextLayerFactory}textLayerFactory\n*@property{number}[textLayerMode]-Controls if the text layer used for\nclass PDFPageView{\nthis.rotation=0;\nthis.scale=options.scale||DEFAULT_SCALE;\nthis.viewport=defaultViewport;\n+ this._annotationStorage=options.annotationStorage||null;\nthis.pdfPageRotate=defaultViewport.rotation;\nthis.hasRestrictedScaling=false;\nthis.textLayerMode=Number.isInteger(options.textLayerMode)\nclass PDFPageView{\nthis.annotationLayer=this.annotationLayerFactory.createAnnotationLayerBuilder(\ndiv,\npdfPage,\n+ this._annotationStorage,\nthis.imageResourcesPath,\nthis.renderInteractiveForms,\nthis.l10n\nfunction renderPage(\ntransform:[PRINT_UNITS,0,0,PRINT_UNITS,0,0],\nviewport:pdfPage.getViewport({scale:1,rotation:size.rotation}),\nintent:\"print\",\n- annotationStorage:pdfDocument.annotationStorage.getAll(),\n+ annotationStorage:pdfDocument.annotationStorage,\n};\nreturn pdfPage.render(renderContext).promise;\n})\n\n\npeft:569\tFix annotationStorage in print mode(fixes issue 10330)\n\n\nicl:569\tMove`annotationStorage`to the page object.\n\n\nslm:Interactive forms:remove the`annotationStorage`param from the`BaseViewer`\n\n\n", "peft": "569\tFix annotationStorage in print mode(fixes issue 10330)\n", "icl": "569\tMove`annotationStorage`to the page object.\n", "slm": "Interactive forms:remove the`annotationStorage`param from the`BaseViewer`\n", "nl": "Merge pull request from Snuffleupagus/render - annotationStorage", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16736, "text": "define(function(require,exports,module){\n}\n};\n\n+ function createRealDocument(dom){\n+ var document=window.document.implementation.createHTMLDocument();\n+\n+ function addNode(parent,node){\n+ if(node.content){\n+ var textNode=document.createTextNode(node.content);\n+ parent.appendChild(textNode);\n+ }else{\n+ var el;\n+ if(node.tag===\"html\"||node.tag===\"body\"||node.tag===\"head\"){\n+ el=document.querySelector(node.tag);\n+ }else{\n+ el=document.createElement(node.tag);\n+ parent.appendChild(el);\n+ }\n+ el.setAttribute(\"data-brackets-id\",node.tagID);\n+ Object.keys(node.attributes).forEach(function(key){\n+ el.setAttribute(key,node.attributes[key]);\n+ });\n+ node.children.forEach(function(child){\n+ addNode(el,child);\n+ });\n+ }\n+ }\n+ addNode(document.querySelector(\"html\"),dom);\n+ return document;\n+ }\n+\n+ function compareDOMs(previousHTML,newSimple){\n+ var previousRoot=previousHTML.querySelector(\"html\");\n+\n+ function compare(previousEl,newNode){\n+ if(newNode.content){\n+ expect(previousEl.nodeType).toEqual(Node.TEXT_NODE);\n+ expect(previousEl.data).toEqual(newNode.content);\n+ }else{\n+ expect(previousEl.tagName.toLowerCase()).toEqual(newNode.tag.toLowerCase());\n+\n+ if(newNode.tag.toLowerCase()===\"html\"){\n+ newNode.children.forEach(function(child){\n+ if(!child.tag){\n+ return;\n+ }\n+ var childTag=child.tag.toLowerCase();\n+ if(childTag===\"head\"){\n+ compare(previousHTML.querySelector(\"head\"),child);\n+ }else if(childTag===\"body\"){\n+ compare(previousHTML.querySelector(\"body\"),child);\n+ }\n+ });\n+ return;\n+ }\n+\n+ var previousAttributes=previousEl.attributes,\n+ newAttributes=newNode.attributes,\n+ newAttributeNames=Object.keys(newAttributes),\n+ previousAttributeCount=previousAttributes.length;\n+\n+ //The SimpleDOM does not contain data-brackets-id but the browser does,\n+ //so we eliminate any data-brackets-id attributes that have been added\n+ if(previousAttributes.getNamedItem(\"data-brackets-id\")){\n+ previousAttributeCount--;\n+ }\n+ expect(previousAttributeCount).toEqual(newAttributeNames.length);\n+ newAttributeNames.forEach(function(key){\n+ expect(previousAttributes.getNamedItem(key)).toEqual(newAttributes[key]);\n+ });\n+\n+ var previousChildren=previousEl.childNodes,\n+ newChildren=newNode.children;\n+ expect(previousChildren.length).toEqual(newChildren.length);\n+ var i;\n+ for(i=0;i<previousChildren.length;i++){\n+ compare(previousChildren.item(i),newChildren[i]);\n+ }\n+ }\n+ }\n+\n+ compare(previousRoot,newSimple);\n+ }\n+\nfunction init(spec,fileEntry){\nspec.fileContent=null;\n\ndefine(function(require,exports,module){\nvar previousDOM=HTMLInstrumentation._buildSimpleDOM(editor.document.getText()),\nresult;\n\n- var clonedDOM=cloneDOM(previousDOM);\n+ var previousHTMLDocument=createRealDocument(previousDOM);\n\nHTMLInstrumentation._markTextFromDOM(editor,previousDOM);\neditFn(editor,previousDOM);\n\nresult=HTMLInstrumentation._updateDOM(previousDOM,editor,(incremental?changeList:null));\n- var htmlDocument=new FakeDocument(clonedDOM.nodeMap);\n- var editHandler=new RemoteFunctions.DOMEditHandler(htmlDocument);\n+ var editHandler=new RemoteFunctions.DOMEditHandler(previousHTMLDocument);\neditHandler.apply(result.edits);\n- clonedDOM.compare(result.dom);\n+ compareDOMs(previousHTMLDocument,result.dom);\nexpectationFn(result,previousDOM,incremental);\n}\n\n\n\npeft:778\tMerge pull request from adobe/fix - dom - comparison\n\n\nicl:778\t### Answer:\n\n\nslm:Merge pull request from adobe/glenn/html - instrumentation\n\n\n", "peft": "778\tMerge pull request from adobe/fix - dom - comparison\n", "icl": "778\t### Answer:\n", "slm": "Merge pull request from adobe/glenn/html - instrumentation\n", "nl": "This was an attempt to use a real HTML document for comparison.", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16737, "text": "\n# else\n# define NW_VERSION_STRING NW_STRINGIFY(NW_MAJOR_VERSION)\".\"\\\nNW_STRINGIFY(NW_MINOR_VERSION)\".\"\\\n- NW_STRINGIFY(NW_PATCH_VERSION)\"-pre\"\n+ NW_STRINGIFY(NW_PATCH_VERSION)\"-rc1\"\n# endif\n\n# define NW_VERSION\"v\"NW_VERSION_STRING\n\n\npeft:1073\tMerge pull request from nwjs/nw-patch-version-rc1\n\n\nicl:1073\tworkflow with build and lint\n\n\nslm:bump version to 0.15.0 - rc1\n\n\n", "peft": "1073\tMerge pull request from nwjs/nw-patch-version-rc1\n", "icl": "1073\tworkflow with build and lint\n", "slm": "bump version to 0.15.0 - rc1\n", "nl": "bump version to 0.10.0 - rc1", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16738, "text": "class ExtensionManager\n\nconstructor:->\natom.on'window:load',@loadExtensions\n+ atom.on'window:close',@unloadExtensions\n\nloadExtensions:=>\nextension.shutdown()for name,extension of@extensions\nclass ExtensionManager\nconsole.warn\"Extension #{extension::name}failed to startup.\"\nconsole.warn error\n\n+ unloadExtensions:=>\n+ extension.shutdown()for name,extension of@extensions\n\\No newline at end of file\nwindowAdditions=\natom.trigger'window:load'\n\nshutdown:->\n- extension.shutdown()for name,extension of@extensions\n-\nframe=$atomController.window.frame\nx=frame.origin.x\ny=frame.origin.y\nwindowAdditions=\n$atomController.window.title=title\n\nreload:->\n- @shutdown()\n- $atomController.close\n+ @close()\nOSX.NSApp.createController@path\n\nopen:(path)->\n\n\npeft:635\tMerge pull request from atom/close - on - window - close - unload - extensions - on - close\n\n\nicl:635\ttests(js):remove trailing comma\n\n\nslm:Call atom.on'window:close'after unloadExtensions\n\n\n", "peft": "635\tMerge pull request from atom/close - on - window - close - unload - extensions - on - close\n", "icl": "635\ttests(js):remove trailing comma\n", "slm": "Call atom.on'window:close'after unloadExtensions\n", "nl": "unload too", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16739, "text": "var UnexpectedResponseException=sharedUtil.UnexpectedResponseException;\nvar UnknownErrorException=sharedUtil.UnknownErrorException;\nvar Util=sharedUtil.Util;\nvar createPromiseCapability=sharedUtil.createPromiseCapability;\n- var combineUrl=sharedUtil.combineUrl;\nvar error=sharedUtil.error;\nvar deprecated=sharedUtil.deprecated;\nvar getVerbosityLevel=sharedUtil.getVerbosityLevel;\nfunction getDocument(src,pdfDataRangeTransport,\nfor(var key in source){\nif(key==='url'&&typeof window!=='undefined'){\n//The full path is required in the'url'field.\n- params[key]=combineUrl(window.location.href,source[key]);\n+ params[key]=new URL(source[key],window.location).href;\ncontinue;\n}else if(key==='range'){\nrangeTransport=source[key];\nvar PDFWorker=(function PDFWorkerClosure(){\n////to the same origin.\n//if(!isSameOrigin(window.location.href,workerSrc)){\n//workerSrc=createCDNWrapper(\n- //combineUrl(window.location.href,workerSrc));\n+ //new URL(workerSrc,window.location).href);\n//}\n//# endif\n//Some versions of FF can't create a worker on localhost,see:\nvar UNSUPPORTED_FEATURES={\nfont:'font'\n};\n\n- //Combines two URLs.The baseUrl shall be absolute URL.If the url is an\n- //absolute URL,it will be returned as is.\n- function combineUrl(baseUrl,url){\n- if(!url){\n- return baseUrl;\n- }\n- return new URL(url,baseUrl).href;\n- }\n-\n//Checks if URLs have the same origin.For non-HTTP based URLs,returns false.\nfunction isSameOrigin(baseUrl,otherUrl){\ntry{\nexports.arrayByteLength=arrayByteLength;\nexports.arraysToBytes=arraysToBytes;\nexports.assert=assert;\nexports.bytesToString=bytesToString;\n- exports.combineUrl=combineUrl;\nexports.createBlob=createBlob;\nexports.createPromiseCapability=createPromiseCapability;\nexports.createObjectURL=createObjectURL;\nvar Driver=(function DriverClosure(){\n\nthis._log('Loading file\"'+task.file+'\"\\n');\n\n- var absoluteUrl=pdfjsSharedUtil.combineUrl(window.location.href,\n- task.file);\n-\n+ var absoluteUrl=new URL(task.file,window.location).href;\nPDFJS.disableRange=task.disableRange;\nPDFJS.disableAutoFetch=!task.enableAutoFetch;\ntry{\n\n/* globals expect,it,describe,Dict,Name,Annotation,AnnotationBorderStyle,\n- AnnotationBorderStyleType,AnnotationFlag,PDFJS,combineUrl,\n+ AnnotationBorderStyleType,AnnotationFlag,PDFJS,\nbeforeEach,afterEach,stringToBytes */\n\n'use strict';\ndescribe('Annotation layer',function(){\nvar annotations;\n\nbeforeEach(function(done){\n- var pdfUrl=combineUrl(window.location.href,\n- '../pdfs/annotation-fileattachment.pdf');\n+ var pdfUrl=new URL('../pdfs/annotation-fileattachment.pdf',\n+ window.location).href;\nloadingTask=PDFJS.getDocument(pdfUrl);\nloadingTask.promise.then(function(pdfDocument){\nreturn pdfDocument.getPage(1).then(function(pdfPage){\n\n- /* globals expect,it,describe,combineUrl,Dict,isDict,Name,PDFJS,\n+ /* globals expect,it,describe,Dict,isDict,Name,PDFJS,\nstringToPDFString,removeNullCharacters */\n\n'use strict';\n\ndescribe('util',function(){\n- describe('combineUrl',function(){\n- it('absolute url with protocol stays as is',function(){\n- var baseUrl='http://server/index.html';\n- var url='http://server2/test2.html';\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server2/test2.html';\n- expect(result).toEqual(expected);\n- });\n-\n- it('absolute url without protocol uses prefix from base',function(){\n- var baseUrl='http://server/index.html';\n- var url='/test2.html';\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server/test2.html';\n- expect(result).toEqual(expected);\n- });\n-\n- it('combines relative url with base',function(){\n- var baseUrl='http://server/index.html';\n- var url='test2.html';\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server/test2.html';\n- expect(result).toEqual(expected);\n- });\n-\n- it('combines relative url(w/hash)with base',function(){\n- var baseUrl='http://server/index.html #!/test';\n- var url='test2.html';\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server/test2.html';\n- expect(result).toEqual(expected);\n- });\n-\n- it('combines relative url(w/query)with base',function(){\n- var baseUrl='http://server/index.html?search=/test';\n- var url='test2.html';\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server/test2.html';\n- expect(result).toEqual(expected);\n- });\n-\n- it('returns base url when url is empty',function(){\n- var baseUrl='http://server/index.html';\n- var url='';\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server/index.html';\n- expect(result).toEqual(expected);\n- });\n-\n- it('returns base url when url is undefined',function(){\n- var baseUrl='http://server/index.html';\n- var url;\n- var result=combineUrl(baseUrl,url);\n- var expected='http://server/index.html';\n- expect(result).toEqual(expected);\n- });\n- });\n-\ndescribe('isDict',function(){\nit('handles empty dictionaries with type check',function(){\nvar dict=new Dict();\n\n\npeft:1786\tMerge pull request from mozilla/fix - combine - url\n\n\nicl:1786\t# Answer:\n\n\nslm:Merge pull request from Snuffleupagus/master\n\n\n", "peft": "1786\tMerge pull request from mozilla/fix - combine - url\n", "icl": "1786\t# Answer:\n", "slm": "Merge pull request from Snuffleupagus/master\n", "nl": "Remove`combineUrl`and replace it with`new URL`.", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16740, "text": "\nimport com.facebook.react.bridge.CatalystInstance;\nimport com.facebook.react.bridge.JSIModuleType;\nimport com.facebook.react.bridge.ReactContext;\n+ import com.facebook.react.bridge.ReactNoCrashSoftException;\nimport com.facebook.react.bridge.ReactSoftException;\nimport com.facebook.react.bridge.UIManager;\nimport com.facebook.react.uimanager.common.UIManagerType;\nprivate static UIManager getUIManager(\nif(!context.hasCatalystInstance()){\nReactSoftException.logSoftException(\n\"UIManagerHelper\",\n- new IllegalStateException(\n+ new ReactNoCrashSoftException(\n\"Cannot get UIManager because the context doesn't contain a CatalystInstance.\"));\nreturn null;\n}\nprivate static UIManager getUIManager(\nif(!context.hasActiveCatalystInstance()){\nReactSoftException.logSoftException(\n\"UIManagerHelper\",\n- new IllegalStateException(\n+ new ReactNoCrashSoftException(\n\"Cannot get UIManager because the context doesn't contain an active CatalystInstance.\"));\nif(returnNullIfCatalystIsInactive){\nreturn null;\n\nimport com.facebook.react.uimanager.Spacing;\nimport com.facebook.react.uimanager.StateWrapper;\nimport com.facebook.react.uimanager.ThemedReactContext;\n+ import com.facebook.react.uimanager.UIManagerHelper;\nimport com.facebook.react.uimanager.UIManagerModule;\nimport com.facebook.react.uimanager.ViewDefaults;\nimport com.facebook.react.uimanager.ViewProps;\nprivate static void updateStagedInputTypeFlag(\nview.setStagedInputType((view.getStagedInputType()&~flagsToUnset)|flagsToSet);\n}\n\n+ private static EventDispatcher getEventDispatcher(\n+ ReactContext reactContext,ReactEditText editText){\n+ return UIManagerHelper.getEventDispatcherForReactTag(reactContext,editText.getId());\n+ }\n+\nprivate class ReactTextInputTextWatcher implements TextWatcher{\n\nprivate EventDispatcher mEventDispatcher;\nprivate static void updateStagedInputTypeFlag(\n\npublic ReactTextInputTextWatcher(\nfinal ReactContext reactContext,final ReactEditText editText){\n- mEventDispatcher=reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();\n+ mEventDispatcher=getEventDispatcher(reactContext,editText);\nmEditText=editText;\nmPreviousText=null;\n}\nprotected void addEventEmitters(\neditText.setOnFocusChangeListener(\nnew View.OnFocusChangeListener(){\npublic void onFocusChange(View v,boolean hasFocus){\n- EventDispatcher eventDispatcher=\n- reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();\n+ EventDispatcher eventDispatcher=getEventDispatcher(reactContext,editText);\nif(hasFocus){\neventDispatcher.dispatchEvent(new ReactTextInputFocusEvent(editText.getId()));\n}else{\npublic boolean onEditorAction(TextView v,int actionId,KeyEvent keyEvent){\n//*!blurOnSubmit&&!isMultiline=>Prevent default behaviour(return true).\n//Additionally we always generate a ` submit ` event.\n\n- EventDispatcher eventDispatcher=\n- reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();\n-\n+ EventDispatcher eventDispatcher=getEventDispatcher(reactContext,editText);\neventDispatcher.dispatchEvent(\nnew ReactTextInputSubmitEditingEvent(\neditText.getId(),editText.getText().toString()));\npublic ReactSelectionWatcher(ReactEditText editText){\nmReactEditText=editText;\n\nReactContext reactContext=getReactContext(editText);\n- mEventDispatcher=reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();\n+ mEventDispatcher=getEventDispatcher(reactContext,editText);\n}\n\n@Override\npublic void onSelectionChanged(int start,int end){\npublic ReactScrollWatcher(ReactEditText editText){\nmReactEditText=editText;\nReactContext reactContext=getReactContext(editText);\n- mEventDispatcher=reactContext.getNativeModule(UIManagerModule.class).getEventDispatcher();\n+ mEventDispatcher=getEventDispatcher(reactContext,editText);\n}\n\n@Override\n\n\npeft:1010\tFix crash when calling` UIManagerHelper.getEventDispatcherForReactTag ` with a context that doesn't contain a CatalystInstance.\n\n\nicl:1010\teditText);\n\n\nslm:Fix TextInput on Android\n\n\n", "peft": "1010\tFix crash when calling` UIManagerHelper.getEventDispatcherForReactTag ` with a context that doesn't contain a CatalystInstance.\n", "icl": "1010\teditText);\n", "slm": "Fix TextInput on Android\n", "nl": "Migrate ReactTextInputManager to be bridgeless", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16741, "text": "const{\nfixed,\nqueueImageResizing,\ngetImageSize,\n+ getImageSizeAsync,\nstats,\nsetBoundActionCreators,\n}=require(`../`)\ndescribe(` gatsby-plugin-sharp `,()=>{\n\nexpect(result).toMatchSnapshot()\n})\n+\n+ it(` handles padding bytes correctly in async version `,async()=>{\n+ const result=await getImageSizeAsync(\n+ getFileObject(path.join(__dirname,` images/padding-bytes.jpg `))\n+ )\n+\n+ expect(result).toMatchInlineSnapshot(`\n+ Object{\n+ \"hUnits\":\"px\",\n+ \"height\":1000,\n+ \"mime\":\"image/jpeg\",\n+ \"type\":\"jpg\",\n+ \"wUnits\":\"px\",\n+ \"width\":746,\n+ }\n+ `)\n+ })\n})\n\ndescribe(` tracedSVG `,()=>{\nconst duotone=require(`./duotone `)\nconst{IMAGE_PROCESSING_JOB_NAME}=require(`./gatsby-worker `)\n\nconst imageSizeCache=new Map()\n+\n+ const getImageSizeAsync=async file=>{\n+ if(\n+ process.env.NODE_ENV!==` test `&&\n+ imageSizeCache.has(file.internal.contentDigest)\n+ ){\n+ return imageSizeCache.get(file.internal.contentDigest)\n+ }\n+ const input=fs.createReadStream(file.absolutePath)\n+ const dimensions=await imageSize(input)\n+\n+ if(!dimensions){\n+ reportError(\n+ ` gatsby-plugin-sharp couldn't determine dimensions for file:\\n${file.absolutePath}\\nThis file is unusable and is most likely corrupt.`,\n+ ` `\n+ )\n+ }\n+\n+ imageSizeCache.set(file.internal.contentDigest,dimensions)\n+ return dimensions\n+ }\n+ //Remove in next major as it's really slow\nconst getImageSize=file=>{\nif(\nprocess.env.NODE_ENV!==` test `&&\nconst getImageSize=file=>{\n){\nreturn imageSizeCache.get(file.internal.contentDigest)\n}else{\n- const dimensions=imageSize.sync(\n- toArray(fs.readFileSync(file.absolutePath))\n- )\n+ const dimensions=imageSize.sync(fs.readFileSync(file.absolutePath))\n\nif(!dimensions){\nreportError(\nasync function fixed({file,args={},reporter,cache}){\nsizes.push(options[fixedDimension])\nsizes.push(options[fixedDimension]* 1.5)\nsizes.push(options[fixedDimension]* 2)\n- const dimensions=getImageSize(file)\n+ const dimensions=await getImageSizeAsync(file)\n\nconst filteredSizes=sizes.filter(size=>size<=dimensions[fixedDimension])\n\nasync function fixed({file,args={},reporter,cache}){\n}\n}\n\n- function toArray(buf){\n- var arr=new Array(buf.length)\n-\n- for(var i=0;i<buf.length;i++){\n- arr[i]=buf[i]\n- }\n-\n- return arr\n- }\n-\nexports.queueImageResizing=queueImageResizing\nexports.resize=queueImageResizing\nexports.base64=base64\nexports.resolutions=fixed\nexports.fluid=fluid\nexports.fixed=fixed\nexports.getImageSize=getImageSize\n+ exports.getImageSizeAsync=getImageSizeAsync\nexports.stats=stats\n\n\npeft:104\tFixes gatsby-plugin-sharp not working in tests due to image-size not supporting streams.\n\n\nicl:104\tAdd LESS support()\n\n\nslm:feat(gatsby - plugin - sharp):Support padding bytes in async version()\n\n\n", "peft": "104\tFixes gatsby-plugin-sharp not working in tests due to image-size not supporting streams.\n", "icl": "104\tAdd LESS support()\n", "slm": "feat(gatsby - plugin - sharp):Support padding bytes in async version()\n", "nl": "perf(gatsby - plugin - image):Optimise image size functions()", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16742, "text": "test:\n- ./gradlew:ReactAndroid:connectedAndroidTest-PdisablePreDex--stacktrace--info:\ntimeout:360\n\n+ # Publish to Sinopia,create a new app using'react-native init'and check the packager starts\n+ -./scripts/e2e-test.sh--packager\n+\n# testing docs generation is not broken\n- cd website&&node./server/generate.js\npost:\n\n\npeft:1803\tPublish to Sinopia,create a new app using'react-native init'and check the packager starts\n\n\nicl:1803\tMerge pull request from github/test-workflow-with-build-and-lint\n\n\nslm:publish to saopia, create a new app using'react - native init'and check the packager starts\n\n\n", "peft": "1803\tPublish to Sinopia,create a new app using'react-native init'and check the packager starts\n", "icl": "1803\tMerge pull request from github/test-workflow-with-build-and-lint\n", "slm": "publish to saopia, create a new app using'react - native init'and check the packager starts\n", "nl": "Run an e2e packager test on CircleCI", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16743, "text": "let Dialog=React.createClass({\nlet maxDialogContentHeight=clientHeight-2 *(styles.body.padding+64);\n\nif(this.props.title)maxDialogContentHeight-=dialogContent.previousSibling.offsetHeight;\n- if(this.props.actions)maxDialogContentHeight-=dialogContent.nextSibling.offsetHeight;\n+ if(this.props.actions.length)maxDialogContentHeight-=dialogContent.nextSibling.offsetHeight;\n\ndialogContent.style.maxHeight=maxDialogContentHeight+'px';\n}\n\n\npeft:1266\tFixes a bug where the dialog actions were not being hidden when there were no actions.\n\n\nicl:1266\ttests(css):newlines around multiple and combinator selectors()\n\n\nslm:Merge pull request from spicyj/actions - length\n\n\n", "peft": "1266\tFixes a bug where the dialog actions were not being hidden when there were no actions.\n", "icl": "1266\ttests(css):newlines around multiple and combinator selectors()\n", "slm": "Merge pull request from spicyj/actions - length\n", "nl": "Fixed bug in Dialog", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16744, "text": "public synchronized void manageChildren(\n}\n}\n\n+ if(tagsToDelete!=null){\n+ for(int i=0;i<tagsToDelete.length;i++){\n+ int tagToDelete=tagsToDelete[i];\n+ final int indexToDelete=indicesToDelete[i];\n+ final View viewToDestroy=mTagsToViews.get(tagToDelete);\n+ if(viewToDestroy==null){\n+ throw new IllegalViewOperationException(\n+ \"Trying to destroy unknown view tag:\"\n+ +tagToDelete+\"\\n detail:\"+\n+ constructManageChildrenErrorMessage(\n+ viewToManage,\n+ viewManager,\n+ indicesToRemove,\n+ viewsToAdd,\n+ tagsToDelete));\n+ }\n+\n+ if(mLayoutAnimationEnabled&&\n+ mLayoutAnimator.shouldAnimateLayout(viewToDestroy)){\n+ int updatedCount=pendingIndicesToDelete.get(indexToDelete,0)+1;\n+ pendingIndicesToDelete.put(indexToDelete,updatedCount);\n+ mLayoutAnimator.deleteView(\n+ viewToDestroy,\n+ new LayoutAnimationListener(){\n+ @Override\n+ public void onAnimationEnd(){\n+ viewManager.removeView(viewToManage,viewToDestroy);\n+ dropView(viewToDestroy);\n+\n+ int count=pendingIndicesToDelete.get(indexToDelete,0);\n+ pendingIndicesToDelete.put(indexToDelete,Math.max(0,count-1));\n+ }\n+ });\n+ }else{\n+ dropView(viewToDestroy);\n+ }\n+ }\n+ }\n+\nif(viewsToAdd!=null){\nfor(int i=0;i<viewsToAdd.length;i++){\nViewAtIndex viewAtIndex=viewsToAdd[i];\npublic synchronized void manageChildren(\nviewManager.addView(viewToManage,viewToAdd,normalizedIndexToAdd);\n}\n}\n-\n- if(tagsToDelete!=null){\n- for(int i=0;i<tagsToDelete.length;i++){\n- int tagToDelete=tagsToDelete[i];\n- final int indexToDelete=indicesToDelete[i];\n- final View viewToDestroy=mTagsToViews.get(tagToDelete);\n- if(viewToDestroy==null){\n- throw new IllegalViewOperationException(\n- \"Trying to destroy unknown view tag:\"\n- +tagToDelete+\"\\n detail:\"+\n- constructManageChildrenErrorMessage(\n- viewToManage,\n- viewManager,\n- indicesToRemove,\n- viewsToAdd,\n- tagsToDelete));\n- }\n-\n- if(mLayoutAnimationEnabled&&\n- mLayoutAnimator.shouldAnimateLayout(viewToDestroy)){\n- int updatedCount=pendingIndicesToDelete.get(indexToDelete,0)+1;\n- pendingIndicesToDelete.put(indexToDelete,updatedCount);\n- mLayoutAnimator.deleteView(\n- viewToDestroy,\n- new LayoutAnimationListener(){\n- @Override\n- public void onAnimationEnd(){\n- viewManager.removeView(viewToManage,viewToDestroy);\n- dropView(viewToDestroy);\n-\n- int count=pendingIndicesToDelete.get(indexToDelete,0);\n- pendingIndicesToDelete.put(indexToDelete,Math.max(0,count-1));\n- }\n- });\n- }else{\n- dropView(viewToDestroy);\n- }\n- }\n- }\n}\n\nprivate boolean arrayContains(@Nullable int[]array,int ele){\n\n\npeft:1584\tFixes bug where views were not being removed from the view hierarchy when they were deleted from the native view hierarchy.\n\n\nicl:1584\ttests(android):add child view to a view group\n\n\nslm:Refactor manageChildren logic\n\n\n", "peft": "1584\tFixes bug where views were not being removed from the view hierarchy when they were deleted from the native view hierarchy.\n", "icl": "1584\ttests(android):add child view to a view group\n", "slm": "Refactor manageChildren logic\n", "nl": "Rearrange order of manageChildren", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16745, "text": "function run(queryString){\n}\n\nfunction runInPhantom(url,retries,resolve,reject){\n- var args=['bower_components/qunit-phantom-runner/runner.js',url];\n+ var args=[require.resolve('qunit-phantomjs-runner'),url,'900'];\n\nconsole.log('Running:phantomjs'+args.join(''));\n\n\n},\n\"dependencies\":{\n\"jquery\":\">=1.11.1<4.0.0\",\n- \"qunit\":\"^1.20.0\",\n- \"qunit-phantom-runner\":\"jonkemp/qunit-phantomjs-runner # 1.2.0\"\n+ \"qunit\":\"^1.20.0\"\n},\n\"devDependencies\":{\n\"ember-dev\":\"https://github.com/emberjs/ember-dev.git # eace5340485bdb5e4223ab67fecf4aff31c1940c\",\n\n\"html-differ\":\"^1.3.4\",\n\"mocha\":\"^2.4.5\",\n\"qunit-extras\":\"^1.5.0\",\n+ \"qunit-phantomjs-runner\":\"^2.2.0\",\n\"qunitjs\":\"^1.22.0\",\n\"route-recognizer\":\"^0.2.8\",\n\"serve-static\":\"^1.10.0\",\nqunit-extras@^1.5.0:\nversion\"1.5.0\"\nresolved\"https://registry.yarnpkg.com/qunit-extras/-/qunit-extras-1.5.0.tgz # a64d1c5088ab20c01c0e1b04c72132c397b3964c\"\n\n+ qunit-phantomjs-runner:\n+ version\"2.2.0\"\n+ resolved\"https://registry.yarnpkg.com/qunit-phantomjs-runner/-/qunit-phantomjs-runner-2.2.0.tgz # 557a0f55d7d83c315312d1b7048ed972ffea4549\"\n+\nqunit@^0.7.2:\nversion\"0.7.7\"\nresolved\"https://registry.yarnpkg.com/qunit/-/qunit-0.7.7.tgz # f0006f60b31a91c41e58aba92147576879b425bd\"\n\n\npeft:1887\tUpdate qunit-phantomjs-runner to 2.2.0\n\n\nicl:1887\t- \"qunit\":\"^1.20.0\",\n\n\nslm:Ensure qunit - phantomjs - runner is resolved via require.resolve.\n\n\n", "peft": "1887\tUpdate qunit-phantomjs-runner to 2.2.0\n", "icl": "1887\t- \"qunit\":\"^1.20.0\",\n", "slm": "Ensure qunit - phantomjs - runner is resolved via require.resolve.\n", "nl": "Use npm for qunit - phantomjs - runner", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16746, "text": "console.log(JSON.stringify({\n\"jsdom\":\"~0.5.2\"\n},\n\"devDependencies\":{\n- \"smash\":\"~0.0.3\",\n+ \"smash\":\"~0.0.4\",\n\"uglify-js\":\"2.2.x\",\n\"vows\":\"0.7.x\"\n},\n\n\npeft:957\tUpdate smash to 0.0.4\n\n\nicl:957\ttests(css):newlines around multiple and combinator selectors()\n\n\nslm:Update smash\n\n\n", "peft": "957\tUpdate smash to 0.0.4\n", "icl": "957\ttests(css):newlines around multiple and combinator selectors()\n", "slm": "Update smash\n", "nl": "Upgrade smash.", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16747, "text": "var getLoadedPackages=_.once(function(){\n//Opens a DDP connection to a package server.Loads the packages needed for a\n//DDP connection,then calls DDP connect to the package server URL in config,\n//using a current user-agent header composed by http-helpers.js.\n- var openPackageServerConnection=function(){\n+ var openPackageServerConnection=function(packageServerUrl){\nreturn new ServiceConnection(\ngetLoadedPackages(),\n- config.getPackageServerUrl(),\n+ packageServerUrl||config.getPackageServerUrl(),\n{headers:{\"User-Agent\":httpHelpers.getUserAgent()},\n_dontPrintErrors:true});\n};\nexports.loadCachedServerData=function(packageStorageFile){\npackageStorageFile=packageStorageFile||config.getPackageStorage();\n\ntry{\n- var data=fs.readFileSync(config.getPackageStorage(),'utf8');\n+ var data=fs.readFileSync(packageStorageFile,'utf8');\n}catch(e){\nif(e.code=='ENOENT'){\nreturn noDataToken;\nvar writePackageDataToDisk=function(syncToken,data,options){\n//all the data.\n//\n//options can include:\n- //-packageStorageFile:String.The file to write the data to(overrides\n- //` config.getPackageStorage()`)\n+ //-packageStorageFile:String.The file to write the data to(overrides\n+ //` config.getPackageStorage()`)\n+ //-packageServerUrl:String.The package server(overrides\n+ //` config.getPackageServerUrl()`)\n//-useShortPages:Boolean.Request short pages of~3 records from the\n//server,instead of~100 that it would send otherwise\nexports.updateServerPackageData=function(cachedServerData,options){\nexports.updateServerPackageData=function(cachedServerData,options){\nvar done=false;\nvar ret={resetData:false};\n\n- var conn=openPackageServerConnection();\n+ var conn=openPackageServerConnection(options.packageServerUrl);\n\nvar getSomeData=function(){\nvar syncToken=cachedServerData.syncToken;\nvar Sandbox=function(options){\n//with.If a test is tagged'test-package-server',it uses the test\n//server.Tests that publish packages should have this flag;tests that\n//assume that the release's packages can be found on the server should not.\n+ //Note that this only affects subprocess meteor runs,not direct invocation\n+ //of packageClient!\nif(runningTest.tags['test-package-server']){\n- self.set('METEOR_PACKAGE_SERVER_URL','https://test-packages.meteor.com');\n+ self.set('METEOR_PACKAGE_SERVER_URL',exports.testPackageServerUrl);\n}\n\nif(_.has(options,'warehouse')){\n_.extend(exports,{\nexpectThrows:expectThrows,\ngetToolsPackage:getToolsPackage,\nexecFileSync:execFileSync,\n- captureAndThrow:captureAndThrow\n+ captureAndThrow:captureAndThrow,\n+ testPackageServerUrl:'https://test-packages.meteor.com'\n});\nselftest.define(\"update server package data unit test\",\n//we'll check that all this data still appears on disk and hasn't\n//been overwritten.\nvar data=packageClient.updateServerPackageData({syncToken:{}},{\n- packageStorageFile:packageStorageFile\n+ packageStorageFile:packageStorageFile,\n+ packageServerUrl:selftest.testPackageServerUrl\n}).data;\n\nvar packageNames=[];\n\n- //Publish more than a page worth of packages.When we pass the\n- //` useShortPages ` options,the server will send 3 records at a time\n- //instead of 100.\n+ //Publish more than a(small)page worth of packages.When we pass the\n+ //` useShortPages ` option to updateServerPackageData,the server will send 3\n+ //records at a time instead of 100,so this is more than a page.\n_.times(5,function(i){\nvar packageName=username+\":\"+utils.randomToken();\ncreateAndPublishPackage(s,packageName);\nselftest.define(\"update server package data unit test\",\n\nvar newData=packageClient.updateServerPackageData(data,{\npackageStorageFile:packageStorageFile,\n+ packageServerUrl:selftest.testPackageServerUrl,\nuseShortPages:true\n}).data;\nvar newOnDiskData=packageClient.loadCachedServerData(packageStorageFile);\n\n\npeft:92\tAdd test - package - server - url to packageClient.js\n\n\nicl:92\t+i;\n\n\nslm:Set Meteor package server url to test - packages.meteor.com\n\n\n", "peft": "92\tAdd test - package - server - url to packageClient.js\n", "icl": "92\t+i;\n", "slm": "Set Meteor package server url to test - packages.meteor.com\n", "nl": "fix'update server package data unit test'", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16748, "text": "vAPI.net.registerListeners=function(){\n//https://github.com/gorhill/uBlock/issues/697\n//Dismiss event if the associated tab is pending.\nvar tab=tabWatcher.tabFromBrowser(browser);\n- if(!tab||tab.hasAttribute('pending')){\n+ if(!vAPI.fennec&&tab&&tab.hasAttribute('pending')){\nreturn;\n}\n\n\n\npeft:105\tFix regression introduced in 1.10.1.\n\n\nicl:105\ttests(css):fix regression where the DOM is not updated when an element is removed from the page\n\n\nslm:Merge pull request from Sing - Li/master\n\n\n", "peft": "105\tFix regression introduced in 1.10.1.\n", "icl": "105\ttests(css):fix regression where the DOM is not updated when an element is removed from the page\n", "slm": "Merge pull request from Sing - Li/master\n", "nl": ":mind Firefox for Android", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16749, "text": "\n- <h3 id=\"example-1\">Example 1</h3>\n-\n<p><a href=\"/url\">the<code>]</code>character</a></p>\n\n- <h3 id=\"example-2\">Example 2</h3>\n-\n<p><a href=\"/url\">the ` character</a></p>\n-\n- <h3 id=\"example-3\">Example 3</h3>\n-\n- <p>[\n- [\n- <a href=\"b\">a</a><a href=\"d\">c</a><a href=\"f\">e</a>\n- ](x)\n- <a href=\"h\">g</a>\n- [\n- <a href=\"j\">i</a><a href=\"l\">k</a>[m<a href=\"o\">n</a>](p)\n- ](w)\n- ](z)</p>\n-\n- <h3 id=\"example-4\">Example 4</h3>\n-\n- <p>[nested:<a href=\"/href\"title=\"description\">ref</a>](/foo)\n-\n- [nested:<a href=\"/bar\">foo</a>][ref]</p>\n-\n- <p>[nested:<a href=\"/href2\">ref2</a>][ref]</p>\n\\No newline at end of file\n\n- # # # Example 1\n-\n[the `]` character](/url)\n\n- # # # Example 2\n-\n[the ` character](/url)\n-\n- # # # Example 3\n-\n- [\n- [\n- [a](b)[c](d)[e](f)\n- ](x)\n- [g](h)\n- [\n- [i](j)[k](l)[m[n](o)](p)\n- ](w)\n- ](z)\n-\n- # # # Example 4\n-\n- [nested:[ref]](/foo)\n- [nested:[foo](/bar)][ref]\n-\n- [nested:[ref2]][ref]\n-\n-\n- [ref]:/href\"description\"\n- [ref2]:/href2\n\\No newline at end of file\n\n\npeft:939\tFixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:\n\n\nicl:939\tfix markdown links\n\n\nslm:Merge pull request from kriadmin/master\n\n\n", "peft": "939\tFixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:Fixes # 1028:\n", "icl": "939\tfix markdown links\n", "slm": "Merge pull request from kriadmin/master\n", "nl": "Revert 13dd38a\"add tests for link nesting\"", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16750, "text": "\n\"welcome\":\"0.19.0\",\n\"whitespace\":\"0.25.0\",\n\"wrap-guide\":\"0.23.0\",\n- \"language-c\":\"0.28.0\",\n+ \"language-c\":\"0.29.0\",\n\"language-coffee-script\":\"0.36.0\",\n\"language-css\":\"0.21.0\",\n\"language-gfm\":\"0.52.0\",\n\n\npeft:1722\tUpdate language - c to 0.29.0\n\n\nicl:1722\tUpdate language-c to 0.29.0\n\n\nslm:Upgrade to language - c@0.29\n\n\n", "peft": "1722\tUpdate language - c to 0.29.0\n", "icl": "1722\tUpdate language-c to 0.29.0\n", "slm": "Upgrade to language - c@0.29\n", "nl": "Upgrade to language - c@0.29", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16751, "text": "class Carousel{\n}\n}\n\n- [].slice.call(SelectorEngine.find(Selector.ITEM_IMG,this._element)).forEach((itemImg)=>{\n+ Util.makeArray(SelectorEngine.find(Selector.ITEM_IMG,this._element)).forEach((itemImg)=>{\nEventHandler.on(itemImg,Event.DRAG_START,(e)=>e.preventDefault())\n})\n\nclass Carousel{\n\n_getItemIndex(element){\nthis._items=element&&element.parentNode\n- ?[].slice.call(SelectorEngine.find(Selector.ITEM,element.parentNode))\n+ ?Util.makeArray(SelectorEngine.find(Selector.ITEM,element.parentNode))\n:[]\n\nreturn this._items.indexOf(element)\n\n- import Util from'../util'\n-\n/* *\n* mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n* Bootstrap(v4.3.1):dom/polyfill.js\nimport Util from'../util'\n* mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n*/\n\n+ import Util from'../util'\n+\n/* istanbul ignore next */\nconst Polyfill=(()=>{\n//MSEdge resets defaultPrevented flag upon dispatchEvent call if at least one listener is attached\nclass Dropdown{\nreturn\n}\n\n- const items=[].slice.call(parent.querySelectorAll(Selector.VISIBLE_ITEMS))\n+ const items=Util.makeArray(parent.querySelectorAll(Selector.VISIBLE_ITEMS))\n\nif(!items.length){\nreturn\n\n+ /* *\n+ * mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n+ * Bootstrap(v4.3.1):index.js\n+ * Licensed under MIT(https://github.com/twbs/bootstrap/blob/master/LICENSE)\n+ * mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n+ */\n+\nimport Alert from'./alert'\nimport Button from'./button'\nimport Carousel from'./carousel'\nimport Toast from'./toast'\nimport Tooltip from'./tooltip'\nimport Util from'./util'\n\n- /* *\n- * mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n- * Bootstrap(v4.3.1):index.js\n- * Licensed under MIT(https://github.com/twbs/bootstrap/blob/master/LICENSE)\n- * mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n- */\n-\nexport{\nUtil,\nAlert,\n\n* mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n*/\n\n+ import Util from'../util'\n+\nconst uriAttrs=[\n'background',\n'cite',\nexport function sanitizeHtml(unsafeHtml,whiteList,sanitizeFn){\nconst domParser=new window.DOMParser()\nconst createdDocument=domParser.parseFromString(unsafeHtml,'text/html')\nconst whitelistKeys=Object.keys(whiteList)\n- const elements=[].slice.call(createdDocument.body.querySelectorAll('*'))\n+ const elements=Util.makeArray(createdDocument.body.querySelectorAll('*'))\n\nfor(let i=0,len=elements.length;i<len;i++){\nconst el=elements[i]\nexport function sanitizeHtml(unsafeHtml,whiteList,sanitizeFn){\ncontinue\n}\n\n- const attributeList=[].slice.call(el.attributes)\n+ const attributeList=Util.makeArray(el.attributes)\nconst whitelistedAttributes=[].concat(whiteList['*']||[],whiteList[elName]||[])\n\nattributeList.forEach((attr)=>{\n\n\npeft:1612\tfix(polyfill):use Util.makeArray()\n\n\nicl:1612\t- const domParser=new window.DOMParser()\n\n\nslm:carousel:add support for tooltips on divs, add support for tooltips on divs + remove imgs\n\n\n", "peft": "1612\tfix(polyfill):use Util.makeArray()\n", "icl": "1612\t- const domParser=new window.DOMParser()\n", "slm": "carousel:add support for tooltips on divs, add support for tooltips on divs + remove imgs\n", "nl": "Use`Util.makeArray()`.", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16752, "text": "All templates,css,and content are * hot reloadable *.\n*[nordnet.se/brand](https://www.nordnet.se/brand/)\n*[likescoffee.com](http://likescoffee.com/)([source](https://github.com/pamo/pamo.github.io/tree/development))\n*[vii.campjs.com](http://vii.campjs.com/)([source](https://github.com/campjs/campjs-vii))\n+ *[brittcrawford.com](http://brittcrawford.com)\n*[Edit this file to add yours!](https://github.com/gatsbyjs/gatsby/blob/master/README.md)\n\n# # # Why use Gatsby instead of other Static Site Generators\n\n\npeft:1824\tAdded brittcrawford.com to the list of sites using Gatsby.\n\n\nicl:1824\tAdd brittcrawford.com to the list of sites\n\n\nslm:Add brittcrawford.com to list of sites\n\n\n", "peft": "1824\tAdded brittcrawford.com to the list of sites using Gatsby.\n", "icl": "1824\tAdd brittcrawford.com to the list of sites\n", "slm": "Add brittcrawford.com to list of sites\n", "nl": "Add brittcrawford.com to list of sites", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16753, "text": "tags:array,intermediate\n\nReturns the difference between two arrays,after applying the provided function to each array element of both.\n\n- -Create a ` Set ` by applying ` fn ` to each element in ` b `,then use ` Array.prototype.map()` to apply ` fn ` to each element in ` a `,then ` Array.prototype.filter()`\n+ -Create a ` Set ` by applying ` fn ` to each element in ` b `.\n+ -Use ` Array.prototype.map()` to apply ` fn ` to each element in ` a `.\n+ -Use ` Array.prototype.filter()` in combination with ` fn ` on ` a ` to only keep values not contained in ` b `,using ` Set.prototype.has()`.\n\n` ` ` js\nconst differenceBy=(a,b,fn)=>{\n\n\npeft:1890\tMerge pull request from jaredpalmer/fix - differenceBy - test - case - 2\n\n\nicl:1890\tfeat(www):add type tabs to code snippets()\n\n\nslm:Update differenceBy.md\n\n\n", "peft": "1890\tMerge pull request from jaredpalmer/fix - differenceBy - test - case - 2\n", "icl": "1890\tfeat(www):add type tabs to code snippets()\n", "slm": "Update differenceBy.md\n", "nl": "Update differenceBy", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16754, "text": "\n\nguard:shell do\nwatch(/^trials\\/(.*)_trial\\.rb/){|m|` bin/catechism run_trial #{m[0]}`}\n+ watch(/^(lib\\/.*)\\.rb/){|m|` bin/catechism run_trial trials/#{m[1]}_trial.rb `}\nend\n\n\npeft:206\tAdd watcher for lib/ files.\n\n\nicl:206\ttests(ruby):newlines around multiple and\n\n\nslm:Add trials to gitignore\n\n\n", "peft": "206\tAdd watcher for lib/ files.\n", "icl": "206\ttests(ruby):newlines around multiple and\n", "slm": "Add trials to gitignore\n", "nl": "Add individual test runs for files in lib", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16755, "text": "\nmodule.exports=function(app){\napp.get('/',function(req,res){\n- res.redirect('/tests/index.html');\n+ res.redirect('/tests/index.html?hidepassed');\n});\n};\n\n\n//Tests should time out after 5 seconds\nQUnit.config.testTimeout=7500;\n- //Hide passed tests by default\n- QUnit.config.hidepassed=true;\n//Handle testing feature flags\nQUnit.config.urlConfig.push({id:'enableoptionalfeatures',label:\"Enable Opt Features\"});\n//Handle extending prototypes\n\n\npeft:1342\tMerge pull request from emberjs/hide - passed - tests - by - default\n\n\nicl:1342\t[tests]Add a new test\n\n\nslm:Fix;redirect to/tests/index.html\n\n\n", "peft": "1342\tMerge pull request from emberjs/hide - passed - tests - by - default\n", "icl": "1342\t[tests]Add a new test\n", "slm": "Fix;redirect to/tests/index.html\n", "nl": "Allow`hidepassed`to be toggled.", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16756, "text": "new file mode 100644\nindex **********00..06f46bf00e4a\nmmm/dev/null\n\n+ //Copyright 2004-present Facebook.All Rights Reserved.\n+\n+ package com.facebook.react.views.art;\n+\n+ import com.facebook.react.module.annotations.ReactModule;\n+\n+ /* *\n+ * ViewManager for shadowed ART group views.\n+ */\n+ @ReactModule(name=ARTRenderableViewManager.CLASS_GROUP)\n+ public class ARTGroupViewManager extends ARTRenderableViewManager{\n+\n+ /* package */ARTGroupViewManager(){\n+ super(CLASS_GROUP);\n+ }\n+ }\n\n\nimport android.view.View;\n\n- import com.facebook.react.uimanager.ReactStylesDiffMap;\nimport com.facebook.react.uimanager.ReactShadowNode;\nimport com.facebook.react.uimanager.ThemedReactContext;\nimport com.facebook.react.uimanager.ViewManager;\n\nprivate final String mClassName;\n\npublic static ARTRenderableViewManager createARTGroupViewManager(){\n- return new ARTRenderableViewManager(CLASS_GROUP);\n+ return new ARTGroupViewManager();\n}\n\npublic static ARTRenderableViewManager createARTShapeViewManager(){\n- return new ARTRenderableViewManager(CLASS_SHAPE);\n+ return new ARTShapeViewManager();\n}\n\npublic static ARTRenderableViewManager createARTTextViewManager(){\n- return new ARTRenderableViewManager(CLASS_TEXT);\n+ return new ARTTextViewManager();\n}\n\n- private ARTRenderableViewManager(String className){\n+ /* package */ARTRenderableViewManager(String className){\nmClassName=className;\n}\n\nnew file mode 100644\nindex **********00..a6343046d23d\nmmm/dev/null\n\n+ //Copyright 2004-present Facebook.All Rights Reserved.\n+\n+ package com.facebook.react.views.art;\n+\n+ import com.facebook.react.module.annotations.ReactModule;\n+\n+ /* *\n+ * ViewManager for shadowed ART shape views.\n+ */\n+ @ReactModule(name=ARTRenderableViewManager.CLASS_SHAPE)\n+ public class ARTShapeViewManager extends ARTRenderableViewManager{\n+\n+ /* package */ARTShapeViewManager(){\n+ super(CLASS_SHAPE);\n+ }\n+ }\nnew file mode 100644\nindex **********00..44d6245df922\nmmm/dev/null\n\n+ //Copyright 2004-present Facebook.All Rights Reserved.\n+\n+ package com.facebook.react.views.art;\n+\n+ import com.facebook.react.module.annotations.ReactModule;\n+\n+ /* *\n+ * ViewManager for shadowed ART text views.\n+ */\n+ @ReactModule(name=ARTRenderableViewManager.CLASS_TEXT)\n+ public class ARTTextViewManager extends ARTRenderableViewManager{\n+\n+ /* package */ARTTextViewManager(){\n+ super(CLASS_TEXT);\n+ }\n+ }\n\n\npeft:217\tAdd ARTGroupViewManager,ARTShapeViewManager,ARTTextViewManager\n\n\nicl:217\tThe diff hunk is for a new class, but the diff\n\n\nslm:Android:Add ARTGroupViewManager annotation\n\n\n", "peft": "217\tAdd ARTGroupViewManager,ARTShapeViewManager,ARTTextViewManager\n", "icl": "217\tThe diff hunk is for a new class, but the diff\n", "slm": "Android:Add ARTGroupViewManager annotation\n", "nl": "create ART * ViewManager classes", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16757, "text": "\n\"proxyquire\":\"2.1.0\",\n\"shelljs\":\"0.8.3\",\n\"sinon\":\"7.2.2\",\n- \"snap-shot-it\":\"7.9.0\",\n+ \"snap-shot-it\":\"7.9.1\",\n\"spawn-mock\":\"1.0.0\",\n\"strip-ansi\":\"4.0.0\"\n},\n\n\"shelljs\":\"0.8.3\",\n\"shx\":\"0.3.2\",\n\"sinon\":\"7.3.2\",\n- \"snap-shot-it\":\"7.9.0\",\n+ \"snap-shot-it\":\"7.9.1\",\n\"stop-only\":\"3.0.1\",\n\"strip-ansi\":\"4.0.0\",\n\"terminal-banner\":\"1.1.0\",\n\n\"proxyquire\":\"2.1.0\",\n\"react\":\"15.6.2\",\n\"repl.history\":\"0.1.4\",\n- \"snap-shot-it\":\"7.9.0\",\n+ \"snap-shot-it\":\"7.9.1\",\n\"ssestream\":\"1.0.1\",\n\"stream-to-promise\":\"1.1.1\",\n\"supertest\":\"4.0.2\",\n\n\npeft:514\tUpdate snap - shot - it to v7.9.1\n\n\nicl:514\ttests(js):updated snap-shot-it to 7.9.1\n\n\nslm:chore(deps):update dependency snap - it to version 7.9.1 🌟\n\n\n", "peft": "514\tUpdate snap - shot - it to v7.9.1\n", "icl": "514\ttests(js):updated snap-shot-it to 7.9.1\n", "slm": "chore(deps):update dependency snap - it to version 7.9.1 🌟\n", "nl": "chore(deps):update snap - shot - it to 7.9.1 🌟()", "lang": "javascript", "label": ["Doc-mod"], "Comments": []}
{"id": 16758, "text": "const Select=React.createClass({\nthis.focusPageDownOption();\nbreak;\ncase 35://end key\n+ if(event.shiftKey){\n+ return;\n+ }\nthis.focusEndOption();\nbreak;\ncase 36://home key\n+ if(event.shiftKey){\n+ return;\n+ }\nthis.focusStartOption();\nbreak;\ndefault:return;\n\n\npeft:1710\tFixes issue where shift+page up/down would not work.\n\n\nicl:1710\ttests(css):keypress on search input()\n\n\nslm:Prevent focusing when key pressed on escape\n\n\n", "peft": "1710\tFixes issue where shift+page up/down would not work.\n", "icl": "1710\ttests(css):keypress on search input()\n", "slm": "Prevent focusing when key pressed on escape\n", "nl": "Merge pull request from piecyk/add - select - all - text", "lang": "javascript", "label": ["Feat-mod"], "Comments": []}
{"id": 16759, "text": "\n\"devDependencies\":{\n\"after\":\"0.8.1\",\n\"ejs\":\"2.3.3\",\n- \"istanbul\":\"0.3.9\",\n+ \"istanbul\":\"0.3.17\",\n\"marked\":\"0.3.5\",\n\"mocha\":\"2.2.5\",\n\"should\":\"7.0.2\",\n\n\npeft:160\tUpdate istanbul to 0.3.17\n\n\nicl:160\tupgrade to latest istanbul\n\n\nslm:deps:istanbul@0.3.17\n\n\n", "peft": "160\tUpdate istanbul to 0.3.17\n", "icl": "160\tupgrade to latest istanbul\n", "slm": "deps:istanbul@0.3.17\n", "nl": "build:istanbul@0.3.17", "lang": "javascript", "label": ["Ref-con"], "Comments": []}
{"id": 16760, "text": "define(function(require,exports,module){\n}else{\n$(\"body\").addClass(\"in-appshell\");\n}\n+\n+ //Enable/Disable HTML Menus\n+ if(brackets.platform!==\"linux\"){\n+ $(\"body\").addClass(\"has-appshell-menus\");\n+ }\n\n//Localize MainViewHTML and inject into<BODY>tag\n$(\"body\").html(Mustache.render(MainViewHTML,Strings));\ndefine(function(require,exports,module){\n}\n\nfunction_isHTMLMenu(id){\n- return(brackets.inBrowser||_isContextMenu(id));\n+ return(!$(\"body\").hasClass(\"has-appshell-menus\")||brackets.inBrowser)||_isContextMenu(id);\n}\n\n/* *\n\n*/\n# titlebar{\n//Visible only in-browser\n- body.in-appshell&{\n+ body.has-appshell-menus&{\ndisplay:none;\n}\n\n\n\npeft:1827\tMerge pull request from adobe/master\n\n\nicl:1827\tadd menu\n\n\nslm:Enable/disable HTML Menus on Linux\n\n\n", "peft": "1827\tMerge pull request from adobe/master\n", "icl": "1827\tadd menu\n", "slm": "Enable/disable HTML Menus on Linux\n", "nl": "Merge pull request from adobe/linux", "lang": "javascript", "label": ["Doc&Code"], "Comments": []}
{"id": 16761, "text": "new file mode 100644\nindex **********000..162d0480389c0\nmmm/dev/null\n\n+ \"\"\"\n+ Support for showing random states.\n+\n+ For more details about this platform,please refer to the documentation at\n+ https://home-assistant.io/components/binary_sensor.random/\n+ \"\"\"\n+ import asyncio\n+ import logging\n+\n+ import voluptuous as vol\n+\n+ import homeassistant.helpers.config_validation as cv\n+ from homeassistant.components.binary_sensor import(\n+ BinarySensorDevice,PLATFORM_SCHEMA,DEVICE_CLASSES_SCHEMA)\n+ from homeassistant.const import CONF_NAME,CONF_DEVICE_CLASS\n+\n+ _LOGGER=logging.getLogger(__name__)\n+\n+ DEFAULT_NAME='Random Binary Sensor'\n+\n+ PLATFORM_SCHEMA=PLATFORM_SCHEMA.extend({\n+ vol.Optional(CONF_NAME,default=DEFAULT_NAME):cv.string,\n+ vol.Optional(CONF_DEVICE_CLASS):DEVICE_CLASSES_SCHEMA,\n+ })\n+\n+\n+ @asyncio.coroutine\n+ def async_setup_platform(hass,config,async_add_devices,discovery_info=None):\n+ \"\"\"Set up the Random binary sensor.\"\"\"\n+ name=config.get(CONF_NAME)\n+ device_class=config.get(CONF_DEVICE_CLASS)\n+\n+ async_add_devices([RandomSensor(name,device_class)],True)\n+\n+\n+ class RandomSensor(BinarySensorDevice):\n+ \"\"\"Representation of a Random binary sensor.\"\"\"\n+\n+ def__init__(self,name,device_class):\n+ \"\"\"Initialize the Random binary sensor.\"\"\"\n+ self._name=name\n+ self._device_class=device_class\n+ self._state=None\n+\n+ @property\n+ def name(self):\n+ \"\"\"Return the name of the sensor.\"\"\"\n+ return self._name\n+\n+ @property\n+ def is_on(self):\n+ \"\"\"Return true if sensor is on.\"\"\"\n+ return self._state\n+\n+ @property\n+ def device_class(self):\n+ \"\"\"Return the sensor class of the sensor.\"\"\"\n+ return self._device_class\n+\n+ @asyncio.coroutine\n+ def async_update(self):\n+ \"\"\"Get new state and update the sensor's state.\"\"\"\n+ from random import getrandbits\n+ self._state=bool(getrandbits(1))\nnew file mode 100644\nindex **********000..9ec1990158dc4\nmmm/dev/null\n\n+ \"\"\"The test for the Random binary sensor platform.\"\"\"\n+ import unittest\n+ from unittest.mock import patch\n+\n+ from homeassistant.setup import setup_component\n+\n+ from tests.common import get_test_home_assistant\n+\n+\n+ class TestRandomSensor(unittest.TestCase):\n+ \"\"\"Test the Random binary sensor.\"\"\"\n+\n+ def setup_method(self,method):\n+ \"\"\"Set up things to be run when tests are started.\"\"\"\n+ self.hass=get_test_home_assistant()\n+\n+ def teardown_method(self,method):\n+ \"\"\"Stop everything that was started.\"\"\"\n+ self.hass.stop()\n+\n+ @patch('random.getrandbits',return_value=1)\n+ def test_random_binary_sensor_on(self,mocked):\n+ \"\"\"Test the Random binary sensor.\"\"\"\n+ config={\n+ 'binary_sensor':{\n+ 'platform':'random',\n+ 'name':'test',\n+ }\n+ }\n+\n+ assert setup_component(self.hass,'binary_sensor',config)\n+\n+ state=self.hass.states.get('binary_sensor.test')\n+\n+ self.assertEqual(state.state,'on')\n+\n+ @patch('random.getrandbits',return_value=False)\n+ def test_random_binary_sensor_off(self,mocked):\n+ \"\"\"Test the Random binary sensor.\"\"\"\n+ config={\n+ 'binary_sensor':{\n+ 'platform':'random',\n+ 'name':'test',\n+ }\n+ }\n+\n+ assert setup_component(self.hass,'binary_sensor',config)\n+\n+ state=self.hass.states.get('binary_sensor.test')\n+\n+ self.assertEqual(state.state,'off')\n\n\npeft:1670\tAdd random binary sensor platform.\n\n\nicl:1670\tEqual(state.state,'on')\n\n\nslm:Add a platform's random_sensor()\n\n\n", "peft": "1670\tAdd random binary sensor platform.\n", "icl": "1670\tEqual(state.state,'on')\n", "slm": "Add a platform's random_sensor()\n", "nl": "Add Random binary sensor()", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16762, "text": "Binary files a/tutorial/images/supervised_scikit_learn.png and b/tutorial/images/supervised_scikit_learn.png differ\n\n\npeft:1776\tMerge pull request from mlfromscratch/master\n\n\nicl:1776\tMore additions to whatsnew.\n\n\nslm:'Updating for version 0.12.12rc1'\n\n\n", "peft": "1776\tMerge pull request from mlfromscratch/master\n", "icl": "1776\tMore additions to whatsnew.\n", "slm": "'Updating for version 0.12.12rc1'\n", "nl": "use vec.fit_transform instead of vec.transform on the training set", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16763, "text": "\nimport json\nimport psycopg2\nimport sys\n+ import signal\n+ import select\nfrom.utils import JSONEncoder\n\n\ndef redshift(connection_string):\ndef column_friendly_name(column_name):\nreturn column_name\n\n+ def wait(conn):\n+ while 1:\n+ state=conn.poll()\n+ if state==psycopg2.extensions.POLL_OK:\n+ break\n+ elif state==psycopg2.extensions.POLL_WRITE:\n+ select.select([],[conn.fileno()],[])\n+ elif state==psycopg2.extensions.POLL_READ:\n+ select.select([conn.fileno()],[],[])\n+ else:\n+ raise psycopg2.OperationalError(\"poll()returned%s\"%state)\n+\ndef query_runner(query):\n- connection=psycopg2.connect(connection_string)\n+ connection=psycopg2.connect(connection_string,async=True)\n+ wait(connection)\n+\ncursor=connection.cursor()\n\ntry:\ncursor.execute(query)\n+ wait(connection)\n\ncolumn_names=[col.name for col in cursor.description]\n\ndef query_runner(query):\nerror=None\ncursor.close()\nexcept psycopg2.DatabaseError as e:\n- connection.rollback()\njson_data=None\nerror=e.message\n-\n+ except KeyboardInterrupt:\n+ connection.cancel()\n+ error=\"Query cancelled by user.\"\n+ json_data=None\nexcept Exception as e:\n- connection.rollback()\nraise sys.exc_info()[1],None,sys.exc_info()[2]\nfinally:\nconnection.close()\ndef cancel(self):\nreturn\n\nif self.status==self.PROCESSING:\n- os.kill(self.process_id,signal.SIGKILL)\n+ os.kill(self.process_id,signal.SIGINT)\nelse:\nself.done(None,\"Interrupted/Cancelled while running.\")\n\n\n\npeft:204\tAdd support for cancelling queries in Redshift.\n\n\nicl:204\tredshift:add signal and select()\n\n\nslm:Merge pull request from getredash/query - async\n\n\n", "peft": "204\tAdd support for cancelling queries in Redshift.\n", "icl": "204\tredshift:add signal and select()\n", "slm": "Merge pull request from getredash/query - async\n", "nl": "Instead of killing the process, send SIGINT and cancel query on interupt.", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16764, "text": "\nfind_xpath_attr,\nsmuggle_url,\ndetermine_ext,\n+ ExtractorError,\n)\nfrom.senateisvp import SenateISVPIE\n\n\n+ def get_text_attr(d,attr):\n+ return d.get(attr,{}).get('# text')\n+\n+\nclass CSpanIE(InfoExtractor):\n_VALID_URL=r'http://(?:www\\.)?c-span\\.org/video/\\?(?P<id>[0-9a-f]+)'\nIE_DESC='C-SPAN'\n_TESTS=[{\n'url':'http://www.c-span.org/video/?313572-1/HolderonV',\n- 'md5':'067803f994e049b455a58b16e5aab442',\n+ 'md5':'94b29a4f131ff03d23471dd6f60b6a1d',\n'info_dict':{\n'id':'315139',\n'ext':'mp4',\nclass CSpanIE(InfoExtractor):\n'skip':'Regularly fails on travis,for unknown reasons',\n},{\n'url':'http://www.c-span.org/video/?c4486943/cspan-international-health-care-models',\n- 'md5':'4eafd1e91a75d2b1e6a3cbd0995816a2',\n+ 'md5':'8e5fbfabe6ad0f89f3012a7943c1287b',\n'info_dict':{\n'id':'c4486943',\n'ext':'mp4',\nclass CSpanIE(InfoExtractor):\n}\n},{\n'url':'http://www.c-span.org/video/?318608-1/gm-ignition-switch-recall',\n- 'md5':'446562a736c6bf97118e389433ed88d4',\n+ 'md5':'2ae5051559169baadba13fc35345ae74',\n'info_dict':{\n'id':'342759',\n'ext':'mp4',\ndef_real_extract(self,url):\nreturn self.url_result(surl,'SenateISVP',video_id,title)\n\ndata=self._download_json(\n- 'http://c-spanvideo.org/videoLibrary/assets/player/ajax-player.php?os=android&html5=%s&id=%s'%(video_type,video_id),\n- video_id)\n+ 'http://www.c-span.org/assets/player/ajax-player.php?os=android&html5=%s&id=%s'%(video_type,video_id),\n+ video_id)['video']\n+ if data['@status']!='Success':\n+ raise ExtractorError('%s said:%s'%(self.IE_NAME,get_text_attr(data,'error')),expected=True)\n\ndoc=self._download_xml(\n'http://www.c-span.org/common/services/flashXml.php?%sid=%s'%(video_type,video_id),\ndef_real_extract(self,url):\ntitle=find_xpath_attr(doc,'.//string','name','title').text\nthumbnail=find_xpath_attr(doc,'.//string','name','poster').text\n\n- files=data['video']['files']\n- try:\n- capfile=data['video']['capfile']['# text']\n- except KeyError:\n- capfile=None\n+ files=data['files']\n+ capfile=get_text_attr(data,'capfile')\n\n- entries=[{\n- 'id':'%s_%d'%(video_id,partnum+1),\n- 'title':(\n- title if len(files)==1 else\n- '%s part%d'%(title,partnum+1)),\n- 'url':unescapeHTML(f['path']['# text']),\n- 'description':description,\n- 'thumbnail':thumbnail,\n- 'duration':int_or_none(f.get('length',{}).get('# text')),\n- 'subtitles':{\n- 'en':[{\n- 'url':capfile,\n- 'ext':determine_ext(capfile,'dfxp')\n- }],\n- }if capfile else None,\n- }for partnum,f in enumerate(files)]\n+ entries=[]\n+ for partnum,f in enumerate(files):\n+ formats=[]\n+ for quality in f['qualities']:\n+ formats.append({\n+ 'format_id':'%s-%sp'%(get_text_attr(quality,'bitrate'),get_text_attr(quality,'height')),\n+ 'url':unescapeHTML(get_text_attr(quality,'file')),\n+ 'height':int_or_none(get_text_attr(quality,'height')),\n+ 'tbr':int_or_none(get_text_attr(quality,'bitrate')),\n+ })\n+ self._sort_formats(formats)\n+ entries.append({\n+ 'id':'%s_%d'%(video_id,partnum+1),\n+ 'title':(\n+ title if len(files)==1 else\n+ '%s part%d'%(title,partnum+1)),\n+ 'formats':formats,\n+ 'description':description,\n+ 'thumbnail':thumbnail,\n+ 'duration':int_or_none(get_text_attr(f,'length')),\n+ 'subtitles':{\n+ 'en':[{\n+ 'url':capfile,\n+ 'ext':determine_ext(capfile,'dfxp')\n+ }],\n+ }if capfile else None,\n+ })\n\nif len(entries)==1:\nentry=dict(entries[0])\n\n\npeft:1151\t[cspan]Fix tests and add get_text_attr()\n\n\nicl:1151\tAnswer:\n\n\nslm:[cspan]fix extraction\n\n\n", "peft": "1151\t[cspan]Fix tests and add get_text_attr()\n", "icl": "1151\tAnswer:\n", "slm": "[cspan]fix extraction\n", "nl": "[cspan]handle error massages and extract qualities", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16765, "text": "def set_alpha(self,alpha):\nexcept TypeError:\nraise TypeError('alpha must be a float or None')\nartist.Artist.set_alpha(self,alpha)\n- self._set_facecolor(self._facecolor)\n+ self._set_facecolor(self._original_facecolor)\nself._set_edgecolor(self._original_edgecolor)\n# stale is already True\n\ndef test_patch_alpha_override():\nax.set_ylim([-1,2])\n\n\n+ @cleanup(style='default')\n+ def test_patch_color_none():\n+ # Make sure the alpha kwarg does not override'none'facecolor.\n+ # Addresses issue # 7478.\n+ c=plt.Circle((0,0),1,facecolor='none',alpha=1)\n+ assert c.get_facecolor()[0]==0\n+\n+\n@image_comparison(baseline_images=['patch_custom_linestyle'],\nremove_text=True)\ndef test_patch_custom_linestyle():\n\n\npeft:1003\tMerge pull request from mwaskom/patch_color_none\n\n\nicl:1003\t[WIP]Create OAuth CS skeleton()\n\n\nslm:BUG:fix test_patch_alpha_override to not override the color kwarg\n\n\n", "peft": "1003\tMerge pull request from mwaskom/patch_color_none\n", "icl": "1003\t[WIP]Create OAuth CS skeleton()\n", "slm": "BUG:fix test_patch_alpha_override to not override the color kwarg\n", "nl": "BUG:preserve'none'facecolor in Patch.Closes.", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16766, "text": "def api_fetch_api_key(request,username=REQ,password=REQ):\nreturn json_error(\"Your username or password is incorrect.\",data={\"reason\":\"incorrect_creds\"},status=403)\nif not user_profile.is_active:\nreturn json_error(\"Your account has been disabled.\",data={\"reason\":\"disabled\"},status=403)\n- return json_success({\"api_key\":user_profile.api_key})\n+ return json_success({\"api_key\":user_profile.api_key,\"email\":user_profile.email})\n\n@authenticated_json_post_view\n@has_request_variables\n\n\npeft:641\tAdd email to api_fetch_api_key()\n\n\nicl:641\tMerge pull request from jeannefukumaru/add_email_to_api_key_response\n\n\nslm:Add email to success response\n\n\n", "peft": "641\tAdd email to api_fetch_api_key()\n", "icl": "641\tMerge pull request from jeannefukumaru/add_email_to_api_key_response\n", "slm": "Add email to success response\n", "nl": "Include email in response to API key retrieval", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16767, "text": "class Path(object):\nclosed,line and curve segments.\n\nThe underlying storage is made up of two parallel numpy arrays:\n- -vertices:an Nx2 float array of vertices\n- -codes:an N-length uint8 array of vertex types\n+ -* vertices *:an Nx2 float array of vertices\n+ -* codes *:an N-length uint8 array of vertex types\n\nThese two arrays always have the same length in the first\ndimension.For example,to represent a cubic curve,you must\nclass Path(object):\n\nUsers of Path objects should not access the vertices and codes\narrays directly.Instead,they should use:meth:` iter_segments `\n- to get the vertex/code pairs.This is important since many Paths,\n- as an optimization,do not store a codes array at all,but have a\n- default one provided for them by:meth:` iter_segments `.\n+ to get the vertex/code pairs.This is important,since many\n+ :class:` Path ` s,as an optimization,do not store a codes array at\n+ all,but have a default one provided for them by\n+ :meth:` iter_segments `.\n\"\"\"\n\n# Path codes\ndef__init__(self,vertices,codes=None):\n\"\"\"\nCreate a new path with the given vertices and codes.\n\n- vertices is an Nx2 numpy float array,masked array or Python\n+ * vertices * is an Nx2 numpy float array,masked array or Python\nsequence.\n\n- codes is an N-length numpy array or Python sequence of type\n- Path.code_type.\n+ * codes * is an N-length numpy array or Python sequence of type\n+ :attr:` matplotlib.path.Path.code_type `.\n\nThese two arrays must have the same length in the first\ndimension.\n\n- If codes is None,vertices will be treated as a series of line\n- segments.If vertices contains masked values,the resulting\n- path will be compressed,with MOVETO codes inserted in the\n- correct places to jump over the masked regions.\n+ If * codes * is None,* vertices * will be treated as a series of\n+ line segments.If * vertices * contains masked values,the\n+ resulting path will be compressed,with ` ` MOVETO ` ` codes\n+ inserted in the correct places to jump over the masked\n+ regions.\n\"\"\"\nif ma.isMaskedArray(vertices):\nis_mask=True\ndef transformed(self,transform):\n\ndef contains_point(self,point,transform=None):\n\"\"\"\n- Returns True if the path contains the given point.\n+ Returns * True * if the path contains the given point.\n\n- If transform is not None,the path will be transformed before\n- performing the test.\n+ If * transform * is not * None *,the path will be transformed\n+ before performing the test.\n\"\"\"\nif transform is not None:\ntransform=transform.frozen()\ndef contains_point(self,point,transform=None):\n\ndef contains_path(self,path,transform=None):\n\"\"\"\n- Returns True if this path completely contains the given path.\n+ Returns * True * if this path completely contains the given path.\n\n- If transform is not None,the path will be transformed before\n- performing the test.\n+ If * transform * is not * None *,the path will be transformed\n+ before performing the test.\n\"\"\"\nif transform is not None:\ntransform=transform.frozen()\ndef contains_path(self,path,transform=None):\n\ndef get_extents(self,transform=None):\n\"\"\"\n- Returns the extents(xmin,ymin,xmax,ymax)of the path.\n+ Returns the extents(* xmin *,* ymin *,* xmax *,* ymax *)of the\n+ path.\n\n- Unlike computing the extents on the vertices alone,this\n+ Unlike computing the extents on the * vertices * alone,this\nalgorithm will take into account the curves and deal with\ncontrol points appropriately.\n\"\"\"\ndef get_extents(self,transform=None):\n\ndef intersects_path(self,other):\n\"\"\"\n- Returns True if this path intersects another given path.\n+ Returns * True * if this path intersects another given path.\n\"\"\"\nreturn path_intersects_path(self,other)\n\ndef intersects_bbox(self,bbox):\n\"\"\"\n- Returns True if this path intersects a given\n+ Returns * True * if this path intersects a given\n:class:`~matplotlib.transforms.Bbox `.\n\"\"\"\nfrom transforms import BboxTransformTo\ndef intersects_bbox(self,bbox):\n\ndef interpolated(self,steps):\n\"\"\"\n- Returns a new path resampled to length N x steps.\n- Does not currently handle interpolating curves.\n+ Returns a new path resampled to length N x steps.Does not\n+ currently handle interpolating curves.\n\"\"\"\nvertices=simple_linear_interpolation(self.vertices,steps)\ncodes=self.codes\ndef to_polygons(self,transform=None,width=0,height=0):\ndisplaying in backends that do not support compound paths or\nBezier curves,such as GDK.\n\n- If width and height are both non-zero then the lines will be\n- simplified so that vertices outside of(0,0),(width,height)\n- will be clipped.\n+ If * width * and * height * are both non-zero then the lines will\n+ be simplified so that vertices outside of(0,0),(width,\n+ height)will be clipped.\n\"\"\"\nif len(self.vertices)==0:\nreturn[]\ndef unit_rectangle(cls):\ndef unit_regular_polygon(cls,numVertices):\n\"\"\"\n(staticmethod)Returns a:class:` Path ` for a unit regular\n- polygon with the given numVertices and radius of 1.0,centered\n- at(0,0).\n+ polygon with the given * numVertices * and radius of 1.0,\n+ centered at(0,0).\n\"\"\"\nif numVertices<=16:\npath=cls._unit_regular_polygons.get(numVertices)\ndef unit_circle(cls):\ndef arc(cls,theta1,theta2,n=None,is_wedge=False):\n\"\"\"\n(staticmethod)Returns an arc on the unit circle from angle\n- theta1 to angle theta2(in degrees).\n+ * theta1 * to angle * theta2 *(in degrees).\n\n- If n is provided,it is the number of spline segments to make.\n- If n is not provided,the number of spline segments is\n- determined based on the delta between theta1 and theta2.\n+ If * n * is provided,it is the number of spline segments to make.\n+ If * n * is not provided,the number of spline segments is\n+ determined based on the delta between * theta1 * and * theta2 *.\n\nMasionobe,L.2003.` Drawing an elliptical arc using\npolylines,quadratic or cubic Bezier curves\ndef arc(cls,theta1,theta2,n=None,is_wedge=False):\ndef wedge(cls,theta1,theta2,n=None):\n\"\"\"\n(staticmethod)Returns a wedge of the unit circle from angle\n- theta1 to angle theta2(in degrees).\n+ * theta1 * to angle * theta2 *(in degrees).\n+\n+ If * n * is provided,it is the number of spline segments to make.\n+ If * n * is not provided,the number of spline segments is\n+ determined based on the delta between * theta1 * and * theta2 *.\n\"\"\"\nreturn cls.arc(theta1,theta2,n,True)\nwedge=classmethod(wedge)\n\n\npeft:290\tMerge pull request from matthewbrett/patch - 1\n\n\nicl:290\t+ Returns * True * if this path intersects another given path.\n\n\nslm:Merge pull request from charris/doc - Path\n\n\n", "peft": "290\tMerge pull request from matthewbrett/patch - 1\n", "icl": "290\t+ Returns * True * if this path intersects another given path.\n", "slm": "Merge pull request from charris/doc - Path\n", "nl": "Fix docstrings.", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16768, "text": "def req_importip_handler(self):\nif google_ip.add_ip(ip,100,\"google.com\",\"gws\"):\ncount+=1\ndata='{\"res\":\"%s\"}'%count\n+ google_ip.save_ip_list(force=True)\n\nelif reqs['cmd']==['exportip']:\ndata='{\"res\":\"'\n\n\npeft:889\tAdded google_ip.save_ip_list(force=True)\n\n\nicl:889\tgoogle_ip.py:add exportip()\n\n\nslm:save ip list when import\n\n\n", "peft": "889\tAdded google_ip.save_ip_list(force=True)\n", "icl": "889\tgoogle_ip.py:add exportip()\n", "slm": "save ip list when import\n", "nl": "Merge pull request from yfdyh000/save_immediately_import", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16769, "text": "def send_result(self,type,task,result):\ndef fetch(self,task,callback=None):\nif self.async:\nreturn self.async_fetch(task,callback)\n+ elif self.ioloop._running:\n+ future=self.async_fetch(task,callback)\n+ while not future.done():\n+ time.sleep(0.1)\n+ return future.result()\nelse:\nreturn self.ioloop.run_sync(functools.partial(self.async_fetch,task,callback))\n\n\n\npeft:1653\tFix:ipython.parallel.Client.fetch()\n\n\nicl:1653\tDOC:add async fetch()\n\n\nslm:Fix asyncio.async_fetch()(closes)\n\n\n", "peft": "1653\tFix:ipython.parallel.Client.fetch()\n", "icl": "1653\tDOC:add async fetch()\n", "slm": "Fix asyncio.async_fetch()(closes)\n", "nl": "fix RuntimeError:IOLoop is already running", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16770, "text": "A collective list of JSON APIs for use in web development.\n|API|Description|OAuth|Link|\n|mmm|mmm|mmm|mmm|\n|Drupal.org|Drupal.org API|No|[Go!](https://www.drupal.org/drupalorg/api)|\n+ |Countly|Countly web analytics API|No|[Go!](http://resources.count.ly/)|\n|Libraries.io|Open source software libraries|No|[Go!](https://libraries.io/api)|\n\n# # # Security\n\n\npeft:1691\tAdded Countly to the list\n\n\nicl:1691\tAdded Countly API.\n\n\nslm:Merge pull request from erikbern/patch - 1\n\n\n", "peft": "1691\tAdded Countly to the list\n", "icl": "1691\tAdded Countly API.\n", "slm": "Merge pull request from erikbern/patch - 1\n", "nl": "Add Countly web analytics", "lang": "python", "label": ["Doc-mod"], "Comments": []}
{"id": 16771, "text": "def credentials(self,* * kwargs):\n\"\"\"\nself._credentials=kwargs\n\n- def authenticate(self,user=None,token=None):\n+ def force_authenticate(self,user=None,token=None):\n\"\"\"\nForcibly authenticates outgoing requests with the given\nuser and/or token.\ndef test_credentials(self):\nresponse=self.client.get('/view/')\nself.assertEqual(response.data['auth'],'example')\n\n- def test_authenticate(self):\n+ def test_force_authenticate(self):\n\"\"\"\n- Setting `.authenticate()` forcibly authenticates each request.\n+ Setting `.force_authenticate()` forcibly authenticates each request.\n\"\"\"\nuser=User.objects.create_user('example','<EMAIL>')\n- self.client.authenticate(user)\n+ self.client.force_authenticate(user)\nresponse=self.client.get('/view/')\nself.assertEqual(response.data['user'],'example')\n\n\n\npeft:828\tMerge pull request from johnnyreilly/force_authenticate\n\n\nicl:828\tbpo - 36324:Improved code formatting for the NormalDist.inv_cdf rational\n\n\nslm:Rename`authenticate`to`force_authenticate`\n\n\n", "peft": "828\tMerge pull request from johnnyreilly/force_authenticate\n", "icl": "828\tbpo - 36324:Improved code formatting for the NormalDist.inv_cdf rational\n", "slm": "Rename`authenticate`to`force_authenticate`\n", "nl": "Rename to force_authenticate", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16772, "text": "def luf(lamdaexpr,* args,* * kwargs):\nlast axis.\nkind:{'quicksort','mergesort','heapsort'},optional\nSorting algorithm.Default is'quicksort'.\n- order:list,optional\n+ order:str or list of str,optional\nWhen ` a ` is an array with fields defined,this argument specifies\n- which fields to compare first,second,etc.Not all fields need be\n- specified.\n+ which fields to compare first,second,etc.A single field can\n+ be specified as a string,and not all fields need be specified,\n+ but unspecified fields will still be used,in the order in which\n+ they come up in the dtype,to break ties.\n\nSee Also\nmmmmmm--\ndef luf(lamdaexpr,* args,* * kwargs):\nlast axis.\nkind:{'introselect'},optional\nSelection algorithm.Default is'introselect'.\n- order:list,optional\n+ order:str or list of str,optional\nWhen ` a ` is an array with fields defined,this argument specifies\n- which fields to compare first,second,etc.Not all fields need be\n- specified.\n+ which fields to compare first,second,etc.A single field can\n+ be specified as a string,and not all fields need be specified,\n+ but unspecified fields will still be used,in the order in which\n+ they come up in the dtype,to break ties.\n\nSee Also\nmmmmmm--\ndef partition(a,kth,axis=-1,kind='introselect',order=None):\nsorting.The default is-1,which sorts along the last axis.\nkind:{'introselect'},optional\nSelection algorithm.Default is'introselect'.\n- order:list,optional\n- When ` a ` is a structured array,this argument specifies which fields\n- to compare first,second,and so on.This list does not need to\n- include all of the fields.\n+ order:str or list of str,optional\n+ When ` a ` is an array with fields defined,this argument specifies\n+ which fields to compare first,second,etc.A single field can\n+ be specified as a string.Not all fields need be specified,but\n+ unspecified fields will still be used,in the order in which they\n+ come up in the dtype,to break ties.\n\nReturns\nmmmmmm-\ndef argpartition(a,kth,axis=-1,kind='introselect',order=None):\nthe flattened array is used.\nkind:{'introselect'},optional\nSelection algorithm.Default is'introselect'\n- order:list,optional\n+ order:str or list of str,optional\nWhen ` a ` is an array with fields defined,this argument specifies\n- which fields to compare first,second,etc.Not all fields need be\n- specified.\n+ which fields to compare first,second,etc.A single field can\n+ be specified as a string,and not all fields need be specified,\n+ but unspecified fields will still be used,in the order in which\n+ they come up in the dtype,to break ties.\n\nReturns\nmmmmmm-\ndef sort(a,axis=-1,kind='quicksort',order=None):\nsorting.The default is-1,which sorts along the last axis.\nkind:{'quicksort','mergesort','heapsort'},optional\nSorting algorithm.Default is'quicksort'.\n- order:list,optional\n- When ` a ` is a structured array,this argument specifies which fields\n- to compare first,second,and so on.This list does not need to\n- include all of the fields.\n+ order:str or list of str,optional\n+ When ` a ` is an array with fields defined,this argument specifies\n+ which fields to compare first,second,etc.A single field can\n+ be specified as a string,and not all fields need be specified,\n+ but unspecified fields will still be used,in the order in which\n+ they come up in the dtype,to break ties.\n\nReturns\nmmmmmm-\ndef argsort(a,axis=-1,kind='quicksort',order=None):\nthe flattened array is used.\nkind:{'quicksort','mergesort','heapsort'},optional\nSorting algorithm.\n- order:list,optional\n+ order:str or list of str,optional\nWhen ` a ` is an array with fields defined,this argument specifies\n- which fields to compare first,second,etc.Not all fields need be\n- specified.\n+ which fields to compare first,second,etc.A single field can\n+ be specified as a string,and not all fields need be specified,\n+ but unspecified fields will still be used,in the order in which\n+ they come up in the dtype,to break ties.\n\nReturns\nmmmmmm-\n\n\npeft:342\tDOC:Add order parameter to partition and argpartition.\n\n\nicl:342\tMerge pull request from eric - wieser/basics - docs\n\n\nslm:Merge pull request from dstansby/doc - sort\n\n\n", "peft": "342\tDOC:Add order parameter to partition and argpartition.\n", "icl": "342\tMerge pull request from eric - wieser/basics - docs\n", "slm": "Merge pull request from dstansby/doc - sort\n", "nl": "DOC:Better document'order'argument of'sort'and friends", "lang": "python", "label": ["Doc-mod"], "Comments": []}
{"id": 16773, "text": "function do_narrow(target_zephyr,description,filter_function){\ncurrent_view($(this))\n});\n\n- $(\"# selected\").closest(\"td\").empty();\n- $(\"#\"+target_zephyr).children(\"td:first\").html(tag_for_selected);\n- $.post(\"update\",{pointer:target_zephyr});\n-\n+ select_zephyr(target_zephyr);\nscroll_to_selected();\n\n$(\"# unhide\").removeAttr(\"disabled\");\n\n\npeft:926\tMerge pull request from jmr/zephyr_narrow_fix\n\n\nicl:926\tupdate do_narrow()\n\n\nslm:Use select_zephyr().\n\n\n", "peft": "926\tMerge pull request from jmr/zephyr_narrow_fix\n", "icl": "926\tupdate do_narrow()\n", "slm": "Use select_zephyr().\n", "nl": "Remove duplication between do_narrow and select_zephyr", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16774, "text": "\nlogger=logging.getLogger(__name__)\n\nimport io\n+ import re\nimport os\nimport sys\nimport six\ndef__str__(self):\nreturn self.text\n\nbokehjs_dir=settings.bokehjsdir()\n+ nodejs_min_version=(6,10,0)\n\ndef_detect_nodejs():\nif settings.nodejs_path()is not None:\ndef_detect_nodejs():\nfor nodejs_path in nodejs_paths:\ntry:\nproc=Popen([nodejs_path,\"--version\"],stdout=PIPE,stderr=PIPE)\n- proc.wait()\n+ (stdout,_)=proc.communicate()\nexcept OSError:\n- pass\n- else:\n- return nodejs_path\n- else:\n- raise RuntimeError('node.js is needed to allow compilation of custom models'+\n- '(\"conda install-c bokeh nodejs\"or follow https://nodejs.org/en/download/)')\n+ continue\n+\n+ if proc.returncode!=0:\n+ continue\n+\n+ match=re.match(r\"^v(\\d+)\\.(\\d+)\\.(\\d+).*$\",stdout.decode(\"utf-8\"))\n+\n+ if match is not None:\n+ version=tuple(int(v)for v in match.groups())\n+\n+ if version>=nodejs_min_version:\n+ return nodejs_path\n+\n+ # if we've reached here,no valid version was found\n+ version=\".\".join(map(str,nodejs_min_version))\n+ raise RuntimeError('node.js v%s or higher is needed to allow compilation of custom models'%version+\n+ '(\"conda install-c bokeh nodejs\"or follow https://nodejs.org/en/download/)')\n\n_nodejs=None\n_npmjs=None\n\nfrom__future__import absolute_import\n\n- from mock import Mock\n-\nimport bokeh.util.compiler as buc\n\ndef test_nodejs_compile_coffeescript():\ndef test_nodejs_compile_less():\ntext=\"some.less:1:21:Unrecognised input\",\nextract=\".bk-some-style color:green;}\",\nannotated=\"some.less:1:21:Unrecognised input\\n.bk-some-style color:green;}\"))\n-\n- def test__detect_nodejs_calls_wait():\n- m=Mock()\n- old_Popen=buc.Popen\n- buc.Popen=lambda * args,* * kw:m\n- buc._detect_nodejs()\n- assert m.wait.called\n- buc.Popen=old_Popen\nrequirements:\nbuild:\n- python\n- setuptools\n- -nodejs>=6.6.0\n+ -nodejs>=6.10.0\n- certifi\n- pycurl\n- packaging\ntest:\n- scipy\n- pillow\n- boto\n- -nodejs>=6.6.0\n+ -nodejs>=6.10.0\n# pyyaml installed by bokeh\n# jinja2 installed by bokeh\n\npackage.\nBecause the Bokeh client library is mostly concerned with providing a nice\nPython interface for generating JSON objects which are then consumed by the\nBokehJS library running in the browser,there shouldn't be a * hard * dependency\n- on any of the standard NumPy/SciPy stack.It is entirely possible to use\n+ on any of the standard NumPy/SciPy stack.It is entirely possible to use\nBokeh with plain Python lists of values.However,the Bokeh plot server does\nmake direct use of NumPy,and it is required to be installed for Bokeh apps\n- to function.\n+ to function.Additionally nodejs is required to allow compilation of custom\n+ bokeh extensions.\n\n.._install_packages:\n\n\n\npeft:1101\tBokehJS:Require node.js v6.10.0 or higher for compilation\n\n\nicl:1101\tBLD:disable windows GHA()\n\n\nslm:Merge pull request from bokeh/feature/nodejs - extensions\n\n\n", "peft": "1101\tBokehJS:Require node.js v6.10.0 or higher for compilation\n", "icl": "1101\tBLD:disable windows GHA()\n", "slm": "Merge pull request from bokeh/feature/nodejs - extensions\n", "nl": "Check nodejs version number in bokeh.util.compiler()", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16775, "text": "def work():\nfor time,test in test_times[:10]:\nprint(\"%s:%.1fs\"%(test,time))\nif bad:\n- bad=sorted(set(bad)-set(environment_changed))\n- if bad:\n- print(count(len(bad),\"test\"),\"failed:\")\n- printlist(bad)\n+ print(count(len(bad),\"test\"),\"failed:\")\n+ printlist(bad)\nif environment_changed:\nprint(\"{}altered the execution environment:\".format(\ncount(len(environment_changed),\"test\")))\n\n\npeft:80\tFix formatting of environment_changed message.\n\n\nicl:80\tfix the bug\n\n\nslm:Remove duplicate set conversion.\n\n\n", "peft": "80\tFix formatting of environment_changed message.\n", "icl": "80\tfix the bug\n", "slm": "Remove duplicate set conversion.\n", "nl": "Merge 3.5", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16776, "text": "where the input parameters are\nthe list of arguments to process(default:` ` sys.argv[1:]` `)\n\n` ` values ` `\n- object to store option arguments in(default:a new instance of\n- :class:` optparse.Values `)\n+ a:class:` optparse.Values ` object to store option arguments in(default:a\n+ new instance of:class:` Values `)--if you give an existing object,the\n+ option defaults will not be initialized on it\n\nand the return values are\n\nold mode 100755\nnew mode 100644\ndef generic_help(self):\nprint>>self.stream,\"that match it are printed.\"\n\ndef do_add(self,line):\n- self.stats.add(line)\n+ if self.stats:\n+ self.stats.add(line)\n+ else:\n+ print>>self.stream,\"No statistics object is loaded.\"\nreturn 0\ndef help_add(self):\nprint>>self.stream,\"Add profile info from given file to current statistics object.\"\ndef do_read(self,line):\nexcept IOError,args:\nprint>>self.stream,args[1]\nreturn\n+ except Exception as err:\n+ print>>self.stream,err.__class__.__name__+':',err\n+ return\nself.prompt=line+\"%\"\nelif len(self.prompt)>2:\n- line=self.prompt[-2:]\n+ line=self.prompt[:-2]\n+ self.do_read(line)\nelse:\nprint>>self.stream,\"No statistics object is current--cannot reload.\"\nreturn 0\ndef help_read(self):\nprint>>self.stream,\"Read in profile data from a specified file.\"\n+ print>>self.stream,\"Without argument,reload the current file.\"\n\ndef do_reverse(self,line):\n- self.stats.reverse_order()\n+ if self.stats:\n+ self.stats.reverse_order()\n+ else:\n+ print>>self.stream,\"No statistics object is loaded.\"\nreturn 0\ndef help_reverse(self):\nprint>>self.stream,\"Reverse the sort order of the profiling report.\"\n\ndef do_sort(self,line):\n+ if not self.stats:\n+ print>>self.stream,\"No statistics object is loaded.\"\n+ return\nabbrevs=self.stats.get_sort_arg_defs()\nif line and all((x in abbrevs)for x in line.split()):\nself.stats.sort_stats(* line.split())\ndef help_stats(self):\nself.generic_help()\n\ndef do_strip(self,line):\n- self.stats.strip_dirs()\n- return 0\n+ if self.stats:\n+ self.stats.strip_dirs()\n+ else:\n+ print>>self.stream,\"No statistics object is loaded.\"\ndef help_strip(self):\nprint>>self.stream,\"Strip leading path information from filenames in the report.\"\n\n+ def help_help(self):\n+ print>>self.stream,\"Show help for a given command.\"\n+\ndef postcmd(self,stop,line):\nif stop:\nreturn stop\ndef test_self_symmetric_difference(self):\nresult=self.set^self.set\nself.assertEqual(result,empty_set)\n\n- def checkempty_symmetric_difference(self):\n+ def test_empty_symmetric_difference(self):\nresult=self.set^empty_set\nself.assertEqual(result,self.set)\n\nLibrary\n- Issue # 9448:Fix a leak of OS resources(mutexes or semaphores)when\nre-initializing a buffered IO object by calling its ` `__init__` ` method.\n\n+ -Issue # 7395:Fix tracebacks in pstats interactive browser.\n+\n+ -Issue # 8230:Fix Lib/test/sortperf.py.\n+\n- Issue # 1713:Fix os.path.ismount(),which returned true for symbolic links\nacross devices.\n\n\n\npeft:1028\tMerge pull request from jmagnusson/patch - 1\n\n\nicl:1028\tFix a leak of OS resources(mutexes or semaphores)when re-initializing a\n\n\nslm:Issue:Profile option parsing improvements.\n\n\n", "peft": "1028\tMerge pull request from jmagnusson/patch - 1\n", "icl": "1028\tFix a leak of OS resources(mutexes or semaphores)when re-initializing a\n", "slm": "Issue:Profile option parsing improvements.\n", "nl": "Merged revisions 83385 - 83389, 83391 via svnmerge from", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16777, "text": "FT2Font::FT2Font(Py::PythonClassInstance * self,Py::Tuple&args,Py::Dict&kwds)\n{\nstd::ostringstream s;\ns<<\"Could not load facefile\"<<facefile<<\";Unknown_File_Format\"<<std::endl;\n- ob_refcnt--;\nthrow Py::RuntimeError(s.str());\n}\nelse if(error==FT_Err_Cannot_Open_Resource)\n{\nstd::ostringstream s;\ns<<\"Could not open facefile\"<<facefile<<\";Cannot_Open_Resource\"<<std::endl;\n- ob_refcnt--;\nthrow Py::RuntimeError(s.str());\n}\nelse if(error==FT_Err_Invalid_File_Format)\n{\nstd::ostringstream s;\ns<<\"Could not open facefile\"<<facefile<<\";Invalid_File_Format\"<<std::endl;\n- ob_refcnt--;\nthrow Py::RuntimeError(s.str());\n}\nelse if(error)\n{\nstd::ostringstream s;\ns<<\"Could not open facefile\"<<facefile<<\";freetype error code\"<<error<<std::endl;\n- ob_refcnt--;\nthrow Py::RuntimeError(s.str());\n}\n\nFT2Font::FT2Font(Py::PythonClassInstance * self,Py::Tuple&args,Py::Dict&kwds)\n{\nstd::ostringstream s;\ns<<\"Could not set the fontsize for facefile\"<<facefile<<std::endl;\n- ob_refcnt--;\nthrow Py::RuntimeError(s.str());\n}\n\n\n\npeft:1997\tFix FT2Font constructor\n\n\nicl:1997\tcircleci:Use CIRCLE_JOB env var for distro in cache filenames.\n\n\nslm:Merge pull request from mdboom/ob_refcnt_fix\n\n\n", "peft": "1997\tFix FT2Font constructor\n", "icl": "1997\tcircleci:Use CIRCLE_JOB env var for distro in cache filenames.\n", "slm": "Merge pull request from mdboom/ob_refcnt_fix\n", "nl": "Remove redundant ob_refcnt's that cause crashes when built in DEBUG mode.", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16778, "text": "class ConsoleMaster(master.Master):\n\ndef__init__(self,options,server):\nsuper().__init__(options,server)\n+\n+ if not sys.stdout.isatty():\n+ print(\"Error:mitmproxy's console interface requires a tty.\"\n+ \"Please run mitmproxy in an interactive shell environment.\",file=sys.stderr)\n+ sys.exit(1)\n+\nself.view=view.View()# type:view.View\nself.stream_path=None\n# This line is just for type hinting\ndef mitmproxy(args=None):# pragma:no cover\n\"You can run mitmdump or mitmweb instead.\",file=sys.stderr)\nsys.exit(1)\n\n- if not sys.stdout.isatty():\n- print(\"Error:mitmproxy's console interface requires a tty.\"\n- \"Please run mitmproxy in an interactive shell environment.\",file=sys.stderr)\n- sys.exit(1)\n-\nassert_utf8_env()\n\nfrom mitmproxy.tools import console\n\n\npeft:614\tFix console interface not working on non-tty\n\n\nicl:614\t[puppet]Add a check for tty in console.py.\n\n\nslm:lint:Fix formatting and error messages.\n\n\n", "peft": "614\tFix console interface not working on non-tty\n", "icl": "614\t[puppet]Add a check for tty in console.py.\n", "slm": "lint:Fix formatting and error messages.\n", "nl": "check tty after options parsing", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16779, "text": "\nimport sys\nfrom subprocess import Popen,PIPE\n\n+ from IPython.utils.py3compat import cast_bytes_py2\n\n# mmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmmm--\n# Launching Kernels\ndef launch_kernel(cmd,stdin=None,stdout=None,stderr=None,\n\n# Spawn a kernel.\nif sys.platform=='win32':\n+\n+ if cwd:\n+ # Popen on Python 2 on Windows cannot handle unicode cwd.\n+ cwd=cast_bytes_py2(cwd,sys.getfilesystemencoding()or'ascii')\n+\nfrom IPython.kernel.zmq.parentpoller import ParentPollerWindows\n# Create a Win32 event for interrupting the kernel.\ninterrupt_event=ParentPollerWindows.create_interrupt_event()\n\n\npeft:1259\tFix launch_kernel on Windows.\n\n\nicl:1259\tAdd interrupt_event to launch_kernel\n\n\nslm:use cast_bytes_py2 for cwd in launch_kernel\n\n\n", "peft": "1259\tFix launch_kernel on Windows.\n", "icl": "1259\tAdd interrupt_event to launch_kernel\n", "slm": "use cast_bytes_py2 for cwd in launch_kernel\n", "nl": "cast kernel cwd to bytes on Python 2 on Windows", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16780, "text": "def get_vapp(self,vapp_name):\n\ndef get_vm(self,vapp_name,vm_name):\nvapp=self.get_vapp(vapp_name)\n+ children=vapp.me.get_Children()\nvms=[vm for vm in children.get_Vm()if vm.name==vm_name]\ntry:\nreturn vms[0]\n\n\npeft:1084\tFix get_vm()\n\n\nicl:1084\tDOC:add get_vm()\n\n\nslm:vmware_vmware:avoid noisy get_Children()\n\n\n", "peft": "1084\tFix get_vm()\n", "icl": "1084\tDOC:add get_vm()\n", "slm": "vmware_vmware:avoid noisy get_Children()\n", "nl": "Defined children for get_vm()", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16781, "text": "def sanity_check(self):\nraise AnsibleError(\"bad formatting string:%s\"%self.format)\n\ndef generate_sequence(self):\n- numbers=xrange(self.start,self.end+1,self.stride)\n+ if self.stride>0:\n+ adjust=1\n+ else:\n+ adjust=-1\n+ numbers=xrange(self.start,self.end+adjust,self.stride)\n\nfor i in numbers:\ntry:\n\nset_fact:\"{{'x'+item}}={{item}}\"\nwith_sequence:start=0 end=3\n\n+ -name:test with_sequence backwards\n+ set_fact:\"{{'y'+item}}={{item}}\"\n+ with_sequence:start=3 end=0 stride=-1\n+\n- name:verify with_sequence\nassert:\nthat:\n\n- \"x1=='1'\"\n- \"x2=='2'\"\n- \"x3=='3'\"\n+ -\"y3=='3'\"\n+ -\"y2=='2'\"\n+ -\"y1=='1'\"\n+ -\"y0=='0'\"\n+\n+ -name:test with_sequence not failing on count==0\n+ debug:msg='previously failed with backward counting error'\n+ with_sequence:count=0\n+ register:count_of_zero\n+\n+ -assert:\n+ that:\n+ -count_of_zero|skipped\n+ -not count_of_zero|failed\n\n# WITH_RANDOM_CHOICE\n\n\n\npeft:218\tFix with_sequence backwards count\n\n\nicl:218\t[WIP]\n\n\nslm:Merge pull request from sieben/backwards - counting\n\n\n", "peft": "218\tFix with_sequence backwards count\n", "icl": "218\t[WIP]\n", "slm": "Merge pull request from sieben/backwards - counting\n", "nl": "fixed corner case when counting backwards, added test cases for count=0 and backwards counts", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16782, "text": "\nimport os\nimport sys\nimport unittest\n+ from youtube_dl.extractor.ivi import IviCompilationIE\nsys.path.insert(0,os.path.dirname(os.path.dirname(os.path.abspath(__file__))))\n\nfrom test.helper import FakeYDL\ndef test_AcademicEarthCourse(self):\nself.assertEqual(result['title'],u'Building Dynamic Websites')\nself.assertEqual(result['description'],u\"Today's websites are increasingly dynamic.Pages are no longer static HTML files but instead generated by scripts and database calls.User interfaces are more seamless,with technologies like Ajax replacing traditional page reloads.This course teaches students how to build dynamic websites with Ajax and with Linux,Apache,MySQL,and PHP(LAMP),one of today's most popular frameworks.Students learn how to set up domain names with DNS,how to structure pages with XHTML and CSS,how to program in JavaScript and PHP,how to configure Apache and MySQL,how to design and query databases with SQL,how to use Ajax with both XML and JSON,and how to build mashups.The course explores issues of security,scalability,and cross-browser support and also discusses enterprise-level deployments of websites,including third-party hosting,virtualization,colocation in data centers,firewalling,and load-balancing.\")\nself.assertEqual(len(result['entries']),10)\n+\n+ def test_ivi_compilation(self):\n+ dl=FakeYDL()\n+ ie=IviCompilationIE(dl)\n+ result=ie.extract('http://www.ivi.ru/watch/dezhurnyi_angel')\n+ self.assertIsPlaylist(result)\n+ self.assertEqual(result['id'],u'dezhurnyi_angel')\n+ self.assertEqual(result['title'],u'Дежурный ангел(2010-2012)')\n+ self.assertTrue(len(result['entries'])>=36)\n+\n+ def test_ivi_compilation_season(self):\n+ dl=FakeYDL()\n+ ie=IviCompilationIE(dl)\n+ result=ie.extract('http://www.ivi.ru/watch/dezhurnyi_angel/season2')\n+ self.assertIsPlaylist(result)\n+ self.assertEqual(result['id'],u'dezhurnyi_angel/season2')\n+ self.assertEqual(result['title'],u'Дежурный ангел(2010-2012)2 сезон')\n+ self.assertTrue(len(result['entries'])>=20)\n\n\nif__name__=='__main__':\n\n\npeft:1571\t[ivi]Fix test_ivi_compilation()\n\n\nicl:1571\ttest:add ivi_compilation()\n\n\nslm:Merge remote - tracking branch'upstream/master'into work\n\n\n", "peft": "1571\t[ivi]Fix test_ivi_compilation()\n", "icl": "1571\ttest:add ivi_compilation()\n", "slm": "Merge remote - tracking branch'upstream/master'into work\n", "nl": "[ivi]Add playlist tests", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16783, "text": "You can determine your currently installed version using ` pip show `:\n\"\"\"\nDrop ` maybe_none ` field if None.\n\"\"\"\n- data=super().data()\n+ data=super().data\nif'maybe_none'in data and data['maybe_none']is None:\ndel data['maybe_none']\nreturn data\n\n\npeft:627\tRemove unused import\n\n\nicl:627\tMerge pull request from eric - wieser/basics - docs\n\n\nslm:remove trailing whitespace\n\n\n", "peft": "627\tRemove unused import\n", "icl": "627\tMerge pull request from eric - wieser/basics - docs\n", "slm": "remove trailing whitespace\n", "nl": "Merge pull request from encode/xordoquy/fix_release_notes", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16784, "text": "\n# endif\n# include CBLAS_HEADER\n\n+ # include<limits.h>\n# include<stdio.h>\n\nstatic char module_doc[]=\nstatic char module_doc[]=\n\nstatic PyArray_DotFunc * oldFunctions[NPY_NTYPES];\n\n+ # define MIN(a,b)((a)<(b)?(a):(b))\n+\n+ /*\n+ * The following functions do a\"chunked\"dot product using BLAS when\n+ * sizeof(npy_intp)>sizeof(int),because BLAS libraries can typically not\n+ * handle more than INT_MAX elements per call.\n+ *\n+ * The chunksize is the greatest power of two less than INT_MAX.\n+ */\n+ # if NPY_MAX_INTP>INT_MAX\n+ # define CHUNKSIZE(INT_MAX/2+1)\n+ # else\n+ # define CHUNKSIZE NPY_MAX_INTP\n+ # endif\n+\nstatic void\nFLOAT_dot(void * a,npy_intp stridea,void * b,npy_intp strideb,void * res,\nnpy_intp n,void * tmp)\n{\n- register npy_intp na=stridea/sizeof(float);\n- register npy_intp nb=strideb/sizeof(float);\n-\n- if((sizeof(float)* na==(size_t)stridea)&&\n- (sizeof(float)* nb==(size_t)strideb)&&\n- (na>=0)&&(nb>=0))\n- *((float *)res)=cblas_sdot((int)n,(float *)a,na,(float *)b,nb);\n-\n- else\n- oldFunctions[NPY_FLOAT](a,stridea,b,strideb,res,n,tmp);\n+ npy_intp na=stridea/(int)sizeof(float);\n+ npy_intp nb=strideb/(int)sizeof(float);\n+\n+ /* XXX we could handle negative strides as well */\n+ if(stridea>0&&strideb>0&&\n+ stridea==sizeof(float)* na&&\n+ strideb==sizeof(float)* nb){\n+ double r=0.;/* double for stability */\n+ float * fa=a,* fb=b;\n+\n+ while(n>0){\n+ int chunk=MIN(n,CHUNKSIZE);\n+\n+ r+=cblas_sdot(chunk,fa,na,fb,nb);\n+ fa+=chunk * na;\n+ fb+=chunk * nb;\n+ n-=chunk;\n+ }\n+ *((float *)res)=r;\n+ }\n+ else{\n+ oldFunctions[NPY_FLOAT](a,stridea,b,strideb,res,n,tmp);\n+ }\n}\n\nstatic void\nDOUBLE_dot(void * a,npy_intp stridea,void * b,npy_intp strideb,void * res,\nnpy_intp n,void * tmp)\n{\n- register int na=stridea/sizeof(double);\n- register int nb=strideb/sizeof(double);\n-\n- if((sizeof(double)* na==(size_t)stridea)&&\n- (sizeof(double)* nb==(size_t)strideb)&&\n- (na>=0)&&(nb>=0))\n- *((double *)res)=cblas_ddot((int)n,(double *)a,na,(double *)b,nb);\n- else\n- oldFunctions[NPY_DOUBLE](a,stridea,b,strideb,res,n,tmp);\n+ npy_intp na=stridea/(int)sizeof(double);\n+ npy_intp nb=strideb/(int)sizeof(double);\n+\n+ if(stridea>0&&strideb>0&&\n+ stridea==sizeof(double)* na&&\n+ strideb==sizeof(double)* nb){\n+ double r=0.;\n+ double * da=a,* db=b;\n+\n+ while(n>0){\n+ int chunk=MIN(n,CHUNKSIZE);\n+\n+ r+=cblas_ddot(chunk,da,na,db,nb);\n+ da+=chunk * na;\n+ db+=chunk * nb;\n+ n-=chunk;\n+ }\n+ *((double *)res)=r;\n+ }\n+ else{\n+ oldFunctions[NPY_DOUBLE](a,stridea,b,strideb,res,n,tmp);\n+ }\n}\n\nstatic void\nCFLOAT_dot(void * a,npy_intp stridea,void * b,npy_intp strideb,void * res,\nnpy_intp n,void * tmp)\n{\n+ npy_intp na=stridea/(int)sizeof(npy_cfloat);\n+ npy_intp nb=strideb/(int)sizeof(npy_cfloat);\n\n- register int na=stridea/sizeof(npy_cfloat);\n- register int nb=strideb/sizeof(npy_cfloat);\n-\n- if((sizeof(npy_cfloat)* na==(size_t)stridea)&&\n- (sizeof(npy_cfloat)* nb==(size_t)strideb)&&\n- (na>=0)&&(nb>=0))\n- cblas_cdotu_sub((int)n,(float *)a,na,(float *)b,nb,(float *)res);\n- else\n- oldFunctions[NPY_CFLOAT](a,stridea,b,strideb,res,n,tmp);\n+ if(stridea>0&&strideb>0&&n<=INT_MAX&&\n+ stridea==sizeof(npy_cfloat)* na&&\n+ strideb==sizeof(npy_cfloat)* nb){\n+ cblas_cdotu_sub((int)n,(float *)a,na,(float *)b,nb,(float *)res);\n+ }\n+ else{\n+ oldFunctions[NPY_CFLOAT](a,stridea,b,strideb,res,n,tmp);\n+ }\n}\n\nstatic void\nCDOUBLE_dot(void * a,npy_intp stridea,void * b,npy_intp strideb,void * res,\nnpy_intp n,void * tmp)\n{\n- register int na=stridea/sizeof(npy_cdouble);\n- register int nb=strideb/sizeof(npy_cdouble);\n-\n- if((sizeof(npy_cdouble)* na==(size_t)stridea)&&\n- (sizeof(npy_cdouble)* nb==(size_t)strideb)&&\n- (na>=0)&&(nb>=0))\n- cblas_zdotu_sub((int)n,(double *)a,na,(double *)b,nb,(double *)res);\n- else\n- oldFunctions[NPY_CDOUBLE](a,stridea,b,strideb,res,n,tmp);\n+ npy_intp na=stridea/(int)sizeof(npy_cdouble);\n+ npy_intp nb=strideb/(int)sizeof(npy_cdouble);\n+\n+ if(stridea>0&&strideb>0&&n<=INT_MAX&&\n+ stridea==sizeof(npy_cdouble)* na&&\n+ strideb==sizeof(npy_cdouble)* nb){\n+ cblas_zdotu_sub((int)n,(double *)a,na,(double *)b,nb,\n+ (double *)res);\n+ }\n+ else{\n+ oldFunctions[NPY_CDOUBLE](a,stridea,b,strideb,res,n,tmp);\n+ }\n}\n\n\n\n\npeft:1955\tAdded chunked BLAS dot product for large arrays.\n\n\nicl:1955\tMerge pull request from NelleV/doc_styling\n\n\nslm:Merge pull request from juliantaylor/fix - dot - function\n\n\n", "peft": "1955\tAdded chunked BLAS dot product for large arrays.\n", "icl": "1955\tMerge pull request from NelleV/doc_styling\n", "slm": "Merge pull request from juliantaylor/fix - dot - function\n", "nl": "BUG/ENH:blasdot:make * dot calls 64 - bit safe", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16785, "text": "\n\n# API calls to Windows Azure may be slow.For this reason,we cache the results\n# of an API call.Set this to the path you want cache files to be written to.\n- # One file will be written to this directory:\n+ # Two files will be written to this directory:\n#-ansible-azure.cache\n+ #-ansible-azure.index\n#\ncache_path=/tmp\n\ndef__init__(self):\nself.inventory={}\n# Index of deployment name->host\nself.index={}\n+ self.host_metadata={}\n+\n+ # Cache setting defaults.\n+ # These can be overridden in settings(see ` read_settings `).\n+ cache_dir=os.path.expanduser('~')\n+ self.cache_path_cache=os.path.join(cache_dir,'.ansible-azure.cache')\n+ self.cache_path_index=os.path.join(cache_dir,'.ansible-azure.index')\n+ self.cache_max_age=0\n\n# Read settings and parse CLI arguments\nself.read_settings()\ndef__init__(self):\n\nif self.args.list_images:\ndata_to_print=self.json_format_dict(self.get_images(),True)\n- elif self.args.list:\n+ elif self.args.list or self.args.host:\n# Display list of nodes for inventory\nif len(self.inventory)==0:\n- data_to_print=self.get_inventory_from_cache()\n+ data=json.loads(self.get_inventory_from_cache())\nelse:\n- data_to_print=self.json_format_dict(self.inventory,True)\n+ data=self.inventory\n\n+ if self.args.host:\n+ data_to_print=self.get_host(self.args.host)\n+ else:\n+ # Add the `['_meta']['hostvars']` information.\n+ hostvars={}\n+ if len(data)>0:\n+ for host in set([h for hosts in data.values()for h in hosts if h]):\n+ hostvars[host]=self.get_host(host,jsonify=False)\n+ data['_meta']={'hostvars':hostvars}\n+\n+ # JSONify the data.\n+ data_to_print=self.json_format_dict(data,pretty=True)\nprint data_to_print\n\n+ def get_host(self,hostname,jsonify=True):\n+ \"\"\"Return information about the given hostname,based on what\n+ the Windows Azure API provides.\n+ \"\"\"\n+ if hostname not in self.host_metadata:\n+ return\"No host found:%s\"%json.dumps(self.host_metadata)\n+ if jsonify:\n+ return json.dumps(self.host_metadata[hostname])\n+ return self.host_metadata[hostname]\n+\ndef get_images(self):\nimages=[]\nfor image in self.sms.list_os_images():\ndef read_settings(self):\n\n# Cache related\nif config.has_option('azure','cache_path'):\n- cache_path=config.get('azure','cache_path')\n- self.cache_path_cache=cache_path+\"/ansible-azure.cache\"\n- self.cache_path_index=cache_path+\"/ansible-azure.index\"\n+ cache_path=os.path.expandvars(os.path.expanduser(config.get('azure','cache_path')))\n+ self.cache_path_cache=os.path.join(cache_path,'ansible-azure.cache')\n+ self.cache_path_index=os.path.join(cache_path,'ansible-azure.index')\nif config.has_option('azure','cache_max_age'):\nself.cache_max_age=config.getint('azure','cache_max_age')\n\ndef read_environment(self):\n'''Reads the settings from environment variables'''\n# Credentials\n- if os.getenv(\"AZURE_SUBSCRIPTION_ID\"):self.subscription_id=os.getenv(\"AZURE_SUBSCRIPTION_ID\")\n- if os.getenv(\"AZURE_CERT_PATH\"):self.cert_path=os.getenv(\"AZURE_CERT_PATH\")\n-\n+ if os.getenv(\"AZURE_SUBSCRIPTION_ID\"):\n+ self.subscription_id=os.getenv(\"AZURE_SUBSCRIPTION_ID\")\n+ if os.getenv(\"AZURE_CERT_PATH\"):\n+ self.cert_path=os.getenv(\"AZURE_CERT_PATH\")\n\ndef parse_cli_args(self):\n\"\"\"Command line argument processing\"\"\"\n- parser=argparse.ArgumentParser(description='Produce an Ansible Inventory file based on Azure')\n+ parser=argparse.ArgumentParser(\n+ description='Produce an Ansible Inventory file based on Azure',\n+ )\nparser.add_argument('--list',action='store_true',default=True,\n- help='List nodes(default:True)')\n+ help='List nodes(default:True)')\nparser.add_argument('--list-images',action='store',\n- help='Get all available images.')\n- parser.add_argument('--refresh-cache',action='store_true',default=False,\n- help='Force refresh of cache by making API requests to Azure(default:False-use cache files)')\n+ help='Get all available images.')\n+ parser.add_argument('--refresh-cache',\n+ action='store_true',default=False,\n+ help='Force refresh of thecache by making API requests to Azure'\n+ '(default:False-use cache files)',\n+ )\n+ parser.add_argument('--host',action='store',\n+ help='Get all information about an instance.')\nself.args=parser.parse_args()\n\ndef do_api_calls_update_cache(self):\ndef add_cloud_services(self):\nsys.exit(1)\n\ndef add_deployments(self,cloud_service):\n- \"\"\"Makes an Azure API call to get the list of virtual machines associated with a cloud service\"\"\"\n+ \"\"\"Makes an Azure API call to get the list of virtual machines\n+ associated with a cloud service.\n+ \"\"\"\ntry:\nfor deployment in self.sms.get_hosted_service_properties(cloud_service.service_name,embed_detail=True).deployments.deployments:\n- if deployment.deployment_slot==\"Production\":\n- self.add_deployment(cloud_service,deployment)\n+ self.add_deployment(cloud_service,deployment)\nexcept WindowsAzureError as e:\nprint\"Looks like Azure's API is down:\"\nprint\ndef add_deployments(self,cloud_service):\n\ndef add_deployment(self,cloud_service,deployment):\n\"\"\"Adds a deployment to the inventory and index\"\"\"\n+ for role in deployment.role_instance_list.role_instances:\n+ try:\n+ # Default port 22 unless port found with name'SSH'\n+ port='22'\n+ for ie in role.instance_endpoints.instance_endpoints:\n+ if ie.name=='SSH':\n+ port=ie.public_port\n+ break\n+ except AttributeError as e:\n+ pass\n+ finally:\n+ self.add_instance(role.instance_name,deployment,port,cloud_service,role.instance_status)\n+\n+ def add_instance(self,hostname,deployment,ssh_port,cloud_service,status):\n+ \"\"\"Adds an instance to the inventory and index\"\"\"\n\ndest=urlparse(deployment.url).hostname\n\n# Add to index\n- self.index[dest]=deployment.name\n+ self.index[hostname]=deployment.name\n+\n+ self.host_metadata[hostname]=dict(ansible_ssh_host=dest,\n+ ansible_ssh_port=int(ssh_port),\n+ instance_status=status,\n+ private_id=deployment.private_id)\n\n# List of all azure deployments\n- self.push(self.inventory,\"azure\",dest)\n+ self.push(self.inventory,\"azure\",hostname)\n\n# Inventory:Group by service name\n- self.push(self.inventory,self.to_safe(cloud_service.service_name),dest)\n+ self.push(self.inventory,self.to_safe(cloud_service.service_name),hostname)\n+\n+ if int(ssh_port)==22:\n+ self.push(self.inventory,\"Cloud_services\",hostname)\n\n# Inventory:Group by region\n- self.push(self.inventory,self.to_safe(cloud_service.hosted_service_properties.location),dest)\n+ self.push(self.inventory,self.to_safe(cloud_service.hosted_service_properties.location),hostname)\n\ndef push(self,my_dict,key,element):\n\"\"\"Pushed an element onto an array that may not have been defined in the dict.\"\"\"\n\n\npeft:99\tAdded support for hostvars in azure inventory.\n\n\nicl:99\t[WIP]Create Azure inventory skeleton()\n\n\nslm:Add ansible - inventory - file option to the Azure inventory file\n\n\n", "peft": "99\tAdded support for hostvars in azure inventory.\n", "icl": "99\t[WIP]Create Azure inventory skeleton()\n", "slm": "Add ansible - inventory - file option to the Azure inventory file\n", "nl": "Merge pull request from cchurch/windows_azure_from_tower", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16786, "text": "var IPython=(function(IPython){\nvar reader=new FileReader();\nreader.readAsText(f);\nvar name_and_ext=utils.splitext(f.name);\n- var nbname=name_and_ext[0];\nvar file_ext=name_and_ext[1];\nif(file_ext==='.ipynb'){\nvar item=that.new_notebook_item(0);\n- that.add_name_input(nbname,item);\n+ that.add_name_input(f.name,item);\n//Store the notebook item in the reader so we can use it later\n//to know which item it belongs to.\n$(reader).data('item',item);\nvar IPython=(function(IPython){\nvar len=data.length;\nthis.clear_list();\nif(len===0){\n- var item=this.new_notebook_item(0);\n+ item=this.new_notebook_item(0);\nvar span12=item.children().first();\nspan12.empty();\n- span12.append($('<div style=\"margin:auto;text-align:center;color:grey\"/>').text(message))\n+ span12.append($('<div style=\"margin:auto;text-align:center;color:grey\"/>').text(message));\n}\nvar path=this.notebookPath();\nvar offset=0;\nvar IPython=(function(IPython){\nthis.add_dir(path,name,item);\n}else{\nvar name=data[i].name;\n- var nbname=utils.splitext(name)[0];\nitem=this.new_notebook_item(i+offset);\n- this.add_link(path,nbname,item);\n+ this.add_link(path,name,item);\nname=utils.url_path_join(path,name);\nif(this.sessions[name]===undefined){\nthis.add_delete_button(item);\nvar IPython=(function(IPython){\nNotebookList.prototype.add_link=function(path,nbname,item){\nitem.data('nbname',nbname);\nitem.data('path',path);\n- item.find(\".item_name\").text(nbname+'.ipynb');\n+ item.find(\".item_name\").text(nbname);\nitem.find(\".item_icon\").addClass('icon-book');\nitem.find(\"a.item_link\")\n.attr('href',\nvar IPython=(function(IPython){\nthis.baseProjectUrl(),\n\"notebooks\",\npath,\n- nbname+\".ipynb\"\n+ nbname\n)\n).attr('target','_blank');\n};\nvar IPython=(function(IPython){\nitem.find(\".item_name\").empty().append(\n$('<input/>')\n.addClass(\"nbname_input\")\n- .attr('value',nbname)\n+ .attr('value',utils.splitext(nbname)[0])\n.attr('size','30')\n.attr('type','text')\n);\nvar IPython=(function(IPython){\nnotebooklist.baseProjectUrl(),\n'api/notebooks',\nnotebooklist.notebookPath(),\n- nbname+'.ipynb'\n+ nbname\n);\n$.ajax(url,settings);\n}\nvar IPython=(function(IPython){\n.addClass('btn btn-primary btn-mini upload_button')\n.click(function(e){\nvar nbname=item.find('.item_name>input').val();\n+ if(nbname.slice(nbname.length-6,nbname.length)!=\".ipynb\"){\n+ nbname=nbname+\".ipynb\";\n+ }\nvar path=that.notebookPath();\nvar nbdata=item.data('nbdata');\nvar content_type='application/json';\nvar IPython=(function(IPython){\nthat.baseProjectUrl(),\n'api/notebooks',\nthat.notebookPath(),\n- nbname+'.ipynb'\n+ nbname\n);\n$.ajax(url,settings);\nreturn false;\n\n\npeft:287\tAdded the nbname to the notebook list item.\n\n\nicl:287\tFix the bug of uploading notebooks with wrong name.\n\n\nslm:Merge pull request from jdfreder/readme\n\n\n", "peft": "287\tAdded the nbname to the notebook list item.\n", "icl": "287\tFix the bug of uploading notebooks with wrong name.\n", "slm": "Merge pull request from jdfreder/readme\n", "nl": "don't strip'.ipynb'from notebook names in nblist", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16787, "text": "def add_widget(child):\n\nself.window.set_default_size(w,h)\n\n+ self._destroying=False\nself.window.connect(\"destroy\",lambda * args:Gcf.destroy(self))\nself.window.connect(\"delete_event\",lambda * args:Gcf.destroy(self))\nif mpl.is_interactive():\ndef add_widget(child):\nself.canvas.grab_focus()\n\ndef destroy(self,* args):\n+ if self._destroying:\n+ # Otherwise,this can be called twice when the user presses'q',\n+ # which calls Gcf.destroy(self),then this destroy(),then triggers\n+ # Gcf.destroy(self)once again via\n+ # ` connect(\"destroy\",lambda * args:Gcf.destroy(self))`.\n+ return\n+ self._destroying=True\nself.vbox.destroy()\nself.window.destroy()\nself.canvas.destroy()\ndef_onClose(self,event):\nself.canvas.close_event()\nself.canvas.stop_event_loop()\nGcf.destroy(self)\n- # self.Destroy()\n+ if self:\n+ self.Destroy()\n\ndef GetToolBar(self):\n\"\"\"Override wxFrame::GetToolBar as we don't have managed toolbar\"\"\"\ndef show(self):\n\ndef destroy(self,* args):\n_log.debug(\"%s-destroy()\",type(self))\n- self.frame.Destroy()\n+ self.frame.Close()\nwxapp=wx.GetApp()\nif wxapp:\nwxapp.Yield()\ndef check_alt_backend(alt_backend):\ntimer.add_callback(FigureCanvasBase.key_press_event,fig.canvas,\"q\")\n# Trigger quitting upon draw.\nfig.canvas.mpl_connect(\"draw_event\",lambda event:timer.start())\n+ fig.canvas.mpl_connect(\"close_event\",print)\n\nplt.show()\n\"\"\"\ndef check_alt_backend(alt_backend):\<EMAIL>(\"backend\",_get_testable_interactive_backends())\<EMAIL>(reruns=3)\ndef test_interactive_backend(backend):\n- proc=subprocess.run([sys.executable,\"-c\",_test_script],\n- env={* * os.environ,\"MPLBACKEND\":backend},\n- timeout=_test_timeout)\n+ proc=subprocess.run(\n+ [sys.executable,\"-c\",_test_script],\n+ env={* * os.environ,\"MPLBACKEND\":backend},timeout=_test_timeout,\n+ stdout=subprocess.PIPE,universal_newlines=True)\nif proc.returncode:\npytest.fail(\"The subprocess returned with non-zero exit status\"\nf\"{proc.returncode}.\")\n+ assert proc.stdout.count(\"CloseEvent\")==1\n\n\<EMAIL>('SYSTEM_TEAMFOUNDATIONCOLLECTIONURI'in os.environ,\n\n\npeft:1755\tFix:mpl.backends.backend_wx.FigureCanvasWxAgg.destroy()\n\n\nicl:1755\tBackport PR:Doc:Add a note about the use of the `mpl_connect` method\n\n\nslm:Fix memory leak in wx backend destroy.Closes\n\n\n", "peft": "1755\tFix:mpl.backends.backend_wx.FigureCanvasWxAgg.destroy()\n", "icl": "1755\tBackport PR:Doc:Add a note about the use of the `mpl_connect` method\n", "slm": "Fix memory leak in wx backend destroy.Closes\n", "nl": "Merge pull request from anntzer/closeevent", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16788, "text": "def test_formatting(self):\nstring_tests.MixinStrUnicodeUserStringTest.test_formatting(self)\nself.assertRaises(OverflowError,'%c'.__mod__,0x1234)\n\n+ def test_iterators(self):\n+ # Make sure str objects have an__iter__method\n+ it=\"abc\".__iter__()\n+ self.assertEqual(it.next(),\"a\")\n+ self.assertEqual(it.next(),\"b\")\n+ self.assertEqual(it.next(),\"c\")\n+ self.assertRaises(StopIteration,it.next)\n+\ndef test_conversion(self):\n# Make sure__str__()behaves properly\nclass Foo0:\ndef test_repr(self):\ntestrepr=repr(u''.join(map(unichr,xrange(256))))\nself.assertEqual(testrepr,latin1repr)\n\n+ def test_iterators(self):\n+ # Make sure unicode objects have an__iter__method\n+ it=u\"\\u1111\\u2222\\u3333\".__iter__()\n+ self.assertEqual(it.next(),u\"\\u1111\")\n+ self.assertEqual(it.next(),u\"\\u2222\")\n+ self.assertEqual(it.next(),u\"\\u3333\")\n+ self.assertRaises(StopIteration,it.next)\n+\ndef test_count(self):\nstring_tests.CommonTest.test_count(self)\n# check mixed argument types\nvoid_Py_ReleaseInternedStrings(void)\n\ntypedef struct{\nPyObject_HEAD\n- long it_index;\n+ Py_ssize_t it_index;\nPyStringObject * it_seq;/* Set to NULL when iterator is exhausted */\n}striterobject;\n\nstriter_next(striterobject * it)\nassert(PyString_Check(seq));\n\nif(it->it_index<PyString_GET_SIZE(seq)){\n- item=PyString_FromStringAndSize(PyString_AS_STRING(seq)+it->it_index,1);\n+ item=PyString_FromStringAndSize(\n+ PyString_AS_STRING(seq)+it->it_index,1);\nif(item!=NULL)\n+ +it->it_index;\nreturn item;\nstriter_len(striterobject * it)\nreturn PyInt_FromSsize_t(len);\n}\n\n- PyDoc_STRVAR(length_hint_doc,\"Private method returning an estimate of len(list(it)).\");\n+ PyDoc_STRVAR(length_hint_doc,\n+ \"Private method returning an estimate of len(list(it)).\");\n\nstatic PyMethodDef striter_methods[]={\n- {\"__length_hint__\",(PyCFunction)striter_len,METH_NOARGS,length_hint_doc},\n+ {\"__length_hint__\",(PyCFunction)striter_len,METH_NOARGS,\n+ length_hint_doc},\n{NULL,NULL}/* sentinel */\n};\n\nPyTypeObject PyStringIter_Type={\nPyObject_HEAD_INIT(&PyType_Type)\n0,/* ob_size */\n- \"striterator\",/* tp_name */\n- sizeof(striterobject),/* tp_basicsize */\n+ \"striterator\",/* tp_name */\n+ sizeof(striterobject),/* tp_basicsize */\n0,/* tp_itemsize */\n/* methods */\n(destructor)striter_dealloc,/* tp_dealloc */\n_PyUnicode_Fini(void)\n\ntypedef struct{\nPyObject_HEAD\n- long it_index;\n+ Py_ssize_t it_index;\nPyUnicodeObject * it_seq;/* Set to NULL when iterator is exhausted */\n}unicodeiterobject;\n\nunicodeiter_next(unicodeiterobject * it)\nassert(PyUnicode_Check(seq));\n\nif(it->it_index<PyUnicode_GET_SIZE(seq)){\n- item=PyUnicode_FromUnicode(PyUnicode_AS_UNICODE(seq)+it->it_index,1);\n+ item=PyUnicode_FromUnicode(\n+ PyUnicode_AS_UNICODE(seq)+it->it_index,1);\nif(item!=NULL)\n+ +it->it_index;\nreturn item;\nunicodeiter_len(unicodeiterobject * it)\nPyDoc_STRVAR(length_hint_doc,\"Private method returning an estimate of len(list(it)).\");\n\nstatic PyMethodDef unicodeiter_methods[]={\n- {\"__length_hint__\",(PyCFunction)unicodeiter_len,METH_NOARGS,length_hint_doc},\n+ {\"__length_hint__\",(PyCFunction)unicodeiter_len,METH_NOARGS,\n+ length_hint_doc},\n{NULL,NULL}/* sentinel */\n};\n\nPyTypeObject PyUnicodeIter_Type={\nsizeof(unicodeiterobject),/* tp_basicsize */\n0,/* tp_itemsize */\n/* methods */\n- (destructor)unicodeiter_dealloc,/* tp_dealloc */\n+ (destructor)unicodeiter_dealloc,/* tp_dealloc */\n0,/* tp_print */\n0,/* tp_getattr */\n0,/* tp_setattr */\n\n\npeft:1188\tAdd test_iterators()to test_stringobject.c.\n\n\nicl:1188\tMerge pull request from NelleV/doc_styling\n\n\nslm:Rewrite striter to support new string API.\n\n\n", "peft": "1188\tAdd test_iterators()to test_stringobject.c.\n", "icl": "1188\tMerge pull request from NelleV/doc_styling\n", "slm": "Rewrite striter to support new string API.\n", "nl": "Make the it_index field in the str/unicode iterators Py_ssize_t's.", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16789, "text": "environment:\n\nmatrix:\n\n+ -PYTHON:\"C:\\\\Python27-x64\"\n+ PYTHON_VERSION:\"2.7.x\"\n+ PYTHON_ARCH:\"64\"\n+ TOXENV:\"py27\"\n+\n+ -PYTHON:\"C:\\\\Python34-x64\"\n+ PYTHON_VERSION:\"3.4.x\"\n+ PYTHON_ARCH:\"64\"\n+ TOXENV:\"py34\"\n+\n+ -PYTHON:\"C:\\\\Python35-x64\"\n+ PYTHON_VERSION:\"3.5.x\"\n+ PYTHON_ARCH:\"64\"\n+ TOXENV:\"py35\"\n+\n- PYTHON:\"C:\\\\Python36-x64\"\nPYTHON_VERSION:\"3.6.x\"\nPYTHON_ARCH:\"64\"\ndef python_version(path_to_python):\n\ntry:\nTEMPLATE='Python{}.{}.{}'\n- c=delegator.run('{0}--version'.format(path_to_python),block=False)\n+ c=delegator.run([path_to_python,'--version'],block=False)\nc.return_code==0\nexcept Exception:\nreturn None\ndef test_requirements_to_pipfile(self):\nwith open('requirements.txt','w')as f:\nf.write('requests[socks]==2.18.1\\n'\n'git+https://github.com/kennethreitz/records.git@v0.5.0 # egg=records\\n'\n- '-e git+https://github.com/kennethreitz/tablib.git@v0.11.5 # egg=tablib\\n'\n+ '-e git+https://github.com/kennethreitz/maya.git@v0.3.2 # egg=maya\\n'\n'six==1.10.0\\n')\n\n- print(c.err)\n- assert c.return_code==0\n-\n+ assert delegator.run('pipenv install').return_code==0\nprint(delegator.run('pipenv lock').err)\nassert delegator.run('pipenv lock').return_code==0\n\ndef test_requirements_to_pipfile(self):\nlockfile_output=delegator.run('type Pipfile.lock').out\n\n# Ensure extras work.\n- assert'extras=[\"socks\",]'in pipfile_output\n+ assert'socks'in pipfile_output\nassert'pysocks'in lockfile_output\n\n# Ensure vcs dependencies work.\n- assert'packages.records'in pipfile_output\n+ assert'records'in pipfile_output\nassert'\"git\":\"https://github.com/kennethreitz/records.git\"'in lockfile_output\n\n# Ensure editable packages work.\n- assert'ref=\"v0.11.5\"'in pipfile_output\n+ assert'ref=\"v0.3.2\"'in pipfile_output\nassert'\"editable\":true'in lockfile_output\n\n# Ensure BAD_PACKAGES aren't copied into Pipfile from requirements.txt.\nassert'six=\"==1.10.0\"'not in pipfile_output\n\nos.chdir('..')\n- shutil.rmtree('test_requirements_to_pip')\n+ # shutil.rmtree('test_requirements_to_pip')\ndel os.environ['PIPENV_MAX_DEPTH']\n\ndef test_timeout_long(self):\ndef test_timeout_short(self):\n\nassert delegator.run('copy/y nul Pipfile').return_code==0\n\n-\nos.chdir('..')\nshutil.rmtree('test_timeout_short')\ndel os.environ['PIPENV_TIMEOUT']\ndef test_pipenv_uninstall(self):\n# Build the environment.\nos.environ['PIPENV_VENV_IN_PROJECT']='1'\nassert delegator.run('copy/y nul Pipfile').return_code==0\n+ assert delegator.run('pipenv install').return_code==0\n\n# Add entries to Pipfile.\nassert delegator.run('pipenv install Werkzeug').return_code==0\ndef test_pipenv_uninstall(self):\nassert'Werkzeug=\"*\"'in pipfile_list\nassert'pytest=\"*\"'not in pipfile_list\nassert'[packages]'in pipfile_list\n- assert'[dev-packages]'not in pipfile_list\n+ # assert'[dev-packages]'not in pipfile_list\n\nos.chdir('..')\nshutil.rmtree('test_pipenv_uninstall')\n\n\npeft:569\tMerge pull request from kennethreitz/fix - pipenv - requirements - to - pipfile\n\n\nicl:569\t[WIP]Create OAuth CS skeleton()\n\n\nslm:revert\n\n\n", "peft": "569\tMerge pull request from kennethreitz/fix - pipenv - requirements - to - pipfile\n", "icl": "569\t[WIP]Create OAuth CS skeleton()\n", "slm": "revert\n", "nl": "Merge pull request from kennethreitz/windows - tests", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16790, "text": "be used.The default is ` ` False ` `,but this setting as ` ` True ` ` uses more stric\n\n.._math_tests:\n\n- Group theory tests\n- ` ` ` ` ` ` ` ` ` ` ` ` ` ` ` ` ` `\n+ Set theory tests\n+ ` ` ` ` ` ` ` ` ` ` ` ` ` ` ` `\n\n..versionadded::2.1\n\n\n\npeft:1981\tMerge pull request from jabberwocky/set_theory_tests\n\n\nicl:1981\tMerge pull request from eric - wieser/basics - docs\n\n\nslm:Update README.rst\n\n\n", "peft": "1981\tMerge pull request from jabberwocky/set_theory_tests\n", "icl": "1981\tMerge pull request from eric - wieser/basics - docs\n", "slm": "Update README.rst\n", "nl": "Describe subset and superset as\"set theory tests\"()", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16791, "text": "\n\nclass SBSIE(InfoExtractor):\nIE_DESC='sbs.com.au'\n- _VALID_URL=r'https?://(?:www\\.)?sbs\\.com\\.au/ondemand/video/single/(?P<id>[0-9]+)/'\n+ _VALID_URL=r'https?://(?:www\\.)?sbs\\.com\\.au/ondemand/video/(?:single/)?(?P<id>[0-9]+)'\n\n_TESTS=[{\n# Original URL is handled by the generic IE which finds the iframe:\nclass SBSIE(InfoExtractor):\n'thumbnail':'re:http://.*\\.jpg',\n},\n'add_ies':['generic'],\n+ },\n+ {\n+ 'url':'http://www.sbs.com.au/ondemand/video/320403011771/Dingo-Conservation-The-Feed',\n+ 'only_matching':True,\n}]\n\ndef_real_extract(self,url):\n\n\npeft:1596\t[sbs]Fix URL pattern\n\n\nicl:1596\tSBSIE:fix url matching()\n\n\nslm:[sbs]Add support for single URLs\n\n\n", "peft": "1596\t[sbs]Fix URL pattern\n", "icl": "1596\tSBSIE:fix url matching()\n", "slm": "[sbs]Add support for single URLs\n", "nl": "[sbs]Recognize urls with format'http://www.sbs.com.au/ondemand/video/<id>'()", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16792, "text": "var timerender=require('js/timerender.js');\ntwenty_four_hour_time:false,\n});\n\n- //timestamp with hour>12\n+ //timestamp with hour>12,same year\nvar timestamp=1555091573000;//4/12/2019 5:52:53 PM(UTC+0)\n- var expected='Apr 12,2019 05:52 PM';\n- var actual=timerender.absolute_time(timestamp);\n+ var today=new Date(timestamp);\n+ var expected='Apr 12 05:52 PM';\n+ var actual=timerender.absolute_time(timestamp,today);\nassert.equal(expected,actual);\n\n- //timestamp with hour<12\n+ //timestamp with hour>12,different year\n+ today.setFullYear(today.getFullYear()+1);\n+ expected='Apr 12,2019 05:52 PM';\n+ actual=timerender.absolute_time(timestamp,today);\n+ assert.equal(expected,actual);\n+\n+ //timestamp with hour<12,same year\ntimestamp=1495091573000;//5/18/2017 7:12:53 AM(UTC+0)\n+ today=new Date(timestamp);\n+ expected='May 18 07:12 AM';\n+ actual=timerender.absolute_time(timestamp,today);\n+ assert.equal(expected,actual);\n+\n+ //timestamp with hour<12,different year\n+ today.setFullYear(today.getFullYear()+1);\nexpected='May 18,2017 07:12 AM';\n- actual=timerender.absolute_time(timestamp);\n+ actual=timerender.absolute_time(timestamp,today);\nassert.equal(expected,actual);\n}());\n\nvar timerender=require('js/timerender.js');\ntwenty_four_hour_time:true,\n});\n\n- //timestamp with hour>12\n- var timestamp=1555091573000;//4/12/2019 17:52:53(UTC+0)\n- var expected='Apr 12,2019 17:52';\n- var actual=timerender.absolute_time(timestamp);\n+ //timestamp with hour>12,same year\n+ var timestamp=1555091573000;//4/12/2019 5:52:53 PM(UTC+0)\n+ var today=new Date(timestamp);\n+ var expected='Apr 12 17:52';\n+ var actual=timerender.absolute_time(timestamp,today);\nassert.equal(expected,actual);\n\n- //timestamp with hour<12\n+ //timestamp with hour>12,different year\n+ today.setFullYear(today.getFullYear()+1);\n+ expected='Apr 12,2019 17:52';\n+ actual=timerender.absolute_time(timestamp,today);\n+ assert.equal(expected,actual);\n+\n+ //timestamp with hour<12,same year\ntimestamp=1495091573000;//5/18/2017 7:12:53 AM(UTC+0)\n+ today=new Date(timestamp);\n+ expected='May 18 07:12';\n+ actual=timerender.absolute_time(timestamp,today);\n+ assert.equal(expected,actual);\n+\n+ //timestamp with hour<12,different year\n+ today.setFullYear(today.getFullYear()+1);\nexpected='May 18,2017 07:12';\n- actual=timerender.absolute_time(timestamp);\n+ actual=timerender.absolute_time(timestamp,today);\nassert.equal(expected,actual);\n}());\n\nexports.absolute_time=(function(){\nreturn str;\n};\n\n- return function(timestamp){\n+ return function(timestamp,today){\n+ if(typeof today==='undefined'){\n+ today=new Date();\n+ }\nvar date=new Date(timestamp);\n+ var is_older_year=(today.getFullYear()-date.getFullYear())>0;\nvar H_24=page_params.twenty_four_hour_time;\n-\n- return MONTHS[date.getMonth()]+\"\"+date.getDate()+\",\"+date.getFullYear()+\"\"+fmt_time(date,H_24);\n+ var str=MONTHS[date.getMonth()]+\"\"+date.getDate();\n+ //include year if message date is from a previous year\n+ if(is_older_year){\n+ str+=\",\"+date.getFullYear();\n+ }\n+ str+=\"\"+fmt_time(date,H_24);\n+ return str;\n};\n}());\n\n\n\npeft:1232\tAdded tests for absolute_time()\n\n\nicl:1232\tAnswer:\n\n\nslm:tests:Update twenty_four_hour_time.js.\n\n\n", "peft": "1232\tAdded tests for absolute_time()\n", "icl": "1232\tAnswer:\n", "slm": "tests:Update twenty_four_hour_time.js.\n", "nl": "uploads:Only display year uploaded if previous year.", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16793, "text": "def bxp(self,bxpstats,positions=None,widths=None,vert=True,\n- ` ` q1 ` `:The first quartile(25th percentile)(scalar\nfloat).\n\n- -` ` q3 ` `:The first quartile(50th percentile)(scalar\n+ -` ` q3 ` `:The third quartile(75th percentile)(scalar\nfloat).\n\n- ` ` whislo ` `:Lower bound of the lower whisker(scalar\n\n\npeft:1580\tMerge pull request from jakevdp/patch - 1\n\n\nicl:1580\tMerge pull request from eric - wieser/fix - bxp - docstring\n\n\nslm:DOC:Fix typo in whisker Quartile docstring.\n\n\n", "peft": "1580\tMerge pull request from jakevdp/patch - 1\n", "icl": "1580\tMerge pull request from eric - wieser/fix - bxp - docstring\n", "slm": "DOC:Fix typo in whisker Quartile docstring.\n", "nl": "Merge pull request from ischwabacher/patch - 1", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16794, "text": "Performance Improvements\n\nBug Fixes\n~~~~~~~~~\n- -Bug in:func:` to_datetime ` which would raise an(incorrect)` ` ValueError ` ` when called with a date far into the future and the ` ` format ` ` argument specified instead of raising ` ` OutOfBoundsDatetime ` `(:issue:` 23830 `)\n- -Bug in an error message in:meth:` DataFrame.plot `.Improved the error message if non-numerics are passed to:meth:` DataFrame.plot `(:issue:` 25481 `)\n- -Bug in error messages in:meth:` DataFrame.corr ` and:meth:` Series.corr `.Added the possibility of using a callable.(:issue:` 25729 `)\n\nCategorical\n^^^^^^^^^^^\nCategorical\nDatetimelike\n^^^^^^^^^^^^\n\n+ -Bug in:func:` to_datetime ` which would raise an(incorrect)` ` ValueError ` ` when called with a date far into the future and the ` ` format ` ` argument specified instead of raising ` ` OutOfBoundsDatetime ` `(:issue:` 23830 `)\n-\n-\n-\nNumeric\n\n- Bug in:meth:` to_numeric ` in which large negative numbers were being improperly handled(:issue:` 24910 `)\n- Bug in:meth:` to_numeric ` in which numbers were being coerced to float,even though ` ` errors ` ` was not ` ` coerce ` `(:issue:` 24910 `)\n+ -Bug in error messages in:meth:` DataFrame.corr ` and:meth:` Series.corr `.Added the possibility of using a callable.(:issue:` 25729 `)\n-\n-\n-\nPlotting\n^^^^^^^^\n\n- Fixed bug where:class:` api.extensions.ExtensionArray ` could not be used in matplotlib plotting(:issue:` 25587 `)\n+ -Bug in an error message in:meth:` DataFrame.plot `.Improved the error message if non-numerics are passed to:meth:` DataFrame.plot `(:issue:` 25481 `)\n-\n-\n-\n\n\npeft:461\tMerge pull request from pandas/fixes - 25730\n\n\nicl:461\tMerge pull request from eric - wieser/fix - docs\n\n\nslm:DOC:whatsnew edits\n\n\n", "peft": "461\tMerge pull request from pandas/fixes - 25730\n", "icl": "461\tMerge pull request from eric - wieser/fix - docs\n", "slm": "DOC:whatsnew edits\n", "nl": "DOC:clean bug fix section in whatsnew()", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16795, "text": "changes,you can write something like the following::\n>>>plt.show()\n\n\n- .._matplotlibrc:http://matplotlib.sourceforge.net/users/customizing.html\n+ .._matplotlibrc:http://matplotlib.org/users/customizing.html\n.._ggplot:http://had.co.nz/ggplot/\n.._R:http://www.r-project.org/\n\n\npeft:722\tUpdate matplotlib link in plotting.rst\n\n\nicl:722\tMerge pull request from jeannefukumaru/update_matplotlib_link\n\n\nslm:Merge pull request from sieben/typo\n\n\n", "peft": "722\tUpdate matplotlib link in plotting.rst\n", "icl": "722\tMerge pull request from jeannefukumaru/update_matplotlib_link\n", "slm": "Merge pull request from sieben/typo\n", "nl": "Directly link matplotlib.org and not sourceforge.net", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16796, "text": "\n\"\"\"Support for deCONZ covers.\"\"\"\nfrom homeassistant.components.cover import(\nATTR_POSITION,\n+ ATTR_TILT_POSITION,\nDEVICE_CLASS_WINDOW,\nDOMAIN,\nSUPPORT_CLOSE,\n+ SUPPORT_CLOSE_TILT,\nSUPPORT_OPEN,\n+ SUPPORT_OPEN_TILT,\nSUPPORT_SET_POSITION,\n+ SUPPORT_SET_TILT_POSITION,\nSUPPORT_STOP,\n+ SUPPORT_STOP_TILT,\nCoverEntity,\n)\nfrom homeassistant.core import callback\ndef__init__(self,device,gateway):\nself._features|=SUPPORT_STOP\nself._features|=SUPPORT_SET_POSITION\n\n- @property\n- def current_cover_position(self):\n- \"\"\"Return the current position of the cover.\"\"\"\n- return 100-self._device.position\n+ if self._device.tilt is not None:\n+ self._features|=SUPPORT_OPEN_TILT\n+ self._features|=SUPPORT_CLOSE_TILT\n+ self._features|=SUPPORT_STOP_TILT\n+ self._features|=SUPPORT_SET_TILT_POSITION\n\n@property\n- def is_closed(self):\n- \"\"\"Return if the cover is closed.\"\"\"\n- return not self._device.is_open\n+ def supported_features(self):\n+ \"\"\"Flag supported features.\"\"\"\n+ return self._features\n\n@property\ndef device_class(self):\ndef device_class(self):\nreturn DEVICE_CLASS_WINDOW\n\n@property\n- def supported_features(self):\n- \"\"\"Flag supported features.\"\"\"\n- return self._features\n+ def current_cover_position(self):\n+ \"\"\"Return the current position of the cover.\"\"\"\n+ return 100-self._device.lift\n+\n+ @property\n+ def is_closed(self):\n+ \"\"\"Return if the cover is closed.\"\"\"\n+ return not self._device.is_open\n\nasync def async_set_cover_position(self,* * kwargs):\n\"\"\"Move the cover to a specific position.\"\"\"\nposition=100-kwargs[ATTR_POSITION]\n- await self._device.set_position(position)\n+ await self._device.set_position(lift=position)\n\nasync def async_open_cover(self,* * kwargs):\n\"\"\"Open cover.\"\"\"\nasync def async_close_cover(self,* * kwargs):\nasync def async_stop_cover(self,* * kwargs):\n\"\"\"Stop cover.\"\"\"\nawait self._device.stop()\n+\n+ @property\n+ def current_cover_tilt_position(self):\n+ \"\"\"Return the current tilt position of the cover.\"\"\"\n+ if self._device.tilt is not None:\n+ return 100-self._device.tilt\n+\n+ async def async_set_cover_tilt_position(self,* * kwargs):\n+ \"\"\"Tilt the cover to a specific position.\"\"\"\n+ position=100-kwargs[ATTR_TILT_POSITION]\n+ await self._device.set_position(tilt=position)\n+\n+ async def async_open_cover_tilt(self,* * kwargs):\n+ \"\"\"Open cover tilt.\"\"\"\n+ await self._device.set_position(tilt=0)\n+\n+ async def async_close_cover_tilt(self,* * kwargs):\n+ \"\"\"Close cover tilt.\"\"\"\n+ await self._device.set_position(tilt=100)\n+\n+ async def async_stop_cover_tilt(self,* * kwargs):\n+ \"\"\"Stop cover tilt.\"\"\"\n+ await self._device.stop()\n\n\"name\":\"deCONZ\",\n\"config_flow\":true,\n\"documentation\":\"https://www.home-assistant.io/integrations/deconz\",\n- \"requirements\":[\"pydeconz==74\"],\n+ \"requirements\":[\"pydeconz==75\"],\n\"ssdp\":[\n{\n\"manufacturer\":\"Royal Philips Electronics\"\npydaikin==2.3.1\npydanfossair==0.1.0\n\n# homeassistant.components.deconz\n- pydeconz==74\n+ pydeconz==75\n\n# homeassistant.components.delijn\npydelijn==0.6.1\npycountry==19.8.18\npydaikin==2.3.1\n\n# homeassistant.components.deconz\n- pydeconz==74\n+ pydeconz==75\n\n# homeassistant.components.dexcom\npydexcom==0.2.0\n\nfrom copy import deepcopy\n\nfrom homeassistant.components.cover import(\n+ ATTR_CURRENT_TILT_POSITION,\nATTR_POSITION,\n+ ATTR_TILT_POSITION,\nDOMAIN as COVER_DOMAIN,\nSERVICE_CLOSE_COVER,\n+ SERVICE_CLOSE_COVER_TILT,\nSERVICE_OPEN_COVER,\n+ SERVICE_OPEN_COVER_TILT,\nSERVICE_SET_COVER_POSITION,\n+ SERVICE_SET_COVER_TILT_POSITION,\nSERVICE_STOP_COVER,\n+ SERVICE_STOP_COVER_TILT,\n)\nfrom homeassistant.components.deconz.const import DOMAIN as DECONZ_DOMAIN\nfrom homeassistant.components.deconz.gateway import get_gateway_from_config_entry\n\n\"id\":\"deconz old brightness cover id\",\n\"name\":\"deconz old brightness cover\",\n\"type\":\"Level controllable output\",\n- \"state\":{\"bri\":254,\"on\":False,\"reachable\":True},\n+ \"state\":{\"bri\":255,\"on\":False,\"reachable\":True},\n\"modelid\":\"Not zigbee spec\",\n\"uniqueid\":\"00:00:00:00:00:00:00:03-00\",\n},\nasync def test_cover(hass):\nblocking=True,\n)\nawait hass.async_block_till_done()\n- set_callback.assert_called_with(\"put\",\"/lights/2/state\",json={\"bri_inc\":0})\n+ set_callback.assert_called_with(\"put\",\"/lights/2/state\",json={\"stop\":True})\n\n# Verify service calls for legacy cover\n\nasync def test_cover(hass):\nawait hass.config_entries.async_unload(config_entry.entry_id)\n\nassert len(hass.states.async_all())==0\n+\n+\n+ async def test_tilt_cover(hass):\n+ \"\"\"Test that tilting a cover works.\"\"\"\n+ data=deepcopy(DECONZ_WEB_REQUEST)\n+ data[\"lights\"]={\n+ \"0\":{\n+ \"etag\":\"87269755b9b3a046485fdae8d96b252c\",\n+ \"lastannounced\":None,\n+ \"lastseen\":\"2020-08-01T16:22:05Z\",\n+ \"manufacturername\":\"AXIS\",\n+ \"modelid\":\"Gear\",\n+ \"name\":\"Covering device\",\n+ \"state\":{\n+ \"bri\":0,\n+ \"lift\":0,\n+ \"on\":False,\n+ \"open\":True,\n+ \"reachable\":True,\n+ \"tilt\":0,\n+ },\n+ \"swversion\":\"100-5.3.5.1122\",\n+ \"type\":\"Window covering device\",\n+ \"uniqueid\":\"00:24:46:00:00:12:34:56-01\",\n+ }\n+ }\n+ config_entry=await setup_deconz_integration(hass,get_state_response=data)\n+ gateway=get_gateway_from_config_entry(hass,config_entry)\n+\n+ assert len(hass.states.async_all())==1\n+ entity=hass.states.get(\"cover.covering_device\")\n+ assert entity.state==STATE_OPEN\n+ assert entity.attributes[ATTR_CURRENT_TILT_POSITION]==100\n+\n+ covering_device=gateway.api.lights[\"0\"]\n+\n+ with patch.object(covering_device,\"_request\",return_value=True)as set_callback:\n+ await hass.services.async_call(\n+ COVER_DOMAIN,\n+ SERVICE_SET_COVER_TILT_POSITION,\n+ {ATTR_ENTITY_ID:\"cover.covering_device\",ATTR_TILT_POSITION:40},\n+ blocking=True,\n+ )\n+ await hass.async_block_till_done()\n+ set_callback.assert_called_with(\"put\",\"/lights/0/state\",json={\"tilt\":60})\n+\n+ with patch.object(covering_device,\"_request\",return_value=True)as set_callback:\n+ await hass.services.async_call(\n+ COVER_DOMAIN,\n+ SERVICE_OPEN_COVER_TILT,\n+ {ATTR_ENTITY_ID:\"cover.covering_device\"},\n+ blocking=True,\n+ )\n+ await hass.async_block_till_done()\n+ set_callback.assert_called_with(\"put\",\"/lights/0/state\",json={\"tilt\":0})\n+\n+ with patch.object(covering_device,\"_request\",return_value=True)as set_callback:\n+ await hass.services.async_call(\n+ COVER_DOMAIN,\n+ SERVICE_CLOSE_COVER_TILT,\n+ {ATTR_ENTITY_ID:\"cover.covering_device\"},\n+ blocking=True,\n+ )\n+ await hass.async_block_till_done()\n+ set_callback.assert_called_with(\"put\",\"/lights/0/state\",json={\"tilt\":100})\n+\n+ # Service stop cover movement\n+\n+ with patch.object(covering_device,\"_request\",return_value=True)as set_callback:\n+ await hass.services.async_call(\n+ COVER_DOMAIN,\n+ SERVICE_STOP_COVER_TILT,\n+ {ATTR_ENTITY_ID:\"cover.covering_device\"},\n+ blocking=True,\n+ )\n+ await hass.async_block_till_done()\n+ set_callback.assert_called_with(\"put\",\"/lights/0/state\",json={\"stop\":True})\n\n\npeft:68\tdeCONZ:Add support for cover tilt()\n\n\nicl:68\t# Answer:\n\n\nslm:Update deCONZ()\n\n\n", "peft": "68\tdeCONZ:Add support for cover tilt()\n", "icl": "68\t# Answer:\n", "slm": "Update deCONZ()\n", "nl": "Add tilt support to deCONZ covers()", "lang": "python", "label": ["Doc&Code"], "Comments": []}
{"id": 16797, "text": "\n+ #-*-coding:utf-8-*-\n\"\"\"Allow cookiecutter to be executable from a checkout or zip file.\"\"\"\nimport runpy\n\n\n+ #-*-coding:utf-8-*-\n\"\"\"Allow cookiecutter to be executable through ` python-m cookiecutter `.\"\"\"\nfrom__future__import absolute_import\n\n\n#!/usr/bin/env python\n+ #-*-coding:utf-8-*-\n\nimport os\nimport io\n\n+ #-*-coding:utf-8-*-\nimport pytest\n\n\n\n\npeft:1152\tAdd encoding comments to cookiecutter.py\n\n\nicl:1152\tUpdate cookiecutter to 1.1.0\n\n\nslm:Add coding to setup.py\n\n\n", "peft": "1152\tAdd encoding comments to cookiecutter.py\n", "icl": "1152\tUpdate cookiecutter to 1.1.0\n", "slm": "Add coding to setup.py\n", "nl": "Merge pull request from andytom/fix/consistently_use_encoding_declarations", "lang": "python", "label": ["Doc-mod"], "Comments": []}
{"id": 16798, "text": "you can also pass a ` ` latex ` ` flag to make.py to build a pdf,or pass no\narguments to build everything.\n\nThe output produced by Sphinx can be configured by editing the ` conf.py `\n- file located in the ` doc\\`.\n+ file located in the ` doc/`.\n\nOrganization of Matplotlib's Documentation\n==========================================\nworking with Sphinx in general.Here are a few additional things to keep in mind\n\nIn[69]:lines=plot([1,2,3])\n\n- which would yield::\n+ which would yield:\n\n- ..sourcecode::ipython\n+ ..sourcecode::ipython\n\nIn[69]:lines=plot([1,2,3])\n\n\n\npeft:1001\tFix typo in doc/tutorial/index.rst\n\n\nicl:1001\tMerge pull request from eric - wieser/basics - docs\n\n\nslm:Merge pull request from tacaswell/doc_typo\n\n\n", "peft": "1001\tFix typo in doc/tutorial/index.rst\n", "icl": "1001\tMerge pull request from eric - wieser/basics - docs\n", "slm": "Merge pull request from tacaswell/doc_typo\n", "nl": "fix some formatting in documentation guide", "lang": "python", "label": ["Ref-con"], "Comments": []}
{"id": 16799, "text": "\\section{\\module{xmllib}mmm\n\n\n\\begin{seealso}\n- \\seetext{The XML specification,published by the World Wide Web\n- Consortium(W3C),is available online at\n- \\url{http://www.w3.org/TR/REC-xml}.References to\n- additional material on XML are available at\n- \\url{http://www.w3.org/XML/}.}\n-\n- \\seetext{The Python XML Topic Guide provides a great deal of information\n- on using XML from Python and links to other sources of information\n- on XML.It's located on the Web at\n- \\url{http://www.python.org/topics/xml/}.}\n-\n- \\seetext{The Python XML Special Interest Group is developing substantial\n- support for processing XML from Python.See\n- \\url{http://www.python.org/sigs/xml-sig/}for more information.}\n+ \\seetitle[http://www.w3.org/TR/REC-xml]{Extensible Markup Language\n+ (XML)1.0}{The XML specification,published by the World\n+ Wide Web Consortium(W3C),defines the syntax and\n+ processor requirements for XML.References to additional\n+ material on XML,including translations of the\n+ specification,are available at\n+ \\url{http://www.w3.org/XML/}.}\n+\n+ \\seetitle[http://www.python.org/topics/xml/]{Python and XML\n+ Processing}{The Python XML Topic Guide provides a great\n+ deal of information on using XML from Python and links to\n+ other sources of information on XML.}\n+\n+ \\seetitle[http://www.python.org/sigs/xml-sig/]{SIG for XML\n+ Processing in Python}{The Python XML Special Interest\n+ Group is developing substantial support for processing XML\n+ from Python.}\n\\end{seealso}\n\n\n\\subsection{XML Namespaces\\label{xml-namespace}}\n\nAn older draft of the XML Namespaces proposal is also recognized,but\ntriggers a warning.\n+\n+ \\begin{seealso}\n+ \\seetitle[http://www.w3.org/TR/REC-xml-names/]{Namespaces in XML}{\n+ This World-Wide Web Consortium recommendation describes the\n+ proper syntax and processing requirements for namespaces in\n+ XML.}\n+ \\end{seealso}\n\n\npeft:1758\tMerge revisions 1.111 and 1.112 of xmllib.rst.\n\n\nicl:1758\txmllib:fix xml namespace\n\n\nslm:Update the\"See also\"section\n\n\n", "peft": "1758\tMerge revisions 1.111 and 1.112 of xmllib.rst.\n", "icl": "1758\txmllib:fix xml namespace\n", "slm": "Update the\"See also\"section\n", "nl": "Add a reference item pointing to the Namespaces in XML recommendation.", "lang": "python", "label": ["Feat-mod"], "Comments": []}
{"id": 16800, "text": "\n- _Last updated Aug 30 2015_\n-\n# # Introduction\nThis is a collection of thoughts on securing a modern Mac running OS X Yosemite and some steps on how to improve privacy.\n\nIf you wish to make a correction or improvement,please send a pull request.\n# # Preparing Yosemite\nThere are several ways to install a fresh copy of OS X Yosemite.\n\n- The simplest one is to boot into * * Recovery Mode * * by holding * * Command * * and * * R * * keys at boot.One can then download and apply an image right from Apple.However,I don't like this way because the machine's serial number and other identifying information is sent to Apple over * * HTTP * *.\n+ The simplest way is to boot into[Recovery Mode](https://support.apple.com/en-us/HT201314)by holding ` Command ` and ` R ` keys at boot.One can then download and apply an image right from Apple.However,this way exposes the computer's serial number and other identifying information to Apple over plain * * HTTP * *.\n+\n+ An alternative way is to download Yosemite build * * 14A389 * * or newer from the * * App Store * * or some other place and create an installable system image which you can customize and reuse.\n\n- An alternative way is to download Yosemite build * * 14A389 * * or later from the * * App Store * * or some other place and create an installable system image which you can customize and reuse.\n+ OS X installers can be made with the ` createinstallmedia ` utility included in `/Applications/Install OS X Yosemite.app `.See[Create a bootable installer for OS X Yosemite](https://support.apple.com/en-us/HT201372)or run the utility without arguments to see how it works.\n\n- The file needed to create an installable image is * * InstallESD.dmg * *,which is inside the * * Install OS X Yosemite * * application bundle.Just right click,select * * Show Package Contents * * and navigate to * * Contents>SharedSupport * * to find the dmg.\n+ If you'd like to do it the * * manual * * way,you will need to find the file ` InstallESD.dmg `,which is inside `/Applications/Install OS X Yosemite.app `.\n\n- The following are cryptographic hashes of the file.You can use ` shasum-a256 InstallESD.dmg ` for example,and compare the output to make sure you got the same,authentic copy.You can also Google these hashes to ensure your copy is genuine and hasn't been tampered with.\n+ Just right click,select * * Show Package Contents * * and navigate to * * Contents>SharedSupport * * to find the dmg.\n\n- InstallESD.dmg\n+ You can verify the following cryptographic hashes to ensure you have the same,authentic copy by using a command like ` shasum-a256 InstallESD.dmg ` and so on.You can also Google these hashes to ensure your copy is genuine and hasn't been tampered with.\n+\n+ InstallESD.dmg(Build 14A389)\n\nSHA-256:af244af020424d803ea8fc143bdd2c067db19f663484d735d6b6733a0feeeb4d\nSHA-1:eebf02a20ac27665a966957eec6f5e6fe3228a19\nNext,mount and install the OS to a temporary image,or use the GUI app[MagerVa\n\nThis part will take a while,so just be patient.You can ` tail-F/var/log/install.log ` to check progress.\n\n- Next,install the 10.10.4 combo updater.The 10.10.5 update(and later?)will not install until 10.10.4 is.\n-\n- Download and install * * 10.10.4 Combo Update * * from<https://support.apple.com/downloads/DL1820/en_US/osxupdcombo10.10.4.dmg>\n+ Download and install the[10.10.4 Combo Update](https://support.apple.com/downloads/DL1820/en_US/osxupdcombo10.10.4.dmg)\n\nosxupdcombo10.10.4.dmg\n\nThen\nsudo installer-pkg/Volumes/OS\\X\\10.10.4\\Update\\Combo/OSXUpdCombo10.10.4.pkg-tgt/tmp/os\nhdiutil unmount/Volumes/OS\\X\\10.10.4\\Update\\Combo\n\n- Download and install * * 10.10.5 Combo Update * * from<https://support.apple.com/downloads/DL1832/en_US/osxupdcombo10.10.5.dmg>\n+ Download and install the[10.10.5 Combo Update](https://support.apple.com/downloads/DL1832/en_US/osxupdcombo10.10.5.dmg)\n\nosxupdcombo10.10.5.dmg\n\nThen\nsudo installer-pkg/Volumes/OS\\X\\10.10.5\\Update/OSXUpd10.10.5.pkg-tgt/tmp/os\nhdiutil unmount/Volumes/OS\\X\\10.10.5\\Update\n\n- (Optional)Install any other packages to your new image,such as * * Wireshark * *.\n+ (Optional)Install any other packages to your new image,such as[Wireshark](https://www.wireshark.org/download.html).\n\nhdiutil mount Wireshark\\1.99.5\\Intel\\64.dmg\nsudo installer-pkg/Volumes/Wireshark/Wireshark\\1.99.5\\Intel\\64.pkg-tgt/tmp/os\n\n\npeft:16\tAdded instructions for manual installation of OS X Yosemite.\n\n\nicl:16\tMerge pull request from anntzer/yosemite - preparing\n\n\nslm:Update OS X Yosemite info page\n\n\n", "peft": "16\tAdded instructions for manual installation of OS X Yosemite.\n", "icl": "16\tMerge pull request from anntzer/yosemite - preparing\n", "slm": "Update OS X Yosemite info page\n", "nl": "mention the createinstallmedia way to install yosemite.fix.", "lang": "python", "label": ["Doc-mod"], "Comments": []}
