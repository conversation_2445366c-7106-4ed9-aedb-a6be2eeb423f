{"input": "@@ -1,6 +1,6 @@\n <script type=\"text/javascript\">\n   window.analytics||(window.analytics=[]),window.analytics.methods=[\"identify\",\"track\",\"trackLink\",\"trackForm\",\"trackClick\",\"trackSubmit\",\"page\",\"pageview\",\"ab\",\"alias\",\"ready\",\"group\",\"on\",\"once\",\"off\"],window.analytics.factory=function(t){return function(){var a=Array.prototype.slice.call(arguments);return a.unshift(t),window.analytics.push(a),window.analytics}};for(var i=0;i<window.analytics.methods.length;i++){var method=window.analytics.methods[i];window.analytics[method]=window.analytics.factory(method)}window.analytics.load=function(t){var a=document.createElement(\"script\");a.type=\"text/javascript\",a.async=!0,a.src=(\"https:\"===document.location.protocol?\"https://\":\"http://\")+\"d2dq2ahtl5zl1z.cloudfront.net/analytics.js/v1/\"+t+\"/analytics.min.js\";var n=document.getElementsByTagName(\"script\")[0];n.parentNode.insertBefore(a,n)},window.analytics.SNIPPET_VERSION=\"2.0.8\",\n-  window.analytics.load(\"2nexpdgku3\");\n+  window.analytics.load(<%= ENV['SEGMENT_KEY']%>);\n   window.analytics.page();\n </script>\n ", "target": "I didn't realize we were hardcoding this, thanks for moving it to an env value.", "old": "", "peft": "2\tI think we should use the same key for all environments.\n", "icl-diff": "2\tThis is a bit of a hack. I'm not sure if it's a good idea to do this.\n", "icl-code": "", "slm": "Instead of `parseInt`, use `is()` directly.\n", "label": ["Feat"]}
{"input": "@@ -99,7 +99,7 @@ module.exports = function fileItem (props) {\n         }\n       </h4>\n       <div class=\"UppyDashboardItem-status\">\n-        ${file.data.size && html`<div class=\"UppyDashboardItem-statusSize\">${prettyBytes(file.data.size)}</div>`}\n+        ${isNaN(file.data.size) ? '' : html`<div class=\"UppyDashboardItem-statusSize\">${prettyBytes(file.data.size)}</div>`}\n         ${file.source && html`<div class=\"UppyDashboardItem-sourceIcon\">\n             ${acquirers.map(acquirer => {\n               if (acquirer.id === file.source) return html`<span title=\"${props.i18n('fileSource')}: ${acquirer.name}\">${acquirer.icon()}</span>`", "target": "We are trying to support IE 10-11, so we'll need a polyfill for this one, I think.", "old": "", "peft": "3\tI think we should use `isNaN` here.\n", "icl-diff": "3\tI think it's a bit of a code smell to have a ternary that returns an empty string. I'd probably just use an if/else block.\n", "icl-code": "", "slm": "I don't think this is true. What is the difference between `x` and `max`?\n", "label": ["Feat"]}
{"input": "@@ -1067,7 +1067,7 @@ fpga_result mmio_error(struct RASCommandLine *rasCmdLine)\n \tif ( rasCmdLine->function >0 )\n \t\tfunction = rasCmdLine->bus;\n \n-\tsnprintf(sysfs_path, sizeof(sysfs_path),\n+\tsnprintf_s_iiii(sysfs_path, sizeof(sysfs_path),\n \t\t\tDEVICEID_PATH,0,bus,device,function);\n \n \tresult = sysfs_read_u64(sysfs_path, &value);", "target": "Can you explain why is this necessary? Is `snprintf()` with four integer arguments unsafe?", "old": "", "peft": "7\tThis is a bit of a nitpick, but I think it's better to use `snprintf_s` here.\n", "icl-diff": "7\tnit: s/sysfs_path/sysfs_path_buffer/ on line 1068.\n", "icl-code": "", "slm": "@bparees do we want to remove this capabilities from the agent capabilities?\n", "label": ["Ref"]}
{"input": "@@ -198,6 +198,10 @@ class Driver extends webdriver.WebDriver {\n    * @return {!Driver} A new driver instance.\n    */\n   static createSession(options, service = getDefaultService()) {\n+    if (!service) {\n+      service = getDefaultService();\n+    }\n+    \n     let client = service.start().then(url => new http.HttpClient(url));\n     let executor = new http.Executor(client);\n ", "target": "Would you mind removing the default parameter above? (I doubt I'll ever use defaults again since you still have to protect against callers explicitly passing `null` or `undefined`)", "old": "", "peft": "9\tI think this should be `service = getDefaultService()`\n", "icl-diff": "9\tI think this is a bug. The `service` argument is optional, so it should be fine to pass `undefined` to `createSession`.\n", "icl-code": "", "slm": "not sure if this should be an error?\n", "label": ["Feat"]}
{"input": "@@ -2186,7 +2186,7 @@ class WebElement {\n     if (!this.driver_.fileDetector_) {\n       return this.schedule_(\n           new command.Command(command.Name.SEND_KEYS_TO_ELEMENT).\n-              setParameter('text', keys).\n+              setParameter('text', keys.then(keys => keys.join(''))).\n               setParameter('value', keys),\n           'WebElement.sendKeys()');\n     }", "target": "Also update line 2205 below", "old": "", "peft": "10\tI think this is a bug. The `keys` parameter should be a `string` not a `Promise\n", "icl-diff": "10\t@gianm I think this is a bug in the selenium-webdriver library.\n", "icl-code": "", "slm": "Might as well change to `self.delegate_with_default(:delegate_with_default)`\n", "label": ["Feat"]}
{"input": "@@ -114,11 +114,16 @@ func (w *watcher) run(ctx context.Context, provider imageprovider.Provider, inte\n \t\t\t\tupdates = append(updates, u...)\n \t\t\t}\n \t\t\tif len(updates) == 0 {\n-\t\t\t\tw.logger.Info(\"no image to be updated\")\n+\t\t\t\tw.logger.Info(\"no image to be updated\",\n+\t\t\t\t\tzap.String(\"image-provider\", provider.Name()),\n+\t\t\t\t)\n \t\t\t\tcontinue\n \t\t\t}\n \t\t\tif err := update(updates); err != nil {\n-\t\t\t\tw.logger.Error(\"failed to update image\", zap.Error(err))\n+\t\t\t\tw.logger.Error(\"failed to update image\", zap.String(\"image-provider\",\n+\t\t\t\t\tprovider.Name()),\n+\t\t\t\t\tzap.Error(err),\n+\t\t\t\t)\n \t\t\t\tcontinue\n \t\t\t}\n \t\t}", "target": "`zap.String(\"image-provider\", provider.Name())` should be in a same line.", "old": "", "peft": "14\tI think we should use `zap.Errorf` here.\n", "icl-diff": "14\tI think it is better to log the error with the image provider name.\n", "icl-code": "", "slm": "Can we link to the docs for `AutoRowSize` instead?\n", "label": ["Feat"]}
{"input": "@@ -1,8 +1,6 @@\n /*exported DqElement */\n \n function truncate(str, maxLength) {\n-\t'use strict';\n-\n \tmaxLength = maxLength || 300;\n \n \tif (str.length > maxLength) {", "target": "Why this deletion?", "old": "", "peft": "15\tThis is a bug fix.\n", "icl-diff": "15\tI don't see any reason to use strict mode here.\n", "icl-code": "", "slm": "Don't we have a test for this?\n", "label": ["Feat"]}
{"input": "@@ -419,7 +419,10 @@ class S3KeyTest(unittest.TestCase):\n         remote_metadata = check._get_remote_metadata()\n \n         # TODO: investigate whether encoding ' ' as '%20' makes sense\n-        self.assertEqual(check.cache_control, 'public,%20max-age=500')\n+        self.assertIn(\n+            check.cache_control,\n+            ('public,%20max-age=500', 'public, max-age=500')\n+        )\n         self.assertEqual(remote_metadata['cache-control'], 'public,%20max-age=500')\n         self.assertEqual(check.get_metadata('test-plus'), 'A plus (+)')\n         self.assertEqual(check.content_disposition, 'filename=Sch%C3%B6ne%20Zeit.txt')", "target": "This change looks unrelated to the CL description?", "old": "", "peft": "16\tI think this is a bug. The `cache-control` header should be `public, max-age=500`\n", "icl-diff": "16\tI think it's better to use `assertIn` instead of `assertEqual`\n", "icl-code": "", "slm": "can you revert this change\n", "label": ["Feat"]}
{"input": "@@ -182,6 +182,7 @@ public class FetchActiveFlowDao {\n             + \" LEFT JOIN \"\n             + \" executors et ON ex.executor_id = et.id\"\n             + \" WHERE ex.status NOT IN (\"\n+            + Status.EXECUTION_STOPPED.getNumVal() + \", \"\n             + Status.SUCCEEDED.getNumVal() + \", \"\n             + Status.KILLED.getNumVal() + \", \"\n             + Status.FAILED.getNumVal() + \")\";", "target": "This method can cause bugs. Why don't you use list of finalize statuses and then use join by comma and that will give you correct value. Use Apache Commons Lang library List<Integer> list = Arrays.asList(1,2,3,4,5,6,7); String str = org.apache.commons.lang.StringUtils.join(list, \",\"); You can use it at multiple places shown in this diff.", "old": "", "peft": "18\tI think we should add `Status.EXECUTION_STOPPED.getNumVal()` to the list of values.\n", "icl-diff": "18\tI'm not sure what the purpose of this query is, but it looks like it's missing a `GROUP BY` clause.\n", "icl-code": "", "slm": "Do we really want to use `libkbfs.RepoDoesntExistError` in the common case?\n", "label": ["Feat"]}
{"input": "@@ -23,7 +23,10 @@ void wlr_input_device_init(struct wlr_input_device *dev,\n }\n \n void wlr_input_device_destroy(struct wlr_input_device *dev) {\n-\tif (!dev) return;\n+\tif (!dev) {\n+\t\treturn;\n+\t}\n+\t\n \tif (dev->_device) {\n \t\tswitch (dev->type) {\n \t\tcase WLR_INPUT_DEVICE_KEYBOARD:", "target": "Merge with next condition", "old": "", "peft": "19\tI think this is a bug. It should not be possible to destroy a device that is not initialized.\n", "icl-diff": "19\tAdd a line break after this.\n", "icl-code": "", "slm": "Layout/EmptyLinesAroundBlockBody: Extra empty line detected at block body beginning.\n", "label": ["Ref"]}
{"input": "@@ -348,6 +348,7 @@ type appResourcesGetter interface {\n \n type taskDeployer interface {\n \tDeployTask(input *deploy.CreateTaskResourcesInput, opts ...cloudformation.StackOption) error\n+\tDeleteTask(task deploy.TaskStackInfo) error\n }\n \n type taskRunner interface {", "target": "Maybe add it when it is used.", "old": "", "peft": "20\tI think this should be `DeleteTask(task deploy.TaskStackInfo, opts...cloudformation.StackOption)`\n", "icl-diff": "20\tI think this is a mistake. The `taskRunner` interface should be implemented by `taskDeployer`\n", "icl-code": "", "slm": "Ideally we would have changed these values from `true` to `false`, but I wonder if it would be better to call `pStart->calcImplicitValence(false)`. And then just call `pStart->calcImplicitValence(true)`.\n", "label": ["Feat"]}
{"input": "@@ -282,10 +282,10 @@ public class TypeUtil {\n \n     switch (from.typeId()) {\n       case INTEGER:\n-        return to == Types.LongType.get();\n+        return to.equals(Types.LongType.get());\n \n       case FLOAT:\n-        return to == Types.DoubleType.get();\n+        return to.equals(Types.DoubleType.get());\n \n       case DECIMAL:\n         Types.DecimalType fromDecimal = (Types.DecimalType) from;", "target": "why is this change necessary?", "old": "", "peft": "22\tI think we should use `to.equals(Types.LongType.get())` here.\n", "icl-diff": "22\tI think we should use `equals` instead of `==`\n", "icl-code": "", "slm": "This should probably be named `ErrNotFound`\n", "label": ["Ref"]}
{"input": "@@ -0,0 +1,13 @@\n+using System;\n+using System.Collections.Generic;\n+using System.Linq;\n+using System.Text;\n+using System.Threading.Tasks;\n+using MvvmCross.Core.Views;\n+\n+namespace MvvmCross.Uwp.Attributes\n+{\n+    public class MvxPagePresentationAttribute : MvxBasePresentationAttribute\n+    {\n+    }\n+}", "target": "Are all these namespaces required for this attribute?", "old": "", "peft": "30\tI think this should be `MvxPagePresentationAttribute`\n", "icl-diff": "30\tThis is a good example of a class that is only used in one place. It is a good candidate for a local function.\n", "icl-code": "", "slm": "The extra space at the end of the line is unnecessary\n", "label": ["Feat"]}
{"input": "@@ -53,9 +53,12 @@ module Beaker\n           result.stdout << std_out\n           result.stderr << std_err\n           result.exit_code = status.exitstatus\n+          @logger.info(result.stdout)\n+          @logger.info(result.stderr)\n         end\n       rescue => e\n         result.stderr << e.inspect\n+        @logger.info(result.stderr)\n         result.exit_code = 1\n       end\n ", "target": "Given this may be used and printed in other ways, isn't `debug` more appropriate?", "old": "", "peft": "31\tI think this is a bug. The logger should be called with the result, not the result with the logger.\n", "icl-diff": "31\tI don't think this is a good idea. We should be logging the output of the command, not the output of the logger.\n", "icl-code": "", "slm": "Prefer double-quoted strings unless you need single quotes to avoid extra backslashes for escaping.\n", "label": ["Feat"]}
{"input": "@@ -540,3 +540,7 @@ func (s *blockDiskStore) remove(id kbfsblock.ID) error {\n \t}\n \treturn err\n }\n+\n+func (s blockDiskStore) clear() error {\n+\treturn ioutil.RemoveAll(s.dir)\n+}", "target": "Looks like this is unused? Did you mean to call it when clearing the block journal?", "old": "", "peft": "32\tI think this should be `s.clear()`\n", "icl-diff": "32\tI think this is a bug. The `clear` method should be a method on `blockDiskStore`, not a method on `*blockDiskStore`.\n", "icl-code": "", "slm": "Instead of passing `false` to `d.dial`, maybe we should pass `true` here?\n", "label": ["Feat"]}
{"input": "@@ -1549,7 +1549,7 @@ func (js *jetStream) processStreamLeaderChange(mset *stream, isLeader bool) {\n \t\tresp.Error = jsError(err)\n \t\ts.sendAPIErrResponse(client, acc, subject, reply, _EMPTY_, s.jsonResponse(&resp))\n \t} else {\n-\t\tresp.StreamInfo = &StreamInfo{Created: mset.createdTime(), State: mset.state(), Config: mset.config(), Cluster: js.clusterInfo(mset.raftGroup())}\n+\t\tresp.StreamInfo = &StreamInfo{Created: mset.createdTime(), State: mset.state(), Config: mset.config(), Cluster: js.clusterInfo(mset.raftGroup()), Sources: mset.sourcesInfo(), Mirror: mset.mirrorInfo()}\n \t\ts.sendAPIResponse(client, acc, subject, reply, _EMPTY_, s.jsonResponse(&resp))\n \t\tif node := mset.raftNode(); node != nil {\n \t\t\tmset.sendCreateAdvisory()", "target": "not sure if this has to be here or not tbh", "old": "", "peft": "34\tI think we should also add the `Mirror` field to the `StreamInfo` struct.\n", "icl-diff": "34\t@gianm I think this is a bug, the `StreamInfo` struct has a `Sources` field, but it is not set here.\n", "icl-code": "", "slm": "We should either change this to `new Dotenv(false)` or change the default to `true`\n", "label": ["Feat"]}
{"input": "@@ -0,0 +1,10 @@\n+class TraceDestroyerJob < ApplicationJob\n+  queue_as :default\n+\n+  def perform(trace)\n+    trace.destroy\n+  rescue StandardError => ex\n+    logger.info ex.to_s\n+    ex.backtrace.each { |l| logger.info l }\n+  end\n+end", "target": "Why are we catching and logging exceptions? By doing that we make it look like the job has succeeded and it will be removed from the queue - if we didn't do that then it would stay queued...", "old": "", "peft": "43\tI think this should be `ex.message` instead of `ex.to_s`\n", "icl-diff": "43\tThis is a job that is scheduled to run in the background. It is not a controller action.\n", "icl-code": "", "slm": "remove empty line\n", "label": ["Feat"]}
{"input": "@@ -90,14 +90,11 @@ class CartController extends FrontBaseController\n      */\n     public function indexAction(Request $request)\n     {\n-        $cart = $this->cartFacade->getCartOfCurrentCustomer();\n-\n-        if ($cart->isEmpty()) {\n-            $this->cartFacade->cleanAdditionalData();\n-        }\n+        $cart = $this->cartFacade->findCartOfCurrentCustomer();\n+        $cartItems = $cart === null ? [] : $cart->getItems();\n \n         $cartFormData = ['quantities' => []];\n-        foreach ($cart->getItems() as $cartItem) {\n+        foreach ($cartItems as $cartItem) {\n             $cartFormData['quantities'][$cartItem->getId()] = $cartItem->getQuantity();\n         }\n ", "target": "If there are `@param` tags in docblock, there should be `@return` tag also. (applies for a whole file)", "old": "", "peft": "44\tI think we should use `findCartOfCurrentCustomer` here.\n", "icl-diff": "44\tI see the objective, but I don't like relying on a task to tell the flow if it's mapped, especially because there's nothing preventing a task that's mapped in one flow from being added manually to a second flow, where it is not mapped. We can infer the correct value of `t.mapped` by instead checking `any(edge.mapped for edge in self.edges_to(t))`\n", "icl-code": "", "slm": "why is this change needed?\n", "label": ["Feat"]}
{"input": "@@ -34,7 +34,8 @@ public enum BesuMetricCategory implements MetricCategory {\n   PRUNER(\"pruner\"),\n   RPC(\"rpc\"),\n   SYNCHRONIZER(\"synchronizer\"),\n-  TRANSACTION_POOL(\"transaction_pool\");\n+  TRANSACTION_POOL(\"transaction_pool\"),\n+  VALIDATORS(\"validators\");\n \n   private static final Optional<String> BESU_PREFIX = Optional.of(\"besu_\");\n   public static final Set<MetricCategory> DEFAULT_METRIC_CATEGORIES;", "target": "Is the concept of validators exclusive to IBFT2? I wonder if this category should be more explicitly linked to IBFT2.", "old": "", "peft": "45\tI think we should add a `BESU_METRIC_CATEGORIES` constant here.\n", "icl-diff": "45\tI think it's better to use `enum` instead of `String` for `TRANSACTION_POOL` and `VALIDATORS`.\n", "icl-code": "", "slm": "> FPGA_DISCRETE_DEVICEID [](start = 29, length = 14) This line will then fail FPGA_INTEGRATED_DEVICEID but not FPGA_DISCRETE_DEVICEID. So why do we need this check?\n", "label": ["Feat"]}
{"input": "@@ -898,6 +898,16 @@ public final class DBReader {\n         }\n         final LongIntMap feedCounters = adapter.getFeedCounters(feedIds);\n \n+        int feedFilter = UserPreferences.getFeedFilter();\n+        if (feedFilter == UserPreferences.FEED_FILTER_COUNTER_ZERO) {\n+            for (int i = 0; i < feeds.size(); i++) {\n+                if (feedCounters.get(feeds.get(i).getId()) <= 0) {\n+                    feedCounters.delete(feeds.get(i).getId());\n+                    feeds.remove(i);\n+                }\n+            }\n+        }\n+\n         Comparator<Feed> comparator;\n         int feedOrder = UserPreferences.getFeedOrder();\n         if (feedOrder == UserPreferences.FEED_ORDER_COUNTER) {", "target": "Please turn the iteration order around (instead of `0...size` to `size...0`). The reason is that this sometimes skips indices when removing an item. You can therefore end up with feeds that have counter 0 and are still displayed.", "old": "", "peft": "48\tI think this is a bug. The feed filter should not affect the feed counters.\n", "icl-diff": "48\tI think this is a bug. The comparator should be reversed if the feed order is counter.\n", "icl-code": "", "slm": "I think we can name this `IsolationLevel` since that's what the user is using. Also please make it clear that the user uses the isolation level.\n", "label": ["Feat"]}
{"input": "@@ -609,6 +609,14 @@ bool Game::removeCreature(Creature* creature, bool isLogout/* = true*/)\n \treturn true;\n }\n \n+void Game::executeDeath(uint32_t creatureId)\n+{\n+\tCreature* creature = getCreatureByID(creatureId);\n+\tif (creature && !creature->isRemoved() && creature->getHealth() < 1) {\n+\t\tcreature->onDeath();\n+\t}\n+}\n+\n void Game::playerMoveThing(uint32_t playerId, const Position& fromPos,\n                            uint16_t spriteId, uint8_t fromStackPos, const Position& toPos, uint8_t count)\n {", "target": "checking health again? I think it is not necessary.", "old": "", "peft": "53\tI think this function should be called `executeDeath`\n", "icl-diff": "53\tI don't like the idea of having a function that is called from two places, but only one of them is actually doing the right thing.\n", "icl-code": "", "slm": "we have this in the boost folder, is it needed?\n", "label": ["Feat"]}
{"input": "@@ -0,0 +1,18 @@\n+'use strict';\n+\n+const assert = require('assert');\n+\n+class ProvidedPromise {\n+  set Promise(lib) {\n+    assert.ok(typeof lib === 'function', `mongodb.Promise must be a function, got ${lib}`);\n+    this._promise = lib;\n+  }\n+  get Promise() {\n+    return this._promise;\n+  }\n+}\n+\n+const provided = new ProvidedPromise();\n+provided.Promise = global.Promise;\n+\n+module.exports = provided;", "target": "to reiterate my point above, this class is not the actual provided Promise, but rather something a user can provide a Promise to/with. I think a name like `PromiseProvider` is more appropriate.", "old": "", "peft": "55\tI think this is a bug. We should not be setting the global Promise here.\n", "icl-diff": "55\tI think the code is fine, but I would have used `assert.ok(typeof lib === 'function','mongodb.Promise must be a function')`\n", "icl-code": "", "slm": "As mentioned above, we should probably add the `mirrorInfo` function to the `SourceInfo` type as well.\n", "label": ["Feat"]}
{"input": "@@ -15,6 +15,11 @@ axe.utils.isHidden = function isHidden(el, recursed) {\n \t\treturn false;\n \t}\n \n+\t// do not exclude `video` or `audio` el's\n+\tif ([`VIDEO`, `AUDIO`].includes(el.nodeName.toUpperCase())) {\n+\t\treturn false;\n+\t}\n+\n \t// 11 === Node.DOCUMENT_FRAGMENT_NODE\n \tif (el.nodeType === 11) {\n \t\tel = el.host; // grab the host Node", "target": "Why should audio and video elements always return `false` for `isHidden`?", "old": "", "peft": "58\tI think this is a bug. `video` and `audio` elements should be excluded.\n", "icl-diff": "58\tI think this is a bug. The `video` and `audio` elements are not hidden, but they are not focusable either.\n", "icl-code": "", "slm": "I don't see a good way to check this, this is going to result in false positives. I would probably add `false` as the last argument to `IsEffectInSpell` instead.\n", "label": ["Code&Doc"]}
{"input": "@@ -142,7 +142,7 @@ void PairLubricate::compute(int eflag, int vflag)\n     Ef[2][2] = h_rate[2]/domain->zprd;\n     Ef[0][1] = Ef[1][0] = 0.5 * h_rate[5]/domain->yprd;\n     Ef[0][2] = Ef[2][0] = 0.5 * h_rate[4]/domain->zprd;\n-    Ef[1][2] = Ef[2][1] = 0.5 * h_rate[3]/domain->zprd;\n+    Ef[1][2] = Ef[2][1] = 0.5 * h_rate[3]/domain->xprd;\n \n     // copy updated velocity/omega/angmom to the ghost particles\n     // no need to do this if not shearing since comm->ghost_velocity is set", "target": "Not clear on why this change is correct.", "old": "", "peft": "68\tThis is a bug fix.\n", "icl-diff": "68\tThis is a very common mistake.\n", "icl-code": "", "slm": "I don't see where this is being used.\n", "label": ["Feat"]}
{"input": "@@ -54,6 +54,7 @@ storiesOf( 'PageSpeed Insights Module/Settings', module )\n \t\tdecorators: [\n \t\t\twithRegistry,\n \t\t],\n+\t\tpadding: 0,\n \t} )\n \t.add( 'View, open with all settings', ( args, { registry } ) => {\n \t\treturn <Settings isOpen={ true } registry={ registry } />;", "target": "All stories in this file also need to have the default padding.", "old": "", "peft": "70\tI think this should be `padding: 0`\n", "icl-diff": "70\tI think it's fine to have the padding set to 0. It's a bit of a hack, but it's a small hack and it's not a problem.\n", "icl-code": "", "slm": "`traceparentCollection?.First()` returns a `null`\n", "label": ["Feat"]}
{"input": "@@ -804,8 +804,10 @@ static void subsurface_handle_place_above(struct wl_client *client,\n \t\treturn;\n \t}\n \n+\tassert(sibling->parent == subsurface->parent);\n+\n \twl_list_remove(&subsurface->parent_pending_link);\n-\twl_list_insert(&sibling->parent_pending_link,\n+\twl_list_insert(sibling->parent_pending_link.prev,\n \t\t&subsurface->parent_pending_link);\n \n \tsubsurface->reordered = true;", "target": "I don't think these asserts are necessary, because `subsurface_find_sibling` already searches in the parent. Or am I missing something?", "old": "", "peft": "71\tI think we should use `wl_list_insert_before` here.\n", "icl-diff": "71\tI think this is a bug in the code.\n", "icl-code": "", "slm": "Need to update the file copyright year\n", "label": ["Feat"]}
{"input": "@@ -1569,12 +1569,11 @@ NATable *BindWA::getNATable(CorrName& corrName,\n       ((QualifiedName&)(table->getTableName())).setIsVolatile(TRUE);\n     }\n       \n-  // For now, do not allow access through the Trafodion external name created for\n-  // the HIVE object unless the inDDL flag is set.  inDDL is set for drop \n-  // table and SHOWDDL statements.  \n-  // TDB - may want to merge the Trafodion version with the HIVE version.\n-  // TDB - similar operation may be needed for external HBase tables\n-  if ((table) && (table->isExternalTable() && (! bindWA->inDDL())))\n+  // For now, don't allow access through the Trafodion external name created for\n+  // native HIVE or HBASE objects unless the allowExternalTables flag is set.  \n+  // allowExternalTables is set for drop table and SHOWDDL statements.  \n+  // TDB - may want to merge the Trafodion version with the native version.\n+  if ((table) && (table->isExternalTable() && (! bindWA->allowExternalTables())))\n     {\n       *CmpCommon::diags() << DgSqlCode(-4258)\n                           << DgTableName(table->getTableName().getQualifiedNameAsAnsiString());", "target": "I have forgotten why we thought this restriction is necessary. A user registers a Hive/HBase table with us but is not able to use the table with the registered name in DML. Will UPDATE STATs will be on the original name too (and update stats code will find out registered name and use it for Table_uid?). This was the registration concept is almost like an internal implementation idea, at some point could even be done by some utility program for all existing Hive/hBase tables, and the user need not be aware of it? I am struggling with a user would want to register a table, if they cannot use it by that name.", "old": "", "peft": "73\tI think this should be `if (bindWA-\n", "icl-diff": "73\tI think this is a bug.\n", "icl-code": "", "slm": "Is there a reason for not using `self` here?\n", "label": ["Code&Doc"]}
{"input": "@@ -40,6 +40,18 @@\n \n struct ase_cfg_t *cfg;\n \n+int app2sim_alloc_rx;\t\t// app2sim mesaage queue in RX mode\n+int sim2app_alloc_tx;\t\t// sim2app mesaage queue in TX mode\n+int app2sim_mmioreq_rx;\t\t// MMIO Request path\n+int sim2app_mmiorsp_tx;\t\t// MMIO Response path\n+int app2sim_umsg_rx;\t\t// UMSG    message queue in RX mode\n+int app2sim_portctrl_req_rx;\t// Port Control messages in Rx mode\n+int app2sim_dealloc_rx;\n+int sim2app_dealloc_tx;\n+int sim2app_portctrl_rsp_tx;\n+int sim2app_intr_request_tx;\n+int intr_event_fds[MAX_USR_INTRS];\n+\n int glbl_test_cmplt_cnt;   // Keeps the number of session_deinits received\n \n volatile int sockserver_kill;", "target": "Should most of these non-globals be static?", "old": "", "peft": "74\tI think this should be `int app2sim_mmioreq_rx;`, `int sim2app_mmiorsp_tx;`\n", "icl-diff": "74\tI think it's better to use a `struct` here.\n", "icl-code": "", "slm": "Why was this removed?\n", "label": ["Feat"]}
{"input": "@@ -48,7 +48,7 @@ class WebEngineView(QWebEngineView):\n         else:\n             profile = webenginesettings.default_profile\n         page = WebEnginePage(theme_color=theme_color, profile=profile,\n-                             parent=self)\n+                             parent=self, win_id=win_id)\n         self.setPage(page)\n \n     def shutdown(self):", "target": "Now that you handle this in `webenginetab.py` you can undo all the changes in this file.", "old": "", "peft": "76\tI think this is a bug. The parent should be the view, not the window.\n", "icl-diff": "76\t@gianm I think this is a good idea, but I'm not sure if it's worth the effort.\n", "icl-code": "", "slm": "Can we remove `commandStartExternalEditing` because it's no longer used?\n", "label": ["Feat"]}
{"input": "@@ -57,7 +57,7 @@ func (r *Helper) Apply(obj []byte) (ApplyResult, error) {\n \tif err != nil {\n \t\tr.logger.WithError(err).\n \t\t\tWithField(\"stdout\", ioStreams.Out.(*bytes.Buffer).String()).\n-\t\t\tWithField(\"stderr\", ioStreams.ErrOut.(*bytes.Buffer).String()).Error(\"running the apply command failed\")\n+\t\t\tWithField(\"stderr\", ioStreams.ErrOut.(*bytes.Buffer).String()).Warn(\"running the apply command failed\")\n \t\treturn \"\", err\n \t}\n \treturn changeTracker.GetResult(), nil", "target": "These changes to the resource helpers have some more far-reaching implications as they also affect calls made in hive-operator, I believe. It's probably OK still, though. Ideally, the resource helper would not be the one doing the logging, since it cannot know the severity, but that is well beyond something that we should tackle for this PR. I would rather have the hive-operator log warnings than have the syncsetinstance controller log errors.", "old": "", "peft": "78\tI think this should be a `fatal` error.\n", "icl-diff": "78\tI think it's fine to log the stderr as an error.\n", "icl-code": "", "slm": "Is the err returned by `c.closeConnection(ProtocolViolation)` ok? What's the reason to return from `registerWithAccount`?\n", "label": ["Feat"]}
{"input": "@@ -167,7 +167,7 @@ class ExportCategoryTableMap extends TableMap\n      */\n     public function buildRelations()\n     {\n-        $this->addRelation('Export', '\\\\Thelia\\\\Model\\\\Export', RelationMap::ONE_TO_MANY, array('id' => 'export_category_id', ), 'CASCADE', 'RESTRICT', 'Exports');\n+        $this->addRelation('Export', '\\\\Thelia\\\\Model\\\\Export', RelationMap::ONE_TO_MANY, array('id' => 'export_category_id', ), null, null, 'Exports');\n         $this->addRelation('ExportCategoryI18n', '\\\\Thelia\\\\Model\\\\ExportCategoryI18n', RelationMap::ONE_TO_MANY, array('id' => 'id', ), 'CASCADE', null, 'ExportCategoryI18ns');\n     } // buildRelations()\n ", "target": "do you really want to change the behavior on foreign key ?", "old": "", "peft": "79\tI think we should use the same relation for both `ExportCategoryI18n` and `ExportCategory`\n", "icl-diff": "79\tI don't think it improves performance that much, but you could stream the result from allItems() and collect it into a list, avoiding the for loop.\n", "icl-code": "", "slm": "Please also add the other fields to the `Info` struct.\n", "label": ["Feat"]}
{"input": "@@ -183,7 +183,8 @@ void event_batch_destroy (struct event_batch *batch)\n         if (batch->f)\n             (void)flux_future_wait_for (batch->f, -1);\n         if (batch->state_trans) {\n-            event_publish_state (batch->event, batch->state_trans);\n+            if (json_array_size (batch->state_trans) > 0)\n+                event_publish_state (batch->event, batch->state_trans);\n             json_decref (batch->state_trans);\n         }\n         if (batch->responses) {", "target": "Looks like my bad. Thanks for fixing!", "old": "", "peft": "80\tI think this is a bug. If the array is empty, we should not publish the state.\n", "icl-diff": "80\tI think this is a bug. The `if` statement should be `if (json_array_size (batch-\n", "icl-code": "", "slm": "Why not just call this method in `startExecutionTracerTask`?\n", "label": ["Feat"]}
{"input": "@@ -40,13 +40,11 @@ func AddDiskImportSteps(w *daisy.Workflow, dataDiskInfos []ovfutils.DiskInfo) {\n \tfor i, dataDiskInfo := range dataDiskInfos {\n \t\tdataDiskIndex := i + 1\n \t\tdataDiskFilePath := dataDiskInfo.FilePath\n-\t\tdiskNames = append(\n-\t\t\tdiskNames,\n-\t\t\tfmt.Sprintf(\"%v-data-disk-%v\", w.Vars[\"instance_name\"].Value, dataDiskIndex))\n+\t\tdiskNames = append(diskNames, generateDataDiskName(w.Vars[\"instance_name\"].Value, dataDiskIndex))\n \n \t\tsetupDataDiskStepName := fmt.Sprintf(\"setup-data-disk-%v\", dataDiskIndex)\n-\t\tdiskImporterDiskName := fmt.Sprintf(\"disk-importer-%v\", dataDiskIndex)\n-\t\tscratchDiskDiskName := fmt.Sprintf(\"disk-importer-scratch-%v-%v\", dataDiskIndex, w.Vars[\"instance_name\"].Value)\n+\t\tdiskImporterDiskName := fmt.Sprintf(\"disk-importer-%v-%v\", dataDiskIndex, w.ID())\n+\t\tscratchDiskDiskName := fmt.Sprintf(\"disk-importer-scratch-%v-%v\", dataDiskIndex, w.ID())\n \n \t\tsetupDataDiskStep := daisy.NewStepDefaultTimeout(setupDataDiskStepName, w)\n \t\tsetupDataDiskStep.CreateDisks = &daisy.CreateDisks{", "target": "I'd recommend using generateDataDiskName here as well -- might as well benefit from the safety that it gives to protect yourself from future changes to w.ID().", "old": "", "peft": "83\tI think we should use `w.ID()` instead of `w.Vars[\"instance_name\"].Value` here.\n", "icl-diff": "83\tI think this is a good place to use a `for` loop.\n", "icl-code": "", "slm": "seems we still need to close all connections even if it is zero ?\n", "label": ["Feat"]}
{"input": "@@ -43,7 +43,7 @@ namespace Nethermind.JsonRpc\n         \n         public static ResultWrapper<T> Fail(Exception e)\n         {\n-            return new() { Result = Result.Fail(e.ToString()), ErrorCode = ErrorCodes.InternalError};\n+            return new() { Result = Result.Fail(e.Message), ErrorCode = ErrorCodes.InternalError};\n         }\n \n         public static ResultWrapper<T> Fail(string error, int errorCode, T outputData)", "target": "Any particular reason for this? This potentially will make harder to investigate users issues", "old": "", "peft": "87\tI think this is a bug. The error message should be the same as the error code.\n", "icl-diff": "87\tI think this is a bug. The `error` should be `e.Message`.\n", "icl-code": "", "slm": "This file is about 3rd-party Python2.6, and 3rd-party Python3.6, so I don't think this import is needed.\n", "label": ["Ref"]}
{"input": "@@ -999,7 +999,8 @@ Blockly.BlockSvg.prototype.updatePreviews = function(closestConnection,\n   // grayed-out blocks instead of highlighting the connection; for compatibility\n   // with Web Blockly the name \"highlightedConnection\" will still be used.\n   if (Blockly.highlightedConnection_ &&\n-      Blockly.highlightedConnection_ != closestConnection) {\n+      (Blockly.highlightedConnection_ != closestConnection ||\n+       Blockly.localConnection_ != localConnection)) {\n     if (Blockly.insertionMarker_ && Blockly.insertionMarkerConnection_) {\n       Blockly.BlockSvg.disconnectInsertionMarker();\n     }", "target": "Do you also need to check if Blockly.localConnection_ is non-null?", "old": "", "peft": "88\tThis is a bit of a hack, but I think it's the best we can do.\n", "icl-diff": "88\tAdd a line break after this.\n", "icl-code": "", "slm": "How would you feel about this `if (is_array($resource))` ?\n", "label": ["Feat"]}
{"input": "@@ -3,6 +3,8 @@ const { basename } = require('./path-utils');\n const shim = require('./shim').default;\n const JoplinError = require('./JoplinError').default;\n const { Buffer } = require('buffer');\n+const { Readable } = require('stream').Readable;\n+const { GetObjectCommand, ListObjectsV2Command, HeadObjectCommand, PutObjectCommand, DeleteObjectCommand, DeleteObjectsCommand } = require(\"@aws-sdk/client-s3\");\n \n const S3_MAX_DELETES = 1000;\n ", "target": "The desktop app will load this fine. on iOS I get `TypeError: undefined is not an object (evaluating '_$$_REQUIRE(_dependencyMap[8], \"stream\").Readable.Readable')` if I change it to `const Readable = require('stream').Readable;` or `const { Readable } = require('stream');` I get undefined errors from the stream on iOS: `[TypeError: stream.on is not a function. (In 'stream.on(\"data\", function (chunk) { return chunks.push(chunk); })', 'stream.on' is undefined)]` What am I missing?", "old": "", "peft": "90\tI think we should use the `aws-sdk` package here.\n", "icl-diff": "90\tI think it's better to use `const { Readable, GetObjectCommand, ListObjectsV2Command, HeadObjectCommand, PutObjectCommand, DeleteObjectCommand, DeleteObjectsCommand } = require(\"@aws-sdk/client-s3\");`\n", "icl-code": "", "slm": "you can just use ` axe.testUtils.shadowSupport`\n", "label": ["Feat"]}
{"input": "@@ -42,6 +42,10 @@ import (\n \t\"go.uber.org/yarpc/yarpcerrors\"\n )\n \n+func init() {\n+\topentracing.SetGlobalTracer(nil)\n+}\n+\n func TestHandlerSuccess(t *testing.T) {\n \tmockCtrl := gomock.NewController(t)\n \tdefer mockCtrl.Finish()", "target": "wut. We can do this at the beginning of tests if we want right? Why are we depending on init?", "old": "", "peft": "92\tI think we should not set the global tracer to nil.\n", "icl-diff": "92\tAdded opentracing.SetGlobalTracer(nil) to avoid the error:\n", "icl-code": "", "slm": "This is unused now, right?\n", "label": ["Feat"]}
{"input": "@@ -1403,9 +1403,12 @@ func (c *client) processConnect(arg []byte) error {\n \t\t\t\tc.mu.Lock()\n \t\t\t\tacc := c.acc\n \t\t\t\tc.mu.Unlock()\n+\t\t\t\tsrv.mu.Lock()\n \t\t\t\tif acc != nil && acc != srv.gacc {\n+\t\t\t\t\tsrv.mu.Unlock()\n \t\t\t\t\treturn ErrTooManyAccountConnections\n \t\t\t\t}\n+\t\t\t\tsrv.mu.Unlock()\n \t\t\t}\n \t\t\tc.authViolation()\n \t\t\treturn ErrAuthentication", "target": "Same trick here IMO.", "old": "", "peft": "93\tI think we should check if acc is nil here.\n", "icl-diff": "93\tAdded locking to protect against concurrent access to global account\n", "icl-code": "", "slm": "So these permissions are now user-facing? I don't think anyone is using them.\n", "label": ["Feat"]}
{"input": "@@ -108,10 +108,11 @@ module Selenium\n \n         def initialize(source, duration, x, y, element: nil, origin: nil)\n           super(source)\n+\n           @duration = duration * 1000\n           @x_offset = x\n           @y_offset = y\n-          @origin = element || origin\n+          @origin = origin || POINTER\n         end\n \n         def type", "target": "Should be `@origin = element || origin || POINTER`", "old": "", "peft": "94\tI think we should use `origin` here.\n", "icl-diff": "94\tAdd a line break after this.\n", "icl-code": "", "slm": "Why the underscore here? We're just duplicating this route name.\n", "label": ["Feat"]}
{"input": "@@ -50,11 +50,14 @@ func NewUpgradeCStorSPCJob() *cobra.Command {\n \t\tLong:    cstorSPCUpgradeCmdHelpText,\n \t\tExample: `upgrade cstor-spc --spc-name <spc-name>`,\n \t\tRun: func(cmd *cobra.Command, args []string) {\n+\t\t\tutil.CheckErr(options.RunCStorSPCUpgradeChecks(cmd, args), util.Fatal)\n \t\t\toptions.resourceKind = \"storagePoolClaim\"\n-\t\t\tutil.CheckErr(options.RunPreFlightChecks(cmd), util.Fatal)\n-\t\t\tutil.CheckErr(options.RunCStorSPCUpgradeChecks(cmd), util.Fatal)\n-\t\t\tutil.CheckErr(options.InitializeDefaults(cmd), util.Fatal)\n-\t\t\tutil.CheckErr(options.RunCStorSPCUpgrade(cmd), util.Fatal)\n+\t\t\tif options.cstorSPC.spcName != \"\" {\n+\t\t\t\tsingleCstorSPCUpgrade(cmd)\n+\t\t\t}\n+\t\t\tif len(args) != 0 {\n+\t\t\t\tbulkCstorSPCUpgrade(cmd, args)\n+\t\t\t}\n \t\t},\n \t}\n ", "target": "this example needs a fix?", "old": "", "peft": "95\tI think we should have a `bulkCstorSPCUpgrade` command that takes a list of spc names.\n", "icl-diff": "95\tI think it's better to check for the existence of the spcName flag before running the checks.\n", "icl-code": "", "slm": "Just for my understanding: do we want to allow \"8s\" and \"0.5s\" to be zero?\n", "label": ["Feat"]}
{"input": "@@ -35,6 +35,9 @@ public interface ExecutorLoader {\n   Map<Integer, Pair<ExecutionReference, ExecutableFlow>> fetchActiveFlows()\n       throws ExecutorManagerException;\n \n+  Pair<ExecutionReference, ExecutableFlow> fetchActiveFlowByExecId(int execId)\n+      throws ExecutorManagerException;\n+\n   List<ExecutableFlow> fetchFlowHistory(int skip, int num)\n       throws ExecutorManagerException;\n ", "target": "curious do we have an API to fetch an inactive flow?", "old": "", "peft": "99\tI think this should be `fetchActiveFlowByExecId(int execId, int skip, int num)`\n", "icl-diff": "99\tThis is a new method, so it should be added to the interface.\n", "icl-code": "", "slm": "Do we need to update the tests in this file? If yes then we should also update the tests in the `update` method.\n", "label": ["Feat"]}
{"input": "@@ -0,0 +1,16 @@\n+const { getComposedParent } = axe.commons.dom;\n+\n+function shouldMatchElement(el) {\n+\tif (!el) {\n+\t\treturn true;\n+\t}\n+\tconst ariaHiddenValue = el.getAttribute('aria-hidden')\n+\t\t? el.getAttribute('aria-hidden')\n+\t\t: null;\n+\tif (ariaHiddenValue === null) {\n+\t\treturn shouldMatchElement(getComposedParent(el));\n+\t}\n+\treturn false;\n+}\n+\n+return shouldMatchElement(getComposedParent(node));", "target": "Did you mean to use `hasAttribute`? You shouldn't. This does not improve performance and it messes with the readability.", "old": "", "peft": "101\tI think this should be `getAttribute('aria-hidden') === null`\n", "icl-diff": "101\tI don't understand the logic here.\n", "icl-code": "", "slm": "Use 2 (not 4) spaces for indentation.\n", "label": ["Feat"]}
{"input": "@@ -493,6 +493,14 @@ func (c *Container) GetLabels() map[string]string {\n \treturn c.labels\n }\n \n+// GetPorts gets the ports for a container\n+func (c *Container) GetPorts() []PortBinding {\n+\tc.lock.RLock()\n+\tdefer c.lock.RUnlock()\n+\n+\treturn c.Ports\n+}\n+\n // HealthStatusShouldBeReported returns true if the health check is defined in\n // the task definition\n func (c *Container) HealthStatusShouldBeReported() bool {", "target": "Can you rename `Ports` to `PortsUnsafe`?", "old": "", "peft": "105\tI think this should be `c.Ports`\n", "icl-diff": "105\tI think this is a good idea, but I'm not sure it's the right place for it.\n", "icl-code": "", "slm": "Is this supposed to be `input-`?\n", "label": ["Code&Doc"]}
{"input": "@@ -0,0 +1,10 @@\n+module ReturnToHelper\n+  def make_return_to(name, path)\n+    sig = OpenSSL::HMAC.digest(\n+      OpenSSL::Digest::SHA256.new,\n+      Rails.application.secrets.secret_key_base,\n+      name + \"$\" + path\n+    )\n+    {name: name, path: path, sig: Base64.urlsafe_encode64(sig)}\n+  end\n+end", "target": "This feels like it could be moved out to your first gem!", "old": "", "peft": "106\tI think this should be `Rails.application.secrets.secret_key_base`\n", "icl-diff": "106\tI think this is a helper module for the return_to_helper.rb file.\n", "icl-code": "", "slm": "This is going to be a breaking change since the API is down for a while. It's a breaking change considering how we default to buttonStyles, but I think it's a breaking change for now.\n", "label": ["Feat"]}
{"input": "@@ -7,4 +7,17 @@\n axe.utils.toArray = function (thing) {\n \t'use strict';\n \treturn Array.prototype.slice.call(thing);\n-};\n+};\n+\n+\n+/**\n+ * Creates an array without duplicate values from 2 array inputs\n+ * @param  {Array} arr1 First array\n+ * @param  {Array} arr2 Second array\n+ * @return {Array}\n+ */\n+axe.utils.uniqueArray = (arr1, arr2) => {\n+  return arr1.concat(arr2).filter((elem, pos, arr) => {\n+    return arr.indexOf(elem) === pos;\n+  });\n+};", "target": "This is super minor, but it'd be nice if this iterated over an arbitrary number of array inputs.", "old": "", "peft": "108\tI think this should be `arr1.concat(arr2).filter((elem, pos, arr) =\n", "icl-diff": "108\tThis is a duplicate function that was never used. See `mne.transforms` line 543. It was also reducing coverage.\n", "icl-code": "", "slm": "I'm not sure I agree with this. I _think_ that the default should be ` muted_alpha=0.5` and we should change that to ` muted_alpha=0.5` and set the default to ` muted_alpha=0.5` for consistency.\n", "label": ["Code&Doc"]}
{"input": "@@ -17,6 +17,7 @@ package com.google.api.codegen.util.ruby;\n import com.google.api.codegen.util.Name;\n import com.google.api.codegen.util.NameFormatter;\n import com.google.api.codegen.util.NamePath;\n+import com.google.common.collect.ImmutableSet;\n \n /**\n  * The NameFormatter for Ruby.", "target": "Why are we not wrapping here?", "old": "", "peft": "110\tI think we should use `ImmutableSet.copyOf` here.\n", "icl-diff": "110\tWhy is this a class?\n", "icl-code": "", "slm": "You need to use `isHydrating` from `dom.js#isHydrating`\n", "label": ["Feat"]}
{"input": "@@ -22,16 +22,16 @@ var sourceHashes = map[string]string{\n \t\"libflux/flux-core/src/ast/flatbuffers/mod.rs\":                                                \"00c75dc1da14487953a4a017616fb8a237fe3da437c876f1328532dd7906f015\",\n \t\"libflux/flux-core/src/ast/flatbuffers/monotype.rs\":                                           \"4e82aae8e7edb8e230302241fe702ece9ae84fc708003c24709c2ea944cfaae8\",\n \t\"libflux/flux-core/src/ast/flatbuffers/tests.rs\":                                              \"ea85b78049f0c3d79daedcf9e171ccb00d0a596d1a643aa218590c99e6b160bc\",\n-\t\"libflux/flux-core/src/ast/mod.rs\":                                                            \"676e22d24cd7a97525688ed391a980d08b4bee40c5b5067d187f3ae6d66210ca\",\n+\t\"libflux/flux-core/src/ast/mod.rs\":                                                            \"29b25b75a01cf1e1ac2b52d99b91986cec32011dc55af1ffd7f145838fce858d\",\n \t\"libflux/flux-core/src/ast/tests.rs\":                                                          \"bc7f77d569d8bbd4b9d00653f48bacd47eed46f53024dce836d3c8bbb6a80555\",\n \t\"libflux/flux-core/src/ast/walk/mod.rs\":                                                       \"e8a03023d7426bcf6dfdb1a61ac3263f5cf9194a595a1584dff7c899d06562f1\",\n \t\"libflux/flux-core/src/ast/walk/tests.rs\":                                                     \"f7b2d7dd5643bb795a86c04b6979b136b0de46b52b213caff094aed6d204a05d\",\n \t\"libflux/flux-core/src/formatter/mod.rs\":                                                      \"945736a4ad87adb6a3f359b6f2da6e5492b3cb0efd282e277303e2acae137763\",\n \t\"libflux/flux-core/src/formatter/tests.rs\":                                                    \"b0a10998a65fc4b54a8f68b3a0ed186d8548ba3d7638f911eb188d2ce485206f\",\n \t\"libflux/flux-core/src/lib.rs\":                                                                \"d19b7054e07f234c107d457030a0031374c123fe14a84a5b8e35537d138bac7a\",\n-\t\"libflux/flux-core/src/parser/mod.rs\":                                                         \"e3f11fe29f47271b5c04accc2d7efa35e1dc62c6de036bf0cc0cccda5e4742e8\",\n+\t\"libflux/flux-core/src/parser/mod.rs\":                                                         \"daf24b2c439f4c7a6f138e2b25064a205aee73bb6cffc898df22dd0c962ca5df\",\n \t\"libflux/flux-core/src/parser/strconv.rs\":                                                     \"748c82f6efc2eafaafb872db5b4185ce29aafa8f03ba02c4b84f4a9614e832d2\",\n-\t\"libflux/flux-core/src/parser/tests.rs\":                                                       \"e3a7c9222f90323a7ea9b319bd84f96f66c6f115af6d199a0da332c894713ae4\",\n+\t\"libflux/flux-core/src/parser/tests.rs\":                                                       \"43a1e5d3de85d76da2107544e417f583a1f66c20910286285b9645a309c42425\",\n \t\"libflux/flux-core/src/scanner/mod.rs\":                                                        \"2e15c9e0a73d0936d2eaeec030b636786db6dbe7aab673045de3a3e815c49f8a\",\n \t\"libflux/flux-core/src/scanner/scanner.rl\":                                                    \"530c062363b66daded3c521af44111b99ffee0eeb2a12ccffa8b8f349002d852\",\n \t\"libflux/flux-core/src/scanner/scanner_generated.rs\":                                          \"d65dcc216ccbb88d8f2e261a994ba2789075e434cb7a0f5e39e390bee2821e5b\",", "target": "Does this file actually need to be committed to git? Having to run `make generate` is rather tedious on each PR and it conflicts easily.", "old": "", "peft": "112\tI'm not sure if this is a bug or not, but I think it's worth noting that the hashes for `src/ast/walk/tests.rs` and `src/formatter/tests.rs` are different.\n", "icl-diff": "112\tI think this is a bug in the git hashes.\n", "icl-code": "", "slm": "what is the use case for this?\n", "label": ["Ref"]}
{"input": "@@ -440,18 +440,6 @@ func parseConfig(loc location.Location, opts options.Options) (interface{}, erro\n \t\t\tcfg.ProjectID = os.Getenv(\"GOOGLE_PROJECT_ID\")\n \t\t}\n \n-\t\tif cfg.JSONKeyPath == \"\" {\n-\t\t\tif path := os.Getenv(\"GOOGLE_APPLICATION_CREDENTIALS\"); path != \"\" {\n-\t\t\t\t// Check read access\n-\t\t\t\tif _, err := ioutil.ReadFile(path); err != nil {\n-\t\t\t\t\treturn nil, errors.Fatalf(\"Failed to read google credential from file %v: %v\", path, err)\n-\t\t\t\t}\n-\t\t\t\tcfg.JSONKeyPath = path\n-\t\t\t} else {\n-\t\t\t\treturn nil, errors.Fatal(\"No credential file path is set\")\n-\t\t\t}\n-\t\t}\n-\n \t\tif err := opts.Apply(loc.Scheme, &cfg); err != nil {\n \t\t\treturn nil, err\n \t\t}", "target": "We don't need this anymore, as Google's library handles various auth mechanisms for us. We'll be less explicit about why we're failing but we gain support for several authentication methods- swings and roundabouts!", "old": "", "peft": "113\tI think this is a bug. The error message should be \"No credential file path is set\"\n", "icl-diff": "113\tI think it's better to use `os.Getenv(\"GOOGLE_APPLICATION_CREDENTIALS\")` directly.\n", "icl-code": "", "slm": "Nit: this should be `SetApplicationError(err error)`\n", "label": ["Feat"]}
{"input": "@@ -8,10 +8,12 @@ using System;\n using System.Collections.Generic;\n using System.Globalization;\n using System.IO;\n+using System.Linq;\n using System.Net;\n using System.Net.Http;\n using System.Text;\n using System.Threading;\n+using System.Threading.Tasks;\n using Task = System.Threading.Tasks.Task;\n \n namespace Microsoft.DotNet.Build.CloudTestTasks", "target": "In general I'm a trying to understand the reason behind this PR. Looks like if a blob (name) already exists we check if the contents are identical? In what scenarios this is not the case? When we want to publish a package/asset that has changed but still we want to use the same version?", "old": "", "peft": "115\tI think this should be `using System.Threading.Tasks;`\n", "icl-diff": "115\tnit: s/Task = System.Threading.Tasks.Task;/using Task = System.Threading.Tasks.Task;/ on line 10.\n", "icl-code": "", "slm": "maybe this one should be done in `luigi.util`?\n", "label": ["Feat"]}
{"input": "@@ -266,7 +266,7 @@ bool parse_it(Iterator &first, Iterator last, RDKit::RWMol &mol) {\n       } else {\n         if (!parse_atom_labels(first, last, mol)) return false;\n       }\n-    } else if ((first + 9) < last &&\n+    } else if (std::distance(first, last) > 9 &&\n                std::string(first, first + 9) == \"atomProp:\") {\n       first += 9;\n       if (!parse_atom_props(first, last, mol)) return false;", "target": "Nice use of std::distance. I'm a bit worried about first += 9 though.", "old": "", "peft": "121\tI think this should be `std::distance(first, last) \n", "icl-diff": "121\tI think this is a bug. The `std::distance(first, last) \n", "icl-code": "", "slm": "why is this change needed?\n", "label": ["Feat"]}
{"input": "@@ -41,8 +41,9 @@ namespace Datadog.Trace.ClrProfiler\n \n                 if (parent != null &&\n                     parent.Type == SpanTypes.Http &&\n-                    parent.GetTag(Tags.HttpMethod).Equals(httpMethod, StringComparison.OrdinalIgnoreCase) &&\n-                    parent.GetTag(Tags.HttpUrl).Equals(UriHelpers.CleanUri(requestUri, removeScheme: false, tryRemoveIds: false), StringComparison.OrdinalIgnoreCase))\n+                    parent.GetTag(Tags.InstrumentationName) != null &&\n+                    httpMethod.Equals(parent.GetTag(Tags.HttpMethod), StringComparison.OrdinalIgnoreCase) &&\n+                    UriHelpers.CleanUri(requestUri, removeScheme: false, tryRemoveIds: false).Equals(parent.GetTag(Tags.HttpUrl), StringComparison.OrdinalIgnoreCase))\n                 {\n                     // we are already instrumenting this,\n                     // don't instrument nested methods that belong to the same stacktrace", "target": "As long as we're being extra-vigilant about NREs, should we check that `httpMethod` and `requestUri` are not null?", "old": "", "peft": "123\tI think we should use `Equals` here, not `StringComparison.OrdinalIgnoreCase`\n", "icl-diff": "123\tI think this is a bug. The `parent.GetTag(Tags.HttpMethod).Equals(httpMethod, StringComparison.OrdinalIgnoreCase)` check is redundant, because the `parent.GetTag(Tags.HttpUrl).Equals(UriHelpers.CleanUri(requestUri, removeScheme: false, tryRemoveIds: false), StringComparison.OrdinalIgnoreCase)` check will already fail if the method is different.\n", "icl-code": "", "slm": "what is this used for?\n", "label": ["Feat"]}
{"input": "@@ -22,10 +22,12 @@ import (\n )\n \n // Prometheus defines a Prometheus deployment.\n+// +k8s:openapi-gen=true\n type Prometheus struct {\n \tmetav1.TypeMeta `json:\",inline\"`\n \t// Standard object’s metadata. More info:\n \t// https://github.com/kubernetes/community/blob/master/contributors/devel/api-conventions.md#metadata\n+\t// +k8s:openapi-gen=false\n \tmetav1.ObjectMeta `json:\"metadata,omitempty\"`\n \t// Specification of the desired behavior of the Prometheus cluster. More info:\n \t// https://github.com/kubernetes/community/blob/master/contributors/devel/api-conventions.md#spec-and-status", "target": "how come this is false?", "old": "", "peft": "128\tI think we should keep this openapi-gen=true.\n", "icl-diff": "128\tI don't think we need to add `// +k8s:openapi-gen=false` to the `metav1.ObjectMeta` field.\n", "icl-code": "", "slm": "can't we merge the two into one?\n", "label": ["Doc"]}
{"input": "@@ -83,6 +83,7 @@ type ReporterKBPKI struct {\n \tnotifySyncBuffer chan *keybase1.FSPathSyncStatus\n \tsuppressCh       chan time.Duration\n \tcanceler         func()\n+\tctx              context.Context\n }\n \n // NewReporterKBPKI creates a new ReporterKBPKI.", "target": "I know @jzila suggested this, but I disagree: you're really not supposed to save a context in a struct. Can you get by with just saving the `Done()` channel instead? The `ctx` should continue to be passed around explicitly.", "old": "", "peft": "130\tI think this should be `ctx context.Context`\n", "icl-diff": "130\t@gianm I think we should use a context here, so that we can cancel the goroutine when the FS is shut down.\n", "icl-code": "", "slm": "The PMT is not needed as this private transaction doesn't exist.\n", "label": ["Feat"]}
{"input": "@@ -12,12 +12,7 @@ namespace Microsoft.AspNet.Server.Kestrel.Networking\n         {\n             IsWindows = PlatformApis.IsWindows;\n \n-            var isDarwinMono =\n-#if DNX451\n-                IsWindows ? false : PlatformApis.IsDarwin;\n-#else\n-                false;\n-#endif\n+            var isDarwinMono = !IsWindows && PlatformApis.IsDarwin;\n \n             if (isDarwinMono)\n             {", "target": "This check isn't right though.", "old": "", "peft": "131\tI think this should be `IsDarwin` instead of `IsDarwinMono`\n", "icl-diff": "131\tAdd a line break after this.\n", "icl-code": "", "slm": "Do we need to await this.forceFlushAsync(cancellationToken) here?\n", "label": ["Feat"]}
{"input": "@@ -96,6 +96,8 @@ public abstract class DynamicLangXApiView implements ViewModel {\n     return missingDefaultServiceAddress() || missingDefaultServiceScopes();\n   }\n \n+  public abstract String codeGenVersion();\n+\n   @Override\n   public String resourceRoot() {\n     return SnippetSetRunner.SNIPPET_RESOURCE_ROOT;", "target": "maybe `toolkitVersion` instead? It doesn't necessarily have to be called that in the generated code, but in the view model classes, I think it makes it clearer that it is the version of toolkit itself.", "old": "", "peft": "132\tI think this should be `codeGenVersion()`\n", "icl-diff": "132\tI think it's better to use `return missingDefaultServiceAddress() || missingDefaultServiceScopes();`\n", "icl-code": "", "slm": "`if v4IPNet == nil || v6IPNet == nil` isn't really needed, isn't it?\n", "label": ["Feat"]}
{"input": "@@ -123,7 +123,7 @@ func (c *CStorVolumeReplicaController) cVREventHandler(operation common.QueueOpe\n \n \t\terr := volumereplica.DeleteVolume(fullVolName)\n \t\tif err != nil {\n-\t\t\tglog.Errorf(\"Error in deleting volume %q: %s\", cVR.ObjectMeta.Name,err)\n+\t\t\tglog.Errorf(\"Error in deleting volume %q: %s\", cVR.ObjectMeta.Name, err)\n \t\t\tc.recorder.Event(cVR, corev1.EventTypeWarning, string(common.FailureDestroy), string(common.MessageResourceFailDestroy))\n \t\t\treturn string(apis.CVRStatusDeletionFailed), err\n \t\t}", "target": "This line changed due to go formatting. Format was not there earlier.", "old": "", "peft": "135\tI think we should use `glog.Errorf(\"Error in deleting volume %q: %s\", cVR.ObjectMeta.Name, err)`\n", "icl-diff": "135\t@gianm this is a common error in the codebase, please fix it\n", "icl-code": "", "slm": "Should this be `nameof(SqlCommandIntegrationIds.MySql);`?\n", "label": ["Ref"]}
{"input": "@@ -368,5 +368,8 @@ type Instance struct {\n \tEBSOptimized *bool `json:\"ebsOptimized\"`\n \n \t// The tags associated with the instance.\n-\tTags map[string]string `json:\"tag\"`\n+\tTags map[string]string `json:\"tags\"`\n+\n+\t// The security groups associated with the instance.\n+\tSecurityGroups map[string]string `json:\"securityGroups\"`\n }", "target": "I'm not sure if this wanted to be `json:\"tags\"` (which I'd set in my PR) or `json:\"tag\"` that someone elses PR had set. Given that the rest of the fields had their JSON field name set to the same as the struct field name, I opted for `tags`.", "old": "", "peft": "138\tI think we should use `securityGroupIds` instead of `securityGroups`\n", "icl-diff": "138\tI think this is a good spot for a `// TODO` comment.\n", "icl-code": "", "slm": "Is there a reason to rename TestContainerNone to ContainerImplementationNone?\n", "label": ["Code&Doc"]}
{"input": "@@ -42,6 +42,11 @@ class InfluxWriterSubscriber(object):\n         self.time = 0\n \n \n+    def on_connection_closed(self, connection, reply_code, reply_text):\n+        self.log.info('RabbitMQ connection got closed!')\n+        self.connection.add_timeout(5, self.connect_to_rabbitmq)\n+\n+\n     @staticmethod\n     def static_callback(ch, method, properties, body, obj):\n         return obj.callback(ch, method, properties, body)", "target": "there is no static method as a go between -- how does this work?", "old": "", "peft": "140\tI think this should be `self.log.info('RabbitMQ connection got closed!')`.\n", "icl-diff": "140\tThis is a callback function. It is not a method of the class.\n", "icl-code": "", "slm": "I think we still need this line.\n", "label": ["Feat"]}
{"input": "@@ -0,0 +1,8 @@\n+# frozen_string_literal: true\n+# encoding: utf-8\n+\n+class StringifiedSymbol\n+  include Mongoid::Document\n+  store_in collection: \"stringified_symbols\", client: :other\n+  field :stringified_symbol, type: StringifiedSymbol\n+end", "target": "Can you please change the name of this class to be something else?", "old": "", "peft": "141\tI'm not sure if this is the right place to put this, but I think it's a good idea to have a `StringifiedSymbol` class.\n", "icl-diff": "141\tThis is a new file.\n", "icl-code": "", "slm": "We're not using the full name of the package here - is that going to be a problem?\n", "label": ["Code&Doc"]}
{"input": "@@ -403,7 +403,7 @@ size_t h2o_strstr(const char *haysack, size_t haysack_len, const char *needle, s\n }\n \n /* note: returns a zero-width match as well */\n-const char *h2o_next_token(h2o_iovec_t *iter, int separator, size_t *element_len, h2o_iovec_t *value)\n+const char *h2o_next_token(h2o_iovec_t *iter, int separator, size_t *element_len, h2o_iovec_t *value, int coma_separator)\n {\n     const char *cur = iter->base, *end = iter->base + iter->len, *token_start, *token_end;\n ", "target": "I think we might prefer generalizing the interface rather than creating an exception. Current design of `h2o_next_token` assumes the input to be a comma-separated list, and allows the caller to specify a different separator when parsing a nested list. As I understand, what we are trying to attain in this PR is to have a way of parsing a flat list of semicolon-separated list. Assuming that is the case, I think it would be better to change the API of the function to accept two separators (i.e. inner and outer), where for our existing use-cases `outer` would be `,`.", "old": "", "peft": "142\tI think this is a bug. The `coma_separator` parameter is not used.\n", "icl-diff": "142\tI don't see the point of this function. It's just a special case of `h2o_strstr` that returns the next token.\n", "icl-code": "", "slm": "Should we log something here?\n", "label": ["Feat"]}
{"input": "@@ -69,7 +69,7 @@ func (consumer *createConsumer) Consume(requestPtr interface{}) (response interf\n \tissuerID := consumer.peerID\n \tif request.ConsumerInfo != nil {\n \t\tissuerID = request.ConsumerInfo.IssuerID\n-\t\tif request.ConsumerInfo.PaymentVersion == PaymentVersionV2 {\n+\t\tif request.ConsumerInfo.PaymentVersion == PaymentVersionV3 {\n \t\t\tindicateNewVersion = true\n \t\t}\n \t} else {", "target": "Why it is now called `PaymentVersionV3`?", "old": "", "peft": "144\tI think we should use `request.ConsumerInfo.PaymentVersion == PaymentVersionV3` instead of `request.ConsumerInfo.PaymentVersion == PaymentVersionV2`\n", "icl-diff": "144\tI think this is a bug. The version should be checked against `PaymentVersionV2`\n", "icl-code": "", "slm": "@fabienrenaud why are you deleting the reference to the notificationIdsInUse map?\n", "label": ["Feat"]}
{"input": "@@ -46,6 +46,13 @@ namespace OpenTelemetry\n         /// <inheritdoc />\n         public abstract override void OnEnd(T data);\n \n+        internal override void SetParentProvider(BaseProvider parentProvider)\n+        {\n+            base.SetParentProvider(parentProvider);\n+\n+            this.exporter.ParentProvider = parentProvider;\n+        }\n+\n         /// <inheritdoc />\n         protected override bool OnShutdown(int timeoutMilliseconds)\n         {", "target": "@cijothomas I needed a way to set the Exporter.ParentProvider when the Processor's gets set. Internal to SDK though.", "old": "", "peft": "146\tI think this should be `this.exporter.ParentProvider = parentProvider;`\n", "icl-diff": "146\tI don't think this is necessary. The exporter is already set in the constructor.\n", "icl-code": "", "slm": "I wonder if we could simplify this to `User.where(client_slug: client).where('id not IN (?)', observations.pluck('user_id'))` and avoid an extra query for each user.\n", "label": ["Feat"]}
{"input": "@@ -1,5 +1,7 @@\n from .resnet import ResNet, make_res_layer\n from .resnext import ResNeXt\n from .ssd_vgg import SSDVGG\n+from .hrnet import HRNet\n \n-__all__ = ['ResNet', 'make_res_layer', 'ResNeXt', 'SSDVGG']\n+__all__ = ['ResNet', 'make_res_layer', 'ResNeXt', 'SSDVGG',\n+           'HRNet']", "target": "The line breaking is unnecessary.", "old": "", "peft": "149\tI think we should add `HRNet` to the `__all__` of `resnet` module.\n", "icl-diff": "149\tI think it is better to use the following:\n", "icl-code": "", "slm": "is there a reason for changing this error to 20k? Wondering if we want to include that limit as well.\n", "label": ["Feat"]}
{"input": "@@ -1076,7 +1076,8 @@ fpga_result mmio_error(struct RASCommandLine *rasCmdLine)\n \t\treturn result;\n \t}\n \n-\tif(value != FPGA_INTEGRATED_DEVICEID) {\n+\tif( (value != FPGA_INTEGRATED_DEVICEID) ||\n+\t\t(value != FPGA_DISCRETE_DEVICEID) ) {\n \t\tFPGA_ERR(\"Failed  to read Device id\");\n \t\treturn FPGA_NOT_SUPPORTED;\n \t}", "target": "Shouldn't the operator be && ?", "old": "", "peft": "151\tI think this is a bug. The device ID should be the same for both integrated and discrete devices.\n", "icl-diff": "151\tI think this is a bug.\n", "icl-code": "", "slm": "Why are these changes needed?\n", "label": ["Feat"]}
{"input": "@@ -161,6 +161,17 @@ module Beaker\n       FileUtils.rm_rf(@vagrant_path)\n     end\n \n+    #snapshotting depends on https://github.com/scalefactory/vagrant-multiprovider-snap\n+    def take_snapshot(host,snapshot_name)\n+      @logger.debug \"Creating snapshot of #{host}\"\n+      vagrant_cmd(\"snap take #{host} --name=#{snapshot_name}\")\n+    end\n+\n+    def restore_snapshot(host,snapshot_name)\n+      @logger.debug \"Restoring snapshot of #{host}\"\n+      vagrant_cmd(\"snap rollback #{host} --name=#{snapshot_name}\")\n+    end\n+\n     def vagrant_cmd(args)\n       Dir.chdir(@vagrant_path) do\n         exit_status = 1", "target": "My best guess is that you want to use host.name in these parts to get the name of the host ?", "old": "", "peft": "152\tI think this should be `snapshot_name` instead of `snapshot_name`\n", "icl-diff": "152\tI think this is a good idea, but I think it should be a separate command.\n", "icl-code": "", "slm": "Out of curiosity, what is the rationale for that change?\n", "label": ["Code&Doc"]}
{"input": "@@ -205,9 +205,10 @@ public class DownloadService extends Service {\n         Log.d(TAG, \"Service shutting down\");\n         isRunning = false;\n \n+        boolean showAutoDownloadReport = UserPreferences.showAutoDownloadReport();\n         if (ClientConfig.downloadServiceCallbacks.shouldCreateReport()\n-                && UserPreferences.showDownloadReport()) {\n-            notificationManager.updateReport(reportQueue);\n+                && (UserPreferences.showDownloadReport() || showAutoDownloadReport)) {\n+            notificationManager.updateReport(reportQueue, showAutoDownloadReport);\n             reportQueue.clear();\n         }\n ", "target": "Just wondering... The two kinds of notifications are now quite different (Channel, text, maybe even icon). Would it make sense to extract the auto download notification to a new class instead of handling everything in the existing `notificationManager`? I have not checked if this will lead to a lot of code duplication, though.", "old": "", "peft": "154\tI think this should be `UserPreferences.showAutoDownloadReport()`\n", "icl-diff": "154\tI think it's better to use `&&` instead of `||`\n", "icl-code": "", "slm": "This cannot be InputDefinition's static method. The InputDefinition's methods are not public.\n", "label": ["Feat"]}
{"input": "@@ -140,3 +140,11 @@ func (c *Call) RoutingDelegate() string {\n \t}\n \treturn c.ic.req.RoutingDelegate\n }\n+\n+// Features returns the RequestFeatures for this request.\n+func (c *Call) Features() transport.RequestFeatures {\n+\tif c == nil {\n+\t\treturn transport.RequestFeatures{}\n+\t}\n+\treturn c.ic.req.Features\n+}", "target": "Do we need to put this on the Call? We only need the API communication between the Encoding and the Transports which is currently done through the Transport.Request. The Call is used by users right?", "old": "", "peft": "155\tI think this should be `c.ic.req.Features`\n", "icl-diff": "155\tI think this is a good idea, but I'm not sure if it's worth the effort to add this to the transport.\n", "icl-code": "", "slm": "I would recommend removing this empty line\n", "label": ["Code&Doc"]}
{"input": "@@ -66,16 +66,16 @@ import java.util.Set;\n  * the current node) may be queried.\n  *\n  */\n-class DigraphNode implements Cloneable, Serializable {\n+class DigraphNode<E> implements Cloneable, Serializable {\n \n     /** The data associated with this node. */\n-    protected Object data;\n+    protected E data;\n \n     /**\n      * A <code>Set</code> of neighboring nodes pointed to by this\n      * node.\n      */\n-    protected Set outNodes = new HashSet();\n+    protected Set<DigraphNode<E>> outNodes = new HashSet<>();\n \n     /** The in-degree of the node. */\n     protected int inDegree = 0;", "target": "This set of changes is the one part of this that I'm not completely sure is valid. Can someone look over this to make sure I got the E's right?", "old": "", "peft": "165\tI think we should use `DigraphNode` instead of `E` here.\n", "icl-diff": "165\tWhy not use generics?\n", "icl-code": "", "slm": "Is there any reason for removing this?\n", "label": ["Feat"]}
{"input": "@@ -282,8 +282,12 @@ void nano::bootstrap_attempt_legacy::request_push (nano::unique_lock<std::mutex>\n void nano::bootstrap_attempt_legacy::add_frontier (nano::pull_info const & pull_a)\n {\n \tnano::pull_info pull (pull_a);\n-\tnano::lock_guard<std::mutex> lock (mutex);\n-\tfrontier_pulls.push_back (pull);\n+\t// Prevent incorrent or malicious pulls with frontier 0 insertion\n+\tif (!pull.head.is_zero ())\n+\t{\n+\t\tnano::lock_guard<std::mutex> lock (mutex);\n+\t\tfrontier_pulls.push_back (pull);\n+\t}\n }\n \n void nano::bootstrap_attempt_legacy::add_bulk_push_target (nano::block_hash const & head, nano::block_hash const & end)", "target": "There doesn't seem to be a reason to copy this here.", "old": "", "peft": "166\tI think this is not needed.\n", "icl-diff": "166\tThis is a bit of a strange one. The lock is taken, but then the lock is released. This is a bit of a code smell.\n", "icl-code": "", "slm": "$icons is already a array, why do we need to copy it?\n", "label": ["Code&Doc"]}
{"input": "@@ -1774,6 +1774,7 @@ std::string h2o_raw_tracer::bpf_text() {\n \n #include <linux/sched.h>\n #include <linux/limits.h>\n+#include \"include/h2o/ebpf.h\"\n \n #define STR_LEN 64\n ", "target": "We've avoided including h2o headers in BPF programs because it's a runtime dependency. However, IIRC, this is because h2olog was maintained in the separate repository so that h2olog did not know where h2o was installed. Now h2olog can use `H2O_ROOT`, we should add it to BCC's `cflags` in order to include h2o headers in BPF programs.", "old": "", "peft": "167\tThis is not needed.\n", "icl-diff": "167\tThis is a new file.\n", "icl-code": "", "slm": "For completeness, should this raise a `TypeError` instead?\n", "label": ["Feat"]}
{"input": "@@ -28,10 +28,6 @@ namespace Microsoft.DotNet.Build.Tasks.Feed\n \n         public bool PublishFlatContainer { get; set; }\n \n-        public int RetryAttempts { get; set; } = 5;\n-\n-        public int RetryDelayInSeconds { get; set; } = 30;\n-\n         public int MaxClients { get; set; } = 8;\n \n         public bool SkipCreateContainer { get; set; } = false;", "target": "remove these from the targets file.", "old": "", "peft": "174\tI think this should be `RetryDelayInSeconds`\n", "icl-diff": "174\tI don't think we need to expose these properties.\n", "icl-code": "", "slm": "this could just be `if os.Getenv(\"CFLAGS\") != \"\" { cFlags = append(cFlags, f)`\n", "label": ["Feat"]}
{"input": "@@ -1,8 +1,16 @@\n-import React from 'react';\n+import React, {Component} from 'react';\n import 'element-theme-default';\n import {i18n} from 'element-react';\n import locale from 'element-react/src/locale/lang/en';\n \n+import storage from './utils/storage';\n+import logo from './utils/logo';\n+import {makeLogin, isTokenExpire} from './utils/login';\n+\n+import Header from './components/Header';\n+import Footer from './components/Footer';\n+import LoginModal from './components/Login';\n+\n i18n.use(locale);\n \n import Route from './router';", "target": "I'd create a different method for each render section.", "old": "", "peft": "181\tI think we should move this import to the top of the file.\n", "icl-diff": "181\tI think it's better to use `import {Component} from'react'` instead of `import React, {Component} from'react'`.\n", "icl-code": "", "slm": "Just to confirm, this is the correct error to use.\n", "label": ["Feat"]}
{"input": "@@ -23,6 +23,8 @@ public class EstimateGasOperationTracer implements OperationTracer {\n \n   private Gas sStoreStipendNeeded = Gas.ZERO;\n \n+  private boolean isReverted = false;\n+\n   @Override\n   public void traceExecution(\n       final MessageFrame frame, final OperationTracer.ExecuteOperation executeOperation) {", "target": "Instead of storing the revert flag in the tracer is it possible to use org.hyperledger.besu.ethereum.mainnet.TransactionProcessor.Result#getRevertReason? (via org.hyperledger.besu.ethereum.transaction.TransactionSimulatorResult#getResult)? If a TX reverts without a reason do we get an empty revert reason or a revert reason with zero bytes?", "old": "", "peft": "182\tI think this should be `isReverted = true;`\n", "icl-diff": "182\t@gianm this causes lots of retries for duplicate primary key entry errors that will never be transient unless someone manually cleans the DB\n", "icl-code": "", "slm": "can you add a license header?\n", "label": ["Feat"]}
{"input": "@@ -100,6 +100,10 @@ abstract class BaseFile<F>\n           found = true;\n           fromProjectionPos[i] = j;\n         }\n+        if (fields.get(i).fieldId() == ManifestFile.SPEC_ID.fieldId()) {\n+          found = true;\n+          fromProjectionPos[i] = 14;\n+        }\n       }\n \n       if (!found) {", "target": "These modifications allow BaseFile to translate into a SparkRow with the specID as a column", "old": "", "peft": "184\tI think this is a bug. The spec id should be in the first position.\n", "icl-diff": "184\tAdd a line break after this.\n", "icl-code": "", "slm": "`req.getContext()` can be null I think\n", "label": ["Feat"]}
{"input": "@@ -111,6 +111,15 @@ def generate_thrift_files(thrift_files_dir, env, silent=True):\n         LOG.error('Failed to generate viewer server files')\n         return ret\n \n+    auth_thrift = os.path.join(thrift_files_dir, 'authentication.thrift')\n+    auth_thrift = 'authentication.thrift'\n+    auth_cmd = ['thrift', '-r', '-I', '.',\n+                '--gen', 'py', auth_thrift]\n+    ret = run_cmd(auth_cmd, thrift_files_dir, env, silent=silent)\n+    if ret:\n+        LOG.error('Failed to generate authentication interface files')\n+        return ret\n+\n \n # -------------------------------------------------------------------\n def generate_documentation(doc_root, env, silent=True):", "target": "There seems to be some repetition. Does a local function make this code shorter overall?", "old": "", "peft": "187\tI think we should use `thrift` instead of `thriftgen` here.\n", "icl-diff": "187\tI think this is a good idea, but I'm not sure if it's worth the effort.\n", "icl-code": "", "slm": "I'm not sure if `Welcome to NVDA` should be a part of the command string. Since `NVDA+c` is a child of the `NVDA` command, it should probably be a child of the `NVDA` command.\n", "label": ["Feat"]}
{"input": "@@ -298,6 +298,7 @@ function diffElementNodes(\n \t}\n \n \tif (dom == null) {\n+\t\tisHydrating = false;\n \t\tif (newVNode.type === null) {\n \t\t\treturn document.createTextNode(newProps);\n \t\t}", "target": "Might be cheaper to reuse the `null` assignment of line 313 and set `isHydrating` to null instead WDYT?", "old": "", "peft": "188\tI think this should be `isHydrating = true;`\n", "icl-diff": "188\tThis is a very bad idea. You should not be mutating the global state of the library.\n", "icl-code": "", "slm": "Did you mean to remove this `showLockActivity(ctx, false);`?\n", "label": ["Feat"]}
{"input": "@@ -50,8 +50,8 @@ public class MoveReplicaHDFSTest extends MoveReplicaTest {\n       HdfsTestUtil.teardownClass(dfsCluster);\n     } finally {\n       dfsCluster = null;\n-      System.setProperty(\"solr.hdfs.blockcache.blocksperbank\", \"512\");\n-      System.setProperty(\"tests.hdfs.numdatanodes\", \"1\");\n+      System.clearProperty(\"solr.hdfs.blockcache.blocksperbank\");\n+      System.clearProperty(\"tests.hdfs.numdatanodes\");\n     }\n   }\n ", "target": "This was introduced recently for the Hadoop 3 upgrade. Copy/paste error but definitely causing some of the new test failures.", "old": "", "peft": "190\tI think we should remove this test.\n", "icl-diff": "190\tI think this is a good place to use a `@VisibleForTesting` annotation.\n", "icl-code": "", "slm": "Can we show this in the menu even if there are no notes? I don't think it hurts anything to jump to folder\n", "label": ["Feat"]}
{"input": "@@ -56,9 +56,10 @@ class DefaultBucketViewTest(BaseWebTest, unittest.TestCase):\n         self.app.get(self.collection_url, headers=self.headers)\n \n     def test_querystring_parameters_are_taken_into_account(self):\n-        self.app.get(self.collection_url + '/records?_since=invalid',\n-                     headers=self.headers,\n-                     status=400)\n+        resp = self.app.get(self.collection_url + '/records?_since=invalid',\n+                            headers=self.headers,\n+                            status=400)\n+        self.assertIn('Content-Length', resp.headers)\n \n     def test_option_is_possible_without_authentication_for_default(self):\n         headers = 'authorization,content-type'", "target": "I wonder if we should create new tests for header checks; here for instance we're mixing querystring and headers. Thoughts?", "old": "", "peft": "192\tI think this is a bug. The `Content-Length` header should not be present in the response.\n", "icl-diff": "192\t@gianm I think this is a bug in the test, not the code.\n", "icl-code": "", "slm": "Is syncer.chainStore.GetTipSetAndState used anywhere else? If not why can we get rid of it?\n", "label": ["Feat"]}
{"input": "@@ -18,8 +18,8 @@ type Hash struct {\n //\n // See http://docs.aws.amazon.com/amazonglacier/latest/dev/checksum-calculations.html for more information.\n func ComputeHashes(r io.ReadSeeker) Hash {\n-\tr.Seek(0, 0)       // Read the whole stream\n-\tdefer r.Seek(0, 0) // Rewind stream at end\n+\tstart, _ := r.Seek(0, 1)  // Read the whole stream\n+\tdefer r.Seek(start, 0) // Rewind stream at end\n \n \tbuf := make([]byte, bufsize)\n \thashes := [][]byte{}", "target": "replacing the `1` with `io.SeekCurrent` may be good here", "old": "", "peft": "195\tI think this is a bug. The `Seek` call should be `Seek(0, 0)` and the `Seek` call after the `Read` should be `Seek(0, 1)`.\n", "icl-diff": "195\t@gianm I think this is a bug. The defer should be before the Seek.\n", "icl-code": "", "slm": "why change this?\n", "label": ["Feat"]}
{"input": "@@ -125,7 +125,9 @@ public class SalesforceNetworkPlugin extends ForcePlugin {\n                     try {\n                         // Not a 2xx status\n                         if (!response.isSuccess()) {\n-                            callbackContext.error(response.asString());\n+                            JSONObject errorObj = new JSONObject();\n+                            errorObj.putOpt(\"response\", response.fullResponseAsJSONObject());\n+                            callbackContext.error(errorObj.toString());\n                         }\n                         // Binary response\n                         else if (returnBinary) {", "target": "Use `response.asJsonObject()` instead. Also, use `put()` instead of `putOpt()`, `null` as a value is fine.", "old": "", "peft": "216\tI think this is a bug. The callback should be called with the error message, not the error object.\n", "icl-diff": "216\tI think we should return the full response as a string, not just the body.\n", "icl-code": "", "slm": "I don't see why you'd need the `dXNlci1kYXRhCg` string here. I don't see it at all.\n", "label": ["Feat"]}
{"input": "@@ -232,9 +232,12 @@ SchemaDate.prototype.cast = function(value) {\n \n   if (value instanceof Number || typeof value === 'number') {\n     date = new Date(value);\n+  } else if (typeof value === 'string' && !isNaN(Number(value)) && (Number(value) >= 275761 || Number(value) < 0)) {\n+    // string representation of milliseconds take this path\n+    date = new Date(Number(value));\n   } else if (typeof value.valueOf === 'function') {\n-    // support for moment.js. This is also the path strings will take because strings\n-    // have a `valueOf()`\n+    // support for moment.js. This is also the path string representation of years\n+    // will take because strings have a `valueOf()`\n     date = new Date(value.valueOf());\n   } else {\n     // fallback", "target": "I'm not 100% sold on this idea but I like it in general. Nice compromise between using the 'Date' constructor where possible and falling back to the pre #5880 behavior when it makes sense. However, instead of `Number(value) < 0`, let's do `Number(value) < MIN_YEAR` because `new Date('-2017')` is perfectly valid in JS and I don't want to break the date behavior.", "old": "", "peft": "245\tI think this is a bug in moment.js. It should be `Number(value) \n", "icl-diff": "245\tI think this is a bug. The `value` is a string, so `value.valueOf()` is `NaN`.\n", "icl-code": "", "slm": "Why don't we just use `std::min(wal_->lastLogId(), lastLogId)`?\n", "label": ["Code&Doc"]}
{"input": "@@ -1124,7 +1124,7 @@ void Identifier::_exportToJSON(JSONFormatter *formatter) const {\n //! @cond Doxygen_Suppress\n static bool isIgnoredChar(char ch) {\n     return ch == ' ' || ch == '_' || ch == '-' || ch == '/' || ch == '(' ||\n-           ch == ')' || ch == '.' || ch == '&' || ch == ',';\n+           ch == ')' || ch == '.' || ch == '&';\n }\n //! @endcond\n ", "target": "this change should be reverted", "old": "", "peft": "247\tThis is a breaking change.\n", "icl-diff": "247\tI think this is a bug. The comma should be included in the list of ignored characters.\n", "icl-code": "", "slm": "This should be removed.\n", "label": ["Feat"]}
{"input": "@@ -42,4 +42,8 @@ public class CliqueMiningTracker {\n   public boolean blockCreatedLocally(final BlockHeader header) {\n     return CliqueHelpers.getProposerOfBlock(header).equals(localAddress);\n   }\n+\n+  public ProtocolContext getProtocolContext() {\n+    return protocolContext;\n+  }\n }", "target": "this shouldn't be exposed here - this class isn't a carriage for this - its used internally to determine if/how we can mine.", "old": "", "peft": "270\tI think this should be `getProtocolContext(ProtocolContext.class)`\n", "icl-diff": "270\tWhy is this public?\n", "icl-code": "", "slm": "Isn't this needed anymore?\n", "label": ["Feat"]}
{"input": "@@ -279,10 +279,14 @@ class Storage {\n       if (uplink == null) {\n         uplink = new Proxy({\n           url: file.url,\n+          cache: true,\n           _autogenerated: true,\n         }, self.config);\n       }\n-      let savestream = self.local.add_tarball(name, filename);\n+      let savestream = null;\n+      if (uplink.config.cache) {\n+        savestream = self.local.add_tarball(name, filename);\n+      }\n       let on_open = function() {\n         // prevent it from being called twice\n         on_open = function() {};", "target": "Do we need `==` for true?", "old": "", "peft": "274\tI think this should be `false` instead of `true`\n", "icl-diff": "274\tnit: why not just `let savestream = uplink.config.cache? self.local.add_tarball(name, filename) : null;`\n", "icl-code": "", "slm": "No need for null check here, `mongoClient.s.options.cryptdConnectionString` can be null\n", "label": ["Feat"]}
{"input": "@@ -142,15 +142,12 @@ axe.utils.getNodeFromTree = function(vNode, node) {\n \t\treturn vNode;\n \t}\n \tvNode.children.forEach(candidate => {\n-\t\tvar retVal;\n-\n-\t\tif (candidate.actualNode === node) {\n+\t\tif (found) {\n+\t\t\treturn;\n+\t\t} else if (candidate.actualNode === node) {\n \t\t\tfound = candidate;\n \t\t} else {\n-\t\t\tretVal = axe.utils.getNodeFromTree(candidate, node);\n-\t\t\tif (retVal) {\n-\t\t\t\tfound = retVal;\n-\t\t\t}\n+\t\t\tfound = axe.utils.getNodeFromTree(candidate, node);\n \t\t}\n \t});\n \treturn found;", "target": "nit: can remove this `else` since we return from the condition above.", "old": "", "peft": "302\tI think this is a bug. If the node is found, we should return it.\n", "icl-diff": "302\tWhy not just `return` in the `if` block?\n", "icl-code": "", "slm": ">removeEntry = true; [](start = 16, length = 10) Could the code be simplified to not use the cachedNATable->isHiveTable()?\n", "label": ["Feat"]}
{"input": "@@ -8,7 +8,8 @@\n var ip = {},\n     net = require('net'),\n     extIP = require('external-ip'),\n-    plugins = require('../../../plugins/pluginManager.js');\n+    plugins = require('../../../plugins/pluginManager.js'),\n+    offlineMode = plugins.getConfig(\"api\").offline_mode;\n \n /**\n  * Function to get the hostname/ip address/url to access dashboard", "target": "Here would be the same case you don't need to call `loadConfigs`, but you would need to reread configs using `getConfig` on each getHost function call, not once per file.", "old": "", "peft": "331\tI think this should be `offlineMode`\n", "icl-diff": "331\tI think it is better to use `offlineMode` instead of `plugins.getConfig(\"api\").offline_mode`\n", "icl-code": "", "slm": "Why minerNode1.verify(clique.blockIsCreatedByProposer(minerNode1)) here?\n", "label": ["Feat"]}
{"input": "@@ -20,7 +20,6 @@\n  * External dependencies\n  */\n import { getDefaultOptions } from 'expect-puppeteer';\n-import { Page, ElementHandle } from 'puppeteer';\n \n /**\n  * Jest matcher for asserting the given instance has tracking loaded or not.", "target": "Why was this removed here (also in the other file)? Shouldn't we import them so that the reference in the docs below is interpreted correctly?", "old": "", "peft": "345\tThis is a breaking change.\n", "icl-diff": "345\tI think this is a bug in the code, it should be `import { Page, ElementHandle } from 'puppeteer';`\n", "icl-code": "", "slm": "Does it make sense to update the doc string to reflect this change?\n", "label": ["Feat"]}
{"input": "@@ -1922,10 +1922,10 @@ detach_on_permanent_stack(bool internal, bool do_cleanup)\n     DEBUG_DECLARE(bool ok;)\n     DEBUG_DECLARE(int exit_res;)\n     /* synch-all flags: if we fail to suspend a thread (e.g., privilege\n-     * problems) ignore it.  XXX Should we retry instead?\n+     * problems) retry it.\n      */\n     /* i#297: we only synch client threads after process exit event. */\n-    uint flags = THREAD_SYNCH_SUSPEND_FAILURE_IGNORE | THREAD_SYNCH_SKIP_CLIENT_THREAD;\n+    uint flags = THREAD_SYNCH_SUSPEND_FAILURE_RETRY | THREAD_SYNCH_SKIP_CLIENT_THREAD;\n \n     ENTERING_DR();\n ", "target": "I am afraid this is going to cause problems on Windows where it is not uncommon to have injected threads (CTRL_SHUTDOWN, CTRL_LOGOFF, etc.) we have no privileges to suspend -- and thus retrying will just fail again, and with the new \"synchall failure is fatal and should kill the process\" approach it turns what used to work for us on Windows into process death.", "old": "", "peft": "348\tI think we should retry instead.\n", "icl-diff": "348\tI think this is a good idea.\n", "icl-code": "", "slm": "I think `Output` was the wrong name.\n", "label": ["Code&Doc"]}
{"input": "@@ -45,6 +45,7 @@ program\n   .option('-C <build_dir>', 'build config (out/Debug, out/Release')\n   .option('--target_arch <target_arch>', 'target architecture', 'x64')\n   .option('--mac_signing_identifier <id>', 'The identifier to use for signing')\n+  .option('--mac_installer_signing_identifier <id>', 'The identifier to use for signing the installer')\n   .option('--mac_signing_keychain <keychain>', 'The identifier to use for signing', 'login')\n   .option('--debug_build <debug_build>', 'keep debugging symbols')\n   .option('--official_build <official_build>', 'force official build settings')", "target": "think it should only be in create_dist", "old": "", "peft": "349\tI think this should be `--mac_installer_signing_identifier`\n", "icl-diff": "349\tAdd a line break after this.\n", "icl-code": "", "slm": "Should we also do the same thing for IsSystemLog?\n", "label": ["Feat"]}
{"input": "@@ -24,7 +24,6 @@ export default Component.extend({\n \n     init() {\n         this._super(...arguments);\n-        this.container = document.querySelector('.gh-editor-container')[0];\n \n         let mobiledoc = this.get('value') || BLANK_DOC;\n         let userCards = this.get('cards') || [];", "target": "I looked and couldn't find any usage of `container` in any of the editor component files (js or hbs), so I assume this was used once and didn't get removed?", "old": "", "peft": "353\tThis is a breaking change.\n", "icl-diff": "353\tI don't see why we need to query the DOM here.\n", "icl-code": "", "slm": "why not just `assert(iv_state);` ?\n", "label": ["Feat"]}
{"input": "@@ -108,6 +108,11 @@ def start_acm(port=None, asynchronous=False):\n     return start_moto_server('acm', port, name='ACM', asynchronous=asynchronous)\n \n \n+def start_ses(port=None, asynchronous=False, update_listener=None):\n+    port = port or config.PORT_SES\n+    return start_moto_server('ses', port, name='SES', asynchronous=asynchronous, update_listener=update_listener)\n+\n+\n # TODO move to es_starter.py?\n def start_elasticsearch_service(port=None, asynchronous=False):\n     port = port or config.PORT_ES", "target": "I think we can remove this function, right? (duplicate with `ses_starter.py`)", "old": "", "peft": "367\tI think this should be `update_listener=update_listener`\n", "icl-diff": "367\tI think this is a good place to use a `with` statement to ensure that the server is stopped when the function exits.\n", "icl-code": "", "slm": "Should we just remove this?\n", "label": ["Feat"]}
{"input": "@@ -1,6 +1,7 @@\n # frozen_string_literal: true\n \n require 'bolt/application'\n+require 'bolt/plan_creator'\n \n require 'bolt_spec/files'\n ", "target": "We should probably just move the `require 'bolt/plan_creator'` in `Bolt::CLI` to `Bolt::Application` so it will already be loaded.", "old": "", "peft": "392\tI think this should be `Bolt::PlanCreator`\n", "icl-diff": "392\tI think it's better to require the plan creator at the top of the file.\n", "icl-code": "", "slm": "What about just `lazy` and `class`?\n", "label": ["Feat"]}
{"input": "@@ -654,7 +654,17 @@ func MigrateRepository(u *User, opts MigrateRepoOptions) (*Repository, error) {\n \t\treturn repo, UpdateRepository(repo, false)\n \t}\n \n-\tif err = createUpdateHook(repoPath); err != nil {\n+\trepo, err = FinishMigrateRepository(repo, repoPath)\n+\tif err != nil {\n+\t\treturn repo, err\n+\t}\n+\n+\treturn repo, UpdateRepository(repo, false)\n+}\n+\n+// Finish migrating repository with things that don't need to be done for mirrors.\n+func FinishMigrateRepository(repo *Repository, repoPath string) (*Repository, error) {\n+\tif err := createUpdateHook(repoPath); err != nil {\n \t\treturn repo, fmt.Errorf(\"createUpdateHook: %v\", err)\n \t}\n ", "target": "Better call it `CleanUpMirrorInfo`?", "old": "", "peft": "395\tI think we should move this to the `MigrateRepository` function.\n", "icl-diff": "395\tI think this is a bug. The `createUpdateHook` function is called twice, once in the `if` and once in the `else`.\n", "icl-code": "", "slm": "Consider renaming `FileUtil` to `DownloadHelper`\n", "label": ["Code&Doc"]}
{"input": "@@ -146,6 +146,12 @@ class HdfsClient(hdfs_abstract_client.HdfsFileSystem):\n     def put(self, local_path, destination):\n         self.call_check(load_hadoop_cmd() + ['fs', '-put', local_path, destination])\n \n+    def append(self, local_path, destination):\n+        \"\"\"\n+        Requires Hadoop >= 2.3.0\n+        \"\"\"\n+        call_check(load_hadoop_cmd() + ['fs', '-appendToFile', local_path, destination])\n+\n     def get(self, path, local_destination):\n         self.call_check(load_hadoop_cmd() + ['fs', '-get', path, local_destination])\n ", "target": "Good that you mention this constraint in the docstring :)", "old": "", "peft": "412\tI think this should be `load_hadoop_cmd()` + ['fs', '-append', local_path, destination]\n", "icl-diff": "412\tWhy not use `os.system`?\n", "icl-code": "", "slm": "Do you need to add `noImplicit: true` here as well?\n", "label": ["Code&Doc"]}
{"input": "@@ -7,13 +7,7 @@ test_name \"bolt plan run should apply manifest block on remote hosts via ssh\" do\n   extend Acceptance::BoltCommandHelper\n \n   ssh_nodes = select_hosts(roles: ['ssh'])\n-  skip_targets = select_hosts(platform: [/debian-8/])\n   targets = \"ssh_nodes\"\n-  if skip_targets.any?\n-    ssh_nodes -= skip_targets\n-    targets = ssh_nodes.each_with_object([]) { |node, acc| acc.push(node[:vmhostname]) }.join(\",\")\n-  end\n-\n   skip_test('no applicable nodes to test on') if ssh_nodes.empty?\n \n   dir = bolt.tmpdir('apply_ssh')", "target": "Since this var is no longer defined we should remove the `if skip_targets.any?` bit below.", "old": "", "peft": "423\tThis is a bit of a hack, but I think it's the best way to get this test to work.\n", "icl-diff": "423\tI think this is a good use of `select_hosts` but I think it could be a bit more readable. I think the `select_hosts` could be a bit more readable. I think the `select_hosts` could be a bit more readable. I think the `select_hosts` could be a bit more readable. I think the `select_hosts` could be a bit more readable. I think the `select_hosts` could be a bit more readable. I\n", "icl-code": "", "slm": "Use the new Ruby 1.9 hash syntax.\n", "label": ["Feat"]}
{"input": "@@ -250,7 +250,7 @@ func TestS3_EmptyBucket(t *testing.T) {\n \n \t\t\tgotErr := service.EmptyBucket(tc.inBucket)\n \n-\t\t\tif gotErr != nil {\n+\t\t\tif tc.wantErr != nil {\n \t\t\t\trequire.EqualError(t, gotErr, tc.wantErr.Error())\n \t\t\t}\n \t\t})", "target": "The test case already existed but it never tested properly because of the conditional in the test.", "old": "", "peft": "476\tI think this should be `if tc.wantErr!= nil`\n", "icl-diff": "476\tThe error message is not checked.\n", "icl-code": "", "slm": "Do we still need to check `valid_encoding?`? What happens if `encoding` is `utf-8`? Should we force_encoding('utf-8') if valid_encoding?\n", "label": ["Feat"]}
{"input": "@@ -499,6 +499,11 @@ def data(readonly=False):\n              SettingValue(typ.Bool(), 'true'),\n              \"Whether to show favicons in the tab bar.\"),\n \n+            ('tabbar-size',\n+             SettingValue(typ.Int(minval=8), '12'),\n+             \"The height of the tabbar in pixels.\"\n+             \"This also controls the size of the favicons.\"),\n+\n             ('width',\n              SettingValue(typ.PercOrInt(minperc=0, maxperc=100, minint=1),\n                           '20%'),", "target": "As these two strings simply get concatenated for the docs, there's a space missing after the dot here.", "old": "", "peft": "493\tI think this should be `minval=1`\n", "icl-diff": "493\tI think the default should be 12, not 10.\n", "icl-code": "", "slm": "Isn't there a test that tests this?\n", "label": ["Feat"]}
{"input": "@@ -596,6 +596,8 @@ def initialize():\n \tif mainFrame:\n \t\traise RuntimeError(\"GUI already initialized\")\n \tmainFrame = MainFrame()\n+\twxLang = core.getWxLang(languageHandler.getLanguage())\n+\tmainFrame.SetLayoutDirection(wxLang.LayoutDirection)\n \twx.GetApp().SetTopWindow(mainFrame)\n \t# In wxPython >= 4.1,\n \t# wx.CallAfter no longer executes callbacks while NVDA's main thread is within apopup menu or message box.", "target": "What if `wxLang` is returned `None` from `getWxLang`", "old": "", "peft": "531\tI think this is a bug in NVDA. I'm not sure if it's a bug in wxPython.\n", "icl-diff": "531\tAdd a line break after this.\n", "icl-code": "", "slm": "This also seems to be a bit trickier. `File.expand_path?` is the same as `File.exist?(path)`\n", "label": ["Feat"]}
{"input": "@@ -38,7 +38,7 @@ func downloadAndExtractConfigPackage(channel string, targetDir string) (err erro\n }\n \n func downloadConfigPackage(channelName string, targetDir string) (packageFile string, err error) {\n-\ts3, err := s3.MakeS3SessionForDownload()\n+\ts3, err := s3.MakePublicS3SessionForDownload()\n \tif err != nil {\n \t\treturn\n \t}", "target": "Nodecfg should be downloading from a private bucket -- these shouldn't be for public consumption. In general these should be generic and expect environment to provide appropriate credentials and bucket.", "old": "", "peft": "542\tI think this should be `MakeS3SessionForDownload()`\n", "icl-diff": "542\tThis is a public bucket, so it should be public.\n", "icl-code": "", "slm": "(style) 'foo' may be misspelled - perhaps 'foo'?\n", "label": ["Ref"]}
{"input": "@@ -238,9 +238,14 @@ class DetectoRS_ResNet(ResNet):\n                  pretrained=None,\n                  init_cfg=None,\n                  **kwargs):\n-        assert init_cfg is None, 'To prevent abnormal initialization ' \\\n-                                 'behavior, init_cfg is not allowed to be set'\n-        self.pretrained = pretrained\n+        self.init_cfg = init_cfg\n+        if init_cfg is not None:  # init_cfg priority > pretrained\n+            self.pretrained = init_cfg['checkpoint']\n+        else:\n+            if pretrained is not None:\n+                self.pretrained = pretrained\n+            else:\n+                self.pretrained = None\n         self.sac = sac\n         self.stage_with_sac = stage_with_sac\n         self.rfp_inplanes = rfp_inplanes", "target": "For insurance, it's best not to take it directly", "old": "", "peft": "599\tI think we should add a warning here.\n", "icl-diff": "599\tI think this is a bug.\n", "icl-code": "", "slm": "nit: rename to `upgradetemplates`\n", "label": ["Feat"]}
{"input": "@@ -50,8 +50,8 @@ class Phase < ActiveRecord::Base\n   has_many :suffix_sections, -> (phase) {\n     modifiable.where(<<~SQL, phase_id: phase.id, modifiable: false)\n       sections.number > (SELECT MAX(number) FROM sections\n-                           WHERE sections.modifiable = :modifiable)\n-                           AND sections.phase_id = :phase_id\n+                           WHERE sections.modifiable = :modifiable\n+                           AND sections.phase_id = :phase_id)\n     SQL\n   }, class_name: \"Section\"\n ", "target": "@briri Thanks! I just caught this too updating my feature branch", "old": "", "peft": "605\tThis is a bit of a nitpick, but I think it's better to use `where` instead of `modifiable.where`.\n", "icl-diff": "605\tnit: s/modifiable/modifiable_sections/ on line 51.\n", "icl-code": "", "slm": "Can we call this `labels` instead of `in`?\n", "label": ["Ref"]}
{"input": "@@ -73,7 +73,8 @@ func dependenciesCanBeResolved(target *api.Container, by []*api.Container) bool\n \t}\n \n \treturn verifyStatusResolvable(target, nameMap, neededVolumeContainers, volumeCanResolve) &&\n-\t\tverifyStatusResolvable(target, nameMap, linksToContainerNames(target.Links), linkCanResolve)\n+\t\tverifyStatusResolvable(target, nameMap, linksToContainerNames(target.Links), linkCanResolve) &&\n+\t\tverifyStatusResolvable(target, nameMap, target.SteadyStateDependencies, onSteadyStateCanResolve)\n }\n \n // DependenciesAreResolved validates that the `target` container can be", "target": "Can this line be removed, as `SteadyStateDependencies` isn't updated anywhere? Also do we need to check the `TransitionDependencySet` here?", "old": "", "peft": "621\tI think this should be `verifyStatusResolvable`\n", "icl-diff": "621\t@gianm this causes lots of retries for duplicate primary key entry errors that will never be transient unless someone manually cleans the DB\n", "icl-code": "", "slm": "Doesn't this need to check for whether the device is `bind`?\n", "label": ["Feat"]}
{"input": "@@ -0,0 +1,14 @@\n+MAIL_SETTINGS = {\n+  address: \"smtp.sendgrid.net\",\n+  port: \"587\",\n+  authentication: :plain,\n+  user_name: ENV[\"SENDGRID_USERNAME\"],\n+  password: ENV[\"SENDGRID_PASSWORD\"],\n+  domain: \"heroku.com\"\n+}\n+\n+if ENV[\"EMAIL_RECIPIENTS\"]\n+  Mail.register_interceptor(\n+    RecipientInterceptor.new(ENV.fetch(\"EMAIL_RECIPIENTS\")),\n+  )\n+end", "target": "Freeze mutable objects assigned to constants.", "old": "", "peft": "640\tI think this should be `ENV[\"SENDGRID_USERNAME\"]`\n", "icl-diff": "640\tI think this is a good idea, but I'm not sure if it's a good idea to use ENV variables for this.\n", "icl-code": "", "slm": "What is the reason for this change?\n", "label": ["Feat"]}
{"input": "@@ -296,9 +296,17 @@ public class NavListAdapter extends BaseAdapter\n                 .getSystemService(Context.LAYOUT_INFLATER_SERVICE);\n \n         convertView = inflater.inflate(R.layout.nav_section_item, parent, false);\n+        TextView feedsFilteredMsg = convertView.findViewById(R.id.nav_feeds_filtered_message);\n \n-        convertView.setEnabled(false);\n-        convertView.setOnClickListener(null);\n+        if (UserPreferences.getFeedFilter() != UserPreferences.FEED_FILTER_NONE) {\n+            convertView.setEnabled(true);\n+            feedsFilteredMsg.setText(\"{md-info-outline} \" + context.getString(R.string.feed_is_filtered));\n+            Iconify.addIcons(feedsFilteredMsg);\n+            feedsFilteredMsg.setVisibility(View.VISIBLE);\n+        } else {\n+            convertView.setEnabled(false);\n+            feedsFilteredMsg.setVisibility(View.GONE);\n+        }\n \n         return convertView;\n     }", "target": "There is an option to hide the subscriptions list from the sidebar. If it is hidden, the filter text should not be displayed.", "old": "", "peft": "644\tI think this should be `if (UserPreferences.getFeedFilter()!= UserPreferences.FEED_FILTER_NONE) {`\n", "icl-diff": "644\t@gianm this is a very bad idea. It's not the adapter's job to decide whether the user wants to see the feed or not.\n", "icl-code": "", "slm": "I don't think there should be a newline above this line.\n", "label": ["Feat"]}
{"input": "@@ -192,7 +192,7 @@ public interface Context {\n                 methodUsage = ((TypeVariableResolutionCapability) methodDeclaration)\n                                       .resolveTypeVariables(this, argumentsTypes);\n             } else {\n-                throw new UnsupportedOperationException();\n+                return Optional.empty();\n             }\n \n             return Optional.of(methodUsage);", "target": "mmm, why a method declaration should not have the TypeVariableResolutionCapability? Is this ok?", "old": "", "peft": "659\tI think this should be `return Optional.empty();`\n", "icl-diff": "659\t@gianm I think this is a bug in the compiler. It should be `return Optional.empty();`\n", "icl-code": "", "slm": "Use 2 (not 0) spaces for indentation.\n", "label": ["Feat"]}
{"input": "@@ -4,7 +4,7 @@ define([\"jQuery\", \"loading\", \"globalize\", \"dom\"], function($, loading, globalize\n     function loadPage(page, config, systemInfo) {\n         Array.prototype.forEach.call(page.querySelectorAll(\".chkDecodeCodec\"), function(c) {\n             c.checked = -1 !== (config.HardwareDecodingCodecs || []).indexOf(c.getAttribute(\"data-codec\"))\n-        }), page.querySelector(\"#chkHardwareEncoding\").checked = config.EnableHardwareEncoding, $(\"#selectVideoDecoder\", page).val(config.HardwareAccelerationType), $(\"#selectThreadCount\", page).val(config.EncodingThreadCount), $(\"#txtDownMixAudioBoost\", page).val(config.DownMixAudioBoost), page.querySelector(\".txtEncoderPath\").value = config.EncoderAppPath || \"\", $(\"#txtTranscodingTempPath\", page).val(config.TranscodingTempPath || \"\"), $(\"#txtVaapiDevice\", page).val(config.VaapiDevice || \"\"), page.querySelector(\"#selectH264Preset\").value = config.H264Preset || \"\", page.querySelector(\"#txtH264Crf\").value = config.H264Crf || \"\", page.querySelector(\"#chkEnableSubtitleExtraction\").checked = config.EnableSubtitleExtraction || !1, page.querySelector(\"#selectVideoDecoder\").dispatchEvent(new CustomEvent(\"change\", {\n+        }), page.querySelector(\"#chkHardwareEncoding\").checked = config.EnableHardwareEncoding, $(\"#selectVideoDecoder\", page).val(config.HardwareAccelerationType), $(\"#selectThreadCount\", page).val(config.EncodingThreadCount), $(\"#txtDownMixAudioBoost\", page).val(config.DownMixAudioBoost), page.querySelector(\".txtEncoderPath\").value = config.EncoderAppPathDisplay || \"\", $(\"#txtTranscodingTempPath\", page).val(config.TranscodingTempPath || \"\"), $(\"#txtVaapiDevice\", page).val(config.VaapiDevice || \"\"), page.querySelector(\"#selectH264Preset\").value = config.H264Preset || \"\", page.querySelector(\"#txtH264Crf\").value = config.H264Crf || \"\", page.querySelector(\"#chkEnableSubtitleExtraction\").checked = config.EnableSubtitleExtraction || !1, page.querySelector(\"#selectVideoDecoder\").dispatchEvent(new CustomEvent(\"change\", {\n             bubbles: !0\n         })), loading.hide()\n     }", "target": "can you de-uglify at least this line?.. hard to tell what changed...", "old": "", "peft": "661\tI think we should use `config.HardwareAccelerationType` instead of `config.EnableHardwareEncoding`\n", "icl-diff": "661\t@gianm I think this is a bug in the code. The `||` should be `&&`\n", "icl-code": "", "slm": "It would make sense to just have a damage += uniform_random(armor % 2 + 1); if (armor % 2 != 0) { damage += uniform_random(armor % 2 + 1); } else if (armor % 2 != 0) { damage += uniform_random(armor % 2 + 1); }\n", "label": ["Feat"]}
{"input": "@@ -46,6 +46,10 @@ type (\n \t}\n \n \tdefaultServiceNameDetector struct{}\n+\n+\t// noOp is a Detector that only provides an empty resource.  Used\n+\t// to disable automatic detection.\n+\tnoOp struct{}\n )\n \n var (", "target": "Need to update the doc for `TelemetrySDK` and `Host` structs deleting references from removed functions.", "old": "", "peft": "682\tI'm not sure if this is the right place to put this, but I think it's better to have this in the `k8s.io/kubernetes/pkg/kubelet/apis/config/v1alpha1/config` package.\n", "icl-diff": "682\tI think this is a good idea, but I'm not sure why it's necessary.\n", "icl-code": "", "slm": "Should add a pointer to `uint32` for this as well.\n", "label": ["Code&Doc"]}
{"input": "@@ -20,4 +20,10 @@ class License < ActiveRecord::Base\n   def short_name\n     abbreviation.blank? ? nice_name : abbreviation\n   end\n+\n+  class << self\n+    def autocomplete(term)\n+      License.select([:nice_name, :id]).where(['lower(nice_name) LIKE ?', \"#{term.downcase}%\"]).limit(10)\n+    end\n+  end\n end", "target": "I understand that this grabs a Licenses objects but what is the autocomplete method used for? What does this do in context of the auto_completes controller?", "old": "", "peft": "694\tI think this should be `License.select([:nice_name, :id]).where(['lower(nice_name) LIKE?', \"#{term.downcase}%\"]).limit(10)`\n", "icl-diff": "694\t@gianm I think this is a good idea, but I'm not sure if it's worth the effort.\n", "icl-code": "", "slm": "This is only used in the tests. I don't think it makes sense to keep it here.\n", "label": ["Feat"]}
{"input": "@@ -11,9 +11,10 @@ import struct\n \n from scapy.compat import orb, chb\n from scapy.config import conf\n-from scapy.data import MTU, DLT_BLUETOOTH_LE_LL\n+from scapy.data import MTU, DLT_BLUETOOTH_LE_LL, DLT_BLUETOOTH_LE_LL_WITH_PHDR\n from scapy.packet import *\n from scapy.fields import *\n+from scapy.layers.dot11 import _dbmField\n from scapy.layers.ppi import PPI, addPPIType, PPIGenericFldHdr\n \n from scapy.contrib.ppi_geotag import XLEIntField, XLEShortField", "target": "Moved to the bottom of the file. `CtrlPDU` sits on `BTLE_DATA` so let's put it after", "old": "", "peft": "701\tI think we should use `DLT_BLUETOOTH_LE_LL_WITH_PHDR` instead of `DLT_BLUETOOTH_LE_LL_WITH_PHDR_WITH_PHDR`\n", "icl-diff": "701\tPlease remove extra space before `=\n", "icl-code": "", "slm": "Actually, I think we should call this `optionals` instead of `options`.\n", "label": ["Feat"]}
{"input": "@@ -2,7 +2,8 @@ C2::Application.routes.draw do\n   ActiveAdmin.routes(self)\n   root :to => 'home#index'\n   get '/error' => 'home#error'\n-  get '/me'    => 'home#me'\n+  get '/me'    => 'profile#show'\n+  post '/me'   => 'profile#update'\n   get '/feedback' => 'feedback#index'\n   get '/feedback/thanks' => 'feedback#thanks'\n   post '/feedback' => 'feedback#create'", "target": "why use these rather than regular named paths?", "old": "", "peft": "716\tI think this should be `get '/me' =\n", "icl-diff": "716\tI think it is better to use `resources :profile` instead of `get '/me' =\n", "icl-code": "", "slm": "Could be a constant with a better name\n", "label": ["Feat"]}
{"input": "@@ -106,12 +106,14 @@ func TestBuilderForYAML(t *testing.T) {\n \t\t\"Test 2\": {fakeInvalidK8sResource, \"\", true},\n \t}\n \tfor name, mock := range tests {\n+\t\tname := name // pin it\n+\t\tmock := mock // pin it\n \t\tt.Run(name, func(t *testing.T) {\n \t\t\tb := BuilderForYaml(mock.resourceYAML)\n \t\t\tif mock.expectError && len(b.errs) == 0 {\n \t\t\t\tt.Fatalf(\"Test %s failed, expected err but got 0\", name)\n-\t\t\t} else if b.unstruct.object.GetName() != mock.expectedName {\n-\t\t\t\tt.Fatalf(\"Test %s failed, expected %v but got %v\", name, mock.expectedName, b.unstruct.object.GetName())\n+\t\t\t} else if b.unstruct.Object.GetName() != mock.expectedName {\n+\t\t\t\tt.Fatalf(\"Test %s failed, expected %v but got %v\", name, mock.expectedName, b.unstruct.Object.GetName())\n \t\t\t}\n \t\t})\n \t}", "target": "Using the variable on range scope `mock` in function literal (from `scopelint`)", "old": "", "peft": "725\tI think we should pin the `name` and `mock` variables to the `t` variable.\n", "icl-diff": "725\t@gianm I think this is a bug in the test, not the code.\n", "icl-code": "", "slm": "If my understanding is correct, this file should be named `jest-test.js`, since it's part of the Storybook plugin.\n", "label": ["Feat"]}
{"input": "@@ -1020,7 +1020,7 @@ public class Datasets extends AbstractApiBean {\n             PublishDatasetResult res = execCommand(new PublishDatasetCommand(ds,\n                         createDataverseRequest(user),\n                     isMinor));\n-            return res.isCompleted() ? ok(json(res.getDataset())) : accepted(json(res.getDataset()));\n+            return res.isWorkflow() ? accepted(json(res.getDataset())) : ok(json(res.getDataset()));\n             }\n         } catch (WrappedResponse ex) {\n             return ex.getResponse();", "target": "does this result in a 200 when the dataset is still inprogress/publishing not yet finalized? Seems like 202 is the right code for that (as it was) and the test should be watching for a 202?", "old": "", "peft": "726\tI think this is a bug. The `isWorkflow` method should return `true` if the dataset is a workflow.\n", "icl-diff": "726\t@gianm I think this is a bug, because the workflow is not completed until the publication is done, so it should be accepted\n", "icl-code": "", "slm": "Could we import `from` at the top of the file?\n", "label": ["Feat"]}
{"input": "@@ -18,6 +18,7 @@ try:\n except ImportError:\n   from rdkit.piddle import piddle\n import ClusterUtils\n+from rdkit.six.moves import xrange\n \n import numpy\n ", "target": "same question: why not just switch this to range too?", "old": "", "peft": "755\tI think this should be `range`\n", "icl-diff": "755\tI don't think it improves performance that much, but you could stream the result from allItems() and collect it into a list, avoiding the for loop.\n", "icl-code": "", "slm": "Maybe add a docstring to this module to explain what permissions do you need to use?\n", "label": ["Feat"]}
{"input": "@@ -727,7 +727,7 @@ def processNegativeStates(role, states, reason, negativeStates=None):\n \t# but only if it is either focused or this is something other than a change event.\n \t# The condition stops \"not selected\" from being spoken in some broken controls\n \t# when the state change for the previous focus is issued before the focus change.\n-\tif role in (ROLE_LISTITEM, ROLE_TREEVIEWITEM, ROLE_TABLEROW) and STATE_SELECTABLE in states and (reason != REASON_CHANGE or STATE_FOCUSED in states):\n+\tif role in (ROLE_LISTITEM, ROLE_TREEVIEWITEM, ROLE_TABLEROW,ROLE_TABLECELL,ROLE_TABLECOLUMNHEADER,ROLE_TABLEROWHEADER) and STATE_SELECTABLE in states and (reason != REASON_CHANGE or STATE_FOCUSED in states):\n \t\tspeakNegatives.add(STATE_SELECTED)\n \t# Restrict \"not checked\" in a similar way to \"not selected\".\n \tif (role in (ROLE_CHECKBOX, ROLE_RADIOBUTTON, ROLE_CHECKMENUITEM) or STATE_CHECKABLE in states)  and (STATE_HALFCHECKED not in states) and (reason != REASON_CHANGE or STATE_FOCUSED in states):", "target": "Could you split this into multiple lines?", "old": "", "peft": "779\tI think this should be `ROLE_CHECKBOX, ROLE_RADIOBUTTON, ROLE_CHECKMENUITEM, ROLE_CHECKMENUITEMGROUP`\n", "icl-diff": "779\tWhy not `if role in (ROLE_LISTITEM, ROLE_TREEVIEWITEM, ROLE_TABLEROW,ROLE_TABLECELL,ROLE_TABLECOLUMNHEADER,ROLE_TABLEROWHEADER,ROLE_TABLECOLUMNHEADER,ROLE_TABLEROWHEADER) and STATE_SELECTABLE in states and (reason!= REASON_CHANGE or\n", "icl-code": "", "slm": "So the default is 5s?\n", "label": ["Feat"]}
{"input": "@@ -666,9 +666,10 @@ class AbstractAudio(QObject):\n     muted_changed = pyqtSignal(bool)\n     recently_audible_changed = pyqtSignal(bool)\n \n-    def __init__(self, parent=None):\n+    def __init__(self, tab, parent=None):\n         super().__init__(parent)\n         self._widget = None\n+        self._tab = tab\n \n     def set_muted(self, muted: bool):\n         \"\"\"Set this tab as muted or not.\"\"\"", "target": "You'll also need to adjust `FakeWebTabAudio` in `tests/helpers/stubs.py`.", "old": "", "peft": "791\tI think this should be `self._tab = tab`\n", "icl-diff": "791\tI don't see why this is a QObject.\n", "icl-code": "", "slm": "why do we need this? won't it always update?\n", "label": ["Feat"]}
{"input": "@@ -492,10 +492,13 @@ class MainWindow(QWidget):\n     @pyqtSlot(bool)\n     def _on_fullscreen_requested(self, on):\n         if on:\n-            self.state_before_fullscreen = self.windowState()\n-            self.showFullScreen()\n+            self.window_state_before_fullscreen = self.windowState()\n+            self.config_state_before_fullscreen = config.val.content.desktop_fullscreen\n+            if config.val.content.desktop_fullscreen:\n+                self.showFullScreen()\n         elif self.isFullScreen():\n-            self.setWindowState(self.state_before_fullscreen)\n+            if config.val.content.desktop_fullscreen or self.config_state_before_fullscreen:\n+                self.setWindowState(self.window_state_before_fullscreen)\n         log.misc.debug('on: {}, state before fullscreen: {}'.format(\n             on, debug.qflags_key(Qt, self.state_before_fullscreen)))\n ", "target": "If you really want to rename this, you'll also need to adjust the name in `__init__` and in other places it's used (`browser/commands.py`).", "old": "", "peft": "807\tI think this is a bug. If the user has the desktop fullscreen enabled, and then they click the fullscreen button, the window should not go fullscreen.\n", "icl-diff": "807\tI think this is a bug.\n", "icl-code": "", "slm": "This should probably be mutable, since `getMutable` does not return anything for these nodes.\n", "label": ["Feat"]}
{"input": "@@ -63,6 +63,9 @@ const (\n \n \t// SecretTypeEnv is to show secret type being ENVIRONMENT_VARIABLE\n \tSecretTypeEnv = \"ENVIRONMENT_VARIABLE\"\n+\n+\t// TargetLogDriver is to show secret target being \"LOG_DRIVER\", the default will be \"CONTAINER\"\n+\tSecretTargetLogDriver = \"LOG_DRIVER\"\n )\n \n // DockerConfig represents additional metadata about a container to run. It's", "target": "where is the default being set?", "old": "", "peft": "810\tI think this should be `SecretTargetLogDriver`\n", "icl-diff": "810\t@gianm I think this is a typo, should be `SecretTargetLogDriver = \"LOG_DRIVER\"`\n", "icl-code": "", "slm": "Why is this line being removed?\n", "label": ["Code&Doc"]}
{"input": "@@ -31,6 +31,14 @@ import java.util.Date;\n  */\n public class DefaultHistoryRemovalTimeProvider implements HistoryRemovalTimeProvider {\n \n+  public static Date determineRemovalTime(Date initTime, Integer timeToLive) {\n+    Calendar removalTime = Calendar.getInstance();\n+    removalTime.setTime(initTime);\n+    removalTime.add(Calendar.DATE, timeToLive);\n+    \n+    return removalTime.getTime();\n+  }\n+\n   public Date calculateRemovalTime(HistoricProcessInstanceEventEntity historicRootProcessInstance, ProcessDefinition processDefinition) {\n \n     Integer historyTimeToLive = processDefinition.getHistoryTimeToLive();", "target": "Let's only change a file if it is really necessary. Such a change does not bring much value and makes it harder to find the original commit in which the method was introduced.", "old": "", "peft": "818\tI think this should be `return removalTime.getTimeInMillis()`\n", "icl-diff": "818\tThis is a static method, so it should be called as `DefaultHistoryRemovalTimeProvider.determineRemovalTime(initTime, timeToLive)`\n", "icl-code": "", "slm": "I have no idea what the errors we want to compare against...\n", "label": ["Feat"]}
{"input": "@@ -217,15 +217,10 @@ export default class App extends Component {\n \n   // eslint-disable-next-line no-unused-vars\n   handleClickSearch = (_, { suggestionValue, method }) => {\n-    const { packages } = this.state;\n     switch(method) {\n       case 'click':\n-        window.location.href = getDetailPageURL(suggestionValue);\n-      break;\n       case 'enter':\n-        this.setState({\n-          filteredPackages: packages.filter(pkg => pkg.label.match(suggestionValue))\n-        });\n+        window.location.href = getDetailPageURL(suggestionValue);\n       break;\n     }\n   }", "target": "I think we need this. Not sure, I'll test.", "old": "", "peft": "825\tI think we should remove this line.\n", "icl-diff": "825\tI don't think this is correct. The `packages` state is not updated when the user types in the search box.\n", "icl-code": "", "slm": "Do we want to show the full error page?\n", "label": ["Feat"]}
{"input": "@@ -110,12 +110,14 @@ public class SolrMetricManager {\n \n   public static final int DEFAULT_CLOUD_REPORTER_PERIOD = 60;\n \n-  private MetricRegistry.MetricSupplier<Counter> counterSupplier;\n-  private MetricRegistry.MetricSupplier<Meter> meterSupplier;\n-  private MetricRegistry.MetricSupplier<Timer> timerSupplier;\n-  private MetricRegistry.MetricSupplier<Histogram> histogramSupplier;\n+  private final MetricsConfig metricsConfig;\n+  private final MetricRegistry.MetricSupplier<Counter> counterSupplier;\n+  private final MetricRegistry.MetricSupplier<Meter> meterSupplier;\n+  private final MetricRegistry.MetricSupplier<Timer> timerSupplier;\n+  private final MetricRegistry.MetricSupplier<Histogram> histogramSupplier;\n \n   public SolrMetricManager() {\n+    metricsConfig = new MetricsConfig.MetricsConfigBuilder().build();\n     counterSupplier = MetricSuppliers.counterSupplier(null, null);\n     meterSupplier = MetricSuppliers.meterSupplier(null, null);\n     timerSupplier = MetricSuppliers.timerSupplier(null, null);", "target": "*NULL_DEREFERENCE:* object `null` is dereferenced by call to `meterSupplier(...)` at line 122.", "old": "", "peft": "830\tI think we should use the same `MetricsConfig` instance for all the `MetricRegistry.MetricSupplier` instances.\n", "icl-diff": "830\tI don't think it improves performance that much, but you could stream the result from allItems() and collect it into a list, avoiding the for loop.\n", "icl-code": "", "slm": "Is this needed? I thought `ToInt64` already works with littleEndian if it's not just in case.\n", "label": ["Feat"]}
{"input": "@@ -206,6 +206,16 @@ func (d *Dispatcher) Inbounds() Inbounds {\n \treturn inbounds\n }\n \n+// Outbounds returns a copy of the map of outbounds for this RPC object.\n+// The outbounds are already wrapped with middleware\n+func (d *Dispatcher) Outbounds() Outbounds {\n+\toutbounds := make(Outbounds, len(d.outbounds))\n+\tfor k, v := range d.outbounds {\n+\t\toutbounds[k] = v\n+\t}\n+\treturn outbounds\n+}\n+\n // ClientConfig provides the configuration needed to talk to the given\n // service through an outboundKey. This configuration may be directly\n // passed into encoding-specific RPC clients.", "target": "We can assert on the existence of Outbounds through ClientConfig calls right? Since this is only for tests, do we need this function?", "old": "", "peft": "859\tI think this should be `d.outbounds` instead of `d.outboundKeys`\n", "icl-diff": "859\tThis is a comment, not a function.\n", "icl-code": "", "slm": "can you rename this to `mergeBinaryAnnotation`?\n", "label": ["Code&Doc"]}
{"input": "@@ -89,6 +89,13 @@ class SingleStageDetector(BaseDetector):\n         Returns:\n             dict[str, Tensor]: A dictionary of loss components.\n         \"\"\"\n+        # NOTE the batched image size information may be useful, e.g.\n+        # in DETR, this is needed for the construction of masks, which is\n+        # then used for the transformer_head.\n+        input_img_shape = tuple(img.size()[-2:])\n+        for img_meta in img_metas:\n+            img_meta['input_img_shape'] = input_img_shape\n+\n         x = self.extract_feat(img)\n         losses = self.bbox_head.forward_train(x, img_metas, gt_bboxes,\n                                               gt_labels, gt_bboxes_ignore)", "target": "Are these modification duplicate? Or should we move it into base detector.", "old": "", "peft": "861\tI think we should add a `input_img_shape` field to `img_meta`\n", "icl-diff": "861\tI don't think this is necessary, because the `img_metas` are already passed to the `bbox_head`.\n", "icl-code": "", "slm": "I don't understand why you moved the construction of the error check here, but why move it inside the `if err != nil && !strings.HasSuffix(err.Error(), err.Error())` test?\n", "label": ["Code&Doc"]}
{"input": "@@ -80,7 +80,7 @@ func TestOpImmediateNote(t *testing.T) {\n func TestOpDocExtra(t *testing.T) {\n \txd := OpDocExtra(\"bnz\")\n \trequire.NotEmpty(t, xd)\n-\txd = OpDocExtra(\"+\")\n+\txd = OpDocExtra(\"-\")\n \trequire.Empty(t, xd)\n }\n ", "target": "nit: shouldn't be part of this PR.", "old": "", "peft": "885\tI think this is a bug. The `+` and `-` should be ignored.\n", "icl-diff": "885\tI think this is a typo.\n", "icl-code": "", "slm": "`s/Synax highlighting and line numbers/html/`\n", "label": ["Feat"]}
{"input": "@@ -239,6 +239,10 @@ public class ExecuteFlowAction implements TriggerAction {\n     }\n     exflow.setExecutionOptions(executionOptions);\n \n+    if (slaOptions != null && slaOptions.size() > 0) {\n+      exflow.setSlaOptions(slaOptions);\n+    }\n+\n     try {\n       logger.info(\"Invoking flow \" + project.getName() + \".\" + flowName);\n       executorManager.submitExecutableFlow(exflow, submitUser);", "target": "@chengren311 : where does this logic move to?", "old": "", "peft": "893\tI think we should add a check here to make sure that the `slaOptions` is not null.\n", "icl-diff": "893\tWhy not use `if (slaOptions!= null && slaOptions.size() \n", "icl-code": "", "slm": "There doesn't seem to be a test case for these\n", "label": ["Feat"]}
{"input": "@@ -12,9 +12,15 @@ func NewClientFake(IP string) Client {\n \n type clientFake struct {\n \tip string\n+\toutboundIp string\n }\n \n-func (client *clientFake) GetIp() (string, error) {\n+func (client *clientFake) GetPublicIP() (string, error) {\n+\tlog.Info(IPIFY_API_LOG_PREFIX, \"IP faked: \", client.ip)\n+\treturn client.ip, nil\n+}\n+\n+func (client *clientFake) GetOutboundIP() (string, error) {\n \tlog.Info(IPIFY_API_LOG_PREFIX, \"IP faked: \", client.ip)\n \treturn client.ip, nil\n }", "target": "Should be `client.outboundIp`", "old": "", "peft": "916\tI think we should use `GetOutboundIP` instead of `GetPublicIP`\n", "icl-diff": "916\tI think you should return the outbound ip instead of the ip.\n", "icl-code": "", "slm": "I think this would fail on empty input elements.\n", "label": ["Feat"]}
{"input": "@@ -94,6 +94,7 @@ public class SyncManager {\n         this.smartStore = smartStore;\n         this.restClient = restClient;\n         SyncState.setupSyncsSoupIfNeeded(smartStore);\n+        SyncState.cleanupSyncsSoupIfNeeded(smartStore);\n     }\n \n     /**", "target": "The cleanup call", "old": "", "peft": "935\tI think this should be done in `SyncState.setupSyncsSoupIfNeeded`\n", "icl-diff": "935\tI don't think this is needed.\n", "icl-code": "", "slm": "Why not just change this from TRUE to FALSE?\n", "label": ["Feat"]}
{"input": "@@ -19,7 +19,7 @@ if (is_array($env = @include dirname(__DIR__).'/.env.local.php')) {\n     throw new RuntimeException('Please run \"composer require symfony/dotenv\" to load the \".env\" files configuring the application.');\n } else {\n     $path = dirname(__DIR__).'/.env';\n-    $dotenv = new Dotenv(false);\n+    $dotenv = new Dotenv(true);\n \n     // load all the .env files\n     if (method_exists($dotenv, 'loadEnv')) {", "target": "Allow putenv is necessary to be able to get environment variables with `getenv`?", "old": "", "peft": "962\tI think this should be `true` by default.\n", "icl-diff": "962\tI think it should be `new Dotenv(true)`\n", "icl-code": "", "slm": "what is the purpose of this change?\n", "label": ["Feat"]}
{"input": "@@ -1130,7 +1130,9 @@ def getControlFieldSpeech(attrs,ancestorAttrs,fieldType,formatConfig=None,extraD\n \telse:\n \t\ttableID = None\n \n-\troleText=getSpeechTextForProperties(reason=reason,role=role)\n+\troleText=attrs.get('roleText')\n+\tif not roleText:\n+\t\troleText=getSpeechTextForProperties(reason=reason,role=role)\n \tstateText=getSpeechTextForProperties(reason=reason,states=states,_role=role)\n \tkeyboardShortcutText=getSpeechTextForProperties(reason=reason,keyboardShortcut=keyboardShortcut) if config.conf[\"presentation\"][\"reportKeyboardShortcuts\"] else \"\"\n \tariaCurrentText=getSpeechTextForProperties(reason=reason,current=ariaCurrent)", "target": "What if we changed this to this? roleText=attrs.get('roleText', lambda:getSpeechTextForProperties(reason=reason,role=role)) That will prevent the function from ever needing called in the roletext case, and removes that if.", "old": "", "peft": "973\tI think this should be `if not roleText and not states:`.\n", "icl-diff": "973\t@gianm I think this is a bug. The roleText should be computed from the role, not from the roleText.\n", "icl-code": "", "slm": "Should be: `glog.Errorf(\"Error in deleting volume %q: %s\", cVR.ObjectMeta.Name, err)`\n", "label": ["Feat"]}
{"input": "@@ -7,12 +7,13 @@\n package snapshotsync\n \n import (\n+\treflect \"reflect\"\n+\tsync \"sync\"\n+\n \tproto \"github.com/golang/protobuf/proto\"\n \tprotoreflect \"google.golang.org/protobuf/reflect/protoreflect\"\n \tprotoimpl \"google.golang.org/protobuf/runtime/protoimpl\"\n \temptypb \"google.golang.org/protobuf/types/known/emptypb\"\n-\treflect \"reflect\"\n-\tsync \"sync\"\n )\n \n const (", "target": "You can delete this, it will now need to live in `gointerfaces` package", "old": "", "peft": "989\tI think we should use `reflect.Value` instead of `reflect.Type` here.\n", "icl-diff": "989\tI think the order of the imports is wrong.\n", "icl-code": "", "slm": "This makes me a little nervous. Would it be better to just always call `wlr_signal_emit_safe()` than just calling `wlr_signal_emit_safe()`, just in case someone calls `wlr_output_send_present()`?\n", "label": ["Feat"]}
{"input": "@@ -524,6 +524,17 @@\n                 dataType: \"json\"\n             });\n         },\n+        fetchSegmentMap: function() {\n+            return CV.$.ajax({\n+                type: \"GET\",\n+                url: countlyCommon.API_PARTS.data.r + '/data-manager/event-segment',\n+                data: {\n+                    \"app_id\": countlyCommon.ACTIVE_APP_ID,\n+                    \"preventRequestAbort\": true,\n+                },\n+                dataType: \"json\"\n+            });\n+        },\n         fetchRefreshSelectedEventsData: function(context) {\n             return CV.$.ajax({\n                 type: \"GET\",", "target": "This will be only available when data-manager is enabled, is there a fallback in case data manager is disabled?", "old": "", "peft": "1013\tI think we should use `fetchSegmentMap` here.\n", "icl-diff": "1013\tI think this is a good idea, but I'm not sure why we're using a GET request instead of a POST.\n", "icl-code": "", "slm": "I think it should be `h2o_mruby_new_str:failed to create string: `\n", "label": ["Feat"]}
{"input": "@@ -4,12 +4,8 @@\n  *  (found in the LICENSE.Apache file in the root directory)\n  */\n #include <gtest/gtest.h>\n-#include <cstdlib>\n-#include <thread>\n-#include <mutex>\n-#include <atomic>\n-#include \"common/concurrent/Barrier.h\"\n-#include \"common/thread/GenericThreadPool.h\"\n+#include \"concurrent/Barrier.h\"\n+#include \"thread/GenericThreadPool.h\"\n \n namespace vesoft {\n namespace concurrent {", "target": "Need to include \"common/base/Base.h\" in front of this line", "old": "", "peft": "1029\tI think we should remove this file.\n", "icl-diff": "1029\tAdded the missing include files\n", "icl-code": "", "slm": "Why did you add this one? I don't see it in this PR.\n", "label": ["Feat"]}
{"input": "@@ -271,7 +271,8 @@ final class MySQLSpanStore implements SpanStore {\n           .selectDistinct(ZIPKIN_SPANS.NAME)\n           .from(ZIPKIN_SPANS)\n           .join(ZIPKIN_ANNOTATIONS)\n-          .on(ZIPKIN_SPANS.TRACE_ID.eq(ZIPKIN_ANNOTATIONS.TRACE_ID))\n+          .on(ZIPKIN_SPANS.TRACE_ID_HIGH.eq(ZIPKIN_ANNOTATIONS.TRACE_ID_HIGH))\n+          .and(ZIPKIN_SPANS.TRACE_ID.eq(ZIPKIN_ANNOTATIONS.TRACE_ID))\n           .and(ZIPKIN_SPANS.ID.eq(ZIPKIN_ANNOTATIONS.SPAN_ID))\n           .where(ZIPKIN_ANNOTATIONS.ENDPOINT_SERVICE_NAME.eq(serviceName))\n           .orderBy(ZIPKIN_SPANS.NAME)", "target": "guess I'm wondering if this needs to be refactored to use Schema.joinCondition() or similar?", "old": "", "peft": "1038\tI think this is a bug. The `eq` should be `eqs`\n", "icl-diff": "1038\tShould we use `and` instead of `,`?\n", "icl-code": "", "slm": "I would name it `immediate_analyze_results` or something like that. What do you think?\n", "label": ["Feat"]}
{"input": "@@ -107,7 +107,7 @@ namespace Nethermind.Merge.Plugin.Handlers.V1\n             \n             if (headUpdated && shouldUpdateHead)\n             {\n-                _poSSwitcher.ForkchoiceUpdated(newHeadBlock!.Header);\n+                _poSSwitcher.ForkchoiceUpdated(newHeadBlock!.Header, finalizedHeader);\n                 _stateProvider.ResetStateTo(newHeadBlock.StateRoot!);\n                 if (_logger.IsInfo) _logger.Info($\"Block {forkchoiceState.HeadBlockHash} was set as head\");\n             }", "target": "finalizedHeader should be saved in FinalizationManager when we have FinalizationBlockHash != Keccak.Zero", "old": "", "peft": "1052\tI think we should use `finalizedHeader` here.\n", "icl-diff": "1052\t@gianm this is a bug, the finalized header should be passed to the forkchoice updated call\n", "icl-code": "", "slm": "Why don't we need to sanitize?\n", "label": ["Feat"]}
{"input": "@@ -48,7 +48,7 @@ void prepareMolForDrawing(RWMol &mol, bool kekulize, bool addChiralHs,\n   if (kekulize) {\n     try {\n       MolOps::Kekulize(mol, false);  // kekulize, but keep the aromatic flags!\n-    } catch(const RDKit::AtomKekulizeException &e) {\n+    } catch (const RDKit::AtomKekulizeException &e) {\n       std::cerr << e.what() << std::endl;\n     }\n   }", "target": "Should this be boost logged?", "old": "", "peft": "1061\tI think this is a bug. We should not be catching exceptions here.\n", "icl-diff": "1061\tWhy catch an exception and then just print it?\n", "icl-code": "", "slm": "I would use `os.environ.get('QUILT_USE_TQDM', '').lower() == 'true'` :)\n", "label": ["Ref"]}
{"input": "@@ -47,7 +47,7 @@ func accountImport(args []string) string {\n \t}\n \twallet := cfg.Wallet\n \tfmt.Printf(\"#%s: Enter your private key, which will not be exposed on the screen.\\n\", name)\n-\tprivateKeyBytes, err := terminal.ReadPassword(syscall.Stdin)\n+\tprivateKeyBytes, err := terminal.ReadPassword(int(syscall.Stdin))\n \tif err != nil {\n \t\tlog.L().Error(\"fail to get private key\", zap.Error(err))\n \t\treturn err.Error()", "target": "unnecessary conversion (from `unconvert`)", "old": "", "peft": "1066\t@johnnywang1990 I think this is a bug. The `int` type is not the same as `int32`.\n", "icl-diff": "1066\tI think it is better to use `os.Stdin` instead of `syscall.Stdin`.\n", "icl-code": "", "slm": "No reason to create a new class. It would probably be better to just create a new one.\n", "label": ["Ref"]}
{"input": "@@ -158,10 +158,13 @@ func TestBlockDAO(t *testing.T) {\n \t\trequire := require.New(t)\n \n \t\tctx := context.Background()\n-\t\tdao := NewBlockDAO(kvstore, indexer, false, config.Default.DB)\n+\t\ttestDBFile, _ := ioutil.TempFile(os.TempDir(), \"db\")\n+\t\tcfg := config.Default.DB\n+\t\tcfg.DbPath = testDBFile.Name()\n+\t\tdao := NewBlockDAO(kvstore, indexer, false, cfg)\n \t\trequire.NoError(dao.Start(ctx))\n \t\tdefer func() {\n-\t\t\trequire.NoError(dao.Stop(ctx))\n+\t\t\tdao.Stop(ctx)\n \t\t}()\n \n \t\t// receipts for the 3 blocks", "target": "Error return value of `dao.Stop` is not checked (from `errcheck`)", "old": "", "peft": "1073\tI think we should use `require.NoError(dao.Stop(ctx))` here.\n", "icl-diff": "1073\tI don't see why we need to create a new file for the DB.\n", "icl-code": "", "slm": "nit: it would be nice to have a test for this change.\n", "label": ["Feat"]}
{"input": "@@ -3,6 +3,7 @@ class SubscriptionsController < ApplicationController\n \n   def new\n     @plans = IndividualPlan.featured.active.ordered\n+    @team_plans = TeamPlan.featured.ordered\n   end\n \n   def edit", "target": "I'm breaking one of the rules here, it feels like the right thing to do. Open to alternative suggestions.", "old": "", "peft": "1077\tI think we should remove this line.\n", "icl-diff": "1077\t@gianm I think we should use `TeamPlan.featured.active.ordered` instead of `TeamPlan.featured.ordered`\n", "icl-code": "", "slm": "Won't this work? `attr_value['string'] == value` is a `dict` which is a `string`?\n", "label": ["Feat"]}
{"input": "@@ -90,10 +90,10 @@ class AppModule(appModuleHandler.AppModule):\n \t\t\t# Move the review cursor so others can't access its previous position.\n \t\t\tself._oldReviewPos = api.getReviewPosition()\n \t\t\tself._oldReviewObj = self._oldReviewPos.obj\n-\t\t\tapi.setNavigatorObject(eventHandler.lastQueuedFocusObject)\n+\t\t\tapi.setNavigatorObject(eventHandler.lastQueuedFocusObject, isFocus=True)\n \n \tdef event_appModule_loseFocus(self):\n \t\tif not config.conf[\"reviewCursor\"][\"followFocus\"]:\n-\t\t\tapi.setReviewPosition(self._oldReviewPos)\n+\t\t\tapi.setReviewPosition(self._oldReviewPos, isCaret=False)\n \t\t\tdel self._oldReviewPos, self._oldReviewObj\n \t\tinputCore.manager._captureFunc = None", "target": "As isCaret is False by default, explicitly specifying isCaret=False here is not needed, and is perhaps a little confusing. I'd prefer that anywhere in the codebase that isCaret is only ever specified if it needs to be set to true. Mirroring that of isFocus for setNavigatorObject.", "old": "", "peft": "1127\tI think this should be `isFocus=True`\n", "icl-diff": "1127\tThis is a bad idea.\n", "icl-code": "", "slm": "is this meant to be a limit? why dont we add it to the message queue before discarding?\n", "label": ["Feat"]}
{"input": "@@ -213,7 +213,7 @@ namespace pwiz.Skyline.Util\n                 {\n                     // No leading + or - : is it because description starts with a label, or because + mode is implied?\n                     var limit = input.IndexOfAny(new[] { '+', '-', ']' });\n-                    if (limit < 0)\n+                    if (limit < posNext)\n                     {\n                         return null;\n                     }", "target": "I think this should maybe be: var limit = input.IndexOfAny(new[] { '+', '-', ']' }, **posNext**); You pretty much want to ignore any sign that is before the \"M\". But, if there is a sign somewhere before the M, and also after the M, you'd want to be able to find the sign after the M, right?", "old": "", "peft": "1136\tI think this is a bug. The `limit` should be the index of the first character after the `+` or `-` or `]` that is not a space.\n", "icl-diff": "1136\tI think you meant `if (limit \n", "icl-code": "", "slm": "@jridgewell would it make sense to put the host in a variable like `storybook-host=http://localhost:9001/`?\n", "label": ["Feat"]}
{"input": "@@ -263,7 +263,12 @@ class OrderController extends BaseFrontController\n \n         /* check cart count */\n         $this->checkCartNotEmpty();\n-\n+        \n+        /* check stock not empty */\n+        if(true === ConfigQuery::checkAvailableStock()) {\n+          $this->checkStockNotEmpty();\n+        }\n+        \n         /* check delivery address and module */\n         $this->checkValidDelivery();\n ", "target": "if `checkStockNotEmpty` returns a reponse, you must return it or your script will continue its execution.", "old": "", "peft": "1137\tI think we should check the stock before the cart count.\n", "icl-diff": "1137\tI don't think this is a good idea. If the stock check is disabled, then the stock check should not be run.\n", "icl-code": "", "slm": "Why do we need to rollback after any exception? What's the use case?\n", "label": ["Code&Doc"]}
{"input": "@@ -101,6 +101,10 @@ class RangeBase(luigi.WrapperTask):\n     now = luigi.IntParameter(\n         default=None,\n         description=\"set to override current time. In seconds since epoch\")\n+    param_name = luigi.Parameter(\n+        default=None,\n+        description=\"parameter name used to pass in parameterized value. Defaults to None, meaning use first positional parameter\",\n+        positional=False)\n \n     @property\n     def of_cls(self):", "target": "Maybe add `positional=False`. It does not need it more than the other parameters, but one must start somewhere.", "old": "", "peft": "1150\tI think this should be `default=None`\n", "icl-diff": "1150\t@gianm I think this is a bug. The default value for `param_name` should be `None`\n", "icl-code": "", "slm": "I believe this should be `/admin/users` since we don't have a namespace.\n", "label": ["Feat"]}
{"input": "@@ -214,3 +214,10 @@ def postgres_db_info_resource(init_context):\n         dialect='postgres',\n         load_table=_do_load,\n     )\n+\n+\n+if __name__ == '__main__':\n+    # This is a brutal hack. When the SparkSession is created for the first time there is a lengthy\n+    # download process from Maven. This allows us to run python -m airline_demo.resources in the\n+    # Dockerfile and avoid a long runtime delay before each containerized solid executes.\n+    spark_session_local.resource_fn(None)", "target": "saw you're also doing this in `test_types.py`: `spark = _spark_context()['test'].resources['spark'].resource_fn(None)` since `_spark_context()` uses `spark_session_local` won't the above break the tests?", "old": "", "peft": "1152\tI think this is a good idea. I'm not sure if we should do this in the Dockerfile, but I think it's a good idea to have a way to run the resources locally.\n", "icl-diff": "1152\tThis is a hack. We should find a better way to do this.\n", "icl-code": "", "slm": "I'm curious, what's the difference between `TB` and `TB`?\n", "label": ["Code&Doc"]}
{"input": "@@ -5837,6 +5837,12 @@ initialize_exception_record(EXCEPTION_RECORD* rec, app_pc exception_address,\n     case ILLEGAL_INSTRUCTION_EXCEPTION:\n         rec->ExceptionCode = EXCEPTION_ILLEGAL_INSTRUCTION;\n         break;\n+    case GUARD_PAGE_EXCEPTION:\n+        rec->ExceptionCode = STATUS_GUARD_PAGE_VIOLATION;\n+        rec->NumberParameters = 2;\n+        rec->ExceptionInformation[0]=EXCEPTION_EXECUTE_FAULT /* execution tried */;\n+        rec->ExceptionInformation[1]=(ptr_uint_t)exception_address;\n+        break;\n     default:\n         ASSERT_NOT_REACHED();\n     }", "target": "nit: inconsistent spacing around =", "old": "", "peft": "1161\tI think this should be `STATUS_GUARD_PAGE_VIOLATION`\n", "icl-diff": "1161\tI think we should add a `case` for `EXCEPTION_ACCESS_VIOLATION`\n", "icl-code": "", "slm": "if IHasTags is non-null, this will fail, right?\n", "label": ["Feat"]}
{"input": "@@ -155,6 +155,18 @@ class SeriesTest(ReusedSQLTestCase, SQLTestUtils):\n         with self.assertRaisesRegex(TypeError, msg):\n             ds.isin(1)\n \n+    def test_fillna(self):\n+        ps = pd.Series([np.nan, 2, 3, 4, np.nan, 6], name='x')\n+        ks = koalas.from_pandas(ps)\n+\n+        self.assert_eq(ks.fillna(0), ps.fillna(0))\n+        self.assert_eq(ks.fillna(0, inplace=True), ps.fillna(0, inplace=True))\n+        self.assert_eq(ks.fillna({'x': 0}), ps.fillna({'x': 0}))\n+\n+        s_nan = pd.Series([0], index=['x'], dtype=int)\n+        self.assert_eq(ks.fillna(s_nan),\n+                       ps.fillna(s_nan))\n+\n     def test_dropna(self):\n         ps = pd.Series([np.nan, 2, 3, 4, np.nan, 6], name='x')\n ", "target": "i don't think this test case is correct. in both cases inpalce=True returns nothing. We need to compare the ks. Also we probably need to make a copy of it. Otherwise you pollute the following \"ks\" because ks has been changed.", "old": "", "peft": "1185\tI think we should test `fillna` with `inplace=True` and `inplace=False`\n", "icl-diff": "1185\tnit: s/inplace=True/inplace=False/ on line 163.\n", "icl-code": "", "slm": "Is this needed?\n", "label": ["Feat"]}
{"input": "@@ -20,8 +20,12 @@ var (\n \tonce    sync.Once\n )\n \n-// GetContext gets global context instance\n-func GetContext(contextType string) *Context {\n+func init()  {\n+\tInitContext(MsgCtxTypeChannel)\n+}\n+\n+// InitContext gets global context instance\n+func InitContext(contextType string) {\n \tonce.Do(func() {\n \t\tcontext = &Context{}\n \t\tswitch contextType {", "target": "Do we need this `init` here? We have already called the `InitContext` in `StartModule` directly.", "old": "", "peft": "1218\tI think we should use `once.Do(func() { InitContext(MsgCtxTypeChannel) })` instead of `InitContext(MsgCtxTypeChannel)`\n", "icl-diff": "1218\tI think you're trying to do something like this:\n", "icl-code": "", "slm": "Please don't change that line, it is essential to sorting the results correctly.\n", "label": ["Code&Doc"]}
{"input": "@@ -539,11 +539,12 @@ class EAP_MD5(EAP):\n         ByteEnumField(\"code\", 1, eap_codes),\n         ByteField(\"id\", 0),\n         FieldLenField(\"len\", None, fmt=\"H\", length_of=\"optional_name\",\n-                      adjust=lambda p, x: x + p.value_size + 6),\n+                adjust=lambda p, x: (x + p.value_size + 6) if p.value_size is not None else 6),\n         ByteEnumField(\"type\", 4, eap_types),\n-        FieldLenField(\"value_size\", 0, fmt=\"B\", length_of=\"value\"),\n+        FieldLenField(\"value_size\", None, fmt=\"B\", length_of=\"value\"),\n         XStrLenField(\"value\", '', length_from=lambda p: p.value_size),\n-        XStrLenField(\"optional_name\", '', length_from=lambda p: p.len - p.value_size - 6)\n+        XStrLenField(\"optional_name\", '',\n+                length_from=lambda p: (p.len - p.value_size - 6) if p.len is not None and p.value_size is not None else 0)\n     ]\n \n ", "target": "Please keep the correct alignment, it seems broken now (at least in Github). Can you reverse the test (`if p.value_size is None`)? Also, do you want `6` when `p.value_size is None` or `x + 6`? Maybe, in that case, something like `lambda p, x: x + 6 + (0 if p.value_size is None else p.value_size)` would be easier to read. Or even easier: `lambda p, x: x + 6 + (p.value_size or 0)`.", "old": "", "peft": "1235\tI think this is a bug. The length of the optional name field should be 6 if the value size is not None.\n", "icl-diff": "1235\t@gianm I think this is a bug in scapy. The length_of field should be optional, but it is not.\n", "icl-code": "", "slm": "This should be lower case.\n", "label": ["Feat"]}
{"input": "@@ -27,6 +27,9 @@ class ApproxMaxIoUAssigner(MaxIoUAssigner):\n             ignoring any bboxes.\n         ignore_wrt_candidates (bool): Whether to compute the iof between\n             `bboxes` and `gt_bboxes_ignore`, or the contrary.\n+        match_low_quality (bool): Whether to allow quality matches. This is\n+            usually allowed for RPN and single stage detectors, but not allowed\n+            in the second stage.\n         gpu_assign_thr (int): The upper bound of the number of GT for GPU\n             assign. When the number of gt is above this threshold, will assign\n             on CPU device. Negative values mean not assign on CPU.", "target": "typo: allow low quality matches.", "old": "", "peft": "1244\tI think this should be `match_quality`\n", "icl-diff": "1244\tWhy not just use `match_low_quality` as the default value of `ignore_wrt_candidates`?\n", "icl-code": "", "slm": "`--keep-namespaces` should still keep the original namespace paths.\n", "label": ["Feat"]}
{"input": "@@ -101,7 +101,8 @@ public class JdbcFlowTriggerInstanceLoaderImpl implements FlowTriggerInstanceLoa\n           + \"project_json, flow_exec_id \\n\"\n           + \"FROM execution_dependencies JOIN (\\n\"\n           + \"SELECT trigger_instance_id FROM execution_dependencies WHERE trigger_instance_id not in (\\n\"\n-          + \"SELECT distinct(trigger_instance_id)  FROM execution_dependencies WHERE dep_status =  0 or dep_status = 4)\\n\"\n+          + \"SELECT distinct(trigger_instance_id)  FROM execution_dependencies WHERE dep_status =\"\n+          + \"  0 or dep_status = 3)\\n\"\n           + \"GROUP BY trigger_instance_id\\n\"\n           + \"ORDER BY  min(starttime) desc limit %s) temp on execution_dependencies\"\n           + \".trigger_instance_id in (temp.trigger_instance_id);\";", "target": "Would it be better to pass the dependency status as the parameter into the SQL string? In case the enum value is changed in the future, we don't need to change the code here.", "old": "", "peft": "1255\tI think we should use `dep_status = 3` instead of `dep_status = 4`\n", "icl-diff": "1255\tI think you meant `dep_status = 3`\n", "icl-code": "", "slm": "This should be changed to be consistent with the rest of the runner.\n", "label": ["Feat"]}
{"input": "@@ -46,6 +46,7 @@ domReady( () => {\n \tModules.registerModule(\n \t\t'analytics',\n \t\t{\n+\t\t\tname: 'Analytics',\n \t\t\tsettingsEditComponent: SettingsEdit,\n \t\t\tsettingsViewComponent: SettingsView,\n \t\t\tsetupComponent: SetupMain,", "target": "See above, this shouldn't be added.", "old": "", "peft": "1270\tI think this should be `Analytics`\n", "icl-diff": "1270\tAdd a line break after this.\n", "icl-code": "", "slm": "Shouldn't we use `r.request.User()` instead of `r.request.RemoteUserCtxKey`?\n", "label": ["Feat"]}
{"input": "@@ -471,6 +471,9 @@ func (r *Repository) LoadIndex(ctx context.Context) error {\n \t\treturn err\n \t}\n \n+\t// remove obsolete indexes\n+\tvalidIndex.Sub(r.idx.Obsolete())\n+\n \t// remove index files from the cache which have been removed in the repo\n \treturn r.PrepareCache(validIndex)\n }", "target": "Wouldn't that cause the obsolete indexes to be downloaded over and over again? After all these are still stored in the repository.", "old": "", "peft": "1294\tI think we should remove the `validIndex` variable here.\n", "icl-diff": "1294\t@gianm this is a very bad idea. It will cause a lot of retries for duplicate primary key entry errors that will never be transient unless someone manually cleans the DB\n", "icl-code": "", "slm": "Could this class be renamed to `RemoteDistributorTest`?\n", "label": ["Code&Doc"]}
{"input": "@@ -127,6 +127,10 @@ class StepDelegatingExecutor(Executor):\n                     running_steps[step.key] = step\n \n             last_check_step_health_time = pendulum.now(\"UTC\")\n+\n+            # Order of events is important here. During an interation, we call handle_event, then get_steps_to_execute,\n+            # then is_complete. get_steps_to_execute updates the state of ActiveExecution, and without it\n+            # is_complete can return true when we're just between steps.\n             while not active_execution.is_complete:\n \n                 if active_execution.check_for_interrupts():", "target": "Could also consider modifying the ActiveExecution...", "old": "", "peft": "1303\tI think this is a bug. I think we should be calling `handle_event` before `get_steps_to_execute` and `is_complete`.\n", "icl-diff": "1303\t@gianm I think this is a bug. We should be checking for interrupts before we call get_steps_to_execute, not after.\n", "icl-code": "", "slm": "You can do this instead: ```js if (configuration.usingUnifiedTopology()) { // The unified topology does not support authentication\n", "label": ["Doc"]}
{"input": "@@ -1522,3 +1522,19 @@ def get_iterating_dictionary_name(\n         return node.iter.as_string()\n \n     return None\n+\n+\n+def get_subscript_const_value(node: astroid.Subscript) -> astroid.Const:\n+    \"\"\"\n+    Returns the value (subscript.slice) of a Subscript node,\n+    also supports python <3.9 windows where node.slice might be an Index\n+    node\n+    \"\"\"\n+    value = node.slice\n+    if isinstance(value, astroid.Index):\n+        value = value.value\n+    inferred = safe_infer(value)\n+    if not isinstance(inferred, astroid.Const):\n+        raise ValueError(\"Subscript.slice cannot be inferred as an astroid.Const\")\n+\n+    return inferred", "target": "Does it make sense to change that to a custom error instead of `ValueError`?", "old": "", "peft": "1304\tI think this should be `safe_infer(value.value)`\n", "icl-diff": "1304\tThis is a very bad idea.\n", "icl-code": "", "slm": "Do we need this route? If not, can we remove it?\n", "label": ["Code&Doc"]}
{"input": "@@ -218,7 +218,9 @@ func (s *Server) handleSingleRPC(ctx context.Context, req *RPCReq) *RPCRes {\n \t\treturn backendRes\n \t}\n \n-\tbackendRes, err = s.backendGroups[group].Forward(ctx, req)\n+\t// NOTE: We call into the specific backend here to ensure that the RPCRes is synchronized with the blockNum.\n+\tvar blockNum uint64\n+\tbackendRes, blockNum, err = s.backendGroups[group].Forward(ctx, req)\n \tif err != nil {\n \t\tlog.Error(\n \t\t\t\"error forwarding RPC request\",", "target": "See above note re: blockNum synchronization.", "old": "", "peft": "1325\tI think we should use `blockNum` here.\n", "icl-diff": "1325\tWhy do we need to synchronize the RPCRes with the blockNum?\n", "icl-code": "", "slm": "Import the import at the bottom of the file\n", "label": ["Code&Doc"]}
{"input": "@@ -20,6 +20,10 @@ export class TouchScroll extends BasePlugin {\n     return PLUGIN_PRIORITY;\n   }\n \n+  static get ALWAYS_UPDATE() {\n+    return true;\n+  }\n+\n   constructor(hotInstance) {\n     super(hotInstance);\n ", "target": "I don't feel well about the `ALWAYS_UPDATE` option   &nbsp; sounds like a workaround. I can imagine that some of the plugins want to observe all options. Maybe, in that case, we can reuse the `CONFIG_KEYS` that would return an empty array for observing all settings and `false` for disabling observing at all? I just thinking aloud - Like `CONFIG_KEYS` sounds reasonable. We could support and maintain it in the future, even after implementing the \"observability\" to the MetaManager then `ALWAYS_UPDATE` ... I don't know :)", "old": "", "peft": "1329\tI think this should be `false`\n", "icl-diff": "1329\tThis is a static method, so it should be static.\n", "icl-code": "", "slm": "why not using `logger.Fatal`?\n", "label": ["Feat"]}
{"input": "@@ -19,4 +19,15 @@ module IntegrationSpecHelper\n       }\n     )\n   end\n+\n+  def with_18f_procurement_env_variables(setup_vars=nil)\n+    old_approver_email = ENV['GSA18F_APPROVER_EMAIL']\n+    old_purchaser_email = ENV['GSA18F_PURCHASER_EMAIL']\n+\n+    ENV['GSA18F_APPROVER_EMAIL'] = '<EMAIL>'\n+    ENV['GSA18F_PURCHASER_EMAIL'] = '<EMAIL>'\n+    yield\n+    ENV['GSA18F_APPROVER_EMAIL'] = old_approver_email\n+    ENV['GSA18F_PURCHASER_EMAIL'] = old_purchaser_email\n+  end\n end", "target": "Can we put this in a different helper?", "old": "", "peft": "1335\tI'm not sure if this is the right place to put this, but I think it's a good idea to have a `with_18f_procurement_env_variables` method that takes a block and a hash of variables to set.\n", "icl-diff": "1335\tI think it's better to use `let` instead of `before`\n", "icl-code": "", "slm": "Is that definitely needed?\n", "label": ["Feat"]}
{"input": "@@ -498,3 +498,7 @@ type tasksStopper interface {\n type serviceLinkedRoleCreator interface {\n \tCreateECSServiceLinkedRole() error\n }\n+\n+type dockerEngineValidator interface {\n+\tIsDockerEngineRunning() (string, error)\n+}", "target": "nit: I'd expect a public method called IsDockerEngineRunning to return a boolean yes/no, not the error message. Can we change either the return value or the name? Something like `CallDockerEngine`", "old": "", "peft": "1359\tI think this should be `IsDockerEngineRunning()`\n", "icl-diff": "1359\tThis is a very bad idea.\n", "icl-code": "", "slm": "We might want to add `tail: Unknown file system type 0x794c7630` to this PR so that headaches are available.\n", "label": ["Feat"]}
{"input": "@@ -313,6 +313,7 @@ type client struct {\n \treplayMutex   sync.RWMutex\n \tnodeConfig    *config.NodeConfig\n \tnetworkConfig *config.NetworkConfig\n+\tegressConfig  *config.EgressConfig\n \tgatewayOFPort uint32\n \t// ovsDatapathType is the type of the datapath used by the bridge.\n \tovsDatapathType ovsconfig.OVSDatapathType", "target": "Do we still need this?", "old": "", "peft": "1375\tI think this should be `egressConfigs`\n", "icl-diff": "1375\tAdd a line break after this.\n", "icl-code": "", "slm": "Why not use fscanf(file, \"%\"uint8_MAX_VALUE) instead?\n", "label": ["Feat"]}
{"input": "@@ -23,9 +23,10 @@\n       <div class=\"checkbox\">\n         <%= f.label :published, raw(\"#{f.check_box :published, as: :check_boxes, 'data-toggle': 'tooltip', title: _(\"Check this box when you are ready for this guidance to appear on user's plans.\")} #{_('Published?')}\") %>\n       </div>\n-\n-      <%= f.submit _('Save'), name: \"edit_guidance_submit\", class: \"btn btn-primary\" %>\n-      <%= link_to _('Cancel'), admin_index_guidance_path, class: \"btn btn-primary\", role: 'button' %>\n+      <div class=\"form-group clear-fix\">\n+        <%= f.submit _('Save'), name: \"edit_guidance_submit\", class: \"btn btn-primary\" %>\n+        <%= link_to _('Cancel'), admin_index_guidance_path, class: \"btn btn-primary\", role: 'button' %>\n+      </div>\n     <%end%>\n   </div>\n </div>", "target": "do you mean 'clearfix' class?", "old": "", "peft": "1388\tI think we should use `form-group` here as well.\n", "icl-diff": "1388\tAdd a line break after this.\n", "icl-code": "", "slm": "When `CASE_SENSITIVE_DEFAULT` is set, should we also handle that case-insensitive?\n", "label": ["Feat"]}
{"input": "@@ -71,7 +71,7 @@ type (\n \t\tGetContractState(hash.PKHash, hash.Hash32B) (hash.Hash32B, error)\n \t\tSetContractState(hash.PKHash, hash.Hash32B, hash.Hash32B) error\n \t\t// Candidate pool\n-\t\tcandidates() (uint64, []*Candidate)\n+\t\tCandidates() (uint64, []*Candidate)\n \t\tCandidatesByHeight(uint64) ([]*Candidate, error)\n \t}\n ", "target": "Need to do this to unblock mockgen. The interface method needs to be public to be visible in another package. The right fix is to delete this test only interface method", "old": "", "peft": "1397\tI think we should use `Candidates` instead of `CandidatesByHeight`\n", "icl-diff": "1397\t@bboreham: I think this is a typo.\n", "icl-code": "", "slm": "any reason for this change? what's the difference between `hashCode()` and `toString()`?\n", "label": ["Ref"]}
{"input": "@@ -101,6 +101,13 @@ module.exports = function(url, options, callback) {\n         record = record[0].join('');\n         const parsedRecord = qs.parse(record);\n         const items = Object.keys(parsedRecord);\n+        if (\n+          Object.keys(items)\n+            .map(k => k.toLowerCase())\n+            .indexOf('loadbalanced') !== -1\n+        ) {\n+          return callback(new MongoParseError('Load balancer mode requires driver version 4+'));\n+        }\n         if (items.some(item => item !== 'authSource' && item !== 'replicaSet')) {\n           return callback(\n             new MongoParseError('Text record must only set `authSource` or `replicaSet`')", "target": "same question here as I have on the other file regarding doing this check here vs L112 vs not at all", "old": "", "peft": "1398\tI think this should be `if (items.some(item =\n", "icl-diff": "1398\tWhy not just `if (items.some(item =\n", "icl-code": "", "slm": "Is it worth adding a TODO to change this to \"This can be used to have different naming convention in the Realm file...\"?\n", "label": ["Feat"]}
{"input": "@@ -35,7 +35,6 @@ class BZAObject(dict):\n         self.token = None\n         self.log = logging.getLogger(self.__class__.__name__)\n         self.http_session = requests.Session()\n-        self.http_request = self.http_session.request\n \n         # copy infrastructure from prototype\n         if isinstance(proto, BZAObject):", "target": "this is used as important way of mocking for unit tests, FYI", "old": "", "peft": "1400\tI think this is a bug. The `http_request` method is not supposed to be called directly.\n", "icl-diff": "1400\tI think this is a good use of the `super` call.\n", "icl-code": "", "slm": "@Zhao-Andy where do we validate `url_name`?\n", "label": ["Feat"]}
{"input": "@@ -212,8 +212,7 @@ module Bolt\n \n         return unless !stdout.empty? && stdout.to_i < 3\n \n-        msg = \"Detected PowerShell 2 on controller. PowerShell 2 is deprecated and \"\\\n-              \"support will be removed in Bolt 3.0.\"\n+        msg = \"Detected PowerShell 2 on controller. PowerShell 2 is unsupported.\"\n         Bolt::Logger.deprecation_warning(\"PowerShell 2 controller\", msg)\n       end\n     end", "target": "Do we want to raise an error here instead of warning? Or would it be better to just see if Bolt happens to succeed, and let it fail on it's own if it fails? I'd lean towards raising an error, but that's different from \"removing support\".", "old": "", "peft": "1406\tI think this should be `PowerShell 2 is unsupported.`\n", "icl-diff": "1406\tI think this is a good idea, but I'm not sure if it's worth the effort.\n", "icl-code": "", "slm": "nit: Since this is a separate PR, can you move this to the top of the file?\n", "label": ["Feat"]}
{"input": "@@ -78,10 +78,17 @@ class LibraryCardsController extends AbstractBase\n         // Connect to the ILS for login drivers:\n         $catalog = $this->getILS();\n \n+        $config = $this->getConfig();\n+        $allowConnectingCards = !empty(\n+            $config->Catalog\n+                ->auth_based_library_cards\n+        ) &&\n+            ($this->getAuthManager()->getAuthMethod() == 'Shibboleth');\n         return $this->createViewModel(\n             [\n                 'libraryCards' => $user->getLibraryCards(),\n-                'multipleTargets' => $catalog->checkCapability('getLoginDrivers')\n+                'multipleTargets' => $catalog->checkCapability('getLoginDrivers'),\n+                'allowConnectingCards' => $allowConnectingCards,\n             ]\n         );\n     }", "target": "The formatting of this is a bit strange; I'd suggest collapsing this back to a single line, and moving the `&&` to the beginning of the second line to meet the line length restriction.", "old": "", "peft": "1427\tI think this should be `!empty($config-\n", "icl-diff": "1427\tI think this is a bug. The `&&` should be `||`.\n", "icl-code": "", "slm": "How about we remove the `uri`?\n", "label": ["Feat"]}
{"input": "@@ -153,10 +153,8 @@ bool EDPSimple::createSEDPEndpoints()\n         watt.endpoint.multicastLocatorList = this->mp_PDP->getLocalParticipantProxyData()->m_metatrafficMulticastLocatorList;\n         //watt.endpoint.remoteLocatorList = m_discovery.initialPeersList;\n         watt.endpoint.durabilityKind = TRANSIENT_LOCAL;\n-        watt.times.nackResponseDelay.seconds = 0;\n-        watt.times.nackResponseDelay.fraction = 0;\n-        watt.times.initialHeartbeatDelay.seconds = 0;\n-        watt.times.initialHeartbeatDelay.fraction = 0;\n+        watt.times.heartbeatPeriod.seconds = 1;\n+        watt.times.heartbeatPeriod.fraction = 0;\n         if(mp_RTPSParticipant->getRTPSParticipantAttributes().throughputController.bytesPerPeriod != UINT32_MAX &&\n                 mp_RTPSParticipant->getRTPSParticipantAttributes().throughputController.periodMillisecs != 0)\n             watt.mode = ASYNCHRONOUS_WRITER;", "target": "Maybe use specific values for all the watt.times fields? Even better, have a const for it at the top of the file?", "old": "", "peft": "1431\tI think this is a bug. The heartbeat period should be 0.\n", "icl-diff": "1431\t@bboreham I think this is a product issue, not just test coverage.\n", "icl-code": "", "slm": "What if we called this `find_or_create_by` instead of `find_or_create_by`?\n", "label": ["Feat"]}
{"input": "@@ -106,6 +106,7 @@ namespace Datadog.Trace.Agent\n                     {\n                         // stop retrying\n                         Log.Error(exception, \"An error occurred while sending traces to the agent at {0}\", _tracesEndpoint);\n+                        _statsd?.Send();\n                         return false;\n                     }\n ", "target": "I believe you should either remove this or rename the PR. You can't change the behavior of the tracer (even to fix a bug) in a PR named \"unit test improvements\"", "old": "", "peft": "1463\tI think this should be `return false;`\n", "icl-diff": "1463\tI think this is a bug.\n", "icl-code": "", "slm": "I'm not sure if this is the best place to put it. I'm also not sure if it's the best place to put it. I'm also not sure what the best place is for.\n", "label": ["Feat"]}
{"input": "@@ -508,8 +508,13 @@ def remove_xml_preamble(response):\n # --------------\n def get_lifecycle(bucket_name):\n     bucket_name = normalize_bucket_name(bucket_name)\n+    exists, code, body = is_bucket_available(bucket_name)\n+    if not exists:\n+        return requests_response(body, status_code=code)\n+\n     lifecycle = BUCKET_LIFECYCLE.get(bucket_name)\n     status_code = 200\n+\n     if not lifecycle:\n         # TODO: check if bucket actually exists\n         lifecycle = {", "target": "Can we remove the `TODO` statement here? (as this is actually fixed in this PR)", "old": "", "peft": "1475\tI think this should be `if not exists:`.\n", "icl-diff": "1475\tThis is a bit of a hack. We should probably just return a 404 here.\n", "icl-code": "", "slm": "Please just do `str = str.Substring(MsDataFileImpl.PREFIX_PRECURSOR);`\n", "label": ["Feat"]}
{"input": "@@ -168,6 +168,10 @@ func (kvb *kvStoreWithBuffer) MustDelete(ns string, key []byte) {\n \tkvb.buffer.Delete(ns, key, \"failed to delete %x in %s\", key, ns)\n }\n \n+func (kvb *kvStoreWithBuffer) Filter(ns string, c Condition) ([][]byte, [][]byte, error) {\n+\treturn kvb.store.Filter(ns, c)\n+}\n+\n func (kvb *kvStoreWithBuffer) WriteBatch(b batch.KVStoreBatch) (err error) {\n \tb.Lock()\n \tdefer func() {", "target": "need to filter the entities in buffer as well", "old": "", "peft": "1477\tI think this should be `kvb.store.Filter(ns, c)`\n", "icl-diff": "1477\tI think this is a good idea, but I'm not sure if it's worth the effort.\n", "icl-code": "", "slm": "I would just put this on the main page, not on the top of the page.\n", "label": ["Feat"]}
{"input": "@@ -4,7 +4,7 @@\n   plan_title = @plan.title\n   user_name = @plan.owner.name\n   helpdesk_email = Rails.configuration.branding[:organisation][:helpdesk_email]\n-  contact_us_url = Rails.configuration.branding[:organisation][:contact_us_url]\n+  contact_us = Rails.configuration.branding[:organisation][:contact_us_url] || contact_us_url\n   email_subject = _('Query or feedback related to %{tool_name}') %{ :tool_name => tool_name }\n %>\n <% FastGettext.with_locale FastGettext.default_locale do %>", "target": "that change should be sufficient for avoiding the override of contact_us_url helper. The one defined at the .yml it is within a hash so no problem should arise.", "old": "", "peft": "1502\tI think we should use the same variable name for both the `url` and the `url_helper`\n", "icl-diff": "1502\tDid you mean `contact_us_url = Rails.configuration.branding[:organisation][:contact_us_url] || contact_us_url`?\n", "icl-code": "", "slm": "If you have `-f build.gradle` already, why do we need to rename this?\n", "label": ["Feat"]}
{"input": "@@ -26,6 +26,8 @@ type AppliedToGroup struct {\n \tmetav1.ObjectMeta\n \t// Pods is a list of Pods selected by this group.\n \tPods []GroupMemberPod\n+\t// GroupMembers is a list of resources selected by this group.\n+\tGroupMembers []GroupMember\n }\n \n // PodReference represents a Pod Reference.", "target": "So if I understand this correctly the externalEntities should be computed and stored in GroupMember, while current code usage of GroupMemberPod will be migrated to GroupMember later?", "old": "", "peft": "1508\tI think this should be `GroupMembers` instead of `GroupMembers`.\n", "icl-diff": "1508\tThis is a very confusing name.\n", "icl-code": "", "slm": "The property is declared as `[Output]` but its use is not `[Output]`\n", "label": ["Code&Doc"]}
{"input": "@@ -60,7 +60,6 @@ class TestCharacterOffsets(unittest.TestCase):\n \t\tobj = BasicTextProvider(text=u\"\\U0001f926\\U0001f60a\\U0001f44d\") # 🤦😊👍\n \t\tti = obj.makeTextInfo(Offsets(5, 5))\n \t\tti.expand(textInfos.UNIT_CHARACTER) # Range at 👍\n-\t\tself.assertEqual(ti.offsets, (4, 6)) # Two offsets\n \t\tti.move(textInfos.UNIT_CHARACTER, -1)\n \t\tti.expand(textInfos.UNIT_CHARACTER) # Range at 😊\n \t\tself.assertEqual(ti.offsets, (2, 4)) # Two offsets", "target": "Was this intentional? Perhaps this could be replaced with another way to check the offsets?", "old": "", "peft": "1567\tThis is a regression test for #1274.\n", "icl-diff": "1567\tI think this is a bug in the test, not the code. The test is expecting the offsets to be (4, 5), not (4, 6).\n", "icl-code": "", "slm": "That's a dumb question, what about how you expect this to run?\n", "label": ["Code&Doc"]}
{"input": "@@ -93,10 +93,16 @@ func GenerateSelfSignedX509CA(commonName string, extUsage []x509.ExtKeyUsage, ke\n }\n \n // GenerateServerX509UsingCA generates a TLS serverCert that is self-signed\n-func GenerateServerX509UsingCA(commonName string, ca *tls.Certificate) (*tls.Certificate, *rsa.PrivateKey, error) {\n+func GenerateServerX509UsingCAAndSerialNumber(commonName string, serialNumber int64, ca *tls.Certificate) (*tls.Certificate, *rsa.PrivateKey, error) {\n \tnow := time.Now().UTC()\n \n-\ti := mathrand.Int63n(100000000000000000)\n+\tvar i int64\n+\tif serialNumber == 0 {\n+\t\ti = mathrand.Int63n(100000000000000000)\n+\t} else {\n+\t\ti = serialNumber\n+\t}\n+\n \ttemplate := &x509.Certificate{\n \t\tSerialNumber: big.NewInt(i),\n \t\tSubject: pkix.Name{", "target": "[Super NIT]: i := serialNumber if i == 0 { i = mathrand.Int63n(...) }", "old": "", "peft": "1573\tI think this is a bug. The `serialNumber` should be the same as the `i` in the `GenerateServerX509UsingCA` function.\n", "icl-diff": "1573\t@gianm I think this is a bug. The serial number should be unique, but this code will generate the same serial number for every server certificate.\n", "icl-code": "", "slm": "Are you sure this doesn't cause issues? I thought `app_name` and `environment_name` were always equivalent?\n", "label": ["Feat"]}
{"input": "@@ -54,6 +54,12 @@ namespace Microsoft.AspNetCore.Server.Kestrel.Http\n \n                     InitializeHeaders();\n \n+                    if (_corruptedRequest)\n+                    {\n+                        await ProduceEnd();\n+                        return;\n+                    }\n+\n                     while (!_requestProcessingStopping && !TakeMessageHeaders(SocketInput, FrameRequestHeaders))\n                     {\n                         if (SocketInput.RemoteIntakeFin)", "target": "This seems different than what we do for corrupted request headers. I would like to determine the correct behavior and consolidate this logic.", "old": "", "peft": "1618\tI think this should be `if (_corruptedRequest || _requestProcessingStopping)`\n", "icl-diff": "1618\t@gianm this is a bug in the code, it should be \"if (SocketInput.RemoteIntakeFin)\"\n", "icl-code": "", "slm": "@gabriel can you confirm that this is the correct value for `GOMAXPROCS`?\n", "label": ["Feat"]}
{"input": "@@ -292,7 +292,7 @@ class _RunData(object):\n         direct_access_sources = set()\n         for backend in backend_service.backends:\n             instance_group = self.find_instance_group_by_url(\n-                backend.get('group'))\n+                backend.get('resourceGroup'))\n             if not instance_group:\n                 continue\n ", "target": "Please make the change in the CAI inventory, rather than changing the scanner.", "old": "", "peft": "1619\tThis is a bit of a nitpick, but I think it's better to use `get` here.\n", "icl-diff": "1619\tWhat's the meaning of `backend.get('group')` here?\n", "icl-code": "", "slm": "This will make the diff harder to read. I think it's an improvement.\n", "label": ["Ref"]}
{"input": "@@ -256,6 +256,13 @@ func (o *Outbound) call(ctx context.Context, treq *transport.Request) (*transpor\n \n \tspan.SetTag(\"http.status_code\", response.StatusCode)\n \n+\t// Service name match validation, return yarpcerrors.CodeInternal error if not match\n+\tif match, resSvcName := checkServiceMatch(treq.Service, response.Header); !match {\n+\t\treturn nil, transport.UpdateSpanWithErr(span,\n+\t\t\tyarpcerrors.InternalErrorf(\"service name sent from the request \"+\n+\t\t\t\t\"does not match the service name received in the response, sent %q, got: %q\", treq.Service, resSvcName))\n+\t}\n+\n \ttres := &transport.Response{\n \t\tHeaders:          applicationHeaders.FromHTTPHeaders(response.Header, transport.NewHeaders()),\n \t\tBody:             response.Body,", "target": "Might we want a hook to allow emitting metrics or logs in the case of a permitted empty service header response (or similar UpdateSpanWithErr on empty service header in response if strict enforcement is desired by the caller)?", "old": "", "peft": "1640\tI think this should be `resSvcName` instead of `resSvcName`\n", "icl-diff": "1640\tThis is a bug fix.\n", "icl-code": "", "slm": "This should probably be `realpath(dirname(__DIR__) . 'bin', DIRECTORY_SEPARATOR)`.\n", "label": ["Code&Doc"]}
{"input": "@@ -139,6 +139,13 @@ describe('text.formControlValue', function() {\n \t\t\t\taxe.utils\n \t\t\t\t\t.querySelectorAll(axe._tree[0], '#fixture input')\n \t\t\t\t\t.forEach(function(target) {\n+\t\t\t\t\t\t// Safari and IE11 do not support the color input type\n+\t\t\t\t\t\t// and thus treat them as text inputs. ignore fallback\n+\t\t\t\t\t\t// inputs\n+\t\t\t\t\t\tif (target.actualNode.type === 'text') {\n+\t\t\t\t\t\t\treturn;\n+\t\t\t\t\t\t}\n+\n \t\t\t\t\t\tassert.equal(\n \t\t\t\t\t\t\tnativeTextboxValue(target),\n \t\t\t\t\t\t\t'',", "target": "I think you can remove the `(isIE11 ? it.skip : it)(` above, if you're going to skip the test this way.", "old": "", "peft": "1668\tI think this is a bug in the test. The `color` input type is supported by Safari and IE11.\n", "icl-diff": "1668\tI'm not sure why this is necessary. I'm not sure if it's a bug in axe or if it's a bug in the test.\n", "icl-code": "", "slm": "The argument `granteeList` is not used in any case, so you can remove it.\n", "label": ["Code&Doc"]}
{"input": "@@ -0,0 +1,9 @@\n+package com.fsck.k9;\n+\n+import java.util.*;\n+\n+public class SputnikTest {\n+    public void sputnikTest() {\n+        return;\n+    }\n+}", "target": "[Checkstyle] INFO: Using the '._' form of import should be avoided - java.util._.", "old": "", "peft": "1692\tThis is a test class, so it should be `TestSputnikTest`.\n", "icl-diff": "1692\tI don't think it improves performance that much, but you could stream the result from allItems() and collect it into a list, avoiding the for loop.\n", "icl-code": "", "slm": "what's the difference between `part` and `future`?\n", "label": ["Feat"]}
{"input": "@@ -22,10 +22,12 @@ namespace Microsoft.DotNet.Build.Tasks\n         // Additional Dependencies to add to the project.json. May Optionally contain a version.\n         // Will Override dependencies present in the project if there is a conflict.\n         // AdditionalDependencies required metadata:  Name, Version\n-\n         [Required]\n         public ITaskItem[] AdditionalDependencies { get; set; }\n \n+        // Framework section which the additional dependencies apply to.  Empty is the default dependencies section.\n+        public string[] Frameworks { get; set; }\n+\n         public string BuildNumberOverrideStructureRegex { get; set; }\n \n         // Permit overriding package versions found in project.json with custom build number version.", "target": "Unfortunately I think the assumption of \"empty\" means the default dependency section is not correct. For a project.json file the default dependencies section is a shared section between all the different target frameworks and our TargetGroup being empty may map to many different target frameworks so they aren't really equated.", "old": "", "peft": "1695\tI think this should be `string[] Frameworks`\n", "icl-diff": "1695\t@gianm this is a very bad idea.  We should not be doing this.  We should be using the same versioning scheme for all packages.\n", "icl-code": "", "slm": "`from collections import OrderedDict` is the preferred style since we don't have to use `OrderedDict` any more.\n", "label": ["Code&Doc"]}
{"input": "@@ -9,7 +9,7 @@ const build = (buildConfig = config.defaultBuildConfig, options) => {\n     util.updateBranding()\n   }\n \n-  util.buildMuon()\n+  util.buildMuon('brave')\n }\n \n module.exports = build", "target": "pls pr a name change fro muon here too separately.", "old": "", "peft": "1697\tI think we should use `buildMuon` here.\n", "icl-diff": "1697\tI think you mean `util.buildMuon('brave')`\n", "icl-code": "", "slm": "I think you should be more specific here as `assertAlmostEqual(bst.upper_bound(), 3.32, places=2)`\n", "label": ["Feat"]}
{"input": "@@ -70,7 +70,7 @@ void sequential_model::write_proto(lbann_data::Model* proto) {\n   model::write_proto(proto);\n   //Add layers\n   if (m_comm->am_world_master()) {\n-    proto->set_name(name());\n+    proto->set_name(type());\n     for(size_t l = 0; l < m_layers.size(); l++) {\n       auto layer_proto = proto->add_layer();\n       m_layers[l]->write_proto(layer_proto);", "target": "It looks like the proto code needs to change the function name from set_name to set_type to be consistent.", "old": "", "peft": "1702\tI think this should be `proto-\n", "icl-diff": "1702\tI think it should be `proto-\n", "icl-code": "", "slm": "For the media manager image uploads to be within this with (in pixels, 0 = ignore)\n", "label": ["Ref"]}
{"input": "@@ -11,7 +11,7 @@ if (typeof define === 'function' && define.amd) {\n \t});\n }\n if (typeof module === 'object' && module.exports && typeof axeFunction.toString === 'function') {\n-    axe.source = '(' + axeFunction.toString() + ')(this, this.document);';\n+    axe.source = '(' + axeFunction.toString() + ')(typeof window === \"object\" ? window : this);';\n     module.exports = axe;\n }\n if (typeof window.getComputedStyle === 'function') {", "target": "hey, aren't we supposed to be passing in two parameters here?", "old": "", "peft": "1748\tI'm not sure if this is a bug or not, but I think it's a bug. I think this should be `typeof window === 'object'? window : this`\n", "icl-diff": "1748\tI think it's better to use `typeof window === \"object\" && window` instead of `typeof window === \"object\"? window : this`\n", "icl-code": "", "slm": "@lasote It would be much better to use `self.install_role()`.\n", "label": ["Feat"]}
{"input": "@@ -87,8 +87,9 @@ Object.keys(rulesGroupByDocumentFragment).forEach(key => {\n \t\t\t\treturn;\n \t\t\t}\n \n-\t\t\t// check if transform style exists\n-\t\t\tconst transformStyleValue = cssRule.style.transform || false;\n+\t\t\t// check if transform style exists (don't forget vendor prefixes)\n+\t\t\tconst transformStyleValue =\n+\t\t\t\tcssRule.style.transform || cssRule.style.webkitTransform || false;\n \t\t\t// transformStyleValue -> is the value applied to property\n \t\t\t// eg: \"rotate(-90deg)\"\n \t\t\tif (!transformStyleValue) {", "target": "Looks like you've covered `-webkit-transform`, but what about `-ms-transform`?", "old": "", "peft": "1761\tI think we should also check for `transform` and `webkitTransform`\n", "icl-diff": "1761\tI think it's better to use `const transformStyleValue = cssRule.style.transform || cssRule.style.webkitTransform || false;`\n", "icl-code": "", "slm": "Can this be refactored to a different interface in FetchTipsSet?\n", "label": ["Code&Doc"]}
{"input": "@@ -140,8 +140,8 @@ func run(ctx context.Context) {\n \t\t} else {\n \t\t\tsetOsConfig(resp)\n \t\t\tsetPatchPolicies(resp.PatchPolicies)\n-\t\t\trunInventory()\n \t\t}\n+\t\trunInventory()\n \n \t\tselect {\n \t\tcase <-ticker.C:", "target": "Curious about the reason behind this change", "old": "", "peft": "1795\tI think this should be `runInventory()`\n", "icl-diff": "1795\tI think you mean `runInventory()`\n", "icl-code": "", "slm": "Why is this needed?\n", "label": ["Feat"]}
{"input": "@@ -131,6 +131,9 @@ const (\n \n \t// MachineNameTagKey is the key for machine name.\n \tMachineNameTagKey = \"MachineName\"\n+\n+\t// NodeRoleTagValue describes the value for the node role.\n+\tNodeRoleTagValue = \"node\"\n )\n \n // ClusterTagKey generates the key for resources associated with a cluster.", "target": "Let's use this constant when ASG is created as well (currently hardcoded).", "old": "", "peft": "1801\tI think this should be `NodeRole`\n", "icl-diff": "1801\t@gianm this is a very bad idea.  We should be using the cluster name as the tag value, not the node name.\n", "icl-code": "", "slm": "Is this supposed to be `final`?\n", "label": ["Code&Doc"]}
{"input": "@@ -1,10 +1,12 @@\n ﻿using System.Collections.Generic;\n+using System;\n \n namespace ScenarioMeasurement\n {\n     public class LinuxTraceSession : ITraceSession\n     {\n-        public string TraceFilePath { get { return perfCollect?.TraceFilePath; } }\n+        public string TraceFilePath { \n+            get { return perfCollect?.TraceFilePath;} }\n         private PerfCollect perfCollect;\n         private Dictionary<TraceSessionManager.KernelKeyword, PerfCollect.KernelKeyword> kernelKeywords;\n         private Dictionary<TraceSessionManager.ClrKeyword, PerfCollect.ClrKeyword> clrKeywords;", "target": "Why this line break?", "old": "", "peft": "1814\tI think this should be `get { return perfCollect?.TraceFilePath; }`\n", "icl-diff": "1814\tI think this is a bug in the diff hunk.\n", "icl-code": "", "slm": "Is this intentional?\n", "label": ["Feat"]}
{"input": "@@ -27,6 +27,7 @@ import (\n \tadmissionregistrationv1 \"k8s.io/api/admissionregistration/v1\"\n \t\"k8s.io/apiextensions-apiserver/pkg/apis/apiextensions\"\n \tapiextensionsinstall \"k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/install\"\n+\tapiextensionsv1 \"k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1\"\n \tv1 \"k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1\"\n \tmetav1 \"k8s.io/apimachinery/pkg/apis/meta/v1\"\n \t\"k8s.io/apimachinery/pkg/runtime\"", "target": "We're importing this twice with different aliases.", "old": "", "peft": "1815\tI think we should use `apiextensions.v1` instead of `apiextensionsv1`\n", "icl-diff": "1815\tI think it's better to use `apiextensionsv1` instead of `v1`.\n", "icl-code": "", "slm": "Please rename to `applyFixesToNode`\n", "label": ["Feat"]}
{"input": "@@ -296,17 +296,8 @@ func pruneRepository(gopts GlobalOptions, repo restic.Repository) error {\n \t}\n \n \tif len(removePacks) != 0 {\n-\t\tbar = newProgressMax(!gopts.Quiet, uint64(len(removePacks)), \"packs deleted\")\n-\t\tbar.Start()\n-\t\tfor packID := range removePacks {\n-\t\t\th := restic.Handle{Type: restic.DataFile, Name: packID.String()}\n-\t\t\terr = repo.Backend().Remove(ctx, h)\n-\t\t\tif err != nil {\n-\t\t\t\tWarnf(\"unable to remove file %v from the repository\\n\", packID.Str())\n-\t\t\t}\n-\t\t\tbar.Report(restic.Stat{Blobs: 1})\n-\t\t}\n-\t\tbar.Done()\n+\t\tVerbosef(\"remove %d old packs\\n\", len(removePacks))\n+\t\tDeleteFiles(gopts, repo, removePacks, restic.DataFile)\n \t}\n \n \tVerbosef(\"done\\n\")", "target": "As `DeleteFiles` is not only used for prune this function deserves its own file. Maybe something like `delete_files.go` or `parallel.go`?", "old": "", "peft": "1826\tI think we should use `restic.Stat{Blobs: 1}` here as well.\n", "icl-diff": "1826\tI think this is a good idea, but I'm not sure if it's worth the effort.\n", "icl-code": "", "slm": "This should go with the other iotex-address imports.\n", "label": ["Feat"]}
{"input": "@@ -0,0 +1,3 @@\n+# Copyright (c) Open-MMLab. All rights reserved.\n+\n+__version__ = '2.3.0rc0'", "target": "`short_version` and `version_info`", "old": "", "peft": "1834\tI think we should use `git describe --abbrev=0 --dirty` to get the version.\n", "icl-diff": "1834\tI think it is a good idea to add a version number to the file.\n", "icl-code": "", "slm": "I don't see the `coco_instance.py` file in this PR.\n", "label": ["Feat"]}
{"input": "@@ -81,7 +81,7 @@ class Guidance < ActiveRecord::Base\n \n   # Returns whether or not a given user can view a given guidance\n   # we define guidances viewable to a user by those owned by a guidance group:\n-  #   owned by the managing curation center\n+  #   owned by the default orgs\n   #   owned by a funder organisation\n   #   owned by an organisation, of which the user is a member\n   #", "target": "Thanks, this should make things a bit easier for people who pick up the codebase but aren't a `curation center`", "old": "", "peft": "1844\tI think this should be `owned by the default orgs`\n", "icl-diff": "1844\tI think this is a typo.\n", "icl-code": "", "slm": "use `if len(gpu_ids) == 0` is enough.\n", "label": ["Doc"]}
{"input": "@@ -628,7 +628,12 @@ func (s *Server) createLeafNode(conn net.Conn, remote *leafNodeCfg) *client {\n \n \t\tc.mu.Unlock()\n \t\t// Error will be handled below, so ignore here.\n-\t\tc.parse([]byte(info))\n+\t\terr = c.parse([]byte(info))\n+\t\tif err != nil {\n+\t\t\tc.Debugf(\"Error reading remote leafnode's INFO: %s\", err)\n+\t\t\tc.closeConnection(ReadError)\n+\t\t\treturn nil\n+\t\t}\n \t\tc.mu.Lock()\n \n \t\tif !c.flags.isSet(infoReceived) {", "target": "Should be an error... it is important for the admin of the server attempting to create the leafnode connection to see the error asap.", "old": "", "peft": "1859\tI think we should check for `err == nil` here.\n", "icl-diff": "1859\tThis is a bug. The INFO protocol is not optional.\n", "icl-code": "", "slm": "this is the only place where the x number is in there, rather than in the first place, maybe add a `x` arg to the `EXT_SIMD5` one?\n", "label": ["Feat"]}
{"input": "@@ -69,10 +69,8 @@ services:\n     image: {{ .router_image }}:{{ .router_tag }}\n     container_name: nginx-proxy\n     ports:\n-      - \"80:80\"\n-      - {{ .mailhogport }}:{{ .mailhogport }}\n-      - {{ .dbaport }}:{{ .dbaport }}\n-      - {{ .dbport }}:{{ .dbport }}\n+      {{ range $port := .ports }}- \"{{ $port }}:{{ $port }}\"\n+      {{ end }}\n     volumes:\n       - /var/run/docker.sock:/tmp/docker.sock:ro\n networks:", "target": "So this is actually a mistake that I introduced without realizing it, and have known would need to be fixed when we get here  . The format of the ports directive is \"host:container\". We only want the host port to change, not the internal container ports. The ports variable probably needs to be a map which maps external -> internal ports.", "old": "", "peft": "1862\tI think this is a bug. The ports should be in the same order as the ports in the `ports` field.\n", "icl-diff": "1862\tI don't think it improves performance that much, but you could stream the result from allItems() and collect it into a list, avoiding the for loop.\n", "icl-code": "", "slm": "Metrics/LineLength: Line is too long. [93/80]\n", "label": ["Feat"]}
{"input": "@@ -137,8 +137,8 @@ type ConsensusParams struct {\n \tDownCommitteeSize      uint64\n \tDownCommitteeThreshold uint64\n \n-\tFilterTimeoutSmallLambdas             uint64\n-\tFilterTimeoutPeriod0SmallLambdas      uint64\n+\tAgreementFilterTimeout        time.Duration\n+\tAgreementFilterTimeoutPeriod0 time.Duration\n \n \tFastRecoveryLambda    time.Duration // time between fast recovery attempts\n \tFastPartitionRecovery bool          // set when fast partition recovery is enabled", "target": "Add explanations around these variables - what do they mean, how they should be configured, etc.", "old": "", "peft": "1889\tI think this should be `AgreementFilterTimeoutPeriod0`\n", "icl-diff": "1889\tI think this is a mistake. The timeouts are in blocks, not time.\n", "icl-code": "", "slm": "Let's remove this local variable. It's only used in the test.\n", "label": ["Feat"]}
{"input": "@@ -7,6 +7,12 @@ use JavierEguiluz\\Bundle\\EasyAdminBundle\\Exception\\EntityNotFoundException;\n use Symfony\\Component\\EventDispatcher\\GenericEvent;\n use Symfony\\Component\\HttpFoundation\\Request;\n \n+/**\n+ * Adds some custom attributes to the request object to store information\n+ * related to EasyAdmin.\n+ *\n+ * <AUTHOR> Steinhausser\n+ */\n class RequestPostInitializeListener\n {\n     /** @var Request|null */", "target": "Feel free to use the full notation with my email address: `Maxime Steinhausser <<EMAIL>>` :smile:", "old": "", "peft": "1910\tI think this is not a good idea. We should not add any custom attributes to the request object.\n", "icl-diff": "1910\tI don't see the point of this class.\n", "icl-code": "", "slm": "Line is too long. [86/80]\n", "label": ["Doc"]}
{"input": "@@ -418,6 +418,10 @@ void GroupByAgg::generateCacheKey(CacheWA &cwa) const\n     groupExpr_.rebuildExprTree(ITM_ITEM_LIST);\n   if (grpExpr) { \n     cwa += \" gBy:\"; \n+\n+    if (isRollup())\n+      cwa += \" roll:\";\n+\n     grpExpr->generateCacheKey(cwa); \n   }\n }", "target": "I think we also need to add the rollupGroupExprList() to the cache key. If we rebuild the list above from a ValueIdSet on line 418 above, it is probably going to be in the same order, regardless whether it was ROLLUP(a,b) or ROLLUP(b,a).", "old": "", "peft": "1939\tI think we should add a `isRollup` method to `GroupByAgg`\n", "icl-diff": "1939\tI think it's better to use a boolean flag instead of a string.\n", "icl-code": "", "slm": "I don't think we want this import here. I don't see how `state` is used in this file.\n", "label": ["Feat"]}
{"input": "@@ -1976,6 +1976,11 @@ namespace pwiz.Skyline.Model\n         private static Type GetColumnType(string value, IFormatProvider provider)\n         {\n             double result;\n+            var quote = @\"\"\"\";\n+            if (value.StartsWith(quote) && value.EndsWith(quote))\n+            {\n+                value = value.Substring(1, value.Length - 2);\n+            }\n             if (double.TryParse(value, NumberStyles.Number, provider, out result))\n                 return typeof(double);\n             else if (FastaSequence.IsExSequence(value))", "target": "Looks like my fault for sure in trying to handle international inputs regardless of locale. I think the correct fix is actually to get rid of TrySplitColumns and use ParseDsvFields instead. It is what gets used in the end, and it already has logic for dealing with quoted fields.", "old": "", "peft": "1968\tI think this is a bug. The value should be `\"\"`\n", "icl-diff": "1968\t@gianm I think this is a bug, the value is not quoted, but the quote is removed\n", "icl-code": "", "slm": "seems like this is moving to the server.py\n", "label": ["Feat"]}
{"input": "@@ -18,6 +18,7 @@ module Travis\n           CLEANUPS.each do |find_arg|\n             sh.raw \"find #{find_arg[:directory]} -name #{find_arg[:glob]} -delete 2>/dev/null\"\n           end\n+          sh.export 'PATH', '$JAVA_HOME:$PATH'\n         end\n \n         def install", "target": "It is `$JAVA_HOME/bin`, not `$JAVA_HOME`, which should be added.", "old": "", "peft": "1975\tI think this is a bug. We should not export PATH here.\n", "icl-diff": "1975\tAdd a line break after this.\n", "icl-code": "", "slm": "Prefer single-quoted strings when you don't need string interpolation or special symbols.\n", "label": ["Feat"]}
{"input": "@@ -142,13 +142,14 @@ func (cache *httpCache) retrieve(target *core.BuildTarget, key []byte) (bool, er\n \tresp, err := cache.client.Do(req)\n \tif err != nil {\n \t\treturn false, err\n-\t} else if resp.StatusCode == http.StatusNotFound {\n+\t}\n+\tdefer resp.Body.Close()\n+\tif resp.StatusCode == http.StatusNotFound {\n \t\treturn false, nil // doesn't exist - not an error\n \t} else if resp.StatusCode != http.StatusOK {\n \t\tb, _ := ioutil.ReadAll(resp.Body)\n \t\treturn false, fmt.Errorf(\"%s\", string(b))\n \t}\n-\tdefer resp.Body.Close()\n \tgzr, err := gzip.NewReader(resp.Body)\n \tif err != nil {\n \t\treturn false, err", "target": "This seems like a more interesting change. Maybe we should re-name the PR", "old": "", "peft": "1981\tI think we should close the body here, too.\n", "icl-diff": "1981\tI think the defer should be outside the if block\n", "icl-code": "", "slm": "@aparajit-pratap Is there any ideas for how we want to increase the delta between the two rather than 10? I'm not opposed just wondering why we need to change the other tests.\n", "label": ["Feat"]}
