#!/usr/bin/env python3
"""
测试模型加载的简化脚本
用于验证 HF_ENDPOINT 问题是否已修复
"""

import os
import sys
import logging
from transformers import AutoConfig, AutoTokenizer, T5ForConditionalGeneration

# 设置日志
logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

def test_model_loading():
    """测试模型加载"""
    model_name = "Salesforce/codet5-base"
    
    logger.info(f"Testing model loading for: {model_name}")
    logger.info(f"Current HF_ENDPOINT: {os.environ.get('HF_ENDPOINT', 'Not set')}")
    
    try:
        # 测试配置加载
        logger.info("Loading model configuration...")
        config = AutoConfig.from_pretrained(model_name)
        logger.info("✓ Configuration loaded successfully")
        
        # 测试 tokenizer 加载
        logger.info("Loading tokenizer...")
        tokenizer = AutoTokenizer.from_pretrained(model_name)
        logger.info("✓ Tokenizer loaded successfully")
        
        # 测试模型加载（仅配置，不下载权重）
        logger.info("Testing model initialization...")
        model = T5ForConditionalGeneration(config)
        logger.info("✓ Model initialized successfully")
        
        logger.info("🎉 All tests passed! Model loading is working correctly.")
        return True
        
    except Exception as e:
        logger.error(f"❌ Model loading failed: {e}")
        
        # 尝试修复
        logger.info("Attempting to fix the issue...")
        
        # 移除可能有问题的环境变量
        if 'HF_ENDPOINT' in os.environ:
            original_endpoint = os.environ['HF_ENDPOINT']
            logger.info(f"Removing HF_ENDPOINT: {original_endpoint}")
            del os.environ['HF_ENDPOINT']
        
        # 设置缓存目录
        os.environ['TRANSFORMERS_CACHE'] = '/tmp/transformers_cache'
        os.environ['HF_HOME'] = '/tmp/huggingface_cache'
        
        try:
            logger.info("Retrying model loading...")
            config = AutoConfig.from_pretrained(model_name)
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            model = T5ForConditionalGeneration(config)
            logger.info("✓ Model loading successful after fix!")
            return True
        except Exception as e2:
            logger.error(f"❌ Still failed after fix: {e2}")
            return False

def main():
    """主函数"""
    logger.info("=== Model Loading Test ===")
    
    # 显示环境信息
    logger.info("Environment variables:")
    for key in ['HF_ENDPOINT', 'HF_HOME', 'TRANSFORMERS_CACHE', 'HUGGINGFACE_HUB_CACHE']:
        value = os.environ.get(key, 'Not set')
        logger.info(f"  {key}: {value}")
    
    # 测试模型加载
    success = test_model_loading()
    
    if success:
        logger.info("✅ Test completed successfully!")
        logger.info("You can now run your training script.")
        return 0
    else:
        logger.error("❌ Test failed!")
        logger.error("Please check your network connection and try the following:")
        logger.error("1. Ensure you have internet access")
        logger.error("2. Try using a VPN if you're in a restricted network")
        logger.error("3. Check if the Hugging Face Hub is accessible")
        return 1

if __name__ == "__main__":
    sys.exit(main())
