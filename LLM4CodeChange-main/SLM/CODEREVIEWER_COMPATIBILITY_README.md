# CCT5 CodeReviewer Compatibility Guide

This guide explains how to use the modified CCT5 code to work with CodeReviewer's data format and preprocessing methods.

## Overview

The compatibility layer allows you to:
- Use CCT5's advanced model architecture (`codet5_CC` with `CodeChangeModel`)
- Process CodeReviewer's data format without modification
- Maintain CodeReviewer's preprocessing approach
- Leverage CCT5's multi-task training capabilities

## Key Components

### 1. Modified Training Script
- **File**: `src/fine_tuning/finetune_codereviewer_compat.py`
- **Purpose**: Main training script adapted for CodeReviewer data
- **Features**:
  - Uses CCT5's model loading and training pipeline
  - Compatible with CodeReviewer's data format
  - Supports both training and evaluation modes

### 2. Compatibility Data Processing
- **File**: `src/utils_codereviewer_compat.py`
- **Purpose**: Data processing utilities that bridge CCT5 and CodeReviewer
- **Features**:
  - Processes CodeReviewer's JSON format (`oldf`, `patch`, `msg` fields)
  - Maintains CodeReviewer's diff processing logic
  - Adapts to CCT5's tokenization and feature extraction

### 3. Training Script
- **File**: `scripts/finetune_codereviewer_compat.sh`
- **Purpose**: Bash script to run the compatibility training
- **Features**:
  - Configurable data paths
  - GPU selection
  - Training/evaluation modes

## Data Format

The system expects CodeReviewer's standard JSON format:

```json
{
    "oldf": "original file content",
    "patch": "diff content with @@ headers",
    "msg": "commit message or review comment",
    "cmtid": "commit id (optional)",
    "y": 1
}
```

### Field Mapping
- `oldf` or `old_file` → Original file content
- `patch` or `diff` → Diff/patch content
- `msg` → Target message (commit message or review comment)
- `cmtid` → Commit ID (optional)
- `y` → Label (automatically set based on message presence)

## Usage

### 1. Setup

Ensure you have the following files in place:
```
LLM4CodeChange-main/SLM/
├── src/fine_tuning/finetune_codereviewer_compat.py
├── src/utils_codereviewer_compat.py
└── scripts/finetune_codereviewer_compat.sh
```

### 2. Prepare Data

Your data should be in CodeReviewer's JSONL format:
```bash
# Example data structure
/path/to/data/
├── train.jsonl
├── valid.jsonl
└── test.jsonl
```

### 3. Training

#### Basic Training
```bash
cd LLM4CodeChange-main/SLM
bash scripts/finetune_codereviewer_compat.sh
```

#### Custom Data Paths
```bash
bash scripts/finetune_codereviewer_compat.sh \
    -t /path/to/train.jsonl \
    -d /path/to/valid.jsonl \
    -s /path/to/test.jsonl
```

#### Specific GPU
```bash
bash scripts/finetune_codereviewer_compat.sh -g 1
```

#### Evaluation Only
```bash
bash scripts/finetune_codereviewer_compat.sh -e /path/to/model.bin
```

### 4. Configuration Options

The training script supports all CCT5 parameters:

| Parameter | Default | Description |
|-----------|---------|-------------|
| `--model_type` | `codet5_CC` | Use CCT5's CodeChangeModel |
| `--train_batch_size` | `16` | Training batch size |
| `--eval_batch_size` | `16` | Evaluation batch size |
| `--learning_rate` | `3e-4` | Learning rate |
| `--max_source_length` | `512` | Maximum input sequence length |
| `--max_target_length` | `128` | Maximum output sequence length |
| `--train_steps` | `50000` | Total training steps |
| `--save_steps` | `2000` | Save checkpoint every N steps |
| `--beam_size` | `5` | Beam size for generation |

## Key Differences from Original Projects

### vs. Original CCT5
- **Data Format**: Adapted to use CodeReviewer's JSON format instead of CCT5's format
- **Preprocessing**: Uses CodeReviewer's diff processing approach
- **Field Names**: Handles both CCT5 (`diff`, `nl`) and CodeReviewer (`patch`, `msg`) field names

### vs. Original CodeReviewer
- **Model Architecture**: Uses CCT5's `CodeChangeModel` instead of `ReviewerModel`
- **Tokenizer**: Uses CCT5's extended vocabulary with special tokens
- **Training Strategy**: Employs CCT5's multi-task training approach
- **Data Augmentation**: Uses CCT5's sophisticated data augmentation techniques

## Architecture Benefits

By combining CCT5 and CodeReviewer, you get:

1. **Advanced Model**: CCT5's `CodeChangeModel` with classification head
2. **Rich Tokenization**: Extended vocabulary with code-specific tokens
3. **Multi-task Learning**: CCT5's PL2NL and NL2PL training objectives
4. **Proven Data Processing**: CodeReviewer's robust diff processing
5. **Flexible Training**: Support for various code review tasks

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```bash
   # Ensure you're in the correct directory
   cd LLM4CodeChange-main/SLM
   # Check if compatibility files exist
   ls src/utils_codereviewer_compat.py
   ls src/fine_tuning/finetune_codereviewer_compat.py
   ```

2. **Data Format Issues**
   ```python
   # Verify your data format
   import json
   with open('your_data.jsonl', 'r') as f:
       sample = json.loads(f.readline())
       print(sample.keys())  # Should include 'oldf', 'patch', 'msg'
   ```

3. **Memory Issues**
   ```bash
   # Reduce batch size
   # Modify the script or use smaller values:
   --train_batch_size 8 --eval_batch_size 8
   ```

4. **GPU Issues**
   ```bash
   # Check GPU availability
   nvidia-smi
   # Specify GPU ID
   bash scripts/finetune_codereviewer_compat.sh -g 0
   ```

## Performance Expectations

Based on the combination of CCT5's architecture and CodeReviewer's data processing:

- **Training Speed**: Similar to original CCT5 (depends on data size and GPU)
- **Memory Usage**: Slightly higher due to extended vocabulary
- **Model Quality**: Expected to benefit from both projects' strengths
- **Convergence**: Should converge faster due to CCT5's pre-training

## Output

The training will produce:
- **Checkpoints**: Saved in `outputs/models/fine-tuning/CodeReviewerCompat/`
- **Best Model**: `checkpoint-best-ppl/` (based on perplexity)
- **Logs**: Training progress and evaluation metrics
- **Generated Outputs**: Test predictions in the output directory

## Next Steps

After training, you can:
1. Evaluate the model on your test set
2. Use the model for inference on new code review data
3. Fine-tune further on domain-specific data
4. Compare performance with original CodeReviewer models

## Support

For issues specific to this compatibility layer:
1. Check the logs for detailed error messages
2. Verify data format compatibility
3. Ensure all required files are in place
4. Check GPU memory and computational requirements
