#!/usr/bin/env python3
"""
Simple test script to verify that our argument parsing works correctly
"""

import argparse
import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.configs import add_args

def test_args():
    """Test argument parsing"""
    print("Testing argument parsing...")
    
    # Create parser and add arguments
    parser = argparse.ArgumentParser()
    args = add_args(parser)
    
    print("✓ Arguments parsed successfully")
    print(f"Available arguments:")
    
    # Print some key arguments
    key_args = [
        'model_type', 'train_filename', 'dev_filename', 'test_filename',
        'output_dir', 'train_batch_size', 'eval_batch_size', 'learning_rate',
        'max_source_length', 'max_target_length', 'train_steps', 'save_steps',
        'warmup_steps', 'beam_size', 'do_train', 'do_test', 'gpu_id'
    ]
    
    for arg in key_args:
        if hasattr(args, arg):
            print(f"  --{arg}: {getattr(args, arg)}")
        else:
            print(f"  --{arg}: NOT FOUND")
    
    print("\n✓ Argument test completed successfully")

if __name__ == "__main__":
    test_args()
