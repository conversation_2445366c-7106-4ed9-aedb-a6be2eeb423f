# Hugging Face Endpoint 错误修复指南

## 问题描述

在运行训练脚本时遇到以下错误：

```
requests.exceptions.MissingSchema: Invalid URL '/api/resolve-cache/models/Salesforce/codet5-base/...': No scheme supplied.
```

这个错误是由于 Hugging Face Transformers 库在使用镜像站点时 URL 格式不正确导致的。

## 解决方案

### 方案1：使用修复脚本（推荐）

1. 运行测试脚本检查问题：
```bash
cd LLM4CodeChange-main/SLM
python test_model_loading.py
```

2. 如果测试失败，运行修复脚本：
```bash
python fix_hf_endpoint.py
```

3. 重新运行测试确认修复：
```bash
python test_model_loading.py
```

### 方案2：手动修复环境变量

1. 临时移除有问题的环境变量：
```bash
unset HF_ENDPOINT
```

2. 设置正确的缓存目录：
```bash
export TRANSFORMERS_CACHE="/tmp/transformers_cache"
export HF_HOME="/tmp/huggingface_cache"
```

3. 如果需要使用镜像站点，设置正确的端点：
```bash
export HF_ENDPOINT="https://huggingface.co"
# 或者使用镜像站点（如果可用）
# export HF_ENDPOINT="https://hf-mirror.com"
```

### 方案3：使用修改后的训练脚本

修改后的训练脚本 `finetune_codereviewer_compat.py` 已经包含了自动错误处理功能：

1. 自动检测网络错误
2. 临时移除有问题的环境变量
3. 重试模型加载
4. 提供详细的错误信息

### 方案4：网络连接问题

如果上述方案都不能解决问题，可能是网络连接问题：

1. 检查网络连接：
```bash
ping huggingface.co
```

2. 如果在受限网络环境中，尝试使用代理或VPN

3. 检查防火墙设置是否阻止了对 Hugging Face Hub 的访问

## 使用修改后的训练脚本

修改后的训练脚本会自动处理网络问题：

```bash
cd LLM4CodeChange-main/SLM
bash scripts/finetune_codereviewer_compat.sh
```

脚本会：
1. 自动测试模型加载
2. 如果遇到网络问题，提供修复建议
3. 在训练过程中自动处理 HF_ENDPOINT 错误

## 验证修复

运行以下命令验证修复是否成功：

```bash
python -c "
from transformers import AutoConfig, AutoTokenizer
config = AutoConfig.from_pretrained('Salesforce/codet5-base')
tokenizer = AutoTokenizer.from_pretrained('Salesforce/codet5-base')
print('✓ Model loading successful!')
"
```

## 常见问题

### Q: 为什么会出现这个错误？
A: 这通常是由于 HF_ENDPOINT 环境变量设置不当，或者网络连接问题导致的。

### Q: 修复后还是有问题怎么办？
A: 尝试完全清除相关环境变量并重启终端：
```bash
unset HF_ENDPOINT HF_HOME TRANSFORMERS_CACHE HUGGINGFACE_HUB_CACHE
```

### Q: 可以离线使用吗？
A: 如果已经下载了模型文件，可以通过指定本地路径来避免网络问题：
```bash
# 将 model_name_or_path 和 tokenizer_name 设置为本地路径
--model_name_or_path /path/to/local/codet5-base
--tokenizer_name /path/to/local/codet5-base
```

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 完整的错误日志
2. 环境变量设置（`env | grep HF`）
3. 网络连接状态
4. Python 和 transformers 库版本
