{"oldf": "def hello():\n    print(\"Hello\")", "patch": "@@ -1,2 +1,2 @@\n def hello():\n-    print(\"Hello\")\n+    print(\"Hello World\")", "msg": "Update greeting message"}
{"oldf": "class Calculator:\n    def add(self, a, b):\n        return a + b", "patch": "@@ -1,3 +1,5 @@\n class Calculator:\n     def add(self, a, b):\n         return a + b\n+    def subtract(self, a, b):\n+        return a - b", "msg": "Add subtract method to Calculator class"}
{"oldf": "import os\n\ndef read_file(filename):\n    with open(filename, 'r') as f:\n        return f.read()", "patch": "@@ -1,5 +1,7 @@\n import os\n \n def read_file(filename):\n+    if not os.path.exists(filename):\n+        return None\n     with open(filename, 'r') as f:\n         return f.read()", "msg": "Add file existence check before reading"}
{"oldf": "def factorial(n):\n    if n == 0:\n        return 1\n    return n * factorial(n-1)", "patch": "@@ -1,4 +1,6 @@\n def factorial(n):\n+    if n < 0:\n+        raise ValueError(\"Factorial not defined for negative numbers\")\n     if n == 0:\n         return 1\n     return n * factorial(n-1)", "msg": "Add input validation for negative numbers"}
{"oldf": "def process_data(data):\n    result = []\n    for item in data:\n        result.append(item * 2)\n    return result", "patch": "@@ -1,5 +1,5 @@\n def process_data(data):\n-    result = []\n-    for item in data:\n-        result.append(item * 2)\n-    return result\n+    return [item * 2 for item in data]", "msg": "Refactor to use list comprehension for better performance"}
