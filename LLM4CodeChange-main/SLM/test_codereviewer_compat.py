#!/usr/bin/env python3
"""
Test script for CCT5 CodeReviewer compatibility
This script tests the compatibility layer to ensure it can properly process CodeReviewer data
"""

import json
import os
import sys
import tempfile
import multiprocessing
from transformers import Roberta<PERSON>okenizer

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils_codereviewer_compat import CodeReviewerCompatDataset, ReviewExample
from src.models import enrich_vocab
from transformers import T5Config

def create_sample_data():
    """Create sample CodeReviewer format data for testing"""
    sample_data = [
        {
            "oldf": "def hello():\n    print('Hello')",
            "patch": "@@ -1,2 +1,3 @@\n def hello():\n-    print('Hello')\n+    print('Hello World')\n+    return True",
            "msg": "Update greeting message and add return value",
            "cmtid": "abc123",
            "y": 1
        },
        {
            "oldf": "class Calculator:\n    def add(self, a, b):\n        return a + b",
            "patch": "@@ -1,3 +1,5 @@\n class Calculator:\n     def add(self, a, b):\n         return a + b\n+    def subtract(self, a, b):\n+        return a - b",
            "msg": "Add subtract method to Calculator class",
            "cmtid": "def456",
            "y": 1
        },
        {
            "oldf": "import os\nimport sys",
            "patch": "@@ -1,2 +1,3 @@\n import os\n import sys\n+import json",
            "msg": "Add json import",
            "cmtid": "ghi789",
            "y": 1
        }
    ]
    return sample_data

def test_data_processing():
    """Test the data processing pipeline"""
    print("Testing data processing pipeline...")
    
    # Create temporary data file
    sample_data = create_sample_data()
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
        for item in sample_data:
            f.write(json.dumps(item) + '\n')
        temp_file = f.name
    
    try:
        # Test ReviewExample creation
        print("Testing ReviewExample creation...")
        for i, item in enumerate(sample_data):
            example = ReviewExample(
                idx=i,
                oldf=item["oldf"],
                diff=item["patch"],
                msg=item["msg"],
                cmtid=item["cmtid"],
                max_len=512,
                y=item["y"],
                max_tgt_len=128,
                lang="",
                tokenizer=None
            )
            
            print(f"Example {i}:")
            print(f"  Available: {example.avail}")
            print(f"  Lines: {len(example.lines)}")
            print(f"  Labels: {len(example.labels)}")
            print(f"  Input length: {len(example.input)}")
            
            if example.avail:
                print(f"  First few lines: {example.lines[:2]}")
                print(f"  First few labels: {example.labels[:2]}")
            print()
        
        print("✓ ReviewExample creation test passed")
        
    finally:
        # Clean up
        os.unlink(temp_file)

def test_tokenizer_setup():
    """Test tokenizer setup and vocabulary enrichment"""
    print("Testing tokenizer setup...")
    
    try:
        # Create a mock args object
        class MockArgs:
            def __init__(self):
                self.add_lang_ids = False
                self.max_source_length = 512
                self.max_target_length = 128
        
        args = MockArgs()
        
        # Load tokenizer
        tokenizer = RobertaTokenizer.from_pretrained("Salesforce/codet5-base")
        config = T5Config.from_pretrained("Salesforce/codet5-base")
        
        print(f"Original vocab size: {len(tokenizer)}")
        
        # Enrich vocabulary
        tokenizer, config = enrich_vocab(args, tokenizer, config)
        
        print(f"Enriched vocab size: {len(tokenizer)}")
        
        # Test special tokens
        special_tokens = ['<pad>', '<s>', '</s>', '<mask>', '<keep>', '<add>', '<del>', '<start>', '<end>', '<msg>']
        for token in special_tokens:
            if token in tokenizer.get_vocab():
                print(f"✓ {token}: {tokenizer.get_vocab()[token]}")
            else:
                print(f"✗ {token}: not found")
        
        # Test special_dict
        if hasattr(tokenizer, 'special_dict'):
            print(f"✓ special_dict created with {len(tokenizer.special_dict)} entries")
        else:
            print("✗ special_dict not created")
        
        print("✓ Tokenizer setup test passed")
        return tokenizer, config, args
        
    except Exception as e:
        print(f"✗ Tokenizer setup test failed: {e}")
        return None, None, None

def test_dataset_creation():
    """Test dataset creation with the compatibility layer"""
    print("Testing dataset creation...")
    
    tokenizer, config, args = test_tokenizer_setup()
    if tokenizer is None:
        print("✗ Cannot test dataset creation without tokenizer")
        return
    
    # Create temporary data file
    sample_data = create_sample_data()
    with tempfile.NamedTemporaryFile(mode='w', suffix='.jsonl', delete=False) as f:
        for item in sample_data:
            f.write(json.dumps(item) + '\n')
        temp_file = f.name
    
    try:
        # Create dataset
        pool = multiprocessing.Pool(2)
        dataset = CodeReviewerCompatDataset(tokenizer, pool, args, temp_file, samplenum=3)
        
        print(f"Dataset created with {len(dataset)} features")
        
        # Test a few samples
        for i in range(min(3, len(dataset))):
            feature = dataset[i]
            print(f"Feature {i}:")
            print(f"  Example ID: {feature.example_id}")
            print(f"  Source IDs length: {len(feature.source_ids)}")
            print(f"  Target IDs length: {len(feature.target_ids)}")
            print(f"  Type: {feature.type}")
            print()
        
        pool.close()
        pool.join()
        
        print("✓ Dataset creation test passed")
        
    except Exception as e:
        print(f"✗ Dataset creation test failed: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        os.unlink(temp_file)

def test_data_format_compatibility():
    """Test compatibility with different data formats"""
    print("Testing data format compatibility...")
    
    # Test different field name variations
    variations = [
        {
            "oldf": "original content",
            "patch": "diff content",
            "msg": "message",
            "cmtid": "commit1",
            "y": 1
        },
        {
            "old_file": "original content",
            "diff": "diff content", 
            "msg": "message",
            "cmtid": "commit2",
            "y": 1
        },
        {
            "oldf": "original content",
            "patch": "diff content",
            "msg": "message",
            # Missing cmtid and y - should be handled gracefully
        }
    ]
    
    for i, item in enumerate(variations):
        try:
            example = ReviewExample(
                idx=i,
                oldf=item.get("oldf", item.get("old_file", "")),
                diff=item.get("patch", item.get("diff", "")),
                msg=item.get("msg", ""),
                cmtid=item.get("cmtid", ""),
                max_len=512,
                y=item.get("y", 0),
                max_tgt_len=128,
                lang="",
                tokenizer=None
            )
            print(f"✓ Variation {i+1}: Processed successfully (available: {example.avail})")
        except Exception as e:
            print(f"✗ Variation {i+1}: Failed - {e}")
    
    print("✓ Data format compatibility test completed")

def main():
    """Run all tests"""
    print("=" * 60)
    print("CCT5 CodeReviewer Compatibility Test Suite")
    print("=" * 60)
    print()
    
    try:
        test_data_processing()
        print()
        
        test_data_format_compatibility()
        print()
        
        test_dataset_creation()
        print()
        
        print("=" * 60)
        print("All tests completed!")
        print("=" * 60)
        
    except Exception as e:
        print(f"Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
