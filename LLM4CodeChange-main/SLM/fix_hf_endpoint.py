#!/usr/bin/env python3
"""
修复 Hugging Face 端点配置问题的脚本
"""

import os
import sys
import requests
from transformers import AutoConfig, AutoTokenizer
from transformers.utils import cached_path
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_hf_endpoint():
    """测试 HF_ENDPOINT 是否正常工作"""
    endpoint = os.environ.get('HF_ENDPOINT', 'https://huggingface.co')
    logger.info(f"Testing HF_ENDPOINT: {endpoint}")
    
    try:
        # 测试基本连接
        response = requests.get(f"{endpoint}/api/models/Salesforce/codet5-base", timeout=10)
        if response.status_code == 200:
            logger.info("HF_ENDPOINT is working correctly")
            return True
        else:
            logger.warning(f"HF_ENDPOINT returned status code: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"Failed to connect to HF_ENDPOINT: {e}")
        return False

def fix_hf_config():
    """修复 HF 配置问题"""
    
    # 方案1: 清除可能有问题的环境变量
    problematic_vars = ['HF_ENDPOINT']
    for var in problematic_vars:
        if var in os.environ:
            logger.info(f"Temporarily removing {var}: {os.environ[var]}")
            del os.environ[var]
    
    # 方案2: 设置正确的环境变量
    os.environ['TRANSFORMERS_CACHE'] = '/tmp/transformers_cache'
    os.environ['HF_HOME'] = '/tmp/huggingface_cache'
    
    # 方案3: 尝试使用官方端点
    os.environ['HF_ENDPOINT'] = 'https://huggingface.co'
    
    logger.info("HF configuration has been reset")

def test_model_loading():
    """测试模型加载是否正常"""
    try:
        logger.info("Testing model configuration loading...")
        config = AutoConfig.from_pretrained("Salesforce/codet5-base")
        logger.info("✓ Model configuration loaded successfully")
        
        logger.info("Testing tokenizer loading...")
        tokenizer = AutoTokenizer.from_pretrained("Salesforce/codet5-base")
        logger.info("✓ Tokenizer loaded successfully")
        
        return True
    except Exception as e:
        logger.error(f"✗ Model loading failed: {e}")
        return False

def main():
    """主函数"""
    logger.info("=== HF Endpoint Fix Script ===")
    
    # 显示当前环境变量
    logger.info("Current environment variables:")
    for key in ['HF_ENDPOINT', 'HF_HOME', 'TRANSFORMERS_CACHE']:
        value = os.environ.get(key, 'Not set')
        logger.info(f"  {key}: {value}")
    
    # 测试当前配置
    if not test_hf_endpoint():
        logger.info("Current HF_ENDPOINT is not working, applying fixes...")
        fix_hf_config()
    
    # 测试模型加载
    if test_model_loading():
        logger.info("✓ All tests passed! You can now run your training script.")
        return 0
    else:
        logger.error("✗ Model loading still fails. Please check your network connection.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
