"""
CodeReviewer compatibility layer for CCT5 project
This module provides data processing utilities that are compatible with CodeReviewer's data format
while using CCT5's model architecture and training pipeline.
"""

import json
import logging
import os
import random
import torch
from copy import deepcopy as cp
from torch.utils.data import Dataset
from transformers import T5Tokenizer, RobertaTokenizer
import multiprocessing

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s -   %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)
logger = logging.getLogger(__name__)

class ReviewFeatures(object):
    """Compatible with CodeReviewer's ReviewFeatures"""
    def __init__(self, example_id, source_ids, source_labels, target_ids, type):
        self.example_id = example_id
        self.source_ids = source_ids
        self.source_labels = source_labels
        self.target_ids = target_ids
        assert type in ("label", "line", "genmsg", "daemsg")
        self.type = type

class ReviewExample(object):
    """Compatible with CodeReviewer's ReviewExample but adapted for CCT5"""
    def __init__(self, idx, oldf, diff, msg, cmtid, max_len, y, max_tgt_len=128, lang="", tokenizer=None):
        self.idx = idx
        self.oldf = oldf
        self.diff = diff
        self.msg = msg
        self.cmtid = cmtid
        self.max_len = max_len
        self.max_tgt_len = max_tgt_len
        self.y = y
        self.lang = lang
        self.tokenizer = tokenizer
        self.prevlines = []
        self.afterlines = []
        self.lines = []
        self.labels = []
        self.avail = False
        self.input = ""
        self.tokenized = False
        self.align_and_clean()
        self.postprocess()

    def align_and_clean(self):
        """Process diff to extract lines and labels - compatible with CodeReviewer"""
        try:
            lines = self.diff.split('\n')
            if not lines:
                return
            
            # Skip @@ header lines
            start_idx = 0
            for i, line in enumerate(lines):
                if line.startswith('@@'):
                    start_idx = i + 1
                    break
            
            lines = lines[start_idx:]
            
            # Process diff lines
            processed_lines = []
            processed_labels = []
            
            for line in lines:
                if not line.strip():
                    continue
                    
                if line.startswith('+'):
                    processed_lines.append(line[1:].strip())
                    processed_labels.append(1)  # add
                elif line.startswith('-'):
                    processed_lines.append(line[1:].strip())
                    processed_labels.append(0)  # del
                elif line.startswith(' '):
                    processed_lines.append(line[1:].strip())
                    processed_labels.append(2)  # keep
                else:
                    # Handle lines without prefix as context
                    processed_lines.append(line.strip())
                    processed_labels.append(2)  # keep
            
            if processed_lines:
                self.lines = processed_lines
                self.labels = processed_labels
                self.avail = True
                
                # Create input string with special tokens
                input_parts = []
                for i, (line, label) in enumerate(zip(self.lines, self.labels)):
                    if i == 0:
                        input_parts.append("<e0>")
                    input_parts.append(line)
                    if i < len(self.lines) - 1:
                        input_parts.append("<e0>")
                
                self.input = " ".join(input_parts)
            else:
                self.avail = False
                
        except Exception as e:
            logger.warning(f"Error processing diff for example {self.idx}: {e}")
            self.avail = False

    def postprocess(self):
        """Post-process the example - compatible with CodeReviewer"""
        if not self.avail:
            return
        
        # Ensure we don't exceed max lengths
        if len(self.lines) > self.max_len // 10:  # Rough estimation
            # Truncate from both ends like CCT5
            total_lines = len(self.lines)
            keep_lines = self.max_len // 10
            start_idx = (total_lines - keep_lines) // 2
            end_idx = start_idx + keep_lines
            
            self.lines = self.lines[start_idx:end_idx]
            self.labels = self.labels[start_idx:end_idx]
            
            # Recreate input
            input_parts = []
            for i, (line, label) in enumerate(zip(self.lines, self.labels)):
                if i == 0:
                    input_parts.append("<e0>")
                input_parts.append(line)
                if i < len(self.lines) - 1:
                    input_parts.append("<e0>")
            
            self.input = " ".join(input_parts)

class CodeReviewerCompatDataset(Dataset):
    """
    Dataset that uses CodeReviewer's data format but processes it for CCT5 model
    """
    def __init__(self, tokenizer, pool, args, file_path, samplenum=-1):
        self.tokenizer = tokenizer
        self.args = args
        
        # Determine tokenizer type for caching
        if isinstance(tokenizer, T5Tokenizer):
            tokenizer_type = ""
        elif isinstance(tokenizer, RobertaTokenizer):
            tokenizer_type = "rb"
        else:
            tokenizer_type = "unk"
        
        # Cache file path
        savep = file_path.replace(".jsonl", tokenizer_type + ".cct5compat.exps")
        
        if os.path.exists(savep):
            logger.info("Loading examples from {}".format(savep))
            examples = torch.load(savep)
        else:
            logger.info("Reading examples from {}".format(file_path))
            examples = self.read_codereviewer_examples(file_path, samplenum, tokenizer, args)
            logger.info(f"Tokenize examples: {file_path}")
            examples = pool.map(self.tokenize, 
                               [(example, tokenizer, args) for example in examples])
            torch.save(examples, savep)
        
        logger.info("Convert examples to features...")
        self.set_start_end_ids(examples)
        self.featss = pool.map(self.convert_examples_to_features,
                              [(example, tokenizer, args) for example in examples])
        self.feats = [feat for feats in self.featss for feat in feats]  # expand the lists

    def read_codereviewer_examples(self, filename, data_num=-1, tokenizer=None, args=None):
        """Read examples from CodeReviewer format file"""
        examples = []
        idx = 0
        with open(filename, 'r', encoding='utf8') as f:
            for i, line in enumerate(f):
                if args and args.debug and i > 100:
                    break
                try:
                    js = json.loads(line.strip())
                except:
                    logger.warning("Error during reading json data.")
                    continue
                
                maxl = args.max_source_length if args else 512
                
                # Handle different field names
                oldf = js.get("oldf", js.get("old_file", ""))
                diff = js.get("patch", js.get("diff", ""))
                msg = js.get("msg", "")
                cmtid = js.get("cmtid", "")
                
                if "y" not in js:
                    js["y"] = 0
                if msg and len(msg) > 0:
                    js["y"] = 1
                if "lang" not in js:
                    js["lang"] = ""
                
                example = ReviewExample(
                    idx=idx,
                    oldf=oldf,
                    diff=diff,
                    msg=msg,
                    cmtid=cmtid,
                    max_len=maxl,
                    y=int(js["y"]),
                    max_tgt_len=args.max_target_length if args else 128,
                    lang=js["lang"],
                    tokenizer=tokenizer
                )
                
                if example.avail:
                    examples.append(example)
                    idx += 1
                    if data_num != -1 and idx >= data_num:
                        break
                else:
                    idx += 1
                    if data_num != -1 and idx >= data_num:
                        break
        
        return examples

    def set_start_end_ids(self, examples):
        """Set start and end IDs for examples"""
        for example in examples:
            labels = example.labels
            start_id = 0
            end_id = len(labels) - 1
            for i, label in enumerate(labels):
                if label != -100:
                    start_id = i
                    break
            for i in range(len(labels) - 1, -1, -1):
                label = labels[i]
                if label != -100:
                    end_id = i
                    break
            example.start_id = start_id
            example.end_id = end_id

    def tokenize(self, item):
        """Tokenize example - adapted from CCT5's approach"""
        example, tokenizer, args = item
        
        if example.tokenized is False:
            example.msg = self.encode_remove(tokenizer, example.msg, args)
            example.input = self.encode_remove(tokenizer, example.input, args, limit_length=False)
            
            e0id = tokenizer.special_dict["<e0>"]
            inputs = " ".join(str(id) for id in example.input)
            lines = inputs.split(" " + str(e0id) + " ")
            lines = [
                [int(v) for v in line.split(" ") if len(v) > 0] for line in lines
            ]
        else:
            lines = example.lines
        
        # Length control similar to CCT5
        lens = list(map(len, lines))
        curlen = len(lens) + sum(lens)
        left, right = 0, len(lines)
        
        while curlen > args.max_source_length - 2*len(lines) - len(example.msg) - 1:
            if left % 2 == 0:
                curlen -= 1 + len(lines[left])
                left += 1
            else:
                right -= 1
                curlen -= 1 + len(lines[right])
        
        lines = lines[left:right]
        labels = example.labels[left:right]
        
        assert len(lines) + sum(map(len, lines)) <= args.max_source_length - 2, "Too long inputs"
        if len(lines) != len(labels):
            logger.info("Not equal length in tokenize.")
        
        example.lines = lines
        example.labels = labels
        example.tokenized = True
        return example

    def convert_examples_to_features(self, item):
        """Convert examples to features - using CCT5's multi-task approach"""
        example, _, _ = item
        if len(example.msg) > 0:
            exs = []
            # Use CCT5's data augmentation strategy
            for _ in range(3):  # up sampling
                if random.random() < 0.5:
                    exs.append(self.gen_PL2NL_example(item))
                else:
                    exs.append(self.gen_NL2PL_example(item))
            return exs
        else:
            return [self.gen_PL2NL_example(item)]

    def gen_PL2NL_example(self, item):
        """Generate Program-to-Natural Language example"""
        example, tokenizer, args = item
        lines = example.lines
        labels = example.labels
        source_ids, target_ids = [], []
        
        id_dict = {0: tokenizer.del_id, 1: tokenizer.add_id, 2: tokenizer.keep_id}
        
        for i, (line, label) in enumerate(zip(lines, labels)):
            if i == example.start_id:
                source_ids.append(tokenizer.start_id)
            if label != -100:
                source_ids.append(id_dict[label])
            source_ids.extend(line)
            if i == example.end_id:
                source_ids.append(tokenizer.end_id)
        
        target_ids = [tokenizer.msg_id] + example.msg
        
        source_ids, target_ids = self.pad_assert(source_ids, target_ids, args, tokenizer)
        input_labels = [-100] * len(source_ids)
        
        return ReviewFeatures(example.idx, source_ids, input_labels, target_ids, type="genmsg")

    def gen_NL2PL_example(self, item):
        """Generate Natural Language-to-Program example"""
        example, tokenizer, args = item
        source_ids = [tokenizer.msg_id] + example.msg
        target_ids = []
        
        lines = example.lines
        labels = example.labels
        id_dict = {0: tokenizer.del_id, 1: tokenizer.add_id, 2: tokenizer.keep_id}
        
        for i, (line, label) in enumerate(zip(lines, labels)):
            if i == example.start_id:
                target_ids.append(tokenizer.start_id)
            if label != -100:
                target_ids.append(id_dict[label])
            target_ids.extend(line)
            if i == example.end_id:
                target_ids.append(tokenizer.end_id)
        
        source_ids, target_ids = self.pad_assert(source_ids, target_ids, args, tokenizer)
        input_labels = [-100] * len(source_ids)
        
        return ReviewFeatures(example.idx, source_ids, input_labels, target_ids, type="genmsg")

    def encode_remove(self, tokenizer, text, args, limit_length=True):
        """Encode text and remove special tokens"""
        if limit_length is True:
            text = tokenizer.encode(text, max_length=args.max_source_length - 2, truncation=True)
        else:
            text = tokenizer.encode(text)
        
        if type(tokenizer) == T5Tokenizer:
            return text[:-1]
        elif type(tokenizer) == RobertaTokenizer:
            return text[1:-1]
        else:
            raise NotImplementedError

    def pad_assert(self, source_ids, target_ids, args, tokenizer):
        """Pad sequences to fixed length"""
        source_ids = source_ids[:args.max_source_length - 2]
        source_ids = [tokenizer.bos_id] + source_ids + [tokenizer.eos_id]
        pad_len = args.max_source_length - len(source_ids)
        source_ids += [tokenizer.pad_id] * pad_len
        
        target_ids = target_ids[:args.max_target_length - 1]
        target_ids = target_ids + [tokenizer.eos_id]
        pad_len = args.max_target_length - len(target_ids)
        target_ids += [tokenizer.pad_id] * pad_len
        
        assert len(source_ids) == args.max_source_length, "Not equal length."
        assert len(target_ids) == args.max_target_length, "Not equal length."
        
        return source_ids, target_ids

    def __len__(self):
        return len(self.feats)

    def __getitem__(self, i):
        return self.feats[i]
