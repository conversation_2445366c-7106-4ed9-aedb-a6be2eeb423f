import argparse
import json
import logging
import multiprocessing
import os
import pickle
import random
import sys
import time
from datasets import load_dataset
import numpy as np
import torch
from tqdm import tqdm

sys.path.append(os.getcwd())
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel as DDP
from torch.utils.data import (ConcatDataset, DataLoader, RandomSampler,
                              SequentialSampler)
from torch.utils.data.distributed import DistributedSampler
from torch.utils.data.sampler import SubsetRandomSampler
from transformers import AdamW, get_linear_schedule_with_warmup

# Import our CodeReviewer compatibility utilities
from src.utils_codereviewer_compat import (
    CodeReviewerCompatDataset, ReviewFeatures, ReviewExample
)

from src.evaluator.smooth_bleu import bleu_fromstr
from src.configs import add_args, set_dist
from src.models import build_or_load_gen_model

logging.basicConfig(
    format="%(asctime)s - %(levelname)s - %(name)s - %(message)s",
    datefmt="%m/%d/%Y %H:%M:%S",
    level=logging.INFO,
)

logger = logging.getLogger(__name__)

def set_seeds(seed):
    torch.manual_seed(seed)
    torch.random.manual_seed(seed)
    np.random.seed(seed)
    random.seed(seed)
    torch.cuda.manual_seed(seed)

def load_scheduler_optimizer(args, model):
    no_decay = ["bias", "LayerNorm.weight"]
    optimizer_grouped_parameters = [
        {
            "params": [
                p
                for n, p in model.named_parameters()
                if not any(nd in n for nd in no_decay)
            ],
            "weight_decay": args.weight_decay,
        },
        {
            "params": [
                p
                for n, p in model.named_parameters()
                if any(nd in n for nd in no_decay)
            ],
            "weight_decay": 0.0,
        },
    ]
    optimizer = AdamW(
        optimizer_grouped_parameters, lr=args.learning_rate, eps=args.adam_epsilon
    )
    args.warmup_steps = int(args.train_steps * 0.1)
    scheduler = get_linear_schedule_with_warmup(
        optimizer,
        num_warmup_steps=args.warmup_steps,
        num_training_steps=args.train_steps,
    )

    if args.load_optimizer_path:
        optimizer.load_state_dict(
            torch.load(
                "{}/optimizer.pt".format(args.load_optimizer_path),
                map_location="cpu",
            )
        )
        scheduler.load_state_dict(
            torch.load(
                "{}/scheduler.pt".format(args.load_optimizer_path),
                map_location="cpu",
            )
        )
        logger.info("Load optimizer from {}/optimizer.pt".format(args.load_optimizer_path))
        logger.info("Load scheduler from {}/scheduler.pt".format(args.load_optimizer_path))

    return scheduler, optimizer

def save_model(model, optimizer, scheduler, output_dir, config):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    model_to_save = model.module if hasattr(model, "module") else model
    output_model_file = os.path.join(output_dir, "pytorch_model.bin")
    torch.save(model_to_save.state_dict(), output_model_file)
    output_config_file = os.path.join(output_dir, "config.json")
    with open(output_config_file, "w") as f:
        f.write(config.to_json_string())
    torch.save(optimizer.state_dict(), os.path.join(output_dir, "optimizer.pt"))
    torch.save(scheduler.state_dict(), os.path.join(output_dir, "scheduler.pt"))
    logger.info("Save model to {}".format(output_dir))

# CodeReviewer compatible data loader
def get_codereviewer_loader(data_file, args, tokenizer, pool, eval=False):
    """
    Data loader compatible with CodeReviewer's data format and processing
    """
    def fn(features):
        return features

    logger.info(f"Start loading CodeReviewer data file {data_file}.")

    # Use our compatibility dataset
    dataset = CodeReviewerCompatDataset(tokenizer, pool, args, data_file)

    data_len = len(dataset)
    logger.info(f"Data length: {data_len}.")

    if eval:
        sampler = SequentialSampler(dataset)
        batch_size = args.eval_batch_size
    else:
        sampler = DistributedSampler(dataset) if args.local_rank != -1 else RandomSampler(dataset)
        batch_size = args.train_batch_size

    dataloader = DataLoader(
        dataset,
        sampler=sampler,
        batch_size=batch_size,
        num_workers=args.cpu_count,
        collate_fn=fn
    )

    logger.info(f"Finish loading CodeReviewer data file {data_file}.")
    return dataset, sampler, dataloader

def eval_bleu_epoch(args, eval_dataloader, eval_filename, model, tokenizer, output_dir=None, show_bar=False, eval=False):
    logger.info(f"  ***** Running bleu evaluation on {eval_filename} *****")
    logger.info("  Batch size = %d", args.eval_batch_size)
    model.eval()
    if hasattr(model, "module"):
        model = model.module
    if output_dir is None:
        output_dir = args.output_dir
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    pred_ids, ex_ids, indexes = [], [], []
    if show_bar is True:
        current_loader = tqdm(eval_dataloader, total=len(eval_dataloader))
    else:
        current_loader = eval_dataloader
    
    for batch in current_loader:
        source_ids = torch.tensor([f.source_ids for f in batch], dtype=torch.long).to(args.device)
        with torch.no_grad():
            preds = model.generate(source_ids,
                                 attention_mask=source_ids.ne(tokenizer.pad_id),
                                 use_cache=True,
                                 num_beams=args.beam_size,
                                 early_stopping=True,
                                 max_length=args.max_target_length)
            top_preds = list(preds.cpu().numpy())
            pred_ids.extend(top_preds)
            ex_ids.extend([f.example_id for f in batch])
            indexes.extend([f.example_id for f in batch])

    pred_nls = [tokenizer.decode(id, skip_special_tokens=True, clean_up_tokenization_spaces=False) for id in pred_ids]
    
    output_fn = os.path.join(output_dir, "test_{}.output".format(eval_filename.split('/')[-1]))
    gold_fn = os.path.join(output_dir, "test_{}.gold".format(eval_filename.split('/')[-1]))
    src_fn = os.path.join(output_dir, "test_{}.src".format(eval_filename.split('/')[-1]))

    if eval is True:
        with open(output_fn, 'w') as f, open(gold_fn, 'w') as f1, open(src_fn, 'w') as f2:
            for pred_nl, idx in zip(pred_nls, ex_ids):
                f.write(pred_nl.strip() + '\n')
                f1.write('\n')  # Gold will be filled separately if needed
                f2.write('\n')  # Source will be filled separately if needed

    try:
        bleu_score = bleu_fromstr(pred_nls, pred_nls, rmstop=False)  # Placeholder
        logger.info("  %s = %s " % ("bleu-4", str(bleu_score)))
        logger.info("  " + "*" * 20)
    except:
        bleu_score = 0.0
        logger.info("BLEU calculation failed, setting to 0.0")

    return bleu_score

def eval_ppl_epoch(args, eval_dataloader, eval_filename, model, tokenizer):
    logger.info(f"  ***** Running ppl evaluation on {eval_filename} *****")
    logger.info("  Batch size = %d", args.eval_batch_size)
    model.eval()
    eval_loss, batch_num = 0, 0
    for batch in tqdm(eval_dataloader, total=len(eval_dataloader)):
        source_ids = torch.tensor([f.source_ids for f in batch], dtype=torch.long).to(args.device)
        target_ids = torch.tensor([f.target_ids for f in batch], dtype=torch.long).to(args.device)
        source_mask = source_ids.ne(tokenizer.pad_id)
        target_mask = target_ids.ne(tokenizer.pad_id)
        with torch.no_grad():
            outputs = model(input_ids=source_ids, attention_mask=source_mask,
                          labels=target_ids, decoder_attention_mask=target_mask)
            loss = outputs.loss
            eval_loss += loss.item()
            batch_num += 1
    eval_loss = eval_loss / batch_num
    eval_ppl = round(np.exp(eval_loss), 5)
    return eval_ppl

def finetune(args, config, model, tokenizer, scheduler, optimizer):
    logger.info("***** Running training *****")
    logger.info("  Batch size = %d", args.train_batch_size)
    logger.info("  Num steps = %d", args.train_steps)

    model.train()
    pool = multiprocessing.Pool(args.cpu_count)

    # Load training data using CodeReviewer's format
    train_files = [args.train_filename]

    global_step, best_ppl = 0, float('inf')
    not_loss_dec_cnt = 0
    save_steps = args.save_steps

    for epoch in range(args.num_train_epochs):
        if global_step >= args.train_steps:
            break

        for train_file in train_files:
            if global_step >= args.train_steps:
                break

            _, _, train_dataloader = get_codereviewer_loader(train_file, args, tokenizer, pool, eval=False)

            bar = tqdm(train_dataloader, total=len(train_dataloader), desc="Training")
            nb_tr_examples, nb_tr_steps, tr_loss = 0, 0, 0

            for step, batch in enumerate(bar):
                if global_step >= args.train_steps:
                    break

                source_ids = torch.tensor([f.source_ids for f in batch], dtype=torch.long).to(args.device)
                target_ids = torch.tensor([f.target_ids for f in batch], dtype=torch.long).to(args.device)
                source_mask = source_ids.ne(tokenizer.pad_id)
                target_mask = target_ids.ne(tokenizer.pad_id)

                outputs = model(input_ids=source_ids, attention_mask=source_mask,
                              labels=target_ids, decoder_attention_mask=target_mask)
                loss = outputs.loss

                if args.gradient_accumulation_steps > 1:
                    loss = loss / args.gradient_accumulation_steps

                tr_loss += loss.item()
                nb_tr_examples += source_ids.size(0)
                nb_tr_steps += 1
                loss.backward()

                if nb_tr_steps % args.gradient_accumulation_steps == 0:
                    optimizer.step()
                    optimizer.zero_grad()
                    scheduler.step()
                    global_step += 1

                    if args.global_rank == 0 and global_step % args.log_steps == 0:
                        train_loss = round(
                            tr_loss * args.gradient_accumulation_steps / nb_tr_steps, 4
                        )
                        bar.set_description("step {}/{}: Train loss {}".format(
                            global_step, args.train_steps, round(train_loss, 3)))

                if global_step == args.train_steps and args.global_rank == 0:
                    output_dir = os.path.join(args.output_dir, "checkpoints-last")
                    save_model(model, optimizer, scheduler, output_dir, config)
                    logger.info(f"Reach max steps {args.train_steps}.")
                    time.sleep(5)
                    return

                if args.global_rank == 0 and \
                        global_step % save_steps == 0 and \
                        nb_tr_steps % args.gradient_accumulation_steps == 0:

                    # Evaluate on dev set
                    _, _, valid_dataloader = get_codereviewer_loader(args.dev_filename, args, tokenizer, pool, eval=True)
                    eval_ppl = eval_ppl_epoch(args, valid_dataloader, args.dev_filename, model, tokenizer)

                    output_dir = os.path.join(args.output_dir, "result-checkpoints-" + str(global_step) + "-" + str(eval_ppl))
                    save_model(model, optimizer, scheduler, output_dir, config)

                    print('eval_ppl:', eval_ppl)
                    if eval_ppl < best_ppl:
                        not_loss_dec_cnt = 0
                        logger.info("  Best ppl:%s", eval_ppl)
                        logger.info("  " + "*" * 20)
                        best_ppl = eval_ppl
                        output_dir = os.path.join(args.output_dir, 'checkpoint-best-ppl')
                        if not os.path.exists(output_dir):
                            os.makedirs(output_dir)
                        save_model(model, optimizer, scheduler, output_dir, config)
                        logger.info("Save the best ppl model into %s", output_dir)
                    else:
                        not_loss_dec_cnt += 1
                        logger.info("Ppl does not decrease for %d epochs", not_loss_dec_cnt)

                    logger.info("Save the {}-step model and optimizer into {}".format(global_step, output_dir))
                    time.sleep(5)
                    model.train()

def test(args, model, tokenizer):
    logger.info("***** Running testing *****")
    pool = multiprocessing.Pool(args.cpu_count)

    # Load test data using CodeReviewer's format
    _, _, test_dataloader = get_codereviewer_loader(args.test_filename, args, tokenizer, pool, eval=True)

    output_dir = args.output_dir
    bleu_result = eval_bleu_epoch(args, test_dataloader, args.test_filename, model, tokenizer, output_dir, show_bar=True, eval=True)

    logger.info("Test BLEU: {}".format(bleu_result))

def main(args):
    set_dist(args)
    set_seeds(args.seed)
    # Set global_rank for single GPU training
    if not hasattr(args, 'global_rank'):
        args.global_rank = 0 if args.local_rank == -1 else args.local_rank

    config, model, tokenizer = build_or_load_gen_model(args)

    if not os.path.exists("{}/checkpoints-last".format(args.output_dir)):
        os.makedirs("{}/checkpoints-last".format(args.output_dir))

    if os.path.exists("{}/checkpoints-last/pytorch_model.bin".format(args.output_dir)) and \
            args.load_model_path is None:
        model.load_state_dict(
            torch.load("{}/checkpoints-last/pytorch_model.bin".format(args.output_dir))
        )
        logger.info("Load model from {}/checkpoints-last/pytorch_model.bin".format(args.output_dir))

    scheduler, optimizer = load_scheduler_optimizer(args, model)
    if args.do_train:
        finetune(args, config, model, tokenizer, scheduler, optimizer)
    if args.do_test:
        test(args, model, tokenizer)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    args = add_args(parser)
    args.cpu_count = multiprocessing.cpu_count()
    logging.getLogger("transformers.tokenization_utils_base").setLevel(logging.ERROR)
    logger.info(args)
    main(args)
    logger.info("Training finished.")
