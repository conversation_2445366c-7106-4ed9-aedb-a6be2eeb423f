#!/bin/bash

# CCT5 CodeReviewer Compatibility Training Script
# This script adapts CCT5 to work with CodeReviewer's data format and preprocessing

# 设置 Hugging Face 镜像站点环境变量
export HF_ENDPOINT="https://hf-mirror.com"

# 如果镜像站点有问题，可以尝试使用官方站点
# export HF_ENDPOINT="https://huggingface.co"

CURRENT_DIR=/data/xjd/SodaCoder-main/
NCCL_DEBUG=INFO
GPU_ID=0,1
PRETRAINED_MODEL_DIR="$CURRENT_DIR/models/pre-training/Gen"
MODEL_PATH="$CURRENT_DIR/models/pre-training/Gen/pytorch_model.bin"
FINETUNED_MODEL_PATH="$CURRENT_DIR/models/fine-tuning/CodeReviewerCompat/pytorch_model.bin"
EVAL_FLAG=false

# CodeReviewer data paths (modify these to point to your CodeReviewer data)
CODEREVIEWER_TRAIN_DATA="/data/xjd/SodaCoder-main/Comment_Generation/msg-train.jsonl"
CODEREVIEWER_DEV_DATA="/data/xjd/SodaCoder-main/Comment_Generation/msg-valid.jsonl"
CODEREVIEWER_TEST_DATA="/data/xjd/SodaCoder-main/Comment_Generation/msg-test.jsonl"

usage() {
  echo "Usage: ${0} [-g GPU_ID] [-e MODEL_PATH] [-t TRAIN_DATA] [-d DEV_DATA] [-s TEST_DATA]" 1>&2
  echo "  -g: GPU ID to use (default: 0)" 1>&2
  echo "  -e: Model path for evaluation (enables eval mode)" 1>&2
  echo "  -t: Training data path (CodeReviewer format)" 1>&2
  echo "  -d: Development data path (CodeReviewer format)" 1>&2
  echo "  -s: Test data path (CodeReviewer format)" 1>&2
  exit 1 
}

while getopts ":g:e:t:d:s:" opt; do
    case $opt in
        g)  GPU_ID="$OPTARG"
            ;;
        e)  MODEL_PATH="$OPTARG"
            EVAL_FLAG=true
            ;;
        t)  CODEREVIEWER_TRAIN_DATA="$OPTARG"
            ;;
        d)  CODEREVIEWER_DEV_DATA="$OPTARG"
            ;;
        s)  CODEREVIEWER_TEST_DATA="$OPTARG"
            ;;
        \?)
          echo "Invalid option: -$OPTARG" >&2
          exit 1
          ;;
        :)
        echo "Option -$OPTARG requires an argument." >&2
        exit 1
        ;;
    esac
done

function check_data_files() {
    echo "Checking data files..."
    if [[ ! -f "$CODEREVIEWER_TRAIN_DATA" ]]; then
        echo "Error: Training data file not found: $CODEREVIEWER_TRAIN_DATA"
        echo "Please specify the correct path with -t option or modify the script"
        exit 1
    fi
    if [[ ! -f "$CODEREVIEWER_DEV_DATA" ]]; then
        echo "Error: Development data file not found: $CODEREVIEWER_DEV_DATA"
        echo "Please specify the correct path with -d option or modify the script"
        exit 1
    fi
    if [[ ! -f "$CODEREVIEWER_TEST_DATA" ]]; then
        echo "Error: Test data file not found: $CODEREVIEWER_TEST_DATA"
        echo "Please specify the correct path with -s option or modify the script"
        exit 1
    fi
    echo "All data files found successfully!"
}

function finetune() {
    SCRIPT_PATH="src/fine_tuning/finetune_codereviewer_compat.py"
    
    echo "=== CCT5 CodeReviewer Compatibility Training ==="
    echo "Training data: $CODEREVIEWER_TRAIN_DATA"
    echo "Dev data: $CODEREVIEWER_DEV_DATA"
    echo "Test data: $CODEREVIEWER_TEST_DATA"
    echo "Model path: $MODEL_PATH"
    echo "GPU ID: $GPU_ID"
    echo "Eval flag: $EVAL_FLAG"
    echo "=============================================="
    
    if [[ $EVAL_FLAG == false ]]; then
        echo "Starting training..."
        python $SCRIPT_PATH \
            --do_train \
            --do_test \
            --train_filename "$CODEREVIEWER_TRAIN_DATA" \
            --dev_filename "$CODEREVIEWER_DEV_DATA" \
            --test_filename "$CODEREVIEWER_TEST_DATA" \
            --model_type codet5_CC \
            --warmup_steps 500 \
            --learning_rate 3e-4 \
            --tokenizer_name "/data/xjd/SodaCoder-main/models" \
            --model_name_or_path "/data/xjd/SodaCoder-main/models" \
            --load_model_path "$MODEL_PATH" \
            --output_dir "${CURRENT_DIR}/outputs/models/fine-tuning/CodeReviewerCompat" \
            --always_save_model \
            --train_batch_size 16 \
            --gradient_accumulation_steps 2 \
            --eval_batch_size 16 \
            --max_source_length 512 \
            --max_target_length 128 \
            --gpu_id ${GPU_ID} \
            --save_steps 2000 \
            --log_steps 100 \
            --train_steps 50000 \
            --num_train_epochs 10 \
            --beam_size 5 \
            --evaluate_sample_size -1 \
            --seed 42 \
            --raw_input \
            --weight_decay 0.01 \
            --adam_epsilon 1e-8
    else
        echo "Starting evaluation..."
        python $SCRIPT_PATH \
            --do_test \
            --train_filename "$CODEREVIEWER_TRAIN_DATA" \
            --dev_filename "$CODEREVIEWER_DEV_DATA" \
            --test_filename "$CODEREVIEWER_TEST_DATA" \
            --model_type codet5_CC \
            --warmup_steps 500 \
            --learning_rate 3e-4 \
            --tokenizer_name "Salesforce/codet5-base" \
            --model_name_or_path "Salesforce/codet5-base" \
            --load_model_path "$MODEL_PATH" \
            --output_dir "${CURRENT_DIR}/outputs/models/fine-tuning/CodeReviewerCompat" \
            --always_save_model \
            --train_batch_size 16 \
            --gradient_accumulation_steps 2 \
            --eval_batch_size 16 \
            --max_source_length 512 \
            --max_target_length 128 \
            --gpu_id ${GPU_ID} \
            --save_steps 2000 \
            --log_steps 100 \
            --train_steps 50000 \
            --num_train_epochs 10 \
            --beam_size 5 \
            --evaluate_sample_size -1 \
            --seed 42 \
            --raw_input \
            --weight_decay 0.01 \
            --adam_epsilon 1e-8
    fi 
}

function setup_environment() {
    echo "Setting up environment..."


    # Check if the compatibility script exists
    if [[ ! -f "src/fine_tuning/finetune_codereviewer_compat.py" ]]; then
        echo "Error: Compatibility script not found!"
        echo "Please ensure finetune_codereviewer_compat.py is in src/fine_tuning/"
        exit 1
    fi

    # Check if the compatibility utils exist
    if [[ ! -f "src/utils_codereviewer_compat.py" ]]; then
        echo "Error: Compatibility utils not found!"
        echo "Please ensure utils_codereviewer_compat.py is in src/"
        exit 1
    fi

    echo "Environment setup complete!"
}

function print_usage_info() {
    echo ""
    echo "=== CCT5 CodeReviewer Compatibility Training ==="
    echo ""
    echo "This script adapts CCT5 to work with CodeReviewer's data format."
    echo ""
    echo "Key features:"
    echo "- Uses CCT5's model architecture (codet5_CC with CodeChangeModel)"
    echo "- Processes CodeReviewer's data format (oldf, patch, msg fields)"
    echo "- Maintains CodeReviewer's preprocessing approach"
    echo "- Supports both training and evaluation modes"
    echo ""
    echo "Data format expected:"
    echo '{"oldf": "old_file_content", "patch": "diff_content", "msg": "commit_message", "cmtid": "commit_id"}'
    echo ""
    echo "Usage examples:"
    echo "  # Training with default data paths:"
    echo "  bash $0"
    echo ""
    echo "  # Training with custom data paths:"
    echo "  bash $0 -t /path/to/train.jsonl -d /path/to/dev.jsonl -s /path/to/test.jsonl"
    echo ""
    echo "  # Evaluation only:"
    echo "  bash $0 -e /path/to/model.bin"
    echo ""
    echo "  # Training on specific GPU:"
    echo "  bash $0 -g 1"
    echo ""
}

# Main execution
print_usage_info
setup_environment
check_data_files
finetune

echo ""
echo "=== Training Complete ==="
echo "Output directory: ${CURRENT_DIR}/outputs/models/fine-tuning/CodeReviewerCompat"
echo "Check the logs above for training progress and results."
echo ""
