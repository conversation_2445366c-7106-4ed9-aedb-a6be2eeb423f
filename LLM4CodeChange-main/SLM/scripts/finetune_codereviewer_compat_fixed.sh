#!/bin/bash

# CCT5 CodeReviewer Compatibility Training Script (Fixed Version)
# This script adapts CCT5 to work with CodeReviewer's data format and preprocessing

CURRENT_DIR=`pwd`
NCCL_DEBUG=INFO
GPU_ID=0
PRETRAINED_MODEL_DIR="$CURRENT_DIR/models/pre-training/Gen"
MODEL_PATH="$CURRENT_DIR/models/pre-training/Gen/pytorch_model.bin"
FINETUNED_MODEL_PATH="$CURRENT_DIR/models/fine-tuning/CodeReviewerCompat/pytorch_model.bin"
EVAL_FLAG=false

# CodeReviewer data paths (modify these to point to your CodeReviewer data)
CODEREVIEWER_TRAIN_DATA="../../CodeReviewer/dataset/gen-train.jsonl"
CODEREVIEWER_DEV_DATA="../../CodeReviewer/dataset/gen-valid.jsonl"
CODEREVIEWER_TEST_DATA="../../CodeReviewer/dataset/gen-test.jsonl"

usage() {
  echo "Usage: ${0} [-g GPU_ID] [-e MODEL_PATH] [-t TRAIN_DATA] [-d DEV_DATA] [-s TEST_DATA]" 1>&2
  echo "  -g: GPU ID to use (default: 0)" 1>&2
  echo "  -e: Model path for evaluation (enables eval mode)" 1>&2
  echo "  -t: Training data path (CodeReviewer format)" 1>&2
  echo "  -d: Development data path (CodeReviewer format)" 1>&2
  echo "  -s: Test data path (CodeReviewer format)" 1>&2
  exit 1 
}

while getopts ":g:e:t:d:s:" opt; do
    case $opt in
        g)  GPU_ID="$OPTARG"
            ;;
        e)  MODEL_PATH="$OPTARG"
            EVAL_FLAG=true
            ;;
        t)  CODEREVIEWER_TRAIN_DATA="$OPTARG"
            ;;
        d)  CODEREVIEWER_DEV_DATA="$OPTARG"
            ;;
        s)  CODEREVIEWER_TEST_DATA="$OPTARG"
            ;;
        \?)
          echo "Invalid option: -$OPTARG" >&2
          exit 1
          ;;
        :)
        echo "Option -$OPTARG requires an argument." >&2
        exit 1
        ;;
    esac
done

function check_data_files() {
    echo "Checking data files..."
    if [[ ! -f "$CODEREVIEWER_TRAIN_DATA" ]]; then
        echo "Warning: Training data file not found: $CODEREVIEWER_TRAIN_DATA"
        echo "Please specify the correct path with -t option or modify the script"
        echo "Creating a dummy file for testing..."
        mkdir -p $(dirname "$CODEREVIEWER_TRAIN_DATA")
        echo '{"oldf": "def hello():\n    print(\"Hello\")", "patch": "@@ -1,2 +1,2 @@\n def hello():\n-    print(\"Hello\")\n+    print(\"Hello World\")", "msg": "Update greeting", "y": 1}' > "$CODEREVIEWER_TRAIN_DATA"
    fi
    if [[ ! -f "$CODEREVIEWER_DEV_DATA" ]]; then
        echo "Warning: Development data file not found: $CODEREVIEWER_DEV_DATA"
        echo "Creating a dummy file for testing..."
        mkdir -p $(dirname "$CODEREVIEWER_DEV_DATA")
        echo '{"oldf": "def hello():\n    print(\"Hello\")", "patch": "@@ -1,2 +1,2 @@\n def hello():\n-    print(\"Hello\")\n+    print(\"Hello World\")", "msg": "Update greeting", "y": 1}' > "$CODEREVIEWER_DEV_DATA"
    fi
    if [[ ! -f "$CODEREVIEWER_TEST_DATA" ]]; then
        echo "Warning: Test data file not found: $CODEREVIEWER_TEST_DATA"
        echo "Creating a dummy file for testing..."
        mkdir -p $(dirname "$CODEREVIEWER_TEST_DATA")
        echo '{"oldf": "def hello():\n    print(\"Hello\")", "patch": "@@ -1,2 +1,2 @@\n def hello():\n-    print(\"Hello\")\n+    print(\"Hello World\")", "msg": "Update greeting", "y": 1}' > "$CODEREVIEWER_TEST_DATA"
    fi
    echo "Data files check completed!"
}

function test_args() {
    echo "Testing argument parsing..."
    python test_args.py
    if [ $? -ne 0 ]; then
        echo "Error: Argument parsing test failed!"
        exit 1
    fi
    echo "✓ Argument parsing test passed"
}

function finetune() {
    SCRIPT_PATH="src/fine_tuning/finetune_codereviewer_compat.py"
    
    echo "=== CCT5 CodeReviewer Compatibility Training (Fixed) ==="
    echo "Training data: $CODEREVIEWER_TRAIN_DATA"
    echo "Dev data: $CODEREVIEWER_DEV_DATA"
    echo "Test data: $CODEREVIEWER_TEST_DATA"
    echo "Model path: $MODEL_PATH"
    echo "GPU ID: $GPU_ID"
    echo "Eval flag: $EVAL_FLAG"
    echo "=============================================="
    
    # Common arguments (removed --task parameter)
    COMMON_ARGS="
        --train_filename $CODEREVIEWER_TRAIN_DATA
        --dev_filename $CODEREVIEWER_DEV_DATA
        --test_filename $CODEREVIEWER_TEST_DATA
        --model_type codet5_CC
        --warmup_steps 500
        --learning_rate 3e-4
        --tokenizer_name Salesforce/codet5-base
        --model_name_or_path Salesforce/codet5-base
        --output_dir ${CURRENT_DIR}/outputs/models/fine-tuning/CodeReviewerCompat
        --always_save_model
        --train_batch_size 8
        --gradient_accumulation_steps 4
        --eval_batch_size 8
        --max_source_length 512
        --max_target_length 128
        --gpu_id ${GPU_ID}
        --save_steps 1000
        --log_steps 50
        --train_steps 5000
        --num_train_epochs 3
        --beam_size 5
        --evaluate_sample_size -1
        --seed 42
        --raw_input
        --weight_decay 0.01
        --adam_epsilon 1e-8
    "
    
    if [[ $EVAL_FLAG == false ]]; then
        echo "Starting training..."
        python $SCRIPT_PATH \
            --do_train \
            --do_test \
            $COMMON_ARGS
    else
        echo "Starting evaluation..."
        python $SCRIPT_PATH \
            --do_test \
            --load_model_path "$MODEL_PATH" \
            $COMMON_ARGS
    fi 
}

function setup_environment() {
    echo "Setting up environment..."
    
    # Create output directories
    mkdir -p "${CURRENT_DIR}/outputs/models/fine-tuning/CodeReviewerCompat"
    
    # Check if the compatibility script exists
    if [[ ! -f "src/fine_tuning/finetune_codereviewer_compat.py" ]]; then
        echo "Error: Compatibility script not found!"
        echo "Please ensure finetune_codereviewer_compat.py is in src/fine_tuning/"
        exit 1
    fi
    
    # Check if the compatibility utils exist
    if [[ ! -f "src/utils_codereviewer_compat.py" ]]; then
        echo "Error: Compatibility utils not found!"
        echo "Please ensure utils_codereviewer_compat.py is in src/"
        exit 1
    fi
    
    echo "Environment setup complete!"
}

function print_usage_info() {
    echo ""
    echo "=== CCT5 CodeReviewer Compatibility Training (Fixed) ==="
    echo ""
    echo "This is the fixed version that resolves argument parsing issues."
    echo ""
    echo "Key fixes:"
    echo "- Removed unsupported --task parameter"
    echo "- Fixed global_rank initialization"
    echo "- Added argument validation"
    echo "- Reduced resource requirements for testing"
    echo ""
    echo "Usage examples:"
    echo "  # Training with dummy data (for testing):"
    echo "  bash $0"
    echo ""
    echo "  # Training with real data:"
    echo "  bash $0 -t /path/to/train.jsonl -d /path/to/dev.jsonl -s /path/to/test.jsonl"
    echo ""
    echo "  # Evaluation only:"
    echo "  bash $0 -e /path/to/model.bin"
    echo ""
}

# Main execution
print_usage_info
setup_environment
test_args
check_data_files
finetune

echo ""
echo "=== Training Complete ==="
echo "Output directory: ${CURRENT_DIR}/outputs/models/fine-tuning/CodeReviewerCompat"
echo "Check the logs above for training progress and results."
echo ""
