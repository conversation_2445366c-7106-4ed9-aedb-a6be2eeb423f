#!/usr/bin/env python3
"""
Test script to verify that the FCL DeepSpeed training script has proper validation functionality.
"""

import sys
import os

def check_fcl_deepspeed_validation_implementation():
    """Check if the FCL DeepSpeed training script has proper validation implementation."""
    
    # Read the FCL DeepSpeed training script
    fcl_script_path = "CodeReviewer/code/run_finetune_fcl_deepspeed.py"
    
    if not os.path.exists(fcl_script_path):
        print(f"❌ FCL DeepSpeed script not found: {fcl_script_path}")
        return False
    
    with open(fcl_script_path, 'r') as f:
        content = f.read()
    
    # Check for required imports
    required_imports = [
        "CommentGenDataset",
        "SimpleGenDataset", 
        "bleu_fromstr"
    ]
    
    print("🔍 Checking imports...")
    for imp in required_imports:
        if imp in content:
            print(f"  ✅ {imp} imported")
        else:
            print(f"  ❌ {imp} missing")
            return False
    
    # Check for required functions
    required_functions = [
        "get_loaders",
        "eval_bleu_epoch"
    ]
    
    print("\n🔍 Checking functions...")
    for func in required_functions:
        if f"def {func}(" in content:
            print(f"  ✅ {func} function defined")
        else:
            print(f"  ❌ {func} function missing")
            return False
    
    # Check for DeepSpeed-specific adaptations
    print("\n🔍 Checking DeepSpeed adaptations...")
    
    # Check if eval_bleu_epoch is adapted for DeepSpeed
    if "model_engine, tokenizer" in content and "model_engine.eval()" in content:
        print("  ✅ eval_bleu_epoch adapted for DeepSpeed")
    else:
        print("  ❌ eval_bleu_epoch not properly adapted for DeepSpeed")
        return False
    
    # Check if validation is called before saving checkpoints
    if "eval_bleu_epoch(args, valid_dataloader, model_engine, tokenizer)" in content:
        print("  ✅ Validation called before saving checkpoints")
    else:
        print("  ❌ Validation not integrated into checkpoint saving")
        return False
    
    # Check if BLEU score is included in checkpoint directory names
    if 'str(bleu)' in content and 'checkpoints-' in content:
        print("  ✅ BLEU score included in checkpoint directory names")
    else:
        print("  ❌ BLEU score not included in checkpoint names")
        return False
    
    # Check for validation file requirement
    if "dev_filename is None" in content:
        print("  ✅ Validation file requirement check added")
    else:
        print("  ❌ Validation file requirement check missing")
        return False
    
    # Check for rank-0 specific validation logic
    if "args.global_rank == 0" in content and "eval_bleu_epoch" in content:
        print("  ✅ Rank-0 specific validation logic implemented")
    else:
        print("  ❌ Rank-0 specific validation logic missing")
        return False
    
    # Check for beam_size parameter
    if "--beam_size" in content:
        print("  ✅ beam_size parameter added")
    else:
        print("  ❌ beam_size parameter missing")
        return False
    
    print("\n✅ All DeepSpeed validation checks passed!")
    return True

def check_consistency_with_regular_fcl():
    """Check consistency with the regular FCL training script."""
    
    regular_fcl_path = "CodeReviewer/code/run_finetune_fcl.py"
    deepspeed_fcl_path = "CodeReviewer/code/run_finetune_fcl_deepspeed.py"
    
    if not os.path.exists(regular_fcl_path):
        print(f"❌ Regular FCL script not found: {regular_fcl_path}")
        return False
    
    if not os.path.exists(deepspeed_fcl_path):
        print(f"❌ DeepSpeed FCL script not found: {deepspeed_fcl_path}")
        return False
    
    with open(regular_fcl_path, 'r') as f:
        regular_content = f.read()
    
    with open(deepspeed_fcl_path, 'r') as f:
        deepspeed_content = f.read()
    
    print("🔍 Checking consistency with regular FCL script...")
    
    # Check if both have similar validation functions
    common_patterns = [
        "def get_loaders(",
        "def eval_bleu_epoch(",
        "bleu_fromstr(pred_nls, golds, rmstop=False)",
        "CommentGenDataset",
        "SimpleGenDataset"
    ]
    
    for pattern in common_patterns:
        regular_has = pattern in regular_content
        deepspeed_has = pattern in deepspeed_content
        
        if regular_has and deepspeed_has:
            print(f"  ✅ Both have: {pattern}")
        elif regular_has and not deepspeed_has:
            print(f"  ❌ DeepSpeed missing: {pattern}")
            return False
        elif not regular_has and deepspeed_has:
            print(f"  ⚠️  Only DeepSpeed has: {pattern}")
    
    # Check DeepSpeed-specific adaptations
    deepspeed_specific = [
        "model_engine",
        "args.global_rank == 0",
        "torch.no_grad()"
    ]
    
    for pattern in deepspeed_specific:
        if pattern in deepspeed_content:
            print(f"  ✅ DeepSpeed-specific: {pattern}")
        else:
            print(f"  ❌ Missing DeepSpeed-specific: {pattern}")
            return False
    
    print("  ✅ Consistency checks passed!")
    return True

def main():
    """Main test function."""
    print("🧪 Testing FCL DeepSpeed validation implementation...\n")
    
    # Test 1: Check validation implementation
    test1_passed = check_fcl_deepspeed_validation_implementation()
    
    print("\n" + "="*60 + "\n")
    
    # Test 2: Check consistency with regular FCL script
    test2_passed = check_consistency_with_regular_fcl()
    
    print("\n" + "="*60 + "\n")
    
    if test1_passed and test2_passed:
        print("🎉 All tests passed! FCL DeepSpeed validation implementation is correct.")
        return 0
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
