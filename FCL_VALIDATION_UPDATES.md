# FCL 训练脚本验证步骤更新

## 概述

参考 `run_finetune_msg.py` 的实现，为 FCL 训练脚本添加了验证步骤，并在保存检查点时注明验证集上的表现。

## 修改的文件

1. `CodeReviewer/code/run_finetune_fcl.py` - 标准 FCL 训练脚本
2. `CodeReviewer/code/run_finetune_fcl_deepspeed.py` - DeepSpeed 版本的 FCL 训练脚本

## 主要修改内容

### 1. 导入更新

**修改前:**
```python
from utils import FCLDataset
from evaluator.smooth_bleu import bleu_fromstr
```

**修改后:**
```python
from utils import FCLDataset, CommentGenDataset, SimpleGenDataset
from evaluator.smooth_bleu import bleu_fromstr
```

### 2. 新增验证相关函数

#### 2.1 `get_loaders` 函数
- 用于创建验证数据加载器
- 支持 `SimpleGenDataset` 和 `CommentGenDataset`
- 与 `run_finetune_msg.py` 保持一致

#### 2.2 `eval_bleu_epoch` 函数
- 在验证集上评估模型性能
- 计算 BLEU 分数
- 标准版本使用 `model.generate()`
- DeepSpeed 版本适配 `model_engine`，使用 `torch.no_grad()`

### 3. 参数验证增强

**新增检查:**
```python
# Ensure validation file is provided for evaluation
if args.dev_filename is None:
    raise ValueError("--dev_filename is required for validation during FCL training")
```

### 4. 检查点保存更新

#### 4.1 训练结束时的保存

**修改前:**
```python
output_dir = os.path.join(args.output_dir, "checkpoints-last")
save_model(model, optimizer, scheduler, output_dir, config)
logger.info(f"Reach max steps {args.train_steps}.")
```

**修改后:**
```python
_, _, valid_dataloader = next(get_loaders(valid_files, args, tokenizer, pool, eval=True))
bleu = eval_bleu_epoch(args, valid_dataloader, model, tokenizer)
output_dir = os.path.join(args.output_dir, "checkpoints-last" + "-" + str(bleu))
save_model(model, optimizer, scheduler, output_dir, config)
logger.info(f"Reach max steps {args.train_steps}. Final BLEU: {bleu}")
```

#### 4.2 定期保存时的更新

**修改前:**
```python
output_dir = os.path.join(args.output_dir, "checkpoints-" + str(global_step))
save_model(model, optimizer, scheduler, output_dir, config)
logger.info("Save the {}-step model and optimizer into {}".format(global_step, output_dir))
```

**修改后:**
```python
_, _, valid_dataloader = next(get_loaders(valid_files, args, tokenizer, pool, eval=True))
bleu = eval_bleu_epoch(args, valid_dataloader, model, tokenizer)
output_dir = os.path.join(args.output_dir, "checkpoints-" + str(global_step) + "-" + str(bleu))
save_model(model, optimizer, scheduler, output_dir, config)
logger.info("Save the {}-step model and optimizer into {} with BLEU: {}".format(global_step, output_dir, bleu))
```

### 5. DeepSpeed 特定修改

#### 5.1 分布式训练适配
- 只有 rank-0 进程执行验证
- 其他进程使用默认目录名避免冲突
- 所有进程参与检查点保存

#### 5.2 模型访问适配
```python
# Get the underlying model from DeepSpeed engine
model = model_engine.module if hasattr(model_engine, "module") else model_engine

# Use torch.no_grad() for inference
with torch.no_grad():
    preds = model.generate(...)
```

#### 5.3 新增参数
```python
# 验证参数
parser.add_argument("--beam_size", type=int, default=5)
```

## 验证数据格式

验证数据文件（如 `msg-valid.jsonl`）应包含以下字段：
```json
{"patch": "...", "msg": "target comment", ...}
```

验证函数会：
1. 从 `patch` 字段生成预测
2. 与 `msg` 字段进行 BLEU 比较
3. 返回 BLEU 分数

## 检查点目录命名

**修改后的命名格式:**
- 最终检查点: `checkpoints-last-{bleu_score}`
- 定期检查点: `checkpoints-{step}-{bleu_score}`

**示例:**
- `checkpoints-last-23.45`
- `checkpoints-1800-21.32`
- `checkpoints-3600-24.78`

## 使用方法

确保在运行训练时提供验证文件：

```bash
python run_finetune_fcl.py \
    --fcl_data_file /path/to/train.jsonl \
    --dev_filename /path/to/valid.jsonl \
    --output_dir /path/to/output \
    ...
```

## 测试脚本

创建了两个测试脚本来验证修改：
- `test_fcl_validation.py` - 测试标准版本
- `test_fcl_deepspeed_validation.py` - 测试 DeepSpeed 版本

## 兼容性

- 与原有 FCL 训练流程完全兼容
- 保持与 `run_finetune_msg.py` 的一致性
- 支持分布式训练和 DeepSpeed
- 向后兼容现有的配置和脚本
