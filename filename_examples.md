# 文件名自动范围标记功能示例

## 功能说明

现在保存文件时，文件名会自动包含处理的数据序号范围，方便识别和管理不同批次的处理结果。

## 文件名格式

### 处理全部数据
- 输入: `--output-file results.jsonl` (处理全部100条数据)
- 输出: `results_all_100.jsonl`

### 处理指定范围
- 输入: `--output-file results.jsonl --range 10:20` (处理第10-19条数据)
- 输出: `results_10-19.jsonl`

- 输入: `--output-file results.jsonl --start-idx 0 --end-idx 50` (处理前50条)
- 输出: `results_0-49.jsonl`

- 输入: `--output-file results.jsonl --range 50:` (从第50条到结尾)
- 输出: `results_50-99.jsonl` (假设总共100条数据)

- 输入: `--output-file results.jsonl --range :30` (从开始到第30条)
- 输出: `results_0-29.jsonl`

## 使用示例

```bash
# 处理全部数据
python parallel_inference.py --input-file data.jsonl --output-file results.jsonl
# 生成文件: results_all_1000.jsonl (假设有1000条数据)

# 处理前100条数据
python parallel_inference.py --input-file data.jsonl --output-file results.jsonl --range :100
# 生成文件: results_0-99.jsonl

# 处理第100-199条数据
python parallel_inference.py --input-file data.jsonl --output-file results.jsonl --range 100:200
# 生成文件: results_100-199.jsonl

# 处理最后100条数据
python parallel_inference.py --input-file data.jsonl --output-file results.jsonl --range 900:
# 生成文件: results_900-999.jsonl (假设总共1000条数据)
```

## 优势

1. **自动标记**: 无需手动修改文件名，系统自动添加范围信息
2. **批次管理**: 可以轻松识别不同批次的处理结果
3. **避免覆盖**: 不同范围的处理结果不会相互覆盖
4. **便于合并**: 可以根据文件名轻松识别和合并不同批次的结果

## 注意事项

- 索引从0开始
- end_idx是exclusive的（不包含），所以文件名中显示的是实际处理的最后一个索引
- 例如：`--range 10:20` 处理索引10-19，文件名为 `output_10-19.jsonl`
